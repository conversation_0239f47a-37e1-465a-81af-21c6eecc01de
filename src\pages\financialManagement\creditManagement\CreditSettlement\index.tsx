/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-23 10:22:12
 * @LastEditTime: 2023-09-15 18:14:45
 * @LastEditors: zhangfengfei
 */
import { tableConfig } from '@/common/utils/config';
import { SettlementStatusEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import useModal from '@/hooks/useModal';
import { confirmCreditBill, getProviderSettlementList } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Popconfirm, Space, Tag, message } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useRef, useState } from 'react';
import { Access, useAccess, useModel, useRequest } from '@umijs/max';
import SettlementDetailModal from './SettlementDetailModal';

type CreditSettlementProps = Record<string, never>;

/**
 * @description 财务管理->授信管理->授信结算单
 * */
const CreditSettlement: FC<CreditSettlementProps> = () => {
  const { initialState } = useModel('@@initialState');
  const { settlementId = '' } = initialState?.currentCompany || {};
  const actionRef = useRef<ActionType>();
  const access = useAccess();

  const detailModalState = useModal();

  const [tableListItem, setTableListItem] = useState<API.ProviderSettlementItem>();

  const billConfirmReq = useRequest(confirmCreditBill, {
    manual: true,
    onSuccess: () => {
      addOperationLogRequest({
        action: 'confirm',
        content: `确认结算单【${tableListItem?.id}】结算金额`,
      });
      message.success('确认成功');
      actionRef.current?.reload();
    },
  });

  const tableListReq = async (params: API.ProviderSettlementListParams) => {
    const { data } = await getProviderSettlementList(params);

    return {
      data: data.page,
      total: data.totalNumberOfResults,
    };
  };
  const onConfirm = async () => {
    if (tableListItem)
      billConfirmReq.run({
        id: tableListItem.id,
        merchantId: settlementId,
      });
  };
  const columns: ProColumns<API.ProviderSettlementItem>[] = [
    {
      title: '单号',
      dataIndex: 'id',
    },
    {
      title: '创建时间',
      dataIndex: 'creationDate',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            creationFromDate: value[0],
            creationToDate: value[1],
          };
        },
      },
    },
    {
      title: '授信客户',
      dataIndex: 'consumeLegalName',
      search: false,
    },

    {
      title: '结算状态',
      dataIndex: 'settlementStatus',
      valueType: 'select',
      valueEnum: SettlementStatusEnum,
      renderText: (text) => {
        const colorMap = {
          FINISHED: {
            text: '已确认',
            color: 'blue',
          },
          PENDING: {
            text: '待结算',
            color: 'orange',
          },
        };
        return <Tag color={colorMap[text].color}>{colorMap[text].text}</Tag>;
      },
    },
    {
      title: '付款时间',
      dataIndex: 'payedDate',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      search: false,
    },
    {
      title: '付款时间',
      dataIndex: 'pay_date',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            payedFromDate: value[0],
            payedToDate: value[1],
          };
        },
      },
    },
    {
      title: '应结算金额（元）',
      dataIndex: 'amount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      renderText: (_, record) => (
        <Space
          onClick={() => {
            setTableListItem(record);
          }}
        >
          <Access key="add" accessible={access.canCreditBill_detail}>
            <a
              onClick={() => {
                detailModalState.setVisible(true);
              }}
            >
              结算明细
            </a>
          </Access>
          <Access key="add" accessible={access.canCreditBill_confirm}>
            {record.settlementStatus == 'PENDING' && (
              <Popconfirm
                placement="top"
                title={'是否确认已收到全部金额？'}
                onConfirm={onConfirm}
                okButtonProps={{
                  loading: billConfirmReq.loading,
                }}
              >
                <Button type="link">确认</Button>
              </Popconfirm>
            )}
          </Access>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.ProviderSettlementItem, API.ProviderSettlementListParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={tableListReq}
        params={{
          merchantId: settlementId,
        }}
        pagination={{
          defaultPageSize: 10,
        }}
      />
      {/* 结算明细modal */}
      <SettlementDetailModal currentItem={tableListItem} modalState={detailModalState} />
    </>
  );
};

export default CreditSettlement;
