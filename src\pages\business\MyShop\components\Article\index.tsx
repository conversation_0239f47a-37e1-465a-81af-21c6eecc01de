import Delete from '@/common/components/Delete';
import Disabled from '@/common/components/Disabled';
import ImageUpload from '@/common/components/ImageUpload';
import MDEditor from '@/common/components/MDEditor';
import ProModal from '@/common/components/ProModal';
import useModal from '@/common/components/ProModal/useProModal';
import { tableConfig } from '@/common/utils/config';
import {
  ArticleEnableType,
  ArticleSourceType,
  ArticleType,
  GuideStepStatus,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getHashParams, removeStateFromUrl } from '@/common/utils/tool';
import { useGuide } from '@/hooks/useGuide';
import {
  addArticle,
  deleteArticle,
  editArticle,
  editSortArticle,
  enableArticle,
  infoArticle,
  pageArticle,
  sortListArticle,
} from '@/services/api/article';
import { PlusOutlined } from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useRequest } from '@umijs/max';
import { Button, Modal, Select, Typography, message } from 'antd';
import type { DefaultOptionType } from 'antd/lib/select';
import { cloneDeep } from 'lodash';
import dayjs from 'dayjs';
import { useContext, useEffect, useRef, useState } from 'react';
import { TabKeyContext } from '../..';

interface ArticleSortEntity {
  id: string;
  articleName: string;
  articleSort: number;
}

/*
 * @Author: bankewei
 * @Date: 2023 年 12 月 11 日
 */
export default ({ store: { value: storeId } }: any) => {
  const queryParams = getHashParams();
  const { updateGuideInfo } = useGuide();
  const modalState = useModal();
  const actionRef = useRef<ActionType>();
  const actionRef2 = useRef<ActionType>();
  const [id, setId] = useState<string | null>();
  const [sortVisiable, setSortVisiable] = useState(false);
  const [sortDataSource, setSortDataSource] = useState<ArticleSortEntity[]>([]);
  const [sortOption, setSortOption] = useState<DefaultOptionType[]>([]);

  const tabKey = useContext(TabKeyContext);

  const tableColumns: ProColumnType[] = [
    {
      title: '序号',
      valueType: 'index',
    },
    {
      title: '文章名称',
      dataIndex: 'articleName',
      renderText: (text: string) => (
        <Typography.Text style={{ maxWidth: 200 }} ellipsis={true}>
          {text}
        </Typography.Text>
      ),
    },
    {
      title: '文章类型',
      dataIndex: 'articleType',
      valueEnum: ArticleType,
    },

    {
      title: '作者',
      dataIndex: 'articleAuthor',
    },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      hideInSearch: true,
    },
    {
      title: '发布时间',
      key: 'time',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startPublishTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            endPublishTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '文章来源',
      dataIndex: 'articleSource',
      valueEnum: ArticleSourceType,
    },
    {
      title: '阅读量',
      dataIndex: 'readCount',
      hideInSearch: true,
    },
    {
      title: '推荐状态',
      dataIndex: 'recommendState',
      valueEnum: ArticleEnableType,
      renderText: (dom: any, entity: any) => (
        <Disabled
          access={true}
          status={dom == 2}
          params={{
            id: entity.id,
            recommendState: dom == 2 ? 1 : 2,
          }}
          request={async (params) => {
            const data = await enableArticle(params);
            addOperationLogRequest({
              action: 'disable',
              module: tabKey,
              content: `${dom == 1 ? '启用' : '关闭'}【${entity.articleName}】文章推荐`,
            });
            return data;
          }}
          actionRef={actionRef}
        />
      ),
    },
    {
      title: '启用状态',
      dataIndex: 'enableState',
      valueEnum: ArticleEnableType,
      renderText: (dom: any, entity: any) => (
        <Disabled
          access={true}
          status={dom == 2}
          params={{
            id: entity.id,
            enableState: dom == 2 ? 1 : 2,
          }}
          request={async (params) => {
            const data = await enableArticle(params);
            addOperationLogRequest({
              action: 'disable',
              module: tabKey,
              content: `${dom == 1 ? '启用' : '禁用'}【${entity.articleName}】文章`,
            });
            return data;
          }}
          actionRef={actionRef}
        />
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_: any, entity: any) => [
        <a
          onClick={() => {
            setId(entity.id);
            modalState.setType('info');
          }}
          key="k1"
        >
          查看
        </a>,
        <a
          onClick={() => {
            setId(entity.id);
            modalState.setType('edit');
          }}
          key="k2"
        >
          编辑
        </a>,
        <Delete
          key="k3"
          access={true}
          status={entity.enableState == 2}
          params={{ id: entity.id }}
          request={async (params) => {
            const data = await deleteArticle(params);
            addOperationLogRequest({
              action: 'del',
              module: tabKey,
              content: `删除【${entity.articleName}】文章`,
            });
            return data;
          }}
          actionRef={actionRef}
        />,
      ],
    },
  ];

  const modalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '文章名称',
          dataIndex: 'articleName',
          fieldProps: {
            maxLength: 50,
          },
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '文章类型',
          dataIndex: 'articleType',
          valueEnum: ArticleType,
          initialValue: '1',
          convertValue: (value) => String(value ?? '') || value,
          fieldProps: {
            allowClear: false,
            fieldProps: { disabled: modalState.type == 'edit' },
          },
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '作者',
          dataIndex: 'articleAuthor',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '发布日期',
          dataIndex: 'publishTime',
          span: 3,
          valueType: 'date',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '宣传图',
          dataIndex: 'publicizeImgUrl',
          span: 3,
          colProps: { span: 24 },
          formItemProps: { rules: [{ required: true }] },
          renderText: (text: any) => <ImageUpload defaultValue={text} readonly />,
          renderFormItem: (_, __, formRef) => (
            <ImageUpload
              defaultValue={formRef.getFieldValue('publicizeImgUrl')}
              multiple={true}
              size={10240}
              maxCount={6}
            />
          ),
        },
        {
          title: '文章来源',
          dataIndex: 'articleSource',
          valueEnum: ArticleSourceType,
          initialValue: '1',
          span: 3,
          convertValue: (value) => String(value ?? '') || value,
          fieldProps: {
            allowClear: false,
          },
          formItemProps: { rules: [{ required: true }] },
        },
        {
          hideInDescriptions: true,
          valueType: 'dependency',
          name: ['articleSource'],
          columns: ({ articleSource }) =>
            articleSource == 1
              ? [
                  {
                    title: '文章详情',
                    colProps: { span: 24 },
                    dataIndex: 'articleContent',
                    formItemProps: { rules: [{ required: true }] },
                    renderText: (text: any) => <MDEditor value={text} readonly />,
                    renderFormItem: () => <MDEditor />,
                  },
                ]
              : [
                  {
                    title: '文章外链',
                    dataIndex: 'externalUrl',
                    formItemProps: { rules: [{ required: true }] },
                  },
                ],
        },
      ],
    },
  ];

  const sortColumns: ProColumnType[] = [
    {
      title: '文章名称',
      dataIndex: 'articleName',
    },
    {
      title: '当前排序',
      dataIndex: 'articleSort',
      renderText: (v, record: any) => (
        <Select
          defaultValue={v}
          onChange={(value) => changeSort(record, value)}
          options={sortOption}
        />
      ),
    },
  ];

  /**
   * 修改排序的算法
   *
   * @param moveEntity 要移动的实现对象
   * @param targetRank 移动到的目标排名
   */
  const changeSort = (moveEntity: ArticleSortEntity, targetRank: number) => {
    const tempSort: ArticleSortEntity[] = cloneDeep(sortDataSource);
    setSortDataSource([]); //先置空，解决 select 框不刷新问题
    const sourceIndex = tempSort.findIndex((item) => item.id == moveEntity.id);
    tempSort.splice(sourceIndex, 1); //删除原来的项
    const direction = moveEntity.articleSort > targetRank ? 1 : 0;
    tempSort.splice(
      tempSort.findIndex((item) => item.articleSort == targetRank) + direction,
      0,
      moveEntity,
    ); //把原来的项移动到目标元素后一个
    setTimeout(() => {
      //延时刷新，这样避免后面的 select 不刷新问题，但界面会闪一下
      setSortDataSource(
        tempSort.map((entity, index) => {
          //再把 articleSort 重新排
          entity.articleSort = tempSort.length - index;
          return entity;
        }),
      );
    }, 50);
  };

  /** 排完序后点击的确认 */
  const onSortOkClick = async () => {
    const list = sortDataSource.map((item) => {
      return {
        id: item.id,
        articleSort: item.articleSort,
      };
    });
    const result = await editSortArticle({ list });
    addOperationLogRequest({
      action: 'edit',
      module: tabKey,
      content: `编辑文章列表排序`,
    });
    if (result.success) {
      setSortVisiable(false);
      message.success('操作成功');
    } else {
      message.info(result.msg);
    }
  };

  /**获取文章排序列表 */
  const getSortDataRequest = useRequest(sortListArticle, {
    manual: true,
    onSuccess(result) {
      if (result.length > 0) {
        const l = result.length;
        setSortOption(
          new Array(l).fill(null).map((_, i) => {
            return { value: l - i, label: l - i };
          }),
        );
        setSortDataSource(result);
      }
    },
    onError() {},
  });

  useEffect(() => {
    if (queryParams?.operate === 'addArticle') {
      setId(null);
      modalState.setType('add');
    }
  }, [storeId]);

  return (
    <>
      <ProTable
        {...tableConfig}
        style={modalState.tableStyle}
        actionRef={actionRef}
        columns={tableColumns}
        toolBarRender={() => [
          <Button key="k2" type="primary" onClick={() => setSortVisiable(true)}>
            {' '}
            排序
          </Button>,
          <Button
            key="k1"
            type="primary"
            onClick={() => {
              setId(null);
              modalState.setType('add');
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        params={{ storeId }}
        request={pageArticle}
      />
      <ProModal
        page
        {...modalState}
        title="文章"
        actionRef={actionRef}
        columns={modalColumns}
        params={{ id, storeId }}
        infoRequest={async (params) => {
          const data = await infoArticle(params);
          addOperationLogRequest({
            action: 'info',
            module: tabKey,
            content: `查看【${data.articleName}】文章`,
          });
          return data;
        }}
        addRequest={async (params) => {
          const data = await addArticle(params);
          if (queryParams?.operate) {
            history.pushState(null, null, removeStateFromUrl('operate'));
          }
          // 更新引导
          updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_6 });
          addOperationLogRequest({
            action: 'add',
            module: tabKey,
            content: `新增【${params.articleName}】文章`,
          });
          return data;
        }}
        editRequest={async (params) => {
          const data = await editArticle(params);
          addOperationLogRequest({
            action: 'edit',
            module: tabKey,
            content: `编辑【${params.articleName}】文章`,
          });
          return data;
        }}
      />
      <Modal
        title="排序"
        destroyOnClose={true}
        onOk={onSortOkClick}
        onCancel={() => {
          setSortDataSource([]);
          setSortVisiable(false);
        }}
        open={sortVisiable}
      >
        文章类型：
        <Select
          style={{ width: modelWidth.sm / 2 }}
          onChange={(type) => {
            getSortDataRequest.run({
              articleType: type,
              storeId: storeId,
            });
          }}
          options={Object.keys(ArticleType).map((k) => {
            return { value: k, label: ArticleType[k] };
          })}
        />
        <ProTable
          {...tableConfig}
          columns={sortColumns}
          search={false}
          rowKey="id"
          actionRef={actionRef2}
          pagination={{
            showQuickJumper: true,
            pageSize: 10,
          }}
          dataSource={sortDataSource}
        />
      </Modal>
    </>
  );
};
