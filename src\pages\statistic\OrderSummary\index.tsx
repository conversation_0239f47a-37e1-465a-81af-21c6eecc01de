import { tableConfig } from '@/common/utils/config';
import {
  orderTypeEnum,
  payTypeEnum,
  productTypeEnum,
  saleChannelEnum,
  ticketStatusEnum,
  ticketTypeEnum,
} from '@/common/utils/enum';
import { showTicketModal } from '@/global';
import { getAgentsList, getDistributorList, getSupplierList } from '@/services/api/datareport';
import { apiShopList } from '@/services/api/store';
import { CopyOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { Space, Table, Tabs, message } from 'antd';
import copy from 'copy-to-clipboard';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import { useModel } from '@umijs/max';
import Toagent from './components/Toagent';
import Todistributor from './components/Todistributor';
import Touser from './components/Touser';
import './index.less';

const OrderSummary: React.FC = () => {
  const { initialState }: any = useModel('@@initialState');

  // TAP list
  const tapList: any[] = [
    {
      title: '面向渠道商',
    },
    {
      title: '面向用户',
    },
    {
      title: '面向代理商',
    },
  ];

  const [showList, setShowList] = useState<Record<string, any>[]>([]); //店铺列表
  const [supplierList, setSupplierList] = useState<Record<string, any>[]>([]); //供应商列表
  const [distributorList, setDistributorList] = useState<Record<string, any>[]>([]); //全部经销商
  const [Agency, setAgency] = useState<Record<string, any>[]>([]); //全部代理商

  const [index, setIndex] = useState<string | number>(0);

  const getSupplier = async (): Promise<[]> => {
    //获取供应商
    try {
      const params = {
        current: 1,
        pageSize: 20,
        distributorId: initialState.currentCompany.coId,
        type: 1,
      };
      const { code, data } = await getSupplierList(params);
      if (code == 20000) {
        setSupplierList(data);
      }
    } catch (err) {
      console.log(err);
    }
    return [];
  };

  const getDistributor = async () => {
    //获取分销商 - 全部经销商
    try {
      const params = {
        current: 1,
        pageSize: 20,
        distributorId: initialState.currentCompany.coId,
      };
      const { code, data } = await getDistributorList(params);
      if (code == 20000) {
        setDistributorList(data);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const getAgency = async () => {
    //获取分销商 - 全部代理商
    try {
      const params = {
        current: 1,
        pageSize: 20,
        distributorId: initialState.currentCompany.coId,
      };
      const { code, data } = await getAgentsList(params);
      if (code == 20000) {
        setAgency(data);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const onChange = (key: string) => {
    setIndex(key);
  };

  const getShopList = async () => {
    //获取店铺
    try {
      const { success, data } = await apiShopList({ id: initialState.currentCompany.coId });
      if (success) setShowList(data);
    } catch (err) {
      console.log(err);
    }
  };

  const columnsDetail = [
    //详情退单
    {
      title: '基本信息',
      columns: [
        {
          title: '退单号',
          dataIndex: 'refundId',
        },
        {
          title: '退款金额（元）',
          dataIndex: 'refundAmount',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
        {
          title: '退款原因',
          dataIndex: 'remark',
        },
        {
          title: '退款失败原因',
          dataIndex: 'failMessage',
        },
        {
          title: '退款到账时间',
          dataIndex: 'refundTime',
          valueType: 'dateTime',
        },
        {
          title: '订单状态',
          dataIndex: 'orderStatus',
          valueEnum: orderTypeEnum,
          renderText: (_, { orderStatus, refundStatus }) => {
            return orderTypeEnum[orderStatus || refundStatus] || '-';
          },
        },
        {
          title: '结算单号',
          dataIndex: 'tradeNo',
        },
        {
          title: '代理商',
          dataIndex: 'distributorName',
        },
        {
          title: `服务商`,
          dataIndex: 'serviceProviderName',
        },
        {
          title: '登录账号',
          dataIndex: 'username',
        },
        {
          title: '下单时间',
          dataIndex: 'createTime',
        },
        {
          title: '购票终端',
          dataIndex: 'sourceType',
          hideInSearch: true,
          valueType: 'select',
          key: 'sourceType',
          valueEnum: saleChannelEnum,
        },
        {
          title: '支付方式',
          dataIndex: 'payType',
          hideInSearch: 'false',
          key: 'payType',
          valueEnum: payTypeEnum,
        },
        {
          title: '订单金额（元）',
          dataIndex: 'payAmount',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
      ],
    },
    {
      title: '票务信息',
      columns: [
        {
          title: '',
          dataIndex: 'productList',
          render: (dom: any) => (
            <Table {...tableConfig} columns={columns2} dataSource={dom} bordered size="middle" />
          ),
        },
      ],
    },
    // {
    //   title: '票务信息',
    //   columns: [
    //     {
    //       title: '',
    //       dataIndex: 'productList',
    //       render: (dom: any) => (
    //         <Table columns={columns2} dataSource={dom} bordered size="middle" />
    //       ),
    //     },
    //   ],
    // },
  ];

  const columns2: ProColumns[] = [
    {
      width: 154,
      title: '票号',
      dataIndex: 'ticketNumber',
      render: (dom: any) => (
        <>
          <span title={dom}>{dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}</span>
          <CopyOutlined
            style={{ color: '#1890ff' }}
            onClick={() => {
              copy(dom);
              message.success('已复制');
            }}
          />
        </>
      ),
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      render: (dom: any) => productTypeEnum[dom],
    },
    {
      title: '票种',
      dataIndex: 'ticketType',
      render: (dom: any) => ticketTypeEnum[dom],
    },
    {
      title: '销票终端',
      dataIndex: 'sourceType',
      render: (dom: any) => saleChannelEnum[dom],
    },
    {
      title: '退款状态',
      dataIndex: 'refundStatus',
      render: (dom: any) => orderTypeEnum[dom],
    },
    {
      title: '退款金额（元）',
      dataIndex: 'refundAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '手续费（元）',
      dataIndex: 'refundFee',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '人数',
      dataIndex: 'playerNum',
    },
    {
      title: '已核销总次数',
      dataIndex: 'checkedNum',
    },
    {
      title: '姓名/身份证',
      dataIndex: 'realNameList',
      width: 150,
      render: (text: any[]) => {
        if (isEmpty(text || [])) {
          return '-';
        }
        const { idCardName, idCardNumber } = text[0] || {};
        return (
          <Space direction="vertical" size="small" align="center">
            <span>{idCardName}</span>
            <span>{idCardNumber}</span>
            {text.length > 1 && <a onClick={() => showTicketModal(text)}>查看所有</a>}
          </Space>
        );
      },
    },
    {
      title: '入园时间',
      dataIndex: 'day',
      valueType: 'date',
    },
    {
      title: '权益 ID',
      dataIndex: 'rightsId',
    },
    {
      title: '状态',
      dataIndex: 'ticketStatus',
      render: (_, { ticketStatus }) => ticketStatusEnum[`${ticketStatus}`] || '-',
    },
  ];

  useEffect(() => {
    getSupplier();
    getShopList();
    getAgency();
    getDistributor();
  }, []);

  useEffect(() => {
    if (index == 2) getDistributor();
  }, [index]);

  return (
    <>
      <Tabs
        tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
        defaultActiveKey="0"
        onChange={onChange}
      >
        <Tabs.TabPane tab={tapList[0].title} key={0}>
          {index == 0 ? (
            <Todistributor supplierList={supplierList} distributorList={distributorList} />
          ) : (
            ''
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab={tapList[1].title} key={1}>
          {index == 1 ? (
            <Touser columnsDetail={columnsDetail} showList={showList} supplierList={supplierList} />
          ) : (
            ''
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab={tapList[2].title} key={2}>
          {index == 2 ? <Toagent columnsDetail={columnsDetail} distributorList={Agency} /> : ''}
        </Tabs.TabPane>
      </Tabs>
    </>
  );
};

export default OrderSummary;
