import { ErrorBoundary } from '@ant-design/pro-components';
import { Button, Result } from 'antd';
import { Component } from 'react';

enum ERROR_TYPE {
  LoadFailed, // 页面加载失败
}

const getType = (message: string) => {
  if (new RegExp(/Loading.+chunk.+failed/).test(message)) {
    return ERROR_TYPE.LoadFailed;
  }
  return null;
};

export default class CustomErrorBoundary extends Component {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, errorInfo: {}, errorType: null };
  }

  static getDerivedStateFromError(error: any) {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    console.log('getDerivedStateFromError>>>', error);
    return { hasError: true, errorInfo: error, errorType: getType(error) };
  }

  componentDidCatch(error: any, errorInfo: any) {
    // 错误上报
    console.log('componentDidCatch>>>', error, errorInfo);
  }

  componentDidMount(): void {
    window.addEventListener('error', (error) => {
      // JS报错>>>错误上报
      console.log('addEventListener error', error);
    });
  }

  getErrorCom() {
    switch (this.state.errorType) {
      case ERROR_TYPE.LoadFailed:
        return (
          <Result
            status="500"
            title="对不起，该页面已过期，请刷新"
            extra={
              <Button type="primary" onClick={() => window.location.reload()}>
                刷新页面
              </Button>
            }
          />
        );
      default:
        // 非自定义的错误，使用antd提供的<ErrorBoundary />
        return <ErrorBoundary>{this.props.children}</ErrorBoundary>;
    }
  }

  render() {
    if (this.state.hasError) {
      // 有报错 展示降级UI
      return this.getErrorCom();
    }
    // 这里不要包裹<ErrorBoundary />，antd提供的<ErrorBoundary />不会走到自定义的生命周期
    return this.props.children;
  }
}
