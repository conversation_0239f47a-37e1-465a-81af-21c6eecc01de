/**
 * 分销模块接口
 */
import { request } from '@umijs/max';
import { bigDataHost, scenicHost } from '.';
import type { API, ResponseData, ResponseListData2 } from './typings';
// 库存详情
export async function apiManageStockInfo(params: any) {
  return request(`${scenicHost}/manageStock/info`, { params });
}
// 库存变化记录
export async function apiStockChangeRecord(data: any) {
  const res = await request(`${scenicHost}/distribution/distribution/stockChangeRecord`, {
    method: 'POST',
    data,
  });
  return res.data;
}
// 创建分销商凭证
export async function apiAgentCredentials(params: any) {
  return await request(`${scenicHost}/ticketAgent/distributorCredentials`, {
    method: 'POST',
    data: params,
  });
}
// 根据交易哈希获取交易信息
export async function apiOrderTransaction(txId: any) {
  return await request(`${scenicHost}/order/transaction/${txId}`, {
    method: 'GET',
  });
}
// 查看分销商凭证
export async function apiAgentShowCredentials(params: any) {
  try {
    const { data } = await request(`${scenicHost}/ticketAgent/distributorCredentials`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
    };
  } catch (error) {
    return {};
  }
}
// 代理商分组列表
export async function apiAgentGroupList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/ticketAgent/agentGroupList`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}
// 代理商产品配置列表（可配置给下级产品：本级产品 + 景区产品）
export async function apiTicketLists(id: string) {
  try {
    const { data } = await request(`${scenicHost}/ticketAgent/ticketList/${id}`, {
      method: 'GET',
    });
    return {
      success: true,
      data: data,
    };
  } catch (error) {
    return {};
  }
}
// 所有代理商 or 代理商数量详情
export async function apiListAgents(params: any) {
  try {
    const { data } = await request(`${scenicHost}/ticketAgent/agentsList`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}
// 代理商分组新增
export async function apiAgentGroupAdd(params: any) {
  const { code, data } = await request(`${scenicHost}/ticketAgent/agentGroup`, {
    method: 'POST',
    data: params,
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}
// 代理商分组修改
export async function apiAgentGroupEdit(params: any) {
  const { code, data } = await request(`${scenicHost}/ticketAgent/agentGroup`, {
    method: 'PUT',
    data: params,
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}
// 代理商分组修改（禁启用）
export async function apiAgentGroupStatus(params: any) {
  const { code, data } = await request(`${scenicHost}/ticketAgent/agentGroupStatus`, {
    method: 'PUT',
    data: params,
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}
// 代理商分组删除
export async function apiAgentGroupDel(id: string) {
  const { code, data } = await request(`${scenicHost}/ticketAgent/agentGroup/${id}`, {
    method: 'DELETE',
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}
// 代理商分组添加代理商
export async function apiAgentAdd(params: any) {
  const { code, data } = await request(`${scenicHost}/ticketAgent/agentInfo`, {
    method: 'POST',
    data: params,
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}
// 分销商列表
export async function apiDistribuList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/distribution/distribution/downDistributorList`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}

// 分销商分组列表
export async function apiGroupList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/distribution/distribution/groupList`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}

// 分销商 - 收款场景（代理分组）
export function apiAgentGroupApplicationList(params: any) {
  return request(`${scenicHost}/ticketAgent/agentGroupApplicationList`, {
    params,
  });
}

// 分销商 - 收款场景（查看代理）
export async function apiAgentApplicationList(params: any) {
  const { data } = await request(`${scenicHost}/ticketAgent/agentApplicationList`, {
    params,
  });
  return data.map((item: any) => ({
    value: item.code,
    label: item.name,
  }));
}
// 分销商 - 收款场景（查看经销）
export function getDealerApplicationList(params: any) {
  return request(`${scenicHost}/ticketAgent/dealerApplicationList`, {
    params,
  });
}

// 分销商分组新增
export async function apiGroupAdd(params: any) {
  const { code, data } = await request(`${scenicHost}/distribution/distribution/group`, {
    method: 'POST',
    data: params,
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}
// 分销商分组编辑
export async function apiGroupEdit(params: any) {
  const { code, data } = await request(`${scenicHost}/distribution/distribution/group`, {
    method: 'PUT',
    data: params,
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}

/**
 * @description: 启用 - 禁用分销商分组
 * @return {*} Promise
 */
export function updateDistributionGroupStatus(params: { id: string; status: number }) {
  return request(`${scenicHost}/distribution/distribution/groupStatus`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * @description: 查询经销商授权状态
 * @param params  type 1 经销 2 代理
 * @return {*}
 */
export function getDistributionStatus(params: {
  distributorId: string;
  type: number;
  upDistributorId: string;
}) {
  return request<
    ResponseData<{
      distributorId: string;
      status: number;
      type: number;
      upDistributorId: string;
    }>
  >(`${scenicHost}/distributorManage/queryPermission`, {
    params,
  });
}
/**
 * @description: 通过代理授权凭证 查询分销商授权状态
 * @param params  type 1 经销 2 代理
 * @return {*}
 */
export function getAgentStatus(params: {
  appId: string;
  appSecret: string;
  distributorId: string;
}) {
  return request<
    ResponseData<{
      distributorId: string;
      status: number;
      type: number;
      upDistributorId: string;
    }>
  >(`${scenicHost}/distributorManage/queryPermissionByCredentials`, {
    params,
  });
}

// 采购列表－下拉选择框
export function getDistTicketStockSelectList(distributionId: string | undefined) {
  return request(
    `${scenicHost}/distribution/distribution/distTicketStockSelectList/${distributionId}`,
    {
      method: 'GET',
    },
  );
}

// 分销商分组删除
export async function apiGroupDel(id: string) {
  const { code, data } = await request(`${scenicHost}/distribution/distribution/group/${id}`, {
    method: 'DELETE',
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}

// 添加分销商
export async function apiDistributorAdd(params: any) {
  const { code, data } = await request(`${scenicHost}/distribution/distribution/distributor`, {
    method: 'POST',
    data: params,
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}
// 分组分销商列表
export async function apiDistribGroupList(params: any) {
  const { groupId, coName } = params;
  try {
    const { data } = await request(
      `${scenicHost}/distribution/distribution/distributorsGroupList`,
      {
        method: 'GET',
        params: { coName, groupId },
      },
    );
    return {
      success: true,
      data: data,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}
// 供应商信息
export async function apiSupplierInfo(params: any) {
  try {
    const { data } = await request(`${scenicHost}/distribution/distribution/supplierInfo`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}
// 供应商信息（经销）
export async function apiDealerInfo(params: any) {
  try {
    const { data } = await request(`${scenicHost}/distribution/distribution/dealerInfo`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}
// 供应商信息（代理）
export async function apiAgentInfo(params: any) {
  try {
    const { data } = await request(`${scenicHost}/distribution/distribution/agentInfo`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}
// 供应商信息（进货）
export async function apiPurchaseInfo(params: any) {
  try {
    const { data } = await request(`${scenicHost}/distribution/distribution/purchaseInfo`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}

// 分销商关系解除
export async function apiRemoveDistributor(params: any) {
  const { data } = await request(
    `${scenicHost}/distribution/distribution/removeSuppliersRelation`,
    {
      method: 'DELETE',
      data: params,
    },
  );
  return data;
}

// 分销商关系解除（经销）
export async function apiAgentRelation(params: any) {
  await request(`${scenicHost}/ticketAgent/agentRelation`, {
    method: 'DELETE',
    data: params,
  });
}
// 分销商关系解除（代理）
export async function apiSupplierRelation(params: any) {
  await request(`${scenicHost}/ticketAgent/supplierRelation`, {
    method: 'DELETE',
    data: params,
  });
}

// 分销采购订单详情
export async function apiDistributeOrder(params: any) {
  try {
    const { data } = await request(`${scenicHost}/order/getDistributeOrder`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data.records,
      total: data.total,
    };
  } catch (error) {
    return {};
  }
}

// 分销商产品配置列表（可配置给下级产品：本级产品 + 景区产品）
export async function apiTicketConfigList(params: any) {
  try {
    const { data } = await request(
      // `${scenicHost}/distribution/distribution/ticketConfigList`,
      `${scenicHost}/distribution/distribution/distributorTicketsList`,
      {
        method: 'GET',
        params,
      },
    );
    return {
      success: true,
      data: data,
    };
  } catch (error) {
    return {};
  }
}

// 分销商产品 - 商品定价配置详情 参数：产品配置 id
export async function getProductConfigDetail(params: {
  productId?: string;
  distributorId?: string;
  productConfigId: string;
  upDistributorId?: string;
}) {
  return request<ResponseData<API.ProductConfigListItem>>(
    `${scenicHost}/distribution/distribution/configProductDetail`,
    {
      method: 'GET',
      params,
    },
  );
}
// 分销商产品配置列表（可配置给下级产品：本级产品 + 景区产品）(新)
export async function getProductConfigList(params: API.ProductConfigListParams) {
  return request<ResponseListData2<API.ProductConfigListItem[]>>(
    `${scenicHost}/distribution/distribution/productConfigList`,
    {
      method: 'GET',
      params,
    },
  );
}
// 分销商产品配置列表（可配置给下级产品：本级产品 + 景区产品）(新)
export async function getProductConfigListNew(params: any) {
  return request(`${scenicHost}/distribution/distribution/goodsSaleAuthorizeList`, {
    method: 'GET',
    params,
  });
}
// 保存产品配置列表（新）
export async function saveProductConfig(params: API.SaveProductConfigParams) {
  return await request(`${scenicHost}/distribution/distribution/productConfig`, {
    method: 'POST',
    data: params,
  });
}

// 分销商产品已配置列表（已配置给下级产品：本级产品 + 景区产品）
export async function apiTicketConfigList2(params: any) {
  try {
    const { data } = await request(
      `${scenicHost}/distribution/distribution/listGroupTickets/${params.groupId}`,
      {
        method: 'GET',
        // params,
      },
    );
    return {
      success: true,
      data: data,
    };
  } catch (error) {
    return {};
  }
}
// 库存分时详情
export function getDistributorStockTotalInfo(data: any) {
  return request<ResponseListData2<API.TotalStockItem[]>>(
    `${scenicHost}/distribution/distribution/distributorStockTotalInfo`,
    { method: 'POST', data },
  );
}
// 库存分时详情分批
export function getStockGoodsDetailsList(params: any) {
  return request<ResponseListData2<API.TotalStockItem[]>>(
    `${scenicHost}/distribution/distribution/StockGoodsDetailsList`,
    { params },
  );
}
// 分销商产品列表（
export function getStockProductList(params: any) {
  return request<ResponseListData2<API.TotalStockItem[]>>(
    `${scenicHost}/distribution/distribution/distributorStockTotal`,
    {
      method: 'GET',
      params,
    },
  );
}
// 产品库存详情和分时预约
export function getTimeShareAndStockDetail(params: any) {
  return request<ResponseData<API.DistributorStockDetail>>(
    `${scenicHost}/distribution/distribution/distributorStockDetail`,
    {
      method: 'GET',
      params,
    },
  );
}
// 分销商进货产品列表（上级配置产品）
export async function apiSupplierTicketList(params: any) {
  try {
    const { data } = await request(
      // `${scenicHost}/distribution/distribution/supplierTicketList`,
      `${scenicHost}/distribution/distribution/distTicketStockList`,
      {
        method: 'GET',
        params,
      },
    );
    return {
      success: true,
      data: data,
    };
  } catch (error) {
    return {};
  }
}
// 分销商可退货产品列表
export async function apiRefundDistributorStockList(data: any) {
  try {
    const res = await request(
      `${scenicHost}/distribution/distribution/refundDistributorStockList`,
      {
        method: 'POST',
        data,
      },
    );
    return {
      success: true,
      data: res.data,
    };
  } catch (error) {
    return {};
  }
}

// 保存配置列表
export async function apiTicketConfigAdd(params: any) {
  await request(
    // `${scenicHost}/distribution/distribution/configDistributorGroupTicket`,
    `${scenicHost}/distribution/distribution/distributorGroupTicket`,
    {
      method: 'POST',
      data: params,
    },
  );
}
// 创建订单
export async function apiOrderInfo(params: any) {
  return request(`${scenicHost}/tob/create`, {
    method: 'POST',
    data: params,
  });
}
// 区块链余额支付
export async function apiBcPay(params: any) {
  const { data } = await request(`${scenicHost}/order/bcPay/${params.orderId}`, {
    method: 'PUT',
  });
  return data;
}
// 退货
export async function apiDistributeRefund(params: any) {
  const { code, data } = await request(`${scenicHost}/order/distributeRefund`, {
    method: 'POST',
    data: params,
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}
// 重新上链
export async function apiManualHandleDataPushError(params: any) {
  const { code, data } = await request(`${scenicHost}/order/manualHandleDataPushError`, {
    method: 'GET',
    params,
  });
  if (code === 20000) {
    return data;
  } else {
    throw new Error('');
  }
}

// 采购订单列表
export async function apiListDistributeBuyOrder(params: any) {
  try {
    const { data } = await request(`${scenicHost}/order/listDistributeBuyOrder`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data.records,
      total: data.total,
    };
  } catch (error) {
    console.log(error);
    return {};
  }
}
// 采购订单详情
export async function apiDistributeBuyOrder(params: any) {
  try {
    // const { data } = await request(`${scenicHost}/order/getDistributeBuyOrder`, {
    const { data } = await request(`${scenicHost}/order/distributeOrderInfo`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total,
    };
  } catch (error) {
    return {};
  }
}

// 采购退单列表（采购）
export async function apiPurchaseRefundOrderPageList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/order/purchaseRefundOrderPageList`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data.records,
      total: data.total,
    };
  } catch (error) {
    return {};
  }
}
// 采购退单列表（销售）
export async function getSalesRefundOrderPageList(params: any) {
  return request<ResponseListData2<API.RefundOrderListItem[]>>(
    `${scenicHost}/order/salesRefundOrderPageList`,
    {
      method: 'GET',
      params,
    },
  );
}

// 采购退单详情
export async function apiDistributeRefundInfo(params: any) {
  try {
    const { data } = await request(`${scenicHost}/order/distributeRefundInfo`, {
      method: 'POST',
      data: params,
    });
    return {
      success: true,
      data: data,
      // total: data.total,
    };
  } catch (error) {
    return {};
  }
}

//  退单详情 (单个 - 多个子退订单号)B 端
export function getMultipleRefundOrderInfo(params: { refundIds: string[] }) {
  return request<ResponseData<API.RefundInfoListItem[]>>(
    `${scenicHost}/order/distributeRefundInfoByIds`,
    {
      method: 'POST',
      data: params,
    },
  );
}

// 订单退单列表
export function getOrderAndOrderRefundList(params: { sellerId: string }) {
  return request<ResponseListData2<API.OrderAndOrderRefundListItem[]>>(
    `${scenicHost}/order/listDistributeOrderAndOrderRefund`,
    {
      method: 'GET',
      params,
    },
  );
}

//下拉框统一接口封装（经销商 (1)、供应商 (2)、产品名称 (3)）、操作人（4）
export function getUnifyPullDown(params: { getUnifyPullDown: string; type: string }) {
  return request(`${scenicHost}/order/getUnifyPullDown`, {
    method: 'GET',
    params,
  });
}
//下拉框统一接口封装（景区 (1)、供应商 (2)、产品名称 (3)）
export function getUnifyPullDownStock(params: { getUnifyPullDown: string; type: string }) {
  return request(`${scenicHost}/order/getUnifyPullDownStock`, {
    method: 'GET',
    params,
  });
}

//获取所有供应商
export function getAllSuppliers(params: { buyerId: string }) {
  return request(`${scenicHost}/order/getAllSuppliers`, {
    method: 'GET',
    params,
  });
}

// 企业管理列表
export async function apiCoList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/co/pageList`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data.data,
      total: data.total,
    };
  } catch (error) {
    return {};
  }
}
// 企业管理列表（筛选关联用户）
export async function apiCoInfoList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/co/coInfoList`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: data,
      // total: data.total,
    };
  } catch (error) {
    console.log(error);
    return {};
  }
}
// 企业管理列表（包含个人机构）
export async function apiMerchantList() {
  try {
    const { data } = await request(`${scenicHost}/co/merchantList`, {
      method: 'GET',
    });
    return {
      success: true,
      data: data,
      // total: data.total,
    };
  } catch (error) {
    return {};
  }
}

// 个人信息列表
export async function apiPerInfoList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/co/personalInfo`, {
      method: 'GET',
      params,
    });
    return {
      success: true,
      data: [data],
      // total: data.total,
    };
  } catch (error) {
    return {};
  }
}
// 个人认证
export async function apiPersonalRegister(params: any) {
  const { data } = await request(`${scenicHost}/co/personalRegister`, {
    method: 'POST',
    data: params,
  });
  return data;
}
// 企业管理详情
export async function apiCoInfo(id: string) {
  try {
    const { data } = await request(`${scenicHost}/co/info/${id}`, {
      method: 'GET',
    });
    return data;
  } catch (error) {}
}
// 企业管理商户详情（认证校验）
export async function apiTlementInfo(params: any) {
  const { data } = await request(`${scenicHost}/co/settlementInfo`, {
    method: 'GET',
    params,
  });
  return data;
}
// 企业管理编辑
export async function apiCoEdit(params: any) {
  const { code, data } = await request(`${scenicHost}/co/info`, {
    method: 'POST',
    data: params,
  });
  if (code == 20000) {
    return data;
  } else {
    throw new Error('');
  }
}
// 关联企业
export async function apiAssociateCompany(params: any) {
  const { data } = await request(`${scenicHost}/orgStructure/associateCompany`, {
    method: 'POST',
    data: params,
  });
  return data;
}
// 企业授权
export async function apiPermission(params: any) {
  const { data } = await request(`${scenicHost}/role/servicePermission`, {
    method: 'POST',
    data: params,
  });
  return data;
}
// 企业管理状态
export async function apiCoStatus(params: any) {
  try {
    const { data } = await request(`${scenicHost}/co/status`, {
      method: 'POST',
      data: params,
    });
    return data;
  } catch (error) {}
}
// 企业管理删除
export async function apiCoDel(id: string) {
  const { data } = await request(`${scenicHost}/co/del/${id}`, {
    method: 'DELETE',
  });
  return data;
}

// 模拟数据
export async function apiDemoList() {
  const { data } = await request(`api/list`);
  return data;
}
export function apiDemoDetail() {
  return request(`api/detail`);
}
export async function apiDemo(params: any) {
  switch (params.id) {
    case 2:
      return {
        data: [
          {
            id: '2',
            name: '熊猫集团',
            fNum: 100,
            sNum: 100,
            statu: 0,
          },
        ],
        success: true,
      };
    case 22:
      return {
        data: {
          id: '2',
          isEnable: 0,
          name: '熊猫集团',
        },
      };

    default:
      return { data: [], success: true };
  }
}

// 分销商列表
export async function apiList(params: any) {
  // console.log(params);
  return {
    data: [
      {
        id: '0',
        name: '熊猫集团',
        controlType: 'yilvton_44274779',
        man: '熊猫集团',
        phone: '13045671234',
        mail: '<EMAIL>',
        adoptType: '91430111MA4L16JQ9B',
        identityType: 'yilvton_3440',
      },
    ],
    success: true,
    // total: 1,
  };
}

//分销采购取消订单
export async function apiCancellationOrder(params: any) {
  try {
    const data = await request(`${scenicHost}/order/orderCancel`, {
      method: 'PUT',
      data: params,
    });
    return {
      success: true,
      data: data,
    };
  } catch (error) {
    return {};
  }
}

// 保存配置列表
export async function apiAgentConfigTicketAdd(params: any) {
  await request(`${scenicHost}/ticketAgent/configTicket`, {
    method: 'POST',
    data: params,
  });
}

/************************** 价格策略 *********************************/

/** 商品列表、价格策略列表 */
export function getPriceStrategyList(params: API.PriceStrategyListParams) {
  return request<ResponseData<API.PriceStrategyListItem[]>>(
    `${scenicHost}/ticketAgent/agentProductList`,
    {
      method: 'GET',
      params,
    },
  );
}
/** 代理商查看佣金策略 */
export function getProductCommissionList(params: API.ProductCommissionListParams) {
  return request<ResponseData<API.ProductCommissionListItem[]>>(
    `${scenicHost}/ticketAgent/productCommissionList`,
    {
      method: 'GET',
      params,
    },
  );
}
/** 已设置的价格策略列表 */
export function getConfigPriceStrategy(params: API.ConfigPriceStrategyParams) {
  return request<ResponseData<API.ConfigPriceStrategyListItem[]>>(
    `${scenicHost}/ticketAgent/agentPriceGroupList`,
    {
      method: 'GET',
      params,
    },
  );
}
/** 查询直销价格策略列表 */
export function getSelfPriceStrategy(params: API.ConfigPriceStrategyParams) {
  return request<ResponseData<API.SelfPriceStrategyItem>>(`${scenicHost}/price/selfPrice`, {
    method: 'GET',
    params,
  });
}

/** 新增价格策略 包含直销 */
export function addPriceStrategy(params: API.AddPriceStrategyParams, isSelf = false) {
  return request<ResponseData<null>>(
    `${scenicHost}${isSelf ? '/price/selfPrice' : '/ticketAgent/price'}`,
    {
      method: 'POST',
      data: params,
    },
  );
}

/** 编辑价格策略 */
export function updatePriceStrategy(params: API.UpdatePriceStrategyParams) {
  return request<ResponseData<null>>(`${scenicHost}/ticketAgent/price`, {
    method: 'PUT',
    data: params,
  });
}

/** 删除价格策略 */
export function deletePriceStrategy(params: { id: string }) {
  return request<ResponseData<null>>(`${scenicHost}/ticketAgent/price/${params.id}`, {
    method: 'DELETE',
  });
}

/** 代理商查看商品的佣金策略 */
export function getPriceStrategyByAgent(params: API.PriceStrategyByAgentParams) {
  return request<ResponseData<API.PriceStrategyInfoType>>(
    `${scenicHost}/ticketAgent/queryProductAgentPrice`,
    {
      method: 'GET',
      params,
    },
  );
}

// 商品列表
export async function apiSimpleGoods(params: any) {
  return request<ResponseListData2<any[]>>(`${scenicHost}/simpleGoods/pageList`, {
    method: 'GET',
    params,
  });
}

// 商品详情
export function apiSimpleGoodsDetail(id: any) {
  return request(`${scenicHost}/simpleGoods/info/${id}`);
}

// 权益卡详情
export function apiTravelCardDetail(id: any) {
  return request(`${scenicHost}/travelCard/info/${id}`);
}

/**
 * 获取价格策略详情
 */
export async function getPriceStrategyDetail(params: { id: string }) {
  return request<ResponseData<API.PriceStrategyListItem>>(
    '/api/distribution/price-strategy/detail',
    {
      method: 'GET',
      params,
    },
  );
}
/**
 * 直销价格禁启用
 */
export async function toggleSelfPriceEnable(params: {
  agentPriceDynamicsId: string;
  isEnable: boolean;
}) {
  return request<ResponseData<API.PriceStrategyListItem>>(`${scenicHost}/price/selfPriceEnable`, {
    method: 'GET',
    params,
  });
}
/**
 * 价格策略设置开关
 */
export async function apiSetFunctionEnable(params: { agentPriceId: string; isEnable: boolean }) {
  return request<ResponseData<API.PriceStrategyListItem>>(`${scenicHost}/price/setFunctionEnable`, {
    method: 'PUT',
    params,
  });
}
/**
 * 查询价格策略动态定价历史价格
 */
export async function apiQueryHistoryPriceDynamicsList(params: {
  dateType: string;
  priceId: number;
  saleEndDate: string;
  saleStartDate: string;
}) {
  return request<ResponseData<API.PriceStrategyListItem>>(
    `${scenicHost}/price/queryHistoryPriceDynamicsList`,
    {
      method: 'POST',
      data: params,
    },
  );
}

/**
 * 根据商品 id 获取经纬度
 */
export async function apiWeatherForecast(params: { goodsId: string }) {
  return request<ResponseData<{ latitude: string; longitude: string }>>(
    `${scenicHost}/price/weatherForecast`,
    {
      method: 'GET',
      params,
    },
  );
}
/**
 * 天气预报
 */
export async function apiWeatherForecastBigData(params: {
  lng: string;
  lat: string;
  daily_steps: number;
}) {
  console.log('apiWeatherForecastBigData---', bigDataHost, `${bigDataHost}/weather/daily`);
  return request<ResponseData<any>>(`${bigDataHost}/weather/daily/`, {
    method: 'GET',
    params,
  });
}
/**
 * 入园统计
 */
export async function apiParkEntryStatistics(params: {
  goodsId: string;
  saleStartDate: string;
  saleEndDate: string;
  dateType: string;
}) {
  return request(`${scenicHost}/price/getProductCheckTicketStatistics`, {
    method: 'POST',
    data: params,
  });
}
