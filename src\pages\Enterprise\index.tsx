import Province from '@/common/components/Province';
import Tag from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import { accountStatusEnum, companyType } from '@/common/utils/enum';
import { scenicHost } from '@/services/api';
import type { ActionType, ProColumnType } from '@ant-design/pro-components';
import ProTable from '@ant-design/pro-table';
import { request } from '@umijs/max';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import CompanyInfoModal from './components/CompanyInfoModal';
import EditCompanyModal from './components/EditCompanyModal';
import './index.less';

/**
 * 改用轮子的企业信息页
 *
 * @Author: bankewei
 * @Date: 2024 年 8 月 26 日
 */
export default function Page() {
  const actionRef = useRef<ActionType>();

  const [currentRow, setCurrentRow] = useState<any>();

  // 编辑、新增
  const [editVisible, setEditVisible] = useState<boolean>(false);
  // 详情
  const [infoVisible, setInfoVisible] = useState<boolean>(false);

  const tableColumns: ProColumnType[] = [
    {
      title: '企业名称',
      dataIndex: 'coName',
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
      valueType: 'textarea',
    },
    {
      title: '企业类型',
      dataIndex: 'coType',
      valueEnum: companyType,
      renderText: (_, record: any) => {
        return (
          record.coTypeList.map((i: keyof typeof companyType) => companyType[i]).join('、') || '-'
        );
      },
    },
    {
      title: '企业地址',
      dataIndex: 'province',
      search: {
        transform: (values: any[]) => {
          return {
            coProvinceCode: values[0]?.addressId,
            coCityCode: values[1]?.addressId,
            coAreaCode: values[2]?.addressId,
          };
        },
      },
      renderFormItem: () => {
        return <Province />;
      },
      renderText: (_, record: any) => record.coUrl || '-',
    },
    {
      title: '认证状态',
      dataIndex: 'settlementStatus',
      valueEnum: accountStatusEnum,
      tooltip:
        '提交认证失败提示已存在同一认证企业的情况下经运营人员审核为已认证收款系统的慧旅云新企业可由运营人员操作进行结算账户关联',
      renderText: (dom) => <Tag type={'approveStatus'} value={dom} />,
    },
    {
      title: '更新时间',
      dataIndex: 'modifyTime',
      hideInSearch: true,
      render: (dom: any) => {
        return <>{dayjs(dom).format('YYYY-MM-DD')}</>;
      },
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_: any, record: any) => [
        <a
          key="k1"
          onClick={() => {
            setInfoVisible(true);
            setCurrentRow(record);
          }}
        >
          查看
        </a>,
        record?.settlementStatus == '3' ? (
          <></>
        ) : (
          <a
            key="k2"
            onClick={() => {
              setCurrentRow(record);
              setEditVisible(true);
            }}
          >
            {record?.settlementStatus == '0' ? '去认证' : '编辑'}
          </a>
        ),
      ],
    },
  ];

  return (
    <>
      <ProTable
        {...tableConfig}
        search={false}
        rowClassName={(row) => (row.isEnable == '1' ? '' : 'disableRow')}
        actionRef={actionRef}
        rowKey="coId"
        columns={tableColumns}
        request={async (params: any) => {
          const { data, code } = await request(`${scenicHost}/co/pageList`, {
            method: 'GET',
            params,
          });
          return {
            data: data.data,
            success: code == 20000,
            total: data.total,
          };
        }}
      />

      {/* 新增、编辑弹窗 */}
      <EditCompanyModal
        visible={editVisible}
        setVisible={setEditVisible}
        currentRow={currentRow}
        onSuccess={() => {
          actionRef.current?.reload();
          setEditVisible(false);
        }}
      />

      {/* 查看弹窗 */}
      <CompanyInfoModal visible={infoVisible} setVisible={setInfoVisible} currentRow={currentRow} />
    </>
  );
}
