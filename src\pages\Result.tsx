import { goToLogin } from '@/common/utils/tool';
import { logout } from '@/services/api/cas';
import { history, useModel } from '@umijs/max';
import { Button, Result, Row } from 'antd';
import { parse } from 'querystring';
import { getEnv } from '@/common/utils/getEnv';

interface Porps {
  initType?: 'network' | 'noPermissions';
}

const NoFoundPage = ({ initType }: Porps) => {
  const { initialState } = useModel('@@initialState');
  const { hash } = location;

  const { type } = parse(hash.split('?')[1]);

  if (initialState.isInitial) history.replace('/welcome');

  const pageModel = {
    network: {
      status: 500,
      subTitle: '网络错误，请刷新重试',
    },
    noPermissions: {
      status: 500,
      subTitle: '没有权限，请联系管理员',
    },
  };

  return (
    <>
      <Row justify="center" align="middle" style={{ height: '100%' }}>
        <Result
          status={pageModel[initType || type]?.status}
          subTitle={pageModel[initType || type]?.subTitle}
          extra={
            <Button
              type="primary"
              onClick={async () => {
                await logout({
                  appId: getEnv().APPID,
                });
                goToLogin();
              }}
            >
              返回登录页
            </Button>
          }
        />
      </Row>
    </>
  );
};

export default NoFoundPage;
