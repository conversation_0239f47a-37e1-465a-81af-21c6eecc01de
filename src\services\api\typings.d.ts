// @ts-ignore
/* eslint-disable */

export type ResponseData<T> = {
  code: number;
  data: T;
  msg: string;
};
export type PageParams = { current?: number; pageSize?: number; total?: number };

export type ResponseListData<T> = {
  code: number;
  data: {
    list: T;
    page: PageParams;
  };
  msg: string;
};

export type ResponseListData2<T, S extends Record<string, any> = {}> = {
  code: number;
  data: {
    current: number;
    pageSize: number;
    data: T;
    total: number;
  } & S;
  msg: string;
};

declare namespace API {
  type PageParams = { current?: number; pageSize?: number; total?: number };
  interface RoleListItem {
    code: string;
    name: string;
    roleId: string;
    status: number;
  }

  interface DepartmentItem {
    companyId: string;
    deptId: string;
    name: string;
    parentDeptId: string;
  }

  interface RoleInfoListItem {
    roleId: string;
    roleName: string;
  }
  type CurrentUser = {
    name?: string;
    avatar?: string;
    userid?: string;
    email?: string;
    signature?: string;
    title?: string;
    group?: string;
    tags?: { key?: string; label?: string }[];
    notifyCount?: number;
    unreadCount?: number;
    country?: string;
    access?: string;
    geographic?: {
      province?: { label?: string; key?: string };
      city?: { label?: string; key?: string };
    };
    address?: string;
    phone?: string;
  };

  type LoginResult = {
    status?: string;
    type?: string;
    currentAuthority?: string;
  };

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    credential?: string;
    secret?: string;
    autoRegister?: boolean;
    loginTypeCode?: string;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };
  interface MenuPermissionsParams {
    appId: string;
    groups: string;
    type: string;
    companyId: string;
    scenicId?: string;
    users?: string;
  }

  type Permission = {
    group: string;
    code: string;
    action: string;
    user: string;
  };
  //tip: 2022 年 1 月 17 日：智旅云角色管理的权限参数 pr01，用户管理 p01
  type GetPermissionListParam = {
    roleId?: any;
    type?: string;
    relationId?: string;
    companyId?: string;
    scenicId?: string;
  };

  // 2022 年 5 月 16 日  产品配置接口和原型变动
  interface ProductConfigListParams {
    [`bcAccount.ownerAccount`]?: string;
    [`bcAccount.ownerOrg`]?: string;
    distributorId: string;
    groupId?: string;
    groupIdForAdd?: string;
  }

  type ConfigGoodsListItem = {
    batchId: string;
    dayBegin: string;
    dayEnd: string;
    timeShareBeginTime: string;
    timeShareEndTime: string;
    timeShareId: string;
  };

  type ConfigGoodsDetailItem = {
    batchId: string;
    goodsId: string;
    price: number;
    timeShareBeginTime: string;
    timeShareEndTime: string;
    beginDiscount: number;
    endDiscount: number;
    validityDay: number;
    goodsType: string;
    goodsName: string;
    ticketId: string;
    isExchange: number;
  };

  interface ProductConfigListItem {
    batchId: string;
    productId: string;
    name: string;
    proType: number;
    scenicId: string;
    purchasePrice: number[];
    supplierId: string[];
    supplierInfoList: SupplierInfoListItem[];
    salePrice: number;
    priceId: string;
    list: ConfigGoodsListItem[];
    configGoodsDetail: ConfigGoodsDetailItem[];
    editable: boolean;
    batchData: any;
  }

  type SaveProductConfigParams = {
    groupId: string;
    list?: {
      productId: string;
      salePrice: number;
    }[];
    batchId?: string;
  };

  type ProxyProductConfigListItem = {
    goodsType: number;
    name: string;
    proName: string;
    proType: number;
    purchasePrice: any[];
    supplier: SupplierType[];
    timeShareBeginTime: string;
    timeShareEndTime: string;
    timeShareId: string;
    unitId: string;
  };

  type SupplierType = {
    name: string;
    supplierId: string;
  };

  // 财务管理 结算模块
  AddCreditAccountParams;
  interface CreditDefaultParams {
    merchantId: string;
  }
  interface CreditAccountListParams extends CreditDefaultParams, PageParams {
    consumerName?: string;
  }
  interface ConsumerOrProvider {
    legalName: string;
    registrationName: string;
  }
  interface CreditAccountItem {
    balance: number;
    consumer?: ConsumerOrProvider;
    provider?: ConsumerOrProvider;
    id: string;
    settlementCycle: string;
    settlementType: 'WEEK' | 'MONTH' | 'YEAR';
    warningAmount: number;
  }

  interface OperationRecordListParams extends CreditDefaultParams, PageParams {
    consumeName?: string;
    creationFromDate?: string;
    creationToDate?: string;
    operator?: string;
  }

  interface OperationRecordItem {
    account: CreditAccountItem;
    afterBalance: number;
    amount: number;
    beforeBalance: number;
    creationDate: string;
    operator: string;
  }

  type AddCreditAccountParams = CreditDefaultParams &
    Pick<CreditAccountItem, 'settlementCycle' | 'warningAmount' | 'settlementType'> & {
      consumer: {
        id: string;
        type: 'merchant';
      };
    };

  interface AgentInfoItem {
    coName: string;
    id: string;
    settlementId: string;
    settlementName: string;
    settlementStatus: string;
  }

  type UpdateCreditAccountParams = Omit<API.AddCreditAccountParams, 'consumer'> & {
    id: string;
  };

  interface UpdateCreditBalanceParams extends CreditDefaultParams {
    id: string;
    amount: number;
  }
  interface BillDetailParams extends PageParams, CreditDefaultParams {
    creditAccountId: string;
  }

  interface BillDetailItem {
    afterBalance: number;
    amount: number;
    beforeBalance: number;
    businessType: string;
    creationDate: string;
    entryType: string;
    outTradeNo: string;
    originType: string;
  }
  type SettlementStatus = 'FINISHED ' | 'PENDING';

  type ProviderSettlementListParams = CreditDefaultParams &
    PageParams & {
      creationFromDate?: string;
      creationToDate?: string;
      payedFromDate?: string;
      payedToDate?: string;
      settlementStatus?: SettlementStatus;
    };
  interface ProviderSettlementItem {
    amount: number;
    consumeLegalName: string;
    consumeRegistrationName: string;
    creationDate: string;
    id: string;
    payedAmount: number;
    payedDate: string;
    settlementStatus: 'FINISHED' | 'PENDING';
  }
  interface ProviderBillDetailParams extends PageParams, CreditDefaultParams {
    creditSettlementBillId: string;
  }
  interface ProviderBillDetailItem {
    amount: number;
    buyerName: string;
    createDate: string;
    description: string;
    id: string;
    outTradeNo: string;
    paymentDate: string;
    sellerName: string;
    status: string;
    title: string;
    tradeNo: string;
    scenicName: string;
  }

  type ConsumerSettlementListParams = CreditDefaultParams & PageParams;
  interface ConsumerSettlementItem {
    amount: number;
    creationDate: string;
    id: string;
    payedAmount: number;
    payedDate: string;
    provideLegalName: string;
    provideRegistrationName: string;
    settlementStatus: string;
  }

  type BillListParams = {
    billId?: string;
    sellerId?: string;
    settlementStatus?: string;
    startDate?: string;
    endDate?: string;
  };

  type AgentBillListParams = BillListParams & {
    agentId: string;
    activeKey: string;
  };

  type AgentBillItem = {
    amount: number;
    billId: string;
    endDay: string;
    name: string;
    createTime: string;
    settlementStatus: number;
    settlementTime: string;
    startDay: string;
  };

  type AgentBillPreItem = {
    actualComAmount: string;
    orderId: string;
    payTime: string;
    sellerId: string;
    sellerName: string;
    storeId: string;
    storeName: string;
    ticketNumber: string;
  };

  type AgentBillDetailParams = {
    id: string;
  };

  type AgentBillDetailItem = {
    actualComAmount: number;
    billId: string;
    commissionAmount: number;
    commissionRate: number;
    orderId: string;
    payTime: string;
    payType: number;
    price: number;
    sourceType: string;
    ticketNumber: string;
    tradeNo: string;
    userId: string;
  };

  //

  type SellerBillListParams = BillListParams & {
    sellerId: string;
  };
  type SellerBillItem = AgentBillItem;

  type DistributorListItem = {
    coId: string;
    coName: string;
  };
  type AgentListItem = DistributorListItem;

  type AddSettlementParams = {
    agentId?: string;
    startDay?: string;
    endDay?: string;
    sellerId?: string;
    overdueType?: string;
  };

  type ChcekOverdueTicketParams = {
    agentId: string;
    sellerId: string;
  };
  type ChcekOverdueTicketItem = {
    commission: string;
    ticketNumber: string;
  };
  interface PersonInfo {
    personalId: string;
    realName: string;
    identity: string;
    phone: string;
    userId: string;
    settlementId: string;
    settlementName: string;
    createTime: string;
    email: null;
  }

  interface CoInfo {
    coId: string;
    coName: string;
    contactName: string;
    coPhone: string;
    coCode: string;
    coEmail: string;
    coAreaName: string;
    coAddressInfo: string;
    settlementId: string;
    settlementName: string;
    settlementStatus: any;
  }
  interface MerchantListItem {
    id: string;
    name: string;
    type: number;
    settlementId: string;
  }
  interface ApplyStatusParams {
    applyDistributorId: string;
    applyId: string;
    upDistributorId: string;
  }

  interface ApplyStatus {
    applyDistributorId: string;
    applyId: string;
    status: number;
    note: string;
  }
  interface AuditListParams {
    distributorId: string;
    status?: number;
  }
  interface AuditListItem {
    applyDistributorId: string;
    applyId: string;
    coCode: string;
    coName: string;
    contactName: string;
    phone: string;
    settlementName: string;
    status: number;
  }
  type UpdateAuditInfoParams = Pick<API.AuditListItem, 'applyId' | 'status' | 'phone'> & {
    /**申请的上级分销商公司名称 */
    upCompanyName: string;
    note?: string;
    /**	审批人名称 */
    approvalUserName: string;
    /**申请的分销商公司名称 */
    applyCompanyName: string;
    coCode?: string;
    type?: number;
  };

  interface RealNameInfo {
    balanceId: string;
    bindArgNo: string;
    cardNumber: string;
    cardPhone: string;
    idName: string;
    idNumber: string;
    status: number;
    userId: string;
  }

  interface UpdateCommissionCreditParams {
    agentId: string;
    creditType: number;
    distributorId: string;
    paymentType: number;
    agentType: number;
  }

  // 价格策略
  interface PriceStrategyListParams {
    distributorId: string;
    groupId?: string;
    name?: string;
  }
  interface PriceStrategyListItem {
    beginDiscount: number;
    discount: number[];
    endDiscount: number;
    goodsId: string;
    goodsType: number;
    marketPrice: number;
    name: string;
    overallDiscount: number;
    proId: string;
    proName: string;
    proType: number;
    purchasePrice: number[];
    priceId: string;
    salePrice: number[];
    scenicName: string;
    supplierId: any[];
    supplierInfoList: SupplierInfoListItem[];
    timeShareBeginTime: string;
    timeShareEndTime: string;
    timeShareId: string;
    unitId: string;
    unitType: number;
    validityDay: number;
    issueId: string;
    checkId: string;
    retreatId: string;
    ruleType: string | number;
  }

  interface SupplierInfoItem {
    supplierId: string;
    supplierName: string;
  }

  interface ProductCommissionListParams {
    distributorId: string;
    supplierId?: string;
  }
  interface ProductCommissionListItem {
    beginDiscount: number;
    commissionRate: number;
    composePrice: number;
    endDiscount: number;
    goodsId: string;
    goodsName: string;
    goodsType: number;
    isCompose: boolean;
    marketPrice: number;
    overallDiscount: number;
    priceId: string;
    proId: string;
    proName: string;
    proType: number;
    salePrice: number;
    scenicName: string;
    supplierId: string;
    supplierName: string;
    timeShareBeginTime: string;
    timeShareEndTime: string;
    timeShareId: string;
    unitId: string;
    unitType: number;
    validityDay: number;
  }

  interface PriceType {
    commissionRate: number;
    composePrice: number;
    isCompose: boolean;
    salePrice: number;
    discount: number;
    composeDiscount: number;
  }
  type AddPriceStrategyParams = {
    distributorId: string;
    groupId: string[];
    price: PriceType;
    unitId: string;
    goodsId?: string;
  };

  type UpdatePriceStrategyParams = {
    distributorId?: string;
    goodsId?: string;
    addGroupId: string[];
    delGroupId: string[];
    price: PriceType;
    priceId: string;
    unitId: string;
  };

  type PriceStrategyInfo = {
    commissionAmount: number;
    commissionRate: number;
    commissionType: number;
    /** 套餐中购买的折扣价 */
    composePrice: number;
    discount: number;
    composeDiscount: number;
    isCompose: boolean;
    /** 用户单独购买的折扣价 */
    salePrice: number;
  };
  type PriceStrategyInfoType = {
    price: PriceStrategyInfo;
    goodsId: string;
    productName: string;
    goodsName: string;
    unitType: number;
    proType: number;
    goodsType: number;
    remark: string;
    timeShareId: string;
    timeShareBeginTime: string;
    timeShareEndTime: string;
    validityDay: number;
    scenicName: string;
    marketPrice: number;
    overallDiscount: number;
    beginDiscount: number;
    endDiscount: number;
    issueName: string;
    checkName: string;
    retreatName: string;
    retreatId: string;
    checkId: string;
    issueId: string;
  };
  type ConfigPriceStrategyParams = {
    distributorId: string;
    unitId: string;
    priceId?: string;
  };
  type PriceStrategyByAgentParams = ConfigPriceStrategyParams;
  type GroupItem = {
    id: string;
    name: string;
  };
  interface ConfigPriceStrategyListItem {
    group: GroupItem[];
    info: { price: PriceStrategyInfo; priceId: string };
  }
  interface SupplierInfoListItem {
    supplierId: string;
    supplierName: string;
  }

  interface SelfPriceStrategyItem {
    priceId: string;
    price: {
      commissionRate: number;
      composePrice: number;
      isCompose: boolean;
      salePrice: number;
    };
  }

  // 个人交易统计（按支付方式）
  type WindowPaymenntListParams = {
    createDate: string;
    endDate: string;
  };

  type WindowPaymenntListItem = {
    payType: string;
    payPeople: number;
    payAmount: number;
  };

  // 权益卡商品
  type TravelCardGoodsListParams = {
    travelCardId?: string;
    scenicId: string;
  };

  type TravelCardGoodsListItem = {
    overallDiscount: number;
    beginDiscount: number;
    endDiscount: number;
    marketPrice: number;
    dailyPrice: number;
    goodsName: string;
    holidayPrice: number;
    id: string;
    effectiveTime: string;
    isEnable: number;
    isFirst: number;
    issueId: string;
    notices: string;
    rightsId: string;
    rightsName: string;
    scenicId: string;
    serviceChargeRate: number;
    travelCardId: string;
  };

  // 查询现金账单列表
  interface ICashSettlementListResponse {
    countBeginTime: string;
    countEndTime: string;
    createTime: string;
    id: string;
    payAmount: number;
    settlementState: number;
    staffAccount: string;
    staffName: string;
  }
  interface ICashLIstParams {
    createBeginTime?: string;
    createEndTime?: string;
    current?: number;
    pageSize?: number;
    id?: string;
    settlementBeginTime?: string;
    settlementEndTime?: string;
    settlementState?: number;
    staffAccount?: string;
    staffName?: string;
  }

  // 查询账单明细列表
  interface ICashListDetailParams {
    current?: number;
    merchantNo?: string;
    orderId?: string;
    pageSize?: number;
    paymentBeginTime?: string;
    paymentEndTime?: string;
    staffAccount?: string;
    staffName?: string;
    total?: number;
  }
  // 查询账单明细
  interface ICashDetailParams {
    current?: number;
    pageSize?: number;
    total?: number;
    merchantNo?: string;
    orderId?: string;
    paymentBeginTime?: string;
    paymentEndTime?: string;
    staffAccount?: string;
    staffName?: string;
    tradeNo?: string;
  }

  interface ICashDetailResponse {
    current: number;
    data: ICashDetailItem[];
    pageSize: number;
    total: number;
    countAmount: number;
  }
  interface ICashDetailItem {
    orderId: string;
    payAmount: string;
    paymentTime: string;
    tradeNo: string;
    staffAccount: string;
    l;
    staffName: string;
    merchantNo: string;
  }

  // 修改现金账单结算状态
  interface IUpdateCashSettlementStateParams {
    id?: string;
    settlementState?: number;
    settlementTime?: string;
  }

  interface IDetailProps {
    goodsName: string;
    timeShare: string;
    storeGoodsId: string;
  }
  // 获取店铺商品库存编号列表 (单票)
  interface IStoreGoodsPageListParams {
    current: number;
    pageSize: number;
    storeGoodsId: string;
  }

  interface IStoreGoodsNumberPageListItem {
    dayBegin: string;
    dayEnd: string;
    purchaseBeginTime: string;
    purchaseEndTime: string;
    stockId: string;
    stockNumber: string;
    storeGoodsId: string;
    ticketId: string;
    timeShareId: string;
  }
  interface IStoreGoodsTimeSharePageListParams {
    current?: number;
    pageSize?: number;
    storeGoodsId: string;
    stockId: string;
    ticketId: string;
    startDate?: string;
    endDate?: string;
  }

  interface IStoreGoodsTimeSharePageListItem {
    dayBegin: string;
    dayEnd: string;
    id: string;
    purchaseBeginTime: string;
    purchaseEndTime: string;
    stockId: string;
    stockNumber: string;
    storeGoodsId: string;
    ticketId: string;
    timeShareId: string;
  }

  interface OrderProDetailItem {
    num: number;
    productName: string;
    productPrice: string;
    productType: number;
    purchasePrice: string;
    scenicName: string;
    sellNumber: number;
  }

  interface OrderGoodsDetailItem {
    batchId: string;
    createTime: string;
    definePrice: string;
    differencesPrice: string;
    productSkuName: string;
    ticketNumber: string;
    ticketStatus: number;
    ticketType: number;
    timeShare: string;
  }

  interface OrderAndOrderRefundListItem {
    buyerId: string;
    buyerName: string;
    createTime: string;
    isBC: number;
    merchantId: string;
    orderGroupId: string;
    orderId: string;
    orderRefund: OrderRefundItem[];
    orderStatus: string;
    sellerId: string;
    sellerName: string;
    stockCertificate: string;
    totalAmount: number;
    username: string;
    isOrderRefund?: string;
  }

  interface OrderRefundItem {
    billStatus: number;
    certId: string;
    commissionSettledStatus: number;
    createTime: string;
    failMessage: string;
    orderId: string;
    productSkuName: string;
    refundAmount: number;
    refundFee: number;
    refundId: string;
    refundStatus: string;
    refundTime: string;
    refundType: number;
    remark: string;
    stockCertificate: string;
    tradeNo: string;
    txId: string;
    userId: number;
    username: string;
  }

  interface RefundOrderListItem {
    buyerId: string;
    buyerName: string;
    createTime: string;
    refundAmount: number;
    refundId: string;
    refundStatus: number;
    sellerId: string;
    sellerName: string;
    txId: string;
    username: string;
  }

  interface RefundInfoListItem {
    createTime: string;
    orderId: string;
    orderStatus: string;
    orderType: number;
    payAmount: number;
    payTime: string;
    payType: number;
    productList: ProductListItem[];
    totalAmount: number;
    tradeNo: string;
    txId: string;
    userId: string;
    userName: string;
    refundId: string;
  }

  interface ProductListItem {
    buyerId: string;
    buyerName: string;
    dateList: DateList[];
    noDateList: NoDateListItem[];
    productId: string;
    productName: string;
    productType: string;
    scenicId: string;
    scenicName: string;
    sellerId: string;
    sellerName: string;
    ticketType: string;
    totalAmount: number;
    totalNum: number;
  }

  interface DateList {
    dayBegin: string;
    dayEnd: string;
    distributorTicketStockId: string;
    timeShareDetail: TimeShareDetailItem[];
    totalAmount: number;
    totalNum: number;
  }

  interface NoDateListItem {
    batchId: string;
    buyerId: string;
    buyerName: string;
    dayBegin: string;
    dayEnd: string;
    distributorTicketStockId: string;
    num: number;
    productId: string;
    productName: string;
    productPrice: number;
    scenicId: string;
    scenicName: string;
    sellerId: string;
    sellerName: string;
    ticketType: string;
    totalAmount: number;
  }

  interface TimeShareDetailItem {
    batchId: string;
    distributorTicketStockId: string;
    num: number;
    productPrice: number;
    timeShare: string;
    timeShareId: string;
    totalPrice: number;
  }

  interface DistributorStockDetail {
    enterTimeGroupList: EnterTimeGroup[];
    list: EnterTimeDetailListItem[];
  }

  interface EnterTimeGroup {
    detailList: EnterTimeDetailListItem[];
    enterStartTime: string;
    totalNumber: number;
  }

  interface EnterTimeDetailListItem {
    batchId: string;
    distributorId: string;
    enterEndTime: string;
    enterStartTime: string;
    number: number;
    productId: string;
    productName: string;
    productType: number;
    timeShareBeginTime: string;
    timeShareEndTime: string;
    timeShareId: string;
  }

  interface TotalStockItem {
    pid: string;
    productId: string;
    productName: string;
    categoryType: number;
    scenicId: string;
    scenicName: string;
    supplierNames: string;
    timeRestrict: string;
    totalNumber: number;
    proType: number;
    purchaseEndTime: string;
    purchaseBeginTime: string;
    enterStartTime: string;
    enterEndTime: string;
    stockList: StockListItem[];
    goodsId?: string;
  }

  interface UserPageListItem {
    applyName: string;
    companyId: string;
    nickname: string;
    phone: string;
    status: number;
    userId: string;
    username: string;
  }

  interface ShopGoodsPageListParams {
    storeId: string;
    scenicName?: string;
    goodsName?: string;
  }

  interface ShopGoodsPageListItem {
    beginDiscount: number;
    dayBegin: string;
    dayEnd: string;
    endDiscount: number;
    goodsId: string;
    goodsName: string;
    goodsStatus: number;
    goodsType: string;
    isRecommend: boolean;
    marketPrice: number;
    name: string;
    overallDiscount: number;
    picUrl: string;
    priceId: number;
    productId: string;
    productType: number;
    purchaseBeginTime: string;
    purchaseEndTime: string;
    quantity: number;
    quantityItemCount: number;
    scenicId: string;
    scenicName: string;
    sellingPrice: number;
    storeGoodsId: string;
    storeGoodsType: number;
    supplierNames: string;
    timeShareBeginTime: string;
    timeShareEndTime: string;
    upDown: number;
  }

  interface ShopPermissionListItem {
    id: string;
    name: string;
    /** 1 窗口  2 自助售票 */
    permissionType: (1 | 2)[];
  }

  interface AuthorizeListItem {
    permissionType: number;
    storeId: string;
  }

  interface StoreAuthorizeParams {
    authorizeList?: AuthorizeListItem[];
    revokeList?: AuthorizeListItem[];
    userId: string;
  }

  interface SendH5MessageParams {
    phone: string;
    subOrderId: string;
  }

  interface OperationLogItem {
    id: string;
    app: string;
    project: string;
    module: string | null;
    function: string;
    content: string;
    createTime: string;
    creator: string;
    nickname: string;
    deviceIp: string;
    contentDetails: string;
  }
}
