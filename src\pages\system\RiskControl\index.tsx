import Disabled from '@/common/components/Disabled';
import { tableConfig } from '@/common/utils/config';
import { RiskMenuMap, RiskTypeMap } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import ProForm, { ModalForm, ProFormText } from '@ant-design/pro-form';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Popconfirm, message } from 'antd';
import { useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import type { ConditionListItem } from '../../../services/api/erp';
import {
  apiControlConf,
  apiPostScenicPerson,
  apiScenicControlList,
  apiScenicPerson,
  deleteRiskInfo,
  enableRisk,
  getRiskControlList,
} from '../../../services/api/erp';
import AddRiskControlTrigger from './AddRiskControlTrigger';

const contentMap = {
  quantityTicketOnce: '单次最大出票数量',
  amountTicketOnce: '单次最大出票金额',
  quantityPurchaseOnce: '单次进货数量',
  amountPurchaseOnce: '单次进货金额',
};

const logList = [
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
  },
];

const RiskControl = () => {
  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany || {};

  const [modalVisit, setModalVisit] = useState(false);

  const [formObj] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const access = useAccess();

  const [infoData, setInfoData] = useState<Record<string, any>>();

  //删除
  const onDelete = async (record: any) => {
    const { id } = record;
    await deleteRiskInfo({
      groupCode: 'e-commerce',
      relationId: coId,
      riskTargetId: id,
    });
    message.success('删除成功');
    actionRef?.current?.reload();
  };

  // 表格列表 有两个请求 第 1 页 11 条数据
  const tableListReq = async (params) => {
    const { current } = params;

    const { data } = await getRiskControlList(params);
    const list = data.list as any[];
    if (current === 1) {
      const { data } = await apiScenicControlList(coId);
      if (data[0]) {
        // 插入一条  统一数据
        list.unshift({
          ...data[0],
          id: data[0].controlConfId,
          status: data[0].isEnable === 1 ? 1 : 2,
          riskTarget: 'order',
          content: '需通过验证码审核才可删除订单',
        });
      }
    }
    return {
      total: data.total + 1,
      data: list,
    };
  };
  //查询复核员信息
  const onChecker = async () => {
    try {
      const { data } = await apiScenicPerson(coId);
      formObj.setFieldsValue(data);
      setInfoData(data);
    } catch (e) {
      console.error(e);
    }
    setModalVisit(true);
  };

  //修改复核员
  const onSubmit = async (val) => {
    const { name, phone } = val;
    localStorage.setItem('users', JSON.stringify({ name: name, phone: phone }));
    const pars = {
      businessId: coId,
      name,
      phone,
    };
    try {
      await apiPostScenicPerson(pars);
      addOperationLogRequest({
        action: 'edit',
        changeConfig: {
          list: logList,
          afterData: pars,
          beforeData: infoData,
        },

        content: `编辑【${name}】复核员信息`,
      });
      message.success('修改成功');
    } catch (e) {
      console.error(e);
    }
  };

  const columns: ProColumnType<any>[] = [
    {
      title: '风控名称',
      dataIndex: 'riskTarget',
      valueEnum: RiskTypeMap,
    },
    {
      title: '操作菜单',
      key: 'menu',
      dataIndex: 'riskTarget',
      valueEnum: RiskMenuMap,
    },
    {
      title: '操作内容',
      dataIndex: 'conditionList',
      render: (text, { controlConfId, riskTarget, content, conditionList = [] }) => {
        // 特殊数据
        if (controlConfId) {
          return content;
        }
        if (riskTarget === 'bond') {
          return '下级进货 / 代理售票时，账户中需要有足够保证金。待门票核销完毕或已过期后，才可提现';
        } else {
          const conditionText = (conditionList as ConditionListItem[])
            .map(({ attribute, val }) => `${contentMap[attribute]}大于 ${val} `)
            .join('，');
          return `当${conditionText}时，触发拦截`;
        }
      },
    },
    {
      title: '启用状态',
      render: (_: any, entity: any) => (
        <Disabled
          access={access.canRiskManagement_disable}
          status={entity.status == 1}
          params={
            entity.controlConfId
              ? {
                  controlConfId: entity.controlConfId,
                  id: coId,
                  isEnable: 1 - entity.isEnable,
                }
              : {
                  groupCode: 'e-commerce',
                  relationId: coId,
                  riskTargetId: entity.id,
                  status: 3 - entity.status,
                }
          }
          request={async (params) => {
            const data = entity.controlConfId ? await apiControlConf(params) : enableRisk(params);

            addOperationLogRequest({
              action: 'disable',
              content: `${entity.status == 1 ? '禁用' : '启用'}【${
                RiskTypeMap[entity.riskTarget]
              }】风控`,
            });

            return data;
          }}
          actionRef={actionRef}
        />
      ),
    },
    {
      width: 'auto',
      title: '操作',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_, record) => {
        return [
          // <Access key="enable" accessible={access.canRiskManagement_disable}>
          //   <Popconfirm
          //     title={`您确定${statusMap[3 - record.status]?.text}吗?`}
          //     onConfirm={() => onEnable(record)}
          //   >
          //     <a>{statusMap[3 - record.status]?.text}</a>
          //   </Popconfirm>
          // </Access>,
          record.controlConfId !== '100' && (
            <Popconfirm key="delete" title={`您确定删除吗?`} onConfirm={() => onDelete(record)}>
              <a style={{ color: 'red' }}>删除</a>
            </Popconfirm>
          ),
        ];
      },
    },
  ];

  return (
    <>
      <>
        <ProTable
          {...tableConfig}
          actionRef={actionRef}
          request={tableListReq}
          rowKey="id"
          columns={columns}
          params={{
            groupCode: 'e-commerce',
            relationId: coId,
          }}
          pagination={{
            defaultPageSize: 10,
          }}
          search={false}
          toolBarRender={() => {
            return [
              <AddRiskControlTrigger key="add" tableRef={actionRef} />,
              <Access key="info" accessible={access.canRiskManagement_accountant}>
                <Button type="primary" onClick={onChecker}>
                  复核员信息
                </Button>
              </Access>,
            ];
          }}
        />
      </>
      <ModalForm
        title="复核员信息"
        visible={modalVisit}
        form={formObj}
        width={modelWidth.md - 200}
        onFinish={async (val) => {
          onSubmit(val);
          return true;
        }}
        onVisibleChange={setModalVisit}
        submitter={{
          searchConfig: { submitText: '保存' },
        }}
      >
        <ProForm.Group>
          <ProFormText
            label="姓名"
            name="name"
            placeholder="请输入姓名"
            rules={[
              {
                required: true,
                message: '请输入姓名',
              },
            ]}
          />
          <ProFormText
            label="手机号码"
            name="phone"
            placeholder="请输入手机号码"
            rules={[
              {
                required: true,
                message: '请输入手机号码',
              },
              {
                pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
                message: '不符合手机号格式',
              },
            ]}
          />
        </ProForm.Group>
      </ModalForm>
    </>
  );
};
export default RiskControl;
