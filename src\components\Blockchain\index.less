.modal {
  :global {
    .ant-modal-content {
      .ant-descriptions {
        padding-bottom: 20px;
      }
      .ant-modal-header {
        text-align: center;
        background-color: #2374ff;
        .ant-modal-title {
          color: #fff;
          font-size: 20px;
        }
      }
      .ant-modal-body {
        padding: 20px 30px 30px 30px;
        // background: none;
        // background-image: url('../../assets//blockchain_bg.png');
        background-repeat: no-repeat;
        background-position: bottom center;
        background-size: cover;
      }
      .ant-modal-close-x {
        color: #fff;
        font-size: 20px;
      }
      .ant-descriptions-item-label {
        font-weight: initial !important;
      }
    }
    .ant-divider-horizontal {
      display: none;
    }
    .ant-descriptions-row > th,
    .ant-descriptions-row > td {
      padding-bottom: 2px;
    }
    .ant-descriptions-item-content > span {
      display: inline-block;
      margin-bottom: 1px;
    }
  }
}
