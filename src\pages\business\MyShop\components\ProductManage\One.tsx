import { tableConfig } from '@/common/utils/config';
import { productTypeEnum, ticketTypeEnum, whetherEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getAgentStatus, getPriceStrategyByAgent } from '@/services/api/distribution';
import { apiAgentGoodsList, apiStoreGoodsAdd } from '@/services/api/store';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Modal, Tag, message } from 'antd';
import { omit } from 'lodash';
import React, { useEffect, useState } from 'react';
import GoodsDetail from './GoodsDetail';

let valAll: any = [];
export default ({
  visible,
  setVisible,
  storeId,
  actionRefUp,
}: {
  visible: boolean;
  setVisible: any;
  storeId: string;
  actionRefUp: any;
}) => {
  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany;
  const actionRef = React.useRef<ActionType>();
  const [groupType, setGroupType] = useState('1');
  const [activeKey, setActiveKey] = useState<string>('1');
  const [comfirmLoading, setComfirmLoading] = useState<boolean>(false);
  // 商品详情
  const [detailData, setDetailData] = useState<any>({ id: '', isEnable: 0 });
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [detailLoading, setDetailLoading] = useState<boolean>(false);
  const columns: ProColumns[] = [
    {
      title: '平台',
      dataIndex: 'channel',
      valueEnum: { 1: '环球数科电商系统' },
      hideInTable: true,
    },
    {
      title: '导入类型',
      dataIndex: 'groupType',
      valueEnum: { 1: '代理授权凭证', 2: '公池导入', 3: '直销商品' },
      valueType: 'radio',
      hideInTable: true,
      initialValue: '1',
      fieldProps: {
        onChange: (e: any) => {
          setGroupType(e.target.value);
        },
      },
    },
    {
      title: '智能填充 AppID - AppSecret',
      dataIndex: 'appIDSecret',
      hideInTable: true,
      search: groupType !== '1' ? false : undefined,
      fieldProps: (form) => {
        return {
          placeholder: '粘贴数据自动填充',
          onChange: (e: any) => {
            const sum = e.target.value.replace(/AppID|:|;| /g, '').split('AppSecret');
            form.setFieldsValue({ appId: sum[0] });
            form.setFieldsValue({ appSecret: sum[1] });
            form.setFieldsValue({ appIDSecret: null });
          },
        };
      },
    },
    {
      title: 'AppID',
      dataIndex: 'appId',
      hideInTable: true,
      search: groupType !== '1' ? false : undefined,
      formItemProps: {
        rules: [{ required: true }],
      },
    },
    {
      title: 'AppSecret',
      dataIndex: 'appSecret',
      hideInTable: true,
      search: groupType !== '1' ? false : undefined,
      formItemProps: {
        rules: [{ required: true }],
      },
    },
    {
      width: 120,
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
      search: false,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      renderText: (dom: any, entity) => (
        <a
          onClick={async () => {
            // 商品详情
            setDetailLoading(true);
            setDetailVisible(true);
            try {
              // [普通票/旅游卡] 商品数据
              const { data } = await getPriceStrategyByAgent({
                unitId: entity?.unitId,
                distributorId: entity?.supplierId,
                priceId: entity?.priceId,
              });

              const prices = data?.price?.price || {};
              const _resData = {
                ...data,
                ...prices,
              };
              setDetailData(_resData);
              setDetailLoading(false);
            } catch (error) {
              console.log(error);
            }
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      width: 60,
      title: '票种',
      dataIndex: 'goodsType',
      valueEnum: ticketTypeEnum,
      hideInSearch: true,
      renderText: (dom: any, entity: any) => (entity.proType == 20 ? '-' : dom),
    },
    {
      width: 60,
      title: '类型',
      dataIndex: 'proType',
      valueEnum: productTypeEnum,
      hideInSearch: true,
    },
    {
      title: '数字资产',
      dataIndex: 'isDigit',
      valueEnum: whetherEnum,
    },
    {
      title: '分时时段',
      hideInSearch: true,
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '购买有效时间',
      search: false,
      render: (_, entity: any) =>
        entity.purchaseBeginTime && entity.purchaseEndTime
          ? entity.purchaseBeginTime + ' 至 ' + entity.purchaseEndTime
          : '-',
    },
    {
      title: '用户单独购买价格（元）',
      dataIndex: 'salePrice',
      hideInSearch: true,
      renderText: (dom) => {
        const result = parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom;
        return result + '起';
      },
    },
    {
      title: '套餐中用户购买单品价格（元）',
      dataIndex: 'composePrice',
      hideInSearch: true,
      // render: (dom: any, entity: any) => (entity.isCompose ? dom : '-'),
    },
    // {
    //   width: 80,
    //   title: '佣金类型',
    //   dataIndex: 'commissionType',
    //   valueEnum: { 0: '固定佣金', 1: '按比例' },
    //   hideInSearch: true,
    // },
    {
      title: '佣金比例（%）',
      dataIndex: 'commissionRate',
      hideInSearch: true,
      // render: (dom: any, entity: any) => (entity.commissionType ? dom : '-'),
    },
    // {
    //   width: 80,
    //   title: '佣金数值',
    //   dataIndex: 'commissionAmount',
    //   hideInSearch: true,
    //   render: (dom: any, entity: any) => (!entity.commissionType ? dom : '-'),
    // },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      hideInSearch: true,
    },
  ];
  useEffect(() => {
    setGroupType('1');
    valAll = [];
  }, [visible]);
  return (
    <Modal
      width={1400}
      title={'所有商品'}
      visible={visible}
      destroyOnClose
      maskClosable={false}
      cancelText="取消"
      okText="确定"
      confirmLoading={comfirmLoading}
      onCancel={() => {
        setVisible(false);
      }}
      onOk={async () => {
        if (valAll.length == 0) {
          message.info('无提交数据');
          return;
        }
        try {
          setComfirmLoading(true);
          const simpleGoods = valAll.map((item: any) => ({
            // "id": "",
            name: item.proName,
            // "note": item,
            priceId: item.priceId,
          }));

          await apiStoreGoodsAdd({
            simpleGoods,
            storeId,
          });

          addOperationLogRequest({
            action: 'add',
            content: `导入【${valAll.length}】商品`,
          });
          message.success('导入成功');
          setComfirmLoading(false);
          setVisible(false);
          actionRefUp?.current?.reload();
        } catch (error) {}
      }}
    >
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="priceId"
        // search={false}
        // search={{
        //   labelWidth: 'auto',
        //   collapseRender: false,
        //   collapsed: false,
        // }}
        manualRequest
        // options={false}
        params={{ activeKey }}
        onReset={() => {
          setGroupType('1');
        }}
        request={async (params) => {
          if (groupType === '1' && (!params.appId || !params.appSecret)) {
            message.warning('请输入代理授权凭证');
            return;
          }

          let disabled: boolean;

          disabled = false;

          if (groupType === '1' && params.appId && params.appSecret) {
            const { data } = await getAgentStatus({
              appId: params.appId,
              appSecret: params.appSecret,
              distributorId: coId,
            });

            // 被禁用
            if (data.status === 2) {
              disabled = true;
            }
          }

          const res = await apiAgentGoodsList({
            ...omit(params, 'activeKey'),
            status: activeKey,
            storeId: storeId,
            groupType: groupType,
            distributorId: coId,
          });

          return {
            ...res,
            data: (res.data || []).map((i) => ({
              ...i,
              disabled,
            })),
          };
        }}
        columns={columns}
        rowSelection={
          activeKey === '1'
            ? {
                type: 'checkbox',
                onChange: (key, val) => {
                  console.log(key, val);

                  valAll = val;
                  // console.log(key);
                  // userList = String(key).split(',');
                  // if (!userList[0]) userList = [];
                },
                getCheckboxProps: (record) => ({
                  disabled: record.disabled,
                }),
              }
            : false
        }
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: [
              {
                key: '1',
                label: <span>未导入</span>,
              },
              {
                key: '2',
                label: <span>已导入</span>,
              },
            ],
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        search={{
          ...tableConfig.search,
          span: { xs: 24, sm: 24, md: 12, lg: 12, xl: 8, xxl: 8 },
        }}
      />

      {/* 商品详情 */}
      <GoodsDetail
        visible={detailVisible}
        loading={detailLoading}
        setVisible={setDetailVisible}
        dataSource={detailData}
      />
    </Modal>
  );
};
