import ProSvg from '@/common/components/ProSvg';
import { systemPageEnum } from '@/common/utils/enum';
import type { ProFormInstance, FormListActionType } from '@ant-design/pro-components';
import { ProFormList, ProFormText, ProFormSelect, ProForm, ProCard } from '@ant-design/pro-components';
import { ArrowDownOutlined, ArrowUpOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Space } from 'antd';
import type { FC, MutableRefObject } from 'react';
import styles from './index.less';

type CommonNavFormProps = {
  formRef: MutableRefObject<ProFormInstance | undefined>;
  listActionRef: MutableRefObject<FormListActionType | undefined>;
  tabList: any[];
  setTabList: (updater: any) => void;
  setIsModalOpen: (index: number) => void;
  MAX_NAV: number;
  color?: string;
};

const CommonNavForm: FC<CommonNavFormProps> = ({
  formRef,
  listActionRef,
  tabList,
  setTabList,
  setIsModalOpen,
  MAX_NAV,
  color,
}) => {
  const renderIcon = (src?: string) => {
    if (!src) return <div className={styles.iconPlaceholder} />;
    return typeof src === 'string' && src.includes('http') ? (
      <img
        src={src}
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
          width: 'auto',
          height: 'auto',
          objectFit: 'contain',
        }}
      />
    ) : (
      <ProSvg src={src} color={color} />
    );
  };

  return (
    <>
      <ProFormList
        label="导航设置"
        className={styles.navList}
        name="shopNav"
        creatorButtonProps={false}
        actionRef={listActionRef as any}
        min={1}
        max={MAX_NAV}
        copyIconProps={false}
        actionRender={(field, action, defaultAction) => {
          const idx = Array.isArray(field.name)
            ? (field.name[field.name.length - 1] as number)
            : (field.name as number);
          const isEdge = idx === 0 || idx === (tabList?.length || 0) - 1;
          if (isEdge) return [];
          return defaultAction;
        }}
        itemRender={({ listDom, action }, { index }) => (
          <ProCard
            bordered
            title={<div style={{ lineHeight: '32px' }}>{`导航 ${index + 1}`}</div>}
            headStyle={{ padding: '8px 24px' }}
            extra={action}
            type="inner"
          >
            {listDom}
          </ProCard>
        )}
      >
        {(_, index, action) => (
          <>
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: 16 }}>
              <ProForm.Item className={styles.icon} label={false}>
                <Space align="start">
                  <ProFormText
                    name="icon_active"
                    hidden
                    rules={[{ required: true, message: '请添加图标' }]}
                  />
                  <ProFormText
                    name="icon"
                    hidden
                    rules={[{ required: true, message: '请添加图标' }]}
                  />
                  {!(tabList?.[index]?.icon_active || tabList?.[index]?.icon) ? (
                    <div className={styles.addWrap}>
                      <div className={styles.addBlock} onClick={() => setIsModalOpen(index)}>
                        <PlusOutlined className={styles.addIcon} />
                        <div className={styles.addTextInside}>添加图标</div>
                      </div>
                      <Form.Item noStyle shouldUpdate>
                        {(form) => {
                          const iconErrors = form.getFieldError(['shopNav', index, 'icon']);
                          const iconActiveErrors = form.getFieldError([
                            'shopNav',
                            index,
                            'icon_active',
                          ]);
                          const hasErrors =
                            (iconErrors?.length || 0) > 0 || (iconActiveErrors?.length || 0) > 0;
                          return hasErrors ? (
                            <div className={styles.iconError}>请添加图标</div>
                          ) : null;
                        }}
                      </Form.Item>
                    </div>
                  ) : (
                    <div className={styles.iconWrap}>
                      <div className={styles.iconBlock} onClick={() => setIsModalOpen(index)}>
                        <div className={styles.iconsRow}>
                          <div className={`${styles.circle}`}>
                            <div className={`${styles.circle_img}`}>
                              {renderIcon(tabList?.[index]?.icon_active)}
                            </div>
                          </div>
                          <div className={styles.circle}>
                            <div className={`${styles.circle_img}`}>
                              {renderIcon(tabList?.[index]?.icon)}
                            </div>
                          </div>
                        </div>
                        <div className={styles.mask}>替换</div>
                      </div>
                      <div className={styles.labelsRow}>
                        <span>选中</span>
                        <span>未选中</span>
                      </div>
                    </div>
                  )}
                </Space>
              </ProForm.Item>
              <div style={{ display: 'flex', flexDirection: 'column', marginTop: 16 }}>
                <ProFormText
                  labelCol={{ span: 42 }}
                  width={260}
                  name="name"
                  label="名称"
                  fieldProps={{ maxLength: 4, showCount: true }}
                  rules={[{ required: true, message: '请输入导航名称' }]}
                />
                <ProFormSelect
                  width={260}
                  name="link"
                  label="链接"
                  valueEnum={systemPageEnum}
                  disabled={index === 0 || index === (tabList?.length || 0) - 1}
                  rules={[{ required: true, message: '请选择导航链接' }]}
                />
              </div>
            </div>
            {!(index === 0 || index === (tabList?.length || 0) - 1) && (
              <Space size={4} style={{ position: 'absolute', top: 13, left: 78 }}>
                {index === 1 ? (
                  <ArrowUpOutlined style={{ color: '#D9D9D9', cursor: 'not-allowed' }} />
                ) : (
                  <ArrowUpOutlined onClick={() => action.move?.(index, index - 1)} />
                )}
                {index === (tabList?.length || 0) - 2 ? (
                  <ArrowDownOutlined style={{ color: '#D9D9D9', cursor: 'not-allowed' }} />
                ) : (
                  <ArrowDownOutlined onClick={() => action.move?.(index, index + 1)} />
                )}
              </Space>
            )}
          </>
        )}
      </ProFormList>
      {(tabList?.length || 0) < MAX_NAV && (
        <div>
          <Button
            block
            size="large"
            type="dashed"
            icon={<PlusOutlined />}
            onClick={() =>
              listActionRef.current?.add?.({}, Math.max((tabList?.length || 0) - 1, 0))
            }
          >
            添加导航（{tabList?.length || 0}/{MAX_NAV}）
          </Button>
        </div>
      )}
    </>
  );
};

export default CommonNavForm;


