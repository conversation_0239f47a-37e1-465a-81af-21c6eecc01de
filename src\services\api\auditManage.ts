/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-20 11:49:00
 * @LastEditTime: 2023-10-09 10:07:38
 * @LastEditors: zhangfeng<PERSON>i
 */

import { request } from '@umijs/max';
import { scenicHost } from '.';
import type { API, ResponseData, ResponseListData2 } from './typings';

/** 查询审核列表 */
export async function getAuditList(params: API.AuditListParams, options: Record<string, any> = {}) {
  return request<ResponseListData2<API.AuditListItem[]>>(
    `${scenicHost}/agentSupport/applyPageList`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/** 通过/驳回审核 */
export async function updateAuditInfo(
  params: API.UpdateAuditInfoParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<null>>(`${scenicHost}/agentSupport/applyApproval`, {
    method: 'POST',
    data: params,
    ...options,
  });
}

interface AgentGroupListItem {
  id: string;
  name: string;
  agentAmount: number;
  agentProductAmount: number;
  status: number;
  collectionCode: string;
}

// 代理商分组列表
export function getAgentGroupList(params: { distributorId: string }) {
  return request<ResponseData<AgentGroupListItem[]>>(`${scenicHost}/ticketAgent/agentGroupList`, {
    method: 'GET',
    params,
  });
}

// 代理商分组添加代理商
export function agentGroupAdd(params: {
  coCode?: string;
  groupId: string;
  agentType: number;
  phone?: string;
}) {
  return request(`${scenicHost}/ticketAgent/agentInfo`, {
    method: 'POST',
    data: params,
  });
}

interface DistributorGroupListItem {
  id: string;
  name: string;
  distributorAmount: number;
  distributorPriceAmount: number;
  status: number;
  collectionCode: string;
}

// 分销商分组列表
export async function getDistributorGroupList(params: { distributorId: string }) {
  return request<ResponseData<DistributorGroupListItem[]>>(
    `${scenicHost}/distribution/distribution/groupList`,
    {
      method: 'GET',
      params,
    },
  );
}

// 分销商分组添加分销商
export async function distributorGroupAdd(params: { coCode: string; groupId: string }) {
  return request(`${scenicHost}/distribution/distribution/distributor`, {
    method: 'POST',
    data: params,
  });
}
export interface DownInUpDistrItem {
  distributorId: string;
  groupId: string;
  name: string;
}
// 查询分销商 B 添加到了分销商 A 的哪些经销商分组
export function getDownInUpDistr(params: { downDistributorId: string; upDistributorId: string }) {
  return request<ResponseData<DownInUpDistrItem[]>>(
    `${scenicHost}/distribution/distribution/joinGroup`,
    {
      method: 'GET',
      params,
    },
  );
}

// 查询分销商 B 添加到了分销商 A 的哪些代理商分组
export function getDownInUpAgent(params: { downDistributorId: string; upDistributorId: string }) {
  return request<ResponseData<DownInUpDistrItem[]>>(
    `${scenicHost}/ticketAgent/agentGroup/queryJoinGroup`,
    {
      method: 'GET',
      params,
    },
  );
}
