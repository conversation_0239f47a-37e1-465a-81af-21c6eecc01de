import { tableConfig } from '@/common/utils/config';
import { precreditTransactions } from '@/services/api/precredit';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel, useRequest } from '@umijs/max';

export default () => {
  const {
    initialState: {
      currentCompany: { settlementId },
    },
  }: any = useModel('@@initialState');
  const tableReq = useRequest(precreditTransactions, {
    manual: true,
    formatResult(res) {
      return {
        data: res.data.data,
        success: res.code == 20000,
        total: res.data.total,
      };
    },
  });
  const columns: ProColumns[] = [
    {
      title: '交易ID',
      dataIndex: 'id',
    },
    {
      title: '借方账户',
      dataIndex: 'creditedAccountPath',
    },
    {
      title: '贷方账户',
      dataIndex: 'debitedAccountPath',
    },
    {
      title: '交易时间',
      dataIndex: 'transactionTime',
      valueType: 'dateTime',
    },
    {
      title: '币种',
      dataIndex: 'currency',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      valueType: 'money',
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
  ];
  return (
    <ProTable
      {...tableConfig}
      columns={columns}
      search={false}
      params={{ settlementId }}
      request={tableReq.run}
    />
  );
};
