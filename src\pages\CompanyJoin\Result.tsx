/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-16 14:33:51
 * @LastEditTime: 2025-05-29 11:10:02
 * @LastEditors: 李悍宇 zhou<PERSON>@hqyatu.com
 */
import { ApplyStatusEnum } from '@/common/utils/enum';
import { getApplyStatus } from '@/services/api/companyJoin';
import { Result as AntdResult, Button, Space, Steps } from 'antd';
import qs from 'qs';
import type { FC } from 'react';
import { useEffect } from 'react';
import { history, useLocation, useRequest } from '@umijs/max';
import type { URLParams } from '.';

const { Step } = Steps;

interface ResultProps {}

const Result: FC<ResultProps> = () => {
  const { search } = useLocation();

  // 转对象
  const searchParams = qs.parse(search, {
    ignoreQueryPrefix: true,
  }) as unknown as URLParams & {
    applyDistributorId: string;
    applyId: string;
    type: string;
    userId: string;
  };

  const { code, coName, coId, applyDistributorId, applyId, type, userId } = searchParams;

  // 获取申请状态 默认为待审核
  const { data, run } = useRequest(getApplyStatus, {
    manual: true,
  });

  useEffect(() => {
    run({
      applyDistributorId,
      applyId,
      upDistributorId: coId,
    });
  }, []);

  return data?.status === ApplyStatusEnum.审核失败 || data?.status === ApplyStatusEnum.已拒绝 ? (
    <AntdResult
      status="warning"
      title="申请失败"
      subTitle={<>很抱歉您未能通过审核，具体原因：{data?.note}</>}
      extra={
        <>
          <Space style={{ marginTop: 30 }}>
            <Button
              type="primary"
              onClick={() => {
                history.push(
                  `/companyJoin/commit?${qs.stringify({ code, coId, coName, type, userId })}`,
                );
              }}
            >
              再次申请
            </Button>
          </Space>
        </>
      }
    />
  ) : (
    <AntdResult
      status="success"
      title="提交成功"
      subTitle={
        <>
          您已完成{coName}分销商的申请，管理员审批通过后，将会以短信的
          <br />
          形式告知您，敬请留意。
        </>
      }
      extra={
        <>
          <Steps progressDot current={data?.status}>
            <Step title="提交成功" />
            <Step title="审核中" />
            <Step title="审核完成" />
          </Steps>
          <Space style={{ marginTop: 30 }}>
            <Button
              type="primary"
              onClick={() => {
                history.push(
                  `/companyJoin/commit?${qs.stringify({ code, coId, coName, type, userId })}`,
                );
              }}
            >
              再次申请
            </Button>
            <Button
              style={{ marginLeft: 20 }}
              onClick={() => {
                history.push('/enterprise');
              }}
            >
              取消
            </Button>
          </Space>
        </>
      }
    />
  );
};

export default Result;
