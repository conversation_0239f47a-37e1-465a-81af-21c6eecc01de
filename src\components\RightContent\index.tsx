import { addOperationLogRequest } from '@/common/utils/operationLog';
import useModal from '@/hooks/useModal';
import { apiWikiList } from '@/services/api/erp';
import { BellOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Select, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { Access, history, useAccess, useModel } from '@umijs/max';
import AiService from './AiService';
import Avatar from './AvatarDropdown';
import MessageModal from './MessageModal';
import styles from './index.less';
import { getEnv } from '@/common/utils/getEnv';

export type SiderTheme = 'light' | 'dark';

const isDev = process.env.NODE_ENV === 'development';
const currentCompanyId = localStorage.getItem('currentCompanyId');
// let messageTimer = null;
const GlobalHeaderRight: React.FC = () => {
  const access = useAccess();
  const [menuData, setMenuData] = useState([]);
  const [isBusiness, setIsBusiness] = useState(false);
  const [open, setOpen] = useState(false);
  // const [unReadNum, setUnReadNum] = useState(0);
  const strategyModalState = useModal();
  //菜单页
  const getMenuList = async () => {
    const pars = {
      type: 2,
      isEnable: 1,
    };
    const { data } = await apiWikiList(pars);
    setMenuData(data);
  };

  // 初始化
  useEffect(() => {
    getMerchantList();
    getMenuList();
    //   getUnReadMessageCount();
    //   messageTimer = setInterval(() => {
    //     //TODO：是否需要销毁
    //     getUnReadMessageCount();
    //   }, 1000 * 60 * 5);
    //   return () => {
    //     //卸载消息轮训器
    //     messageTimer = null;
    //   };
    // console.log(history.location.pathname);
    setIsBusiness(history.location.pathname == '/dnterprise');
    history.listen((...args: any[]) => {
      // console.log(args[0].pathname)
      setIsBusiness(history.location.pathname == '/dnterprise');
    });

    // 首次加载获取未读消息数
    strategyModalState.getMessageRed(currentCompanyId, userId);

    // 设置5秒轮询定时器
    messageTimer.current = setInterval(() => {
      strategyModalState.getMessageRed(currentCompanyId, userId);
    }, 30000); //

    // 组件卸载时清除定时器
    return () => {
      if (messageTimer.current) {
        clearInterval(messageTimer.current);
        messageTimer.current = null;
      }
    };
  }, []);
  const actionRef = React.useRef<any>();
  const { initialState }: any = useModel('@@initialState');
  const { userId } = initialState.userInfo;
  // console.log(initialState?.userInfo?.userId);
  /* 下拉框 */
  const [defaultValue, setDefaultValue] = useState<any>();
  const messageTimer = useRef<any>(null);
  // const [merchantList, setMerchantList] = useState<any>([]);
  // const [companyList, setCompanyList] = useState<any>([]);
  if (!initialState || !initialState.settings) {
    return null;
  }
  const { navTheme, layout } = initialState.settings;

  // 获取【企业】列表
  async function getMerchantList() {
    try {
      // const list = initialState?.companyList?.map((item: any) => ({
      //   value: item.id,
      //   label: item.name,
      //   settlementId: item.settlementId,
      // }));
      // setMerchantList(list);
      setDefaultValue(initialState?.currentCompany?.coId);

      // //设置默认企业
      // const currentCompany = JSON.parse(localStorage.getItem('currentCompany'))
      // if(currentCompany !== null){
      //   setDefaultValue(currentCompany.coId);
      //   setCoId(currentCompany.coId);
      //   setCoName(currentCompany.coName);
      // }else{
      //   // 读默认第一项
      //   setDefaultValue(list?.[0]?.value);
      //   setCoId(list?.[0]?.value);
      //   setCoName(list?.[0]?.label);
      //   localStorage.setItem("currentCompany", JSON.stringify({
      //     coId: list?.[0]?.value,
      //     coName: list?.[0]?.label,
      //   }))
      // }
    } catch (e) {
      console.error(e);
    }
  }

  const setUnReadNum = (num: number) => {
    strategyModalState.setNum(num);
  };

  const onJumpUrl = (val) => {
    window.open(val);
  };
  return (
    <Space align="center">
      {!isBusiness ? (
        <div id="select_box">
          <span className="select_label" style={{ marginRight: 8 }}>
            当前企业：
          </span>
          <Select
            ref={actionRef}
            className="select_content"
            placeholder="选择当前企业"
            style={{
              minWidth: 200,
            }}
            onChange={(e: any) => {
              localStorage.setItem('currentCompanyId', e);
              // 跳转到欢迎页
              if (history.location.pathname !== '/welcome') history.push('/welcome');
              location.reload();
            }}
            getPopupContainer={(node) => node.parentNode}
            showSearch
            value={defaultValue}
            virtual={false}
          >
            {initialState?.companyDownList?.map((item: any, index: any) => (
              <Select.Option key={index} value={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        </div>
      ) : (
        ''
      )}

      <Avatar />
      <span
        className={styles.message}
        onClick={async () => {
          setOpen(true);
        }}
      >
        <BellOutlined /> 消息
        {strategyModalState.unReadNum > 0 && (
          <div className={styles.tip}>{strategyModalState.unReadNum}</div>
        )}
      </span>

      <AiService />

      <Access accessible={access.canHelp}>
        <span
          onClick={() => {
            addOperationLogRequest({
              action: 'link',
              content: '跳转帮助中心',
              module: '',
            });
            window.open(getEnv().HELP_URL);
          }}
        >
          <QuestionCircleOutlined /> 帮助中心
        </span>
      </Access>
      <MessageModal show={open} close={() => setOpen(false)} setUnReadNum={setUnReadNum} />
    </Space>
  );
};

export default GlobalHeaderRight;
