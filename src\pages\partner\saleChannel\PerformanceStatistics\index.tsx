import { Tabs } from 'antd';
import React, { useState } from 'react';
import Dealer from './Dealer';
import Agent from './Agent';

const { TabPane } = Tabs;

const TableList: React.FC = () => {
  const [firstTabKey, setFirstTabKey] = useState('1');

  return (
    <>
      <Tabs
        tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
        defaultActiveKey="1"  
        activeKey={firstTabKey}
        destroyInactiveTabPane
        onChange={(key) => {
          setFirstTabKey(key);
        }}
      >
        <TabPane tab="经销商" key="1">
          <Dealer />
        </TabPane>
        <TabPane tab="代理商" key="2">
          <Agent />
        </TabPane>
      </Tabs>
    </>
  );
};

export default TableList;
