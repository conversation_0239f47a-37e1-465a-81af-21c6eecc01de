/**
 * 详情 弹窗组件
 **/
import { ModelWidth } from '@/common/utils/enum';
import { getUniqueId } from '@/common/utils/tool';
import { LeftOutlined } from '@ant-design/icons';
import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import { Divider, Modal } from 'antd';
import { useEffect, useState } from 'react';
import { useRequest } from '@umijs/max';
import PrefixTitle from '../PrefixTitle';

type AttributeTypeInfo = {
  page?: any;
  title: string;
  fullTitle: string;
  columns: any;
  type: any;
  setType: any;
  params?: any;
  dataSource?: any;
  infoRequest?: any;
  width?: ModelWidth;
};

export default ({
  page,
  title,
  fullTitle,
  columns,
  type,
  setType,
  params = {},
  dataSource,
  infoRequest,
  width = ModelWidth.md,
}: AttributeTypeInfo) => {
  // 自动优化默认属性
  const optimize = (columns: any) =>
    columns
      .filter((item: any) => !item.hideInDescriptions)
      .map((i: any) => ({
        column: page
          ? { xs: 1, sm: 2, xl: 3, xxl: 4 }
          : { xs: 1, sm: 2, md: 2, lg: 2, xl: 2, xxl: 2 }, // 详情栅格
        ...i,
      }));
  const columnsInit = optimize(columns);
  const [init, setInit] = useState<any>([{}]);
  // 加载数据
  const req = useRequest(
    dataSource ? () => new Promise((r) => r({ data: dataSource })) : infoRequest,
    {
      manual: true,
      onSuccess: (res) => {
        // 构造 ProDescriptions dependency 属性
        for (let i = 0; i < columnsInit.length; i++) {
          if (columnsInit[i].valueType == 'dependency') {
            columnsInit.splice(i, 1, ...optimize(columnsInit[i].columns(res)));
            i--;
          } else {
            for (let j = 0; j < columnsInit[i].columns.length; j++) {
              if (columnsInit[i].columns[j].valueType == 'dependency') {
                columnsInit[i].columns.splice(
                  j,
                  1,
                  ...optimize(columnsInit[i].columns[j].columns(res)),
                );
                j--;
              }
            }
          }
        }
        console.log(columnsInit);

        // 重新渲染配置项
        setInit(columnsInit);
      },
    },
  );
  // 初始化
  useEffect(() => {
    if (type) req?.run(params);
  }, [type]);

  return page ? (
    <ProCard
      title={
        <div
          className="flex align-items-center primary-color pointer"
          onClick={() => setType(null)}
        >
          <LeftOutlined style={{ marginRight: 10 }} />
          {title + '详情'}
        </div>
      }
      headerBordered
    >
      {init.map((item: any, index: any) => {
        return (
          <>
            <ProDescriptions
              key={getUniqueId()}
              dataSource={req.data}
              loading={req.loading}
              {...item}
              title={item.title && <PrefixTitle>{item.title} </PrefixTitle>}
            />
            {index < init.length - 1 && !req.loading && !item.noDivider && <Divider />}
          </>
        );
      })}
    </ProCard>
  ) : (
    <Modal
      width={width}
      title={fullTitle || title + '详情'}
      open={!!type}
      onCancel={() => setType(null)}
      footer={false}
      destroyOnClose
    >
      {init.map((item: any, index: any) => {
        return (
          <>
            <ProDescriptions
              key={index}
              dataSource={req.data}
              loading={!index && req.loading}
              style={(req.loading && { display: 'none' }) || {}}
              {...item}
              title={item.title && <PrefixTitle>{item.title} </PrefixTitle>}
            />
            {index < init.length - 1 && !req.loading && <Divider />}
          </>
        );
      })}
    </Modal>
  );
};
