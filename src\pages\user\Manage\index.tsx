import Disabled from '@/common/components/Disabled';
import SearchTree, { filterPermissions as fp } from '@/common/components/SeachTree';
import { tableConfig } from '@/common/utils/config';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getUniqueId, MD5, randomString, SHA1 } from '@/common/utils/tool';
import {
  addDepartment,
  addUser,
  getCoAllUsers,
  getDepartment,
  getOrgStructure,
  getPermissionList,
  getPermissionListByUserId,
  getPermissionListNew,
  getRoleDownList,
  getRoleInfoList,
  getUserDept,
  getUserPageList,
  getUserSocial,
  operationRequest,
  postPermissionAuthorize,
  putUserDisable,
  resetUserPasswork,
  setDepartment,
  setUser,
  userDataMigrate,
} from '@/services/api/cas';
import type { API } from '@/services/api/typings';
import {
  CopyOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import {
  Alert,
  Button,
  Col,
  message,
  Modal,
  Popover,
  Row,
  Space,
  Tooltip,
  Tree,
  Typography,
} from 'antd';
import DirectoryTree from 'antd/lib/tree/DirectoryTree';
import copy from 'copy-to-clipboard';
import _ from 'lodash';
import type { DataNode, Key } from 'rc-tree/lib/interface';
import React, { useEffect, useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import { getEnv } from '@/common/utils/getEnv';

interface DefaultValueType {
  label: string;
  value: string;
}

//权限信息
interface Permission {
  id: number;
  groupCode: string;
  groupName: string;
  code: string;
  codeName: string;
  actionCode: string;
  action: string;
}

const { APPID, CAS_HOST, LOGIN_HOST } = getEnv();
// import { useModel } from '@umijs/max';
const { Text } = Typography;
// 普通记录变量
let allRolesVE: any; //全部的角色信息
let allPermissionsVE: any; //全部的权限信息
//let companyId = ''; //公司 id
let allUserSourceData: Map<string, any>; //某公司全部用户的信息
//并不需要在值变化的时候刷新，所以单独再多声明一次
let companyName: string = '';
let seleteDepmId: string | number = '0';
let seleteDepname: string = '';
let deptSourceData: any; //部门的 http 原始数据，用于方便显示部门名称
let allCompany: any; //全部公司的信息

const permissionTree: DataNode[] = []; //全局权限树状结构
const companyDetpTree: DataNode[] = []; //全局企业部门树状结构
let checkPermissionsTemp: Key[] = []; //用于存储在弹出窗所选择的权限
let checkCompanyDeptTemp: Key[] = []; //用于存储在弹出窗所选择的部门

/**
 * 递归选出勾选的权限
 * @param keys 选中的 key
 */
function dealCheck(keys: Key[], parent: DataNode[]): DataNode[] {
  const node: DataNode[] = [];
  if (!keys || !parent) {
    return node;
  }
  for (let i = 0; i < parent.length; i++) {
    const element = parent[i];
    const children = _.cloneDeep(element.children); //直接引用会导致数据混乱
    if (children && children.length > 0) {
      const c = dealCheck(keys, children);
      if (c && c.length > 0) {
        //有子集才添加
        node.push({ title: element.title, key: element.key, children: c });
      }
    } else {
      //最后一层，判定是否添加
      if (keys.findIndex((e: Key) => e == element.key) != -1) {
        node.push({ title: element.title, key: element.key });
      }
    }
  }
  return node;
}

const TableList: React.FC = () => {
  const access = useAccess();
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const { currentCompany } = useModel('@@initialState').initialState || {};

  const [createModalVisible, handleModalVisible] = useState<boolean>(false); //新建窗口
  const [companyId, setCompanyId] = useState<string>(''); //公司 id
  const [departmentEditVisible, handleDepmEditVisible] = useState<boolean>(false); //更多选择窗口

  const [departmentNewVisible, handleDepmNewVisible] = useState<boolean>(false); //新建部门窗口
  const [departmentRenameVisible, handleDepmRenameVisible] = useState<boolean>(false); //部门重命名窗口
  const [migrateVisible, setMigrateVisible] = useState<boolean>(false); //数据迁移窗口
  const [userListParams, handleUserListParams] = useState<Record<string, any>>({
    companyId: 0,
    deptId: 0,
  }); //用户列表请求的参数
  /**
   * @en-US The pop-up window of the distribution update window
   * @zh-CN 分布更新窗口的弹窗
   * */
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [currentRow, setCurrentRow] = useState<any>(); //选中的用户信息
  const [departmentSelect, handleDepmSelect] = useState<any>();
  const [departmentTree, handleDepmTree] = useState<any>(); //部门树

  const { initialState } = useModel('@@initialState');
  const { coName, coId } = initialState?.currentCompany || {};

  // console.log(initialState)
  const [migrateUserVM, setMigrateUserVM] = useState<any>(); //数据迁移的用户列表，VE 即：当前列值的枚举，valueEnum
  const [permissionsMV, setPermissionsMV] = useState<boolean>(false); //权限选择窗口 注:MV=ModalVisible
  const [companyDeptMV, setCompanyDeptMV] = useState<boolean>(false); //企业选择窗口 注:MV=ModalVisible
  /**
   * 弹窗所使用到的 key 值
   */
  //关于 useState 原理:https://zhuanlan.zhihu.com/p/94586032
  const [checkPermissions, setCheckPermissions] = useState<Key[]>([]); //已经选择的权限，未过滤父级的数据，如子集全选：1/2/3 会带 1/2

  //尝试通过 user id 获取权限列表
  const tryGetPermissionListByUserId = async () => {
    const id = currentRow.userId;
    try {
      const { data } = await getPermissionListByUserId(id, `e-commerce/${companyId}`);
      const result: any = [];
      for (const e of data) {
        const l = `${e.group}|${e.code}|${e.action}`;
        result.push(l);
      }
      return result;
    } catch (e) {
      console.log(e);
      return [];
    }
  };

  //在树中查第一层 key 的位置
  function findTreeKeyIndex(tree: DataNode[], key: string): number {
    return tree.findIndex((e: DataNode) => {
      return e.key === key;
    });
  }
  //处理服务器取到的数据为本地组件所使用的格式
  function dealPermissionList(data: any): DefaultValueType[] {
    const result: DefaultValueType[] = [];
    if (data.code !== 20000) return [];
    data.data.forEach((e: any) => {
      const gn = e.groupName;
      const cn = e.codeName;
      const a = e.action;
      const name = `${gn} / ${cn} / ${a}`;
      const value = e.groupCode + '|' + e.code + '|' + e.actionCode;
      result.push({ label: name, value: value });

      //树结构构建，以下树结构应为已有时不再新加
      const treeInfo = [
        [gn, e.groupCode],
        [cn, `${e.groupCode}|${e.code}`],
        [a, value],
      ]; //[name,value]
      let treeNode = permissionTree;
      for (let i = 0; i < treeInfo.length; i++) {
        //逐层寻找，然后插入
        const index = findTreeKeyIndex(treeNode, treeInfo[i][1]);
        if (index != -1) {
          treeNode = treeNode[index].children!;
          continue;
        }
        const tempNode: DataNode = {
          title: treeInfo[i][0],
          children: [],
          key: treeInfo[i][1],
        };
        treeNode.push(tempNode);
        treeNode = tempNode.children!;
      }
    });
    return result;
  }

  const initData = async () => {
    try {
      //取全部公司的信息
      setCompanyId(initialState?.currentCompany?.coId);
      allCompany = initialState?.firmList;

      handleGetCoDepm(initialState?.currentCompany?.coId);
    } catch (e) {
      console.log(e);
    }
  };
  //取用户列表
  const handUserList = async (q?: Record<string, any>): Promise<API.RuleList> => {
    const hide = message.loading('正在查询');
    try {
      const { data } = await getUserPageList({
        ...q,
        companyId,
      });
      hide();
      return data;
    } catch (e) {
      console.log(e);
      return {};
    }
  };

  //取企业部门，并处理成 tree 所需要的数据结构类型
  const handleGetCoDepm = async (coid: string) => {
    companyName = allCompany.filter((n: any) => {
      return n.coId == coid;
    })[0].coName;
    seleteDepmId = 0;
    try {
      const r = (await getDepartment({ id: [coid] })) as any;

      if (r.code === 20000) {
        const result: any = [{ title: companyName, key: '0', children: [] }]; //key 一定要为字符串，不然 seachTree 识别 key 无法分割
        //递归插入
        const insertDataToChildren = (parentSet: [], parentDeptId: string, d: any): boolean => {
          if (!parentSet) {
            return false;
          }
          for (let i = 0; i < parentSet.length; i++) {
            const e: any = parentSet[i];
            if (e.key === parentDeptId) {
              if (!e.children) {
                e.children = [];
              }
              /**
               * 一定要有children , 方便用字符串来判定是否有字节点
               * @see getDeptCheck
               * */
              e.children.push({ title: d.name, key: d.deptId, children: [] });
              return true;
            } else {
              if (insertDataToChildren(e.children, parentDeptId, d)) {
                return true;
              }
            }
          }
          return false;
        };

        const d = r.data; //此处可做数据过滤，排除掉没有父部门的部门，不然下面的循环无法结束
        deptSourceData = JSON.parse(JSON.stringify(d)); //d在之下的有操作splice，所以d最终会为[]
        let index = 0;
        while (d.length > 0) {
          //防止不按顺序的部门数据
          const e: any = d[index];
          let insertSuccess = false;
          if (e.parentDeptId == 0) {
            //第一层部门
            result[0].children.push({ title: e.name, key: e.deptId, children: [] });
            insertSuccess = true;
          } else {
            insertSuccess = insertDataToChildren(result[0].children, e.parentDeptId, e);
          }
          if (insertSuccess) {
            d.splice(index, 1);
          } else {
            index++;
          }
          if (index >= d.length) {
            index = 0; //重新扫描
          }
        }
        handleDepmSelect(null); //换选公司时清空之前的部门选择

        handleDepmTree(result);
        handleUserListParams({ companyId: coid });
        // actionRef.current?.reload()
      }
    } catch (e) {
      console.log(e);
    }
  };

  /**
   * 只选中最后一层当入参
   */
  const getDeptCheck = (keys: Key[]): Key[] => {
    const c: Key[] = [];
    if (!keys) {
      return c;
    }
    //直接用字符串来判定比递归来得方便
    const s = JSON.stringify(departmentTree);
    keys.forEach((k: Key) => {
      if (s.indexOf(`"key":"${k}","children":[]`) != -1) {
        c.push(k);
      }
    });
    return c;
  };

  /**
   * 自定义 tree 节点渲染
   * @param node
   * @returns
   */
  const renderTree = (node: DataNode): React.ReactNode => {
    const key = node.key as string;
    if (fp(key)) {
      //是否为最后一个节点
      return (
        <Tooltip placement="right" title={key}>
          {node.title}
        </Tooltip>
      );
    } else {
      return node.title;
    }
  };
  //添加部门
  const handleAddDepm = async (data: any) => {
    //不可转 int，因为 Number 可以准确表达的最大数字是 2^53-1，转了会丢失
    const r: any = await addDepartment({
      ...data,
      companyId: companyId,
      parentDeptId: seleteDepmId,
    });
    if (r.code == 20000) {
      addOperationLogRequest({
        action: 'add',
        content: `新增【${data.name}】企业部门`,
      });
      message.success('添加成功');
      handleDepmNewVisible(false);
      handleGetCoDepm(companyId);
    } else {
      message.error(r.msg);
    }
  };

  //修改部门
  const handleEditDepm = async (data: any) => {
    operationRequest(
      setDepartment({
        ...data,
        deptId: seleteDepmId,
      }),
      () => {
        if (data.name) {
          addOperationLogRequest({
            action: 'edit',
            changeConfig: {
              afterData: {
                name: data.name,
              },
              beforeData: {
                name: seleteDepname,
              },
              list: [
                {
                  title: '部门名称',
                  dataIndex: 'name',
                },
              ],
            },
            content: `编辑【${data.name}】企业部门`,
          });
        } else {
          addOperationLogRequest({
            action: 'del',
            content: `删除【${seleteDepname}】企业部门`,
          });
        }
        handleDepmRenameVisible(false);
        handleGetCoDepm(companyId);
      },
      undefined,
    );
  };

  const handleDisableUser = async (userId: string, status: number, companyId: string) => {
    if (status === 1) {
      //禁用
      const params = {
        appId: APPID,
        coId: companyId,
        // scenicId: scenicId,
        system: 2,
        userId: userId,
      };
      operationRequest(putUserDisable(params), () => {
        actionRef.current?.reload();
      });
    } else {
      //启用
      const params = {
        permissionCode: [
          {
            group: 'e-commerce/user_status',
            code: companyId,
            action: '',
          },
        ],
        users: [userId],
        appId: APPID,
      };
      operationRequest(postPermissionAuthorize(params), () => {
        actionRef.current?.reload();
      });
    }

    // operationRequest(
    //   disableUser({
    //     userId: userId,
    //     //设置的时候是 0 禁用 1 启动，取回的时候是 1 启动 2 禁用，原因：API 未帮转换
    //     userStatusType: activate === 1 ? 0 : 1,
    //   }),
    //   () => {
    //     actionRef.current?.reload();
    //   },
    //   undefined,
    // );
  };

  const handleUserPassworkReset = (username: string) => {
    const newPassword = randomString();

    operationRequest(
      resetUserPasswork({
        code: SHA1(username),
        credential: username,
        newPassword: MD5(newPassword),
        type: 0,
      }),
      () => {
        actionRef.current?.reload();
        showPassword('重置成功', username, newPassword);
      },
      undefined,
    );
  };

  /**
   * 查询其部门的父部门 ID
   * 在原始的部门数据中，通过部门 ID 取前父部门 ID
   *
   * @param id 部门 id
   * @returns
   */
  const findParentId = (id: string | number): string => {
    //找出父部门
    for (const d of deptSourceData) {
      if (d.deptId === id) {
        return d.parentDeptId;
      }
    }
    return '0';
  };

  //过滤权限选择，只保留第三层的最终选择
  const filterPermissions = (permissions: string[]): string[] => {
    return permissions.filter((e: Key) => {
      //例'paas|UserManagement|disable'
      //与角色管理的权限不同，这此没有 ID，固只需要两个 | 号来判定即可
      const title = e as string;
      const symbol = '|';
      let index = title.indexOf(symbol);
      let num = 0;
      while (index !== -1) {
        num++;
        index = title.indexOf(symbol, index + 1);
      }
      return num == 2;
    });
  };

  const [roles, setRoles] = useState([]);

  const logList = [
    {
      title: '所属角色',
      dataIndex: 'roles',
      renderText: (text: any) =>
        text.map((i) => allRolesVE.find((j) => j.value == i)?.label).join('，'),
    },
  ];

  //增加或修改用户
  const handleNewOrEdit = async (value: any) => {
    //权限数据格式化
    const permissionCodeFormat = (data: string[]): object[] => {
      const pc: object[] = [];
      data.forEach((e: string) => {
        const info = e.split('|');
        pc.push({
          group: info[0],
          code: info[1],
          action: info[2],
        });
      });

      return pc;
    };

    //处理部门问题
    //把父级全选上
    const deptIds = new Set();
    departmentSelect?.forEach((s: string) => {
      // let pId = findParentId(s);
      // deptIds.add(s);
      // while (pId !== '0') {
      //   deptIds.add(pId);
      //   pId = findParentId(pId);
      // }
      if (s != '0') {
        deptIds.add(s);
      }
    });

    if (companyId !== '0') {
      value.comDeptList = [{ companyId, deptIds: deptIds.size !== 0 ? Array.from(deptIds) : [] }];
    }

    value.permissionCode = filterPermissions(checkPermissions as string[]); //过滤父级数据
    if (!currentRow) {
      //创建用户
      //处理权限的问题
      value.permissionCode = permissionCodeFormat(value.permissionCode);
      //添加启用权限
      value.permissionCode.push({
        group: 'e-commerce/user_status',
        code: companyId,
        action: '',
      });
      //随机生成密码
      const u = value.username;
      const p = randomString();
      value.password = MD5(p);
      value.companyId = userListParams.companyId;

      //添加
      operationRequest(
        addUser({
          appId: APPID,
          ...value,
        }),
        () => {
          addOperationLogRequest({
            action: 'add',
            content: `新增【${value.username}】企业用户`,
          });
          handleModalVisible(false);
          handleDepmSelect(null); //清除新建时部门的选择
          actionRef.current?.reload();
          showPassword('你的账号和密码是：', u, p);
        },
        undefined,
        '添加成功',
        '添加失败',
      );
    } else {
      //处理权限的问题
      const oldPower = currentRow.power; //之前的数据
      let updatePermission: any = []; //新增的权限
      let deletePermission: any = []; //删除的权限
      value.permissionCode.forEach((e: string) => {
        //新数据在旧数据中找不到，为新增
        if (oldPower.indexOf(e) == -1) {
          updatePermission.push(e);
        }
      });
      oldPower.forEach((e: string) => {
        //旧数据在新数据中找不到，为删除
        if (value.permissionCode.indexOf(e) == -1) {
          deletePermission.push(e);
        }
      });

      updatePermission = permissionCodeFormat(updatePermission);
      deletePermission = permissionCodeFormat(deletePermission);
      if (updatePermission.length != 0) {
        value.updatePermission = updatePermission;
      }
      if (deletePermission.length != 0) {
        value.deletePermission = deletePermission;
      }
      delete value.power;
      delete value.permissionCode;

      value.userId = currentRow.userId;
      delete value.deptNames;
      value.relationId = companyId;

      value.type = '03';
      //编辑
      operationRequest(
        setUser({
          ...value,
          appId: APPID,
        }),
        () => {
          addOperationLogRequest({
            action: 'edit',
            changeConfig: {
              list: logList,
              afterData: {
                roles: value.roleIds,
              },
              beforeData: {
                roles,
              },
            },
            content: `编辑【${value.username}】企业用户`,
          });
          handleModalVisible(false);
          handleDepmSelect(null); //清除修改时部门的选择
          actionRef.current?.reload();
        },
      );
    }
  };
  //选中角色
  const handleRoleSelectChange = async (value: any) => {
    if (value.length == 0) {
      //清空的操作，权限也清可以在这里操作
      return;
    }
    const power = checkPermissionsTemp;
    try {
      await getPermissionList({
        relationId: initialState?.currentCompany?.coId,
        type: '03',
        roleId: value,
      }).then((r) => {
        if (r.code === 20000) {
          r.data.forEach((e: Permission) => {
            const v = e.groupCode + '|' + e.code + '|' + e.actionCode;
            if (power.indexOf(v) == -1) {
              power.push(v);
            }
          });
          setCheckPermissions(_.cloneDeep(power)); //复制一份用于更新
        }
      });
    } catch (e) {
      console.log(e);
    }
  };

  const handleEditUser = async (r: any) => {
    //先刷公司的部门信息
    // await handleGetCoDepm(r.companyId);

    //先把全部部门的子部门数量进行统计
    const deptStatistics = {};

    const { companyId, userId } = r;
    const { data } = await getUserDept({
      companyId,
      userId,
    });
    for (const e of deptSourceData) {
      if (!deptStatistics[e.deptId]) {
        deptStatistics[e.deptId] = 0;
      }
      if (!deptStatistics[e.parentDeptId]) {
        deptStatistics[e.parentDeptId] = 0;
      }
      deptStatistics[e.parentDeptId]++;
    }
    //标记父级已经选上的子部门，当父级子部门全选上时，加入 tree 选择
    for (const e of data) {
      deptStatistics[e.parentDeptId]--;
    }
    const depts: any = [];
    for (const e of data) {
      // if (deptStatistics[e.deptId] == 0) {
      //   depts.push(e.deptId);
      // }
      depts.push(e.deptId);
    }
    handleDepmSelect(depts); //先加正常选的，再把对半的添加上
    setCurrentRow(r);
    handleModalVisible(true);
  };

  const orgOptionDom = (
    <div>
      <Access accessible={access.canOrganizationManage_insertDepartment}>
        <a
          onClick={() => {
            //需用所选部门 id 求出其父级部门 id 用于走原先的增加逻辑
            seleteDepmId = findParentId(seleteDepmId);
            handleDepmNewVisible(true);
          }}
        >
          新增同级部门
        </a>
        <br /> <br />
        <a
          onClick={() => {
            handleDepmNewVisible(true);
          }}
        >
          {departmentTree?.length <= 0 ? '新增部门' : '新增下级部门'}
        </a>{' '}
        <br /> <br />
      </Access>
      <Access accessible={access.canOrganizationManage_edit}>
        <a
          style={{ color: departmentTree?.length <= 0 ? '#ccc' : '' }}
          onClick={() => {
            if (departmentTree?.length > 0) handleDepmRenameVisible(true);
          }}
        >
          重命名
        </a>
      </Access>
      <br />
      <Access accessible={access.canOrganizationManage_deleteDepartment}>
        <br />
        <a
          style={{ color: departmentTree?.length <= 0 ? '#ccc' : '' }}
          onClick={() => {
            if (departmentTree?.length > 0) showDeleteConfirm();
          }}
        >
          删除
        </a>
      </Access>
    </div>
  );

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      valueType: 'textarea',
      hideInForm: true,
      search: false,
      renderText: (_, __, index) => {
        const pageInfo = actionRef.current?.pageInfo;
        if (pageInfo) {
          return index + 1 + (pageInfo.current - 1) * pageInfo.pageSize;
        }
        return '_';
      },
    },
    {
      title: '账号',
      dataIndex: 'username',
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      hideInSearch: true,
      valueType: 'textarea',
    },
    {
      title: '姓名',
      dataIndex: 'applyName',
      hideInSearch: true,
      valueType: 'textarea',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
    },
    {
      title: '企业',
      dataIndex: 'companyId',
      hideInSearch: true,
      render: () => {
        return companyName;
      },
    },
    {
      title: '启用状态',
      dataIndex: 'status',
      search: false,
      render: (dom: any, entity: any) => (
        <Disabled
          access={access.canOrganizationManage_openClose}
          status={dom == 1}
          params={
            dom == 1
              ? {
                  appId: APPID,
                  coId: entity.companyId,
                  system: 2,
                  userId: entity.userId,
                }
              : {
                  permissionCode: [
                    {
                      group: 'e-commerce/user_status',
                      code: entity.companyId,
                      action: '',
                    },
                  ],
                  users: [entity.userId],
                  appId: APPID,
                }
          }
          request={async (params: any) => {
            const data =
              dom === 1 ? await putUserDisable(params) : await postPermissionAuthorize(params);

            addOperationLogRequest({
              action: 'disable',
              content: `${dom === 1 ? '禁用' : '启用'}【${entity.username}】用户`,
            });

            return data;
          }}
          actionRef={actionRef}
        />
      ),
    },
    {
      width: 'auto',
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_, record) => {
        const r = record as any;
        return [
          <Access key={getUniqueId()} accessible={access.canOrganizationManage_edit}>
            <a
              key="edit"
              onClick={() => {
                handleEditUser(r);
                setCurrentRow(record);
              }}
            >
              编辑
            </a>
          </Access>,
          // <Access key={getUniqueId()} accessible={access.canOrganizationManage_openClose}>
          //   <a key="activate" onClick={() => handleDisableUser(r.userId, r.status, r.companyId)}>
          //     {r.status === 1 ? '禁用' : '启用'}
          //   </a>
          // </Access>,
          // <Access key={getUniqueId()} accessible={access.canOrganizationManage_revisePassword}>
          // //2022 年 8 月 24 日去除重置密码的功能
          // <Access key={`reset${r.userId}`} accessible={false}>
          //   <Popconfirm
          //     title="你确定要重置密码吗？"
          //     okText="确定"
          //     key="reset"
          //     cancelText="取消"
          //     onConfirm={() => {
          //       handleUserPassworkReset(r.username);
          //     }}
          //   >
          //     <a>重置密码</a>
          //   </Popconfirm>
          // </Access>,
        ];
      },
    },
  ];

  const showDeleteConfirm = () => {
    Modal.confirm({
      title: '确定删除该部门吗？',
      icon: <ExclamationCircleOutlined />,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        handleEditDepm({ name: '' });
        handleDepmRenameVisible(false);
        handleDepmEditVisible(false);
      },
    });
  };

  const showPassword = (title: string, u: string, p: string) => {
    Modal.success({
      title: title,
      content: (
        <>
          <pre
            style={{
              width: '100%',
              padding: ' 16px',
              overflow: 'auto',
              fontSize: '85%',
              lineHeight: '1.45',
              backgroundColor: '#f6f8fa',
              border: '1px soild #646464',
              borderRadius: '3PX',
            }}
          >
            <code>
              账号:&emsp;{u}
              <br />
              <br />
              密码:&emsp;{p}&emsp;
              <CopyOutlined
                onClick={() => {
                  copy(p); //复制内容到剪切板
                  message.success('已复制');
                }}
              />
            </code>
          </pre>
          <Text type="danger">注意：账号和密码只显示一次</Text>
        </>
      ),
      okText: '确定',
    });
  };

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    if (companyId != '') {
      try {
        //取全部角色的信息，只请求已经启动的
        getRoleDownList({
          relationId: companyId,
          type: '03',
        }).then((r) => {
          const result: any = [];
          r.data.forEach((e: any) => {
            result.push({ label: e.name, value: e.roleId, disabled: e.status === 2 }); //把 roleId 对应成用户列表查询回来的 int roleId
          });
          allRolesVE = result;
        });
        //取全部权限的信息
        getPermissionListNew({
          companyId: companyId,
          // 个人 032 企业 031
          type: currentCompany?.type === 1 ? '031' : '032',
        }).then((r) => {
          if (r.code === 20000) {
            allPermissionsVE = dealPermissionList(r);
          }
        });
      } catch (e) {
        console.log(e);
      }
    }
  }, [companyId]);

  //邀请状态
  // const [modalVisit, setModalVisit] = useState(false);
  //获取当前企业
  const [company, setCompany] = useState('');
  //用户 id
  // const [userId,setUserId] =useState('')
  const { userId, phone, nickname, username } = initialState.userInfo;
  console.log('ggggggggggg', initialState.currentCompany);
  //用户手机号码
  //邀请状态
  const [modalVisit, setModalVisit] = useState(false);
  //重置状态
  // const [restStatus, setRestStatus] = useState(0);
  //用户 id
  // const [userId2, setUserId] = useState('');
  //当前用户信息
  // const [userMessage, setUserMessage] = useState({});
  //链接地址
  const [urlInvite, setUrlInvite] = useState(undefined);
  //邀请人用户名
  const userName2 = encodeURIComponent(initialState.userInfo.nickname);

  //企业 id 和凭证
  const [ents, setEnt] = useState('');
  const [cer, setCer] = useState('');
  // const [useName,setUseName] = useState('')

  //获取链接地址
  const getCode = async (val: any, status: any) => {
    const pars = {
      userId: val,
      inviteStatus: status,
      companyId: coId,
      // url:CAS_HOST
    };
    try {
      const result = await getOrgStructure(pars);

      addOperationLogRequest({
        action: 'invite',
        content: `生成【${coName}】邀请链接`,
      });
      // const url = `${window.location.origin}${window.location.pathname}#/UserApplies?cert=${result.data.cert}&companyId=${coId}&coName=${coName}&userName=${userName2}&userId=${userId}&route=${encodeURIComponent('/UserApplies')}`;
      const url = `${LOGIN_HOST}/#/login?appId=${APPID}&cert=${
        result.data.cert
      }&companyId=${coId}&coName=${coName}&userName=${userName2}&userId=${userId}&route=${encodeURIComponent(
        '/UserApplies',
      )}`;
      console.log(url);
      // setEntsAndCer({cert:result.data.cert,companyId:result.data.companyId})
      // localStorage.setItem('url', url);
      console.log('uuuuuuuuuuuuuuuuuu', url);
      setEnt(result.data.companyId);
      setCer(result.data.cert);
      // console.log(val)
      // setUserId(val);
      setUrlInvite(url);
    } catch (e) {
      console.error(e);
    }
  };
  //获取用户 id
  const getUserId = async (status: any) => {
    try {
      const result = await getUserSocial();
      const { userId } = result.data;
      console.log('ppppppppp', result.data, status);
      getCode(userId, status);
      // setUserMessage(result.data);
      setModalVisit(true);
    } catch (e) {
      console.error(e);
    }
    // setUserId(userId)
    // localStorage.setItem('userId',userId)
  };

  //重置
  const getRest = () => {
    message.success('重置成功');
    // setRestStatus(1)
    getUserId(1);

    // setModalVisit(false);
  };

  // 日期
  const date1 = new Date();
  const date2 = new Date(date1);
  date2.setDate(date1.getDate() + 30);
  const time = date2.getFullYear() + '-' + (date2.getMonth() + 1) + '-' + date2.getDate();
  console.log('三十天后的日期', time);

  //复制链接
  const copyText = () => {
    message.success('已复制到剪切板');
    copy(`${urlInvite}`);
  };

  /**
   * 点击数据迁移之前的处理
   */
  const beforeMigrateVisible = () => {
    allUserSourceData = new Map(); //清空之前保存的某公司全用户的数据
    //取 1000 条，当做是取全部用户
    getCoAllUsers({ companyId })
      .then((r) => {
        (r.data ?? []).forEach((e: any) => {
          allUserSourceData.set(e.userId, e); //先保存一份原始数据用于后续的数据处理
        });
        getMigrateUserVM();
      })
      .catch((e) => console.log(e));
    setMigrateVisible(true);
  };

  /**
   * 获取数据迁移的用户数据枚举，并设置 useState，用于 ProFormSelect
   */
  const getMigrateUserVM = () => {
    const result: any[] = [];
    for (const id of allUserSourceData.keys()) {
      result.push({ label: allUserSourceData.get(id).nickname, value: id });
    }
    setMigrateUserVM(result);
  };

  /**
   * 处理数据迁移
   * @param value form 的传值
   */
  const processMirage = (value: any) => {
    const sourceUser = allUserSourceData.get(value.source);
    const targetUser = allUserSourceData.get(value.target);
    const relationId = initialState?.currentCompany?.coId;
    const groups = `e-commerce/${relationId}`;

    operationRequest(
      userDataMigrate({
        companyId: companyId,
        fromId: sourceUser.userId,
        group: groups,
        relationId: relationId,
        toId: targetUser.userId,
        isSale: true, //是否电商
        appId: APPID,
      }),
      () => {
        addOperationLogRequest({
          action: 'migrate',
          changeConfig: {
            list: [
              {
                title: '账号名称',
                dataIndex: 'nickname',
              },
            ],
            afterData: targetUser,
            beforeData: sourceUser,
          },
          content: `迁移【${sourceUser.nickname}】账号权限`,
        });

        setMigrateVisible(false);
        actionRef.current?.reload();
      },
    );
  };

  return (
    <>
      <ProCard split="vertical" style={{ overflow: 'auto' }}>
        <ProCard colSpan="260px" title="组织架构" headerBordered>
          <h3>{companyName}</h3>
          {/* <Form.Item label="请选择企业">
            {companyId ? (
              <Select defaultValue={companyId} disabled onChange={handleGetCoDepm}>
                <Select.Option value={companyId}>{companyName}</Select.Option>;
              </Select>
            ) : (
              ''
            )}
          </Form.Item> */}
          {departmentTree && departmentTree[0]?.children.length <= 0 ? (
            <Button type="primary" onClick={() => handleDepmNewVisible(true)}>
              新建部门
            </Button>
          ) : (
            ''
          )}
          <Tree
            treeData={departmentTree && departmentTree[0].children}
            //自定义渲染
            titleRender={(d: any) => {
              return (
                <div>
                  <span>{d.title}&nbsp;&nbsp;&nbsp;</span>
                  <Popover placement="rightTop" title="操作" content={orgOptionDom} trigger="click">
                    <EllipsisOutlined
                      onClick={() => {
                        seleteDepname = d.title;
                        seleteDepmId = d.key; //记录点击部门操作时的部门
                        // handleDepmEditVisible(true);
                      }}
                    />
                  </Popover>
                </div>
              );
            }}
            onSelect={(selectedKeys) => {
              // 选中/取消选中
              const key = selectedKeys?.[0] || '';
              userListParams.deptId = key;
              handleDepmSelect([key]); //点击部门时为新建时的部门高亮
              const t = JSON.parse(JSON.stringify(userListParams)); //复制一份，因为直接转原值不会刷新
              handleUserListParams(t);
              // actionRef.current?.reload()
            }}
          />
        </ProCard>
        <ProCard ghost>
          {userListParams.companyId !== 0 && (
            <ProTable<API.RuleListItem, API.PageParams>
              {...tableConfig}
              headerTitle="组织用户"
              actionRef={actionRef}
              toolBarRender={() => (
                <Space>
                  <Access
                    key={getUniqueId()}
                    accessible={access.canOrganizationManage_authorityCopy}
                  >
                    <Button
                      // disabled={companyId === '1'}
                      type="primary"
                      key="primary"
                      onClick={beforeMigrateVisible}
                    >
                      复制权限
                    </Button>
                  </Access>
                  <Access key={getUniqueId()} accessible={access.canOrganizationManage_invite}>
                    <Button
                      // disabled={companyId === '1'}
                      type="primary"
                      key="primary"
                      onClick={() => {
                        getUserId(0);
                      }}
                    >
                      邀请
                    </Button>
                  </Access>
                  <Access
                    key={getUniqueId()}
                    accessible={access.canOrganizationManage_insertDepartment}
                  >
                    <Button
                      type="primary"
                      key="primary"
                      onClick={() => {
                        setCurrentRow(null);
                        handleModalVisible(true);
                      }}
                    >
                      <PlusOutlined />
                      新建
                    </Button>
                  </Access>
                </Space>
              )}
              params={userListParams} //叠加 columns 的参数
              request={handUserList}
              columns={columns}
            />
          )}

          {modalVisit ? (
            <>
              <ModalForm
                title={`${username !== '' ? nickname : phone}邀请你加入企业`}
                visible={modalVisit}
                width={400}
                submitter={{
                  render: (props, doms) => {
                    return [
                      <Button type="primary" key="submit" onClick={copyText}>
                        复制
                      </Button>,
                    ];
                  },
                }}
                // onFinish={async () => {
                //   message.success('提交成功');
                //   return true;
                // }}
                onVisibleChange={setModalVisit}
              >
                <Row gutter={[16, 16]} justify="center" align="top">
                  <Col>
                    <h2 style={{ fontSize: '18px', fontWeight: 700 }}>
                      {company !== undefined ? company : localStorage.getItem('coName')}
                    </h2>
                  </Col>
                  <Col>
                    <div style={{ textAlign: 'center', padding: '0 20px' }}>
                      <a>
                        {urlInvite}
                        {/* {'https://test.shukeyun.com/scenic/backend-v2/#/ol/UserApplies?cert=1653270782221257910&companyId=1527225263761068034&companyName=小猪熊的公司&userName=%E5%B0%8F%E7%8C%AA%E7%86%8A%EF%BC%88%E5%A4%B4%E6%89%93%E6%AD%AA%E7%89%88%EF%BC%89&userId=200827690859761664'} */}
                        {/* {localStorage.getItem('url')} */}
                      </a>
                    </div>
                  </Col>
                  <Col>
                    链接有效时间：{time} <a onClick={getRest}>重置</a>
                  </Col>
                  <Col>
                    <h3>成员可以通过打开链接加入企业</h3>
                  </Col>
                </Row>
              </ModalForm>
            </>
          ) : (
            ''
          )}

          {/*用户新增/编辑 */}
          <ModalForm
            title={`${currentRow ? '编辑' : '新增'}用户`}
            layout="horizontal"
            labelCol={{ style: { width: 70 } }}
            formRef={formRef}
            width={modelWidth.md}
            visible={createModalVisible}
            onVisibleChange={(v) => {
              if (v) {
                //主动阻塞，等待请求完成
                new Promise(async function (resolve, reject) {
                  if (!currentRow) return;
                  const r = await tryGetPermissionListByUserId();
                  currentRow.power = _.cloneDeep(r); //保一份原先的数据用于后期对比修改用
                  //把了保持老旧数据一致，解决分身带来的数据不同步
                  checkPermissionsTemp = [...r];
                  setCheckPermissions(checkPermissionsTemp); //这里下来的数据本来就是第三层的
                  const { userId = '', companyId } = currentRow;
                  const { data } = await getRoleInfoList({
                    type: '03',
                    userId,
                    relationId: companyId,
                  });
                  const arr = data.map((i) => i.roleId);
                  setRoles(arr);
                  formRef.current?.setFieldsValue({ ...currentRow, roleIds: arr });
                }).then();
              } else {
                setCheckPermissions([]);
                checkPermissionsTemp = [];
                checkCompanyDeptTemp = [];
              }
              handleModalVisible(v);
            }}
            modalProps={{
              maskClosable: false, //点击蒙层不可关闭
              destroyOnClose: true, //关闭弹窗销毁子元素
            }}
            onFinish={handleNewOrEdit}
          >
            <ProCard split={true ? 'horizontal' : 'vertical'} headerBordered>
              <ProFormText
                name="nickname"
                label="昵称："
                disabled={currentRow}
                rules={[{ required: !currentRow, message: '请输入昵称' }]}
              />
              <ProFormText
                name="username"
                label="账号："
                disabled={currentRow}
                rules={[
                  { required: !currentRow, message: '请输入账号' },
                  {
                    pattern: /^[a-zA-Z][a-zA-Z0-9_]{2,29}$/,
                    message: '账号字母开头，长度3-30个字符（字母数字下划线）',
                  },
                ]}
              />

              <ProFormSelect
                label="所属角色："
                name="roleIds"
                placeholder="请选择角色"
                options={allRolesVE}
                fieldProps={{ mode: 'multiple', onChange: handleRoleSelectChange }}
              />

              <ProForm.Item
                label={
                  <a
                    onClick={() => {
                      checkCompanyDeptTemp = departmentSelect;
                      setCompanyDeptMV(true);
                    }}
                  >
                    所属企业：
                  </a>
                }
              >
                <div
                  style={{
                    display: `${
                      dealCheck(departmentSelect, departmentTree).length != 0 ? 'none' : 'block'
                    }`,
                  }}
                >
                  {companyName}
                </div>
                <DirectoryTree
                  style={{
                    display: `${
                      dealCheck(departmentSelect, departmentTree).length == 0 ? 'none' : 'block'
                    }`,
                  }}
                  height={modelWidth.sm / 2}
                  selectable={false}
                  checkable={false}
                  showLine={false}
                  showIcon={false}
                  treeData={dealCheck(departmentSelect, departmentTree)}
                />
              </ProForm.Item>

              <ProForm.Item
                label={
                  <a
                    onClick={() => {
                      checkPermissionsTemp = checkPermissions;
                      setPermissionsMV(true);
                    }}
                  >
                    所属权限
                  </a>
                }
              >
                <DirectoryTree
                  height={modelWidth.sm / 2}
                  selectable={false}
                  checkable={false}
                  showLine={false}
                  showIcon={false}
                  treeData={dealCheck(checkPermissions, permissionTree)}
                  titleRender={renderTree}
                />
              </ProForm.Item>
            </ProCard>
          </ModalForm>

          {/* 添加/修改权限窗口 */}
          <ModalForm
            title={(currentRow ? '编辑' : '新增') + '权限'}
            width={modelWidth.md}
            labelCol={{ style: { width: 70 } }}
            layout="horizontal"
            visible={permissionsMV}
            modalProps={{
              maskClosable: false, //点击蒙层不可关闭
              destroyOnClose: true, //关闭弹窗销毁子元素
            }}
            onVisibleChange={setPermissionsMV}
            onFinish={async (__: any) => {
              setCheckPermissions([...checkPermissionsTemp]);
              setPermissionsMV(false);
            }}
          >
            <SearchTree
              searchPlaceholder="搜索"
              treeData={permissionTree}
              defaultCheckedKeys={checkPermissions}
              onCheck={(k, e) => {
                checkPermissionsTemp = k as Key[];
              }}
            />
          </ModalForm>

          <ModalForm
            title={'所属企业'}
            width={modelWidth.md}
            labelCol={{ style: { width: 70 } }}
            layout="horizontal"
            visible={companyDeptMV}
            modalProps={{
              maskClosable: false, //点击蒙层不可关闭
              destroyOnClose: true, //关闭弹窗销毁子元素
            }}
            onVisibleChange={setCompanyDeptMV}
            onFinish={async (__: any) => {
              setCompanyDeptMV(false);
              handleDepmSelect(_.cloneDeep(checkCompanyDeptTemp));
            }}
          >
            <SearchTree
              searchPlaceholder="搜索"
              treeData={departmentTree}
              defaultCheckedKeys={getDeptCheck(departmentSelect)}
              onCheck={(checked, info) => {
                const need = (checked as Key[]).concat(info.halfCheckedKeys!);
                console.log(need);
                checkCompanyDeptTemp = need as Key[];
              }}
            />
          </ModalForm>

          {/* 组织框架选择 */}
          <ModalForm
            title={'更多选择'}
            visible={departmentEditVisible}
            width={modelWidth.sm}
            onVisibleChange={handleDepmEditVisible}
            onFinish={async (value: any) => {
              handleDepmEditVisible(false);
            }}
          >
            <a
              onClick={() => {
                handleDepmNewVisible(true);
              }}
            >
              新增下级部门
            </a>
            <br />
            <br />
            <a
              onClick={() => {
                handleDepmRenameVisible(true);
              }}
            >
              重命名
            </a>
          </ModalForm>

          {/* 新增下级同级部门 */}
          <ModalForm
            title="新增部门"
            width={modelWidth.sm}
            layout="horizontal"
            visible={departmentNewVisible}
            onVisibleChange={handleDepmNewVisible}
            modalProps={{
              zIndex: 2000,
              maskClosable: false, //点击蒙层不可关闭
              destroyOnClose: true, //关闭弹窗销毁子元素
            }}
            onFinish={handleAddDepm}
          >
            <ProFormText width="md" name="name" label="部门名称：" />
          </ModalForm>

          {/* 重命名部门 */}
          <ModalForm
            title={'重命名部门'}
            width={modelWidth.sm}
            layout="horizontal"
            visible={departmentRenameVisible}
            onVisibleChange={(v: boolean) => {
              handleDepmRenameVisible(v);
            }}
            modalProps={{
              zIndex: 2000,
              maskClosable: false, //点击蒙层不可关闭
              destroyOnClose: true, //关闭弹窗销毁子元素
            }}
            onFinish={async (value: any) => {
              if (!value.name) {
                //没有长度认为要删除
                showDeleteConfirm();
              } else {
                handleEditDepm(value);
              }
            }}
          >
            <ProFormText initialValue={seleteDepname} width="md" name="name" label="部门名称：" />
          </ModalForm>

          {/**
           * 复制权限
           * **/}
          <ModalForm
            title={'复制权限'}
            width={modelWidth.md}
            layout="horizontal"
            visible={migrateVisible}
            onVisibleChange={(v: boolean) => {
              setMigrateVisible(v);
            }}
            modalProps={{
              maskClosable: false, //点击蒙层不可关闭
              destroyOnClose: true, //关闭弹窗销毁子元素
            }}
            onFinish={async (value: any) => {
              processMirage(value);
            }}
          >
            <Alert
              message="本功能用于员工离职或调岗时的数据转交工作，迁移账户的员工销售权限、角色、权限这三类数据都会转移到目标账户下；请认真确认后进行提交。"
              type="error"
            />
            <br />
            <ProFormSelect
              name="source"
              label="迁移账号："
              placeholder="请选择账号"
              showSearch
              options={migrateUserVM}
              fieldProps={{
                optionFilterProp: 'children',
                filterOption: (input, option) => {
                  //@see https://ant.design/components/select-cn/
                  return (option!.label as unknown as string)
                    .toLowerCase()
                    .includes(input.toLowerCase());
                },
              }}
            />
            <ProFormSelect
              name="target"
              label="目标账号："
              placeholder="请选择账号"
              showSearch
              options={migrateUserVM}
              fieldProps={{
                optionFilterProp: 'children',
                filterOption: (input, option) => {
                  return (option!.label as unknown as string)
                    .toLowerCase()
                    .includes(input.toLowerCase());
                },
              }}
            />
          </ModalForm>
        </ProCard>
      </ProCard>
    </>
  );
};

export default TableList;
