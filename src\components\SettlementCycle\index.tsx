/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-25 14:52:29
 * @LastEditTime: 2023-09-15 15:17:14
 * @LastEditors: zhangfengfei
 */
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormDependency, ProFormRadio } from '@ant-design/pro-form';
import { Form } from 'antd';
import type { ColProps } from 'antd/lib/col';
import type { FormLabelAlign } from 'antd/lib/form/interface';
import { omit } from 'lodash';
import type { FC } from 'react';
import { BirthdayPicker } from './BirthdayPicker';

interface SettlementCycleProps {
  radioName: string;
  readonly?: boolean;
  dateName: string;
  radioLabel: string;
  labelAlign?: FormLabelAlign;
  labelCol?: ColProps;
  wrapperCol?: ColProps;
  formRef: React.MutableRefObject<ProFormInstance<any> | undefined>;
}

/**
 * @description 结算周期FormItem组件
 */
const SettlementCycle: FC<SettlementCycleProps> = ({
  radioName,
  dateName,
  radioLabel,
  formRef,
  ...restProps
}) => {
  console.log(formRef.current?.getFieldsValue());
  const radioOptions = [
    {
      label: '每周结算',
      value: 'WEEK',
    },
    {
      label: '每月结算',
      value: 'MONTH',
    },
    {
      label: '每年结算',
      value: 'YEAR',
    },
  ];

  const arrWeek = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  const weekOptions = arrWeek.map((item, index) => ({
    label: item,
    value: `${index + 1}`,
  }));

  const arrMonth = new Array(30).fill(0).map((_, index) => `${index + 1}`);
  const monthOptions = [
    ...arrMonth,
    {
      label: '月末最后一天',
      value: '31',
    },
  ];

  return (
    <>
      <ProFormRadio.Group
        name={radioName}
        label={radioLabel}
        options={radioOptions}
        rules={[{ required: true }]}
        formItemProps={{
          style: {
            marginBottom: 8,
          },
        }}
        {...restProps}
      />
      <ProFormDependency name={[radioName]}>
        {({ [radioName]: radioValue }) => {
          const value = radioValue || 'WEEK';
          switch (value) {
            case 'WEEK':
              return (
                <ProFormCheckbox.Group
                  label=" "
                  colon={false}
                  name={dateName}
                  options={weekOptions}
                  {...restProps}
                />
              );
            case 'MONTH':
              return (
                <ProFormCheckbox.Group
                  label=" "
                  colon={false}
                  name={dateName}
                  options={monthOptions}
                  {...restProps}
                />
              );
            case 'YEAR':
              return (
                <Form.Item label=" " colon={false} {...omit(restProps, 'readonly')}>
                  <Form.List name={dateName}>
                    {(fields, action) => {
                      if (fields.length === 0) action.add();
                      return fields.map((field) => (
                        <BirthdayPicker field={field} key={field.key} action={action} />
                      ));
                    }}
                  </Form.List>
                </Form.Item>
              );

            default:
              return null;
          }
        }}
      </ProFormDependency>
    </>
  );
};

export default SettlementCycle;
