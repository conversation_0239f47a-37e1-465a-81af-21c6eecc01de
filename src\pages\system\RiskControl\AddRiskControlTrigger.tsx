/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-01-09 10:22:07
 * @LastEditTime: 2023-01-09 17:27:00
 * @LastEditors: zhangfengfei
 */

import { RiskMenuMap, RiskTypeMap } from '@/common/utils/enum';
import type { ConditionListItem, RiskTargetType } from '@/services/api/erp';
import { setRiskInfo } from '@/services/api/erp';
import { MinusCircleOutlined, PlusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormDependency,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
} from '@ant-design/pro-form';
import type { ActionType } from '@ant-design/pro-table';
import { Button, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import type { FormListOperation } from 'antd/lib/form/FormList';
import { omit } from 'lodash';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { useModel } from '@umijs/max';

const riskNameOptions = Object.entries(omit(RiskTypeMap, 'order')).map((item) => ({
  label: item[1],
  value: item[0],
}));

const riskMenuOptions = Object.entries(omit(RiskMenuMap, 'order')).map((item) => ({
  label: item[1],
  value: item[0],
}));

interface FormValues {
  conditionList?: ConditionListItem[];
  type: RiskTargetType;
}

const initialValues: FormValues = {
  conditionList: [{ attribute: undefined, val: undefined }] as unknown as ConditionListItem[],
};

interface AddRiskControlTriggerProps {
  tableRef: React.MutableRefObject<ActionType | undefined>;
}

/**
 * @description: 新增风控
 */
const AddRiskControlTrigger: FC<AddRiskControlTriggerProps> = ({ tableRef }) => {
  const { initialState } = useModel('@@initialState') || {};
  const { coId = '' } = initialState?.currentCompany || {};
  const [visible, setVisible] = useState(false);

  // formList 操作 ref
  const formListRef = useRef<FormListOperation>();
  const [form] = useForm<FormValues>();

  const onFinish = async (values: FormValues) => {
    const { conditionList, type } = values;

    // 风控条件重复验证
    const riskTypeArr = (values.conditionList ?? []).map((item) => item.attribute);

    if (type !== 'bond' && riskTypeArr.length !== new Set(riskTypeArr).size) {
      message.warning('风控条件重复');
      return false;
    }
    const addList = (conditionList ?? []).map((item) => {
      const { attribute, val } = item;
      return {
        attribute,
        val,
        condition: 'gt',
      };
    });
    await setRiskInfo({
      conditionSetList: {
        add: addList,
      },
      groupCode: 'e-commerce',
      relationId: coId,
      riskTarget: type,
    });
    message.success('添加成功');
    tableRef.current?.reload();
    return true;
  };
  useEffect(() => {
    if (visible) {
    }
  }, [visible]);

  return (
    <ModalForm<FormValues>
      form={form}
      width={568}
      title="新增风控"
      visible={visible}
      layout="horizontal"
      trigger={
        <Button type="primary" icon={<PlusOutlined />}>
          新增
        </Button>
      }
      initialValues={initialValues}
      preserve={false}
      onFinish={onFinish}
      onVisibleChange={setVisible}
      labelAlign="right"
      labelCol={{
        span: 5,
      }}
      onValuesChange={({ type }) => {
        if (type) {
          form.setFieldsValue({ conditionList: [{}] });
        }
      }}
    >
      <ProFormSelect
        name="type"
        width={350}
        label="风控名称"
        options={riskNameOptions}
        rules={[
          {
            required: true,
          },
        ]}
      />
      <ProFormDependency name={['type']}>
        {({ type }) => {
          return (
            <ProFormSelect
              name="menu"
              width={350}
              disabled
              label="操作菜单"
              fieldProps={{
                value: RiskMenuMap[type],
              }}
              options={riskMenuOptions}
            />
          );
        }}
      </ProFormDependency>

      <ProFormDependency name={['type']}>
        {({ type }) => {
          if (type === 'bond') {
            return null;
          }
          return (
            <ProFormList
              name="conditionList"
              actionRef={formListRef}
              label={<span className="required">风控条件</span>}
              creatorButtonProps={false}
              copyIconProps={false}
              deleteIconProps={false}
              style={{ marginBottom: 0 }}
              itemContainerRender={(dom, { field, fields, index }) => {
                const options =
                  type === 'ticketing'
                    ? [
                        {
                          label: '单次最大出票数量',
                          value: 'quantityTicketOnce',
                        },
                        {
                          label: '单次最大出票金额',
                          value: 'amountTicketOnce',
                        },
                      ]
                    : [
                        {
                          label: '单次进货数量',
                          value: 'quantityPurchaseOnce',
                        },
                        {
                          label: '单次进货金额',
                          value: 'amountPurchaseOnce',
                        },
                      ];
                return (
                  <ProFormGroup size="small">
                    <ProFormSelect
                      addonBefore="当"
                      width={180}
                      name="attribute"
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                      options={options}
                    />
                    <ProFormDigit
                      addonBefore="大于"
                      width="xs"
                      name="val"
                      min={0}
                      max={1_000_000}
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                    />
                    {fields.length < options.length && (
                      <PlusCircleOutlined
                        style={{ fontSize: 18, marginTop: 5 }}
                        onClick={() => {
                          formListRef.current?.add();
                        }}
                      />
                    )}

                    {fields.length > 1 && (
                      <MinusCircleOutlined
                        style={{ fontSize: 18, marginTop: 5 }}
                        onClick={() => {
                          formListRef.current?.remove(field.name);
                        }}
                      />
                    )}
                  </ProFormGroup>
                );
              }}
            >
              {null}
            </ProFormList>
          );
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default AddRiskControlTrigger;
