/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-13 16:05:31
 * @LastEditTime: 2023-10-13 16:05:41
 * @LastEditors: zhangfengfei
 */
import { apiExportHeaderSet } from '@/services/api/config';
import type { ProTableProps } from '@ant-design/pro-components';
import { getUniqueId } from './tool';

export const tableConfig: ProTableProps<any, any> & ProComponentTableProps<any, any> = {
  tableLayout: 'auto',
  search: { labelWidth: 'auto' },
  scroll: { x: 'auto' },
  rowKey: () => getUniqueId(),
};

export function columnsState(exportState: any) {
  return {
    value: exportState.columnsValue,
    onChange: (v: any) => {
      let sort = 0;
      const headerInfos: any = [];
      for (const value of exportState.columns) {
        if (!value.hideInTable) {
          const obj = {
            ...{
              show: true,
              order: sort++,
              fixed: value.fixed,
            },
            ...v[value.dataIndex],
          };
          headerInfos.push({
            fieldDescribe: value.title,
            headerField: value.dataIndex,
            sort: obj.order,
            state: obj.show ? 1 : 0,
            fixed: obj.fixed,
          });
        }
      }
      apiExportHeaderSet({
        headerInfos,
        modulePath:
          exportState.uri +
          (exportState.params.moduleFlag ? `/export_${exportState.params.moduleFlag}` : '/export'),
      });
      exportState.setColumnsValue(v);
    },
  };
}
