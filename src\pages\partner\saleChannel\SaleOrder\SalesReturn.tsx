import { tableConfig } from '@/common/utils/config';
import { productTypeEnum } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import Blockchain from '@/components/Blockchain';
import { getMultipleRefundOrderInfo } from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Card, List, Modal } from 'antd';
import { uniqueId } from 'lodash';
import React, { useRef, useState } from 'react';

interface SaleReturnProps {
  refundIds: string[];
}

const TableList: React.FC<SaleReturnProps> = ({ dataItem, refundIds }) => {
  const actionRef = useRef<ActionType>();

  //查看区块链交易数据
  const [txIds, setTxIds] = useState<any>([]);
  const [tokenIds, setTokenIds] = useState<any>([]);
  const [cochainVisible, setCochainVisible] = useState(false);
  const [cochainDataDataSource, setCochainDataDataSource] = useState<any>({});
  const cochainColumns = [
    {
      title: '区块链账号',
      columns: [
        {
          title: '发行方',
          dataIndex: 'fromAccount',
          span: 5,
          render: (dom: any) => dom,
        },
        {
          title: '接收方',
          dataIndex: 'toAccount',
          span: 5,
          render: (dom: any) => dom,
        },
      ],
    },
    {
      title: '存证信息',
      columns: [
        {
          title: '存证时间',
          dataIndex: 'transactionAt',
          span: 5,
        },
        {
          title: 'TokenID',
          dataIndex: 'tokenIds',
          span: 5,
          render: (dom: any) => {
            return dom && dom.data.length > 0
              ? dom?.data.map((item: any, index: number) => (
                  <>
                    <span>{item}</span>
                    <br />
                    {index + 1 == dom.data.length && dom.data.length > 1 ? (
                      <span
                        style={{ color: '#1890ff', cursor: 'pointer' }}
                        onClick={() => {
                          const obj = { ...cochainDataDataSource };
                          if (dom.unfold) {
                            obj.tokenIds = {
                              unfold: false,
                              data: cochainDataDataSource.tokenIds.data.slice(0, 5),
                            };
                            setCochainDataDataSource(obj);
                          } else {
                            obj.tokenIds = { unfold: true, data: tokenIds };
                            setCochainDataDataSource(obj);
                          }
                        }}
                      >
                        {dom.unfold ? '收起 ' : '展开 '}
                        {dom.unfold ? <UpOutlined /> : <DownOutlined />}
                      </span>
                    ) : (
                      ''
                    )}
                  </>
                ))
              : '-';
          },
        },
        {
          title: '交易哈希',
          dataIndex: 'txId',
          span: 5,
          render: (dom: any) => {
            return dom && dom.data.length > 0
              ? dom?.data.map((item: any, index: number) => (
                  <>
                    <span>{item}</span>
                    <br />
                    {index + 1 == dom.data.length && dom.data.length > 1 ? (
                      <span
                        style={{ color: '#1890ff', cursor: 'pointer' }}
                        onClick={() => {
                          const obj = { ...cochainDataDataSource };
                          if (dom.unfold) {
                            obj.txId = {
                              unfold: false,
                              data: cochainDataDataSource.txId.data.slice(0, 5),
                            };
                            setCochainDataDataSource(obj);
                          } else {
                            obj.txId = { unfold: true, data: txIds };
                            setCochainDataDataSource(obj);
                          }
                        }}
                      >
                        {dom.unfold ? '收起 ' : '展开 '}
                        {dom.unfold ? <UpOutlined /> : <DownOutlined />}
                      </span>
                    ) : (
                      ''
                    )}
                  </>
                ))
              : '-';
          },
        },
      ],
    },
  ];

  // 【列表】数据绑定
  const columns: ProColumns<API.ProductListItem>[] = [
    {
      title: '销售退单号',
      dataIndex: 'refundId',
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      hideInSearch: true,
      valueEnum: productTypeEnum,
    },
    {
      title: '入园日期',
      dataIndex: 'dayBegin',
      hideInSearch: true,
      render: (dom: any, entity: any) =>
        entity.noDateList ? (
          <a
            onClick={() => {
              setUnTimeDataSource(entity.noDateList);
              setUnTimeVisible(true);
            }}
          >
            详情
          </a>
        ) : (
          '-'
        ),
    },
    {
      title: '退货总量',
      dataIndex: 'totalNum',
      hideInSearch: true,
    },
    {
      key: 'blockChain',
      render: (_, { txId }) => {
        return null;
      },
    },
    {
      title: () => {
        return '退款金额（元）';
      },
      dataIndex: 'totalAmount',
      hideInSearch: true,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  // 分时数据绑定
  const [timeVisible, setTimeVisible] = useState<any>();
  // 分时序列 [表格序，列表序]
  const [timeTitle, setTimeTitle] = useState<any>();
  const [timeDataSource, setTimeDataSource] = useState<any>();
  // 分时表格配置
  const timeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '分时预约',
      dataIndex: 'timeShare',
      // render: (_, entity) =>
      //   entity.timeBeginTime && entity.timeEndTime ? (
      //     <span>{entity.timeBeginTime + ' 至 ' + entity.timeEndTime}</span>
      //   ) : (
      //     '-'
      //   ),
    },
    // {
    //   title: '当前库存',
    //   dataIndex: 'number',
    // },
    {
      title: '退货数量',
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  // 非分时数据绑定
  const [unTimeVisible, setUnTimeVisible] = useState<boolean>(false);
  const [unTimeDataSource, setUnTimeDataSource] = useState<any>();
  const unTimeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '入园日期',
      dataIndex: 'dayBegin',
      hideInSearch: true,
      render: (dom: any, entity: any) => entity.dayBegin + ' 至 ' + entity.dayEnd,
    },
    {
      title: '退货数量',
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      dataIndex: 'productPrice',
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  const tableListRequest = async (params: any) => {
    const { data } = await getMultipleRefundOrderInfo({ refundIds });
    let arr: any[] = [];
    for (const { txId, productList, refundId } of data) {
      // 是否区块链的标志 退单 id
      const newList = productList.map((i) => ({ ...i, txId, refundId, id: uniqueId() }));
      arr = arr.concat(newList);
    }
    console.log(arr);

    return {
      data: arr,
      total: arr.length,
    };
  };
  return (
    <>
      {/* 商品表格 */}
      <ProTable
        {...tableConfig}
        actionRef={actionRef}
        headerTitle={'退款记录'}
        search={false}
        request={tableListRequest}
        columns={columns}
        expandable={{
          expandedRowRender: (record: any, indexTable: number) => (
            <>
              <span style={{ padding: '8px', display: 'block' }}>入园日期及分时预约信息：</span>
              <List
                style={{
                  padding: '4px 8px',
                  maxHeight: '300px',
                  overflowY: 'auto',
                  overflowX: 'hidden',
                }}
                grid={{
                  gutter: 16,
                  xs: 1,
                  sm: 2,
                  md: 4,
                  lg: 4,
                  xl: 5,
                  xxl: 5,
                }}
                dataSource={record.dateList}
                renderItem={(item: any, indexList: number) => (
                  <List.Item className="cardBox">
                    <Card
                      title={item.dayBegin}
                      hoverable
                      size="small"
                      extra={
                        <a
                          onClick={() => {
                            // 修改分时信息
                            setTimeDataSource(item.timeShareDetail);
                            // setTimeTitle(item.dayBegin)
                            setTimeVisible(true);
                          }}
                        >
                          详情
                        </a>
                      }
                    >
                      <p>数量：{item.totalNum}</p>
                      <p>金额：￥ {item.totalAmount}</p>
                    </Card>
                  </List.Item>
                )}
              />
            </>
          ),
          rowExpandable: (record: any) => record.dateList,
        }}
      />
      {/* 查看区块链交易记录 */}
      <Blockchain
        cochainVisible={cochainVisible}
        setCochainVisible={setCochainVisible}
        cochainColumns={cochainColumns}
        cochainDataDataSource={cochainDataDataSource}
      />
      {/* 分时预约详情弹窗 */}
      <Modal
        width={modelWidth.lg}
        title={'分时预约信息'}
        visible={timeVisible}
        destroyOnClose
        footer={null}
        onCancel={() => {
          setTimeVisible(false);
        }}
      >
        <ProTable
          rowKey="distributorTicketStockId"
          headerTitle={timeTitle}
          columns={timeTableColumns}
          dataSource={timeDataSource}
          search={false}
          options={false}
        />
      </Modal>

      {/* 非分时预约详情弹窗 */}
      <Modal
        width={modelWidth.lg}
        title={'入园日期信息'}
        visible={unTimeVisible}
        destroyOnClose
        footer={null}
        onCancel={() => {
          setUnTimeVisible(false);
        }}
      >
        <ProTable
          {...tableConfig}
          columns={unTimeTableColumns}
          dataSource={unTimeDataSource}
          search={false}
        />
      </Modal>
    </>
  );
};

export default TableList;
