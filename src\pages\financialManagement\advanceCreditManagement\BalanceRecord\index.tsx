import { tableConfig } from '@/common/utils/config';
import { precreditAccountHistory } from '@/services/api/precredit';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel, useRequest } from '@umijs/max';

export default () => {
  const {
    initialState: {
      currentCompany: { settlementId },
    },
  }: any = useModel('@@initialState');
  const tableReq = useRequest(precreditAccountHistory, {
    manual: true,
    formatResult(res) {
      return {
        data: res.data.data,
        success: res.code == 20000,
        total: res.data.total,
      };
    },
  });
  const columns: ProColumns[] = [
    {
      title: '账户ID',
      dataIndex: 'id',
    },
    {
      title: '账户所有者',
      dataIndex: 'owner',
    },
    {
      title: '账户代码',
      dataIndex: 'path',
    },
    {
      title: '交易ID',
      dataIndex: 'transactionId',
    },
    {
      title: '变化金额',
      dataIndex: 'amount',
      valueType: 'money',
    },
    {
      title: '发生时间',
      dataIndex: 'entryTime',
      valueType: 'dateTime',
    },
  ];
  return (
    <ProTable
      {...tableConfig}
      columns={columns}
      search={false}
      params={{ settlementId }}
      request={tableReq.run}
    />
  );
};
