import React, { useState, useEffect, CSSProperties } from 'react';
import Chart from '@/components/Chart';
import { DatePicker, Radio, Empty, message, Spin } from 'antd';
import dayjs from 'dayjs';
import { apiQueryHistoryPriceDynamicsList } from '@/services/api/distribution';

const { RangePicker } = DatePicker;

// 整合 CSS 样式为组件内部样式对象
const styles = {
  container: {
    padding: '16px',
    background: '#fff',
    borderRadius: '4px',
  },
  toolbar: {
    display: 'flex',
    justifyContent: 'flex-end',
    marginBottom: '16px',
    gap: '10px',
  },
  chartContainer: {
    height: '350px',
    position: 'relative' as 'relative',
  },
  emptyContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '300px',
    background: '#f9f9f9',
    borderRadius: '4px',
  },
  // 从 PriceSettingModal.css 整合的样式
  priceHistoryContainer: {
    padding: '8px',
    borderLeft: '1px solid #f0f0f0',
    // height: '600px',
    minHeight: '420px',
    display: 'flex',
    flexDirection: 'column' as 'column',
    overflowY: 'auto' as 'auto',
  },
  dateRangePicker: {
    width: '220px',
  },
  sectionTitle: {
    fontSize: '16px',
    fontWeight: 500,
  },
};

interface PriceHistoryChartProps {
  // 价格 ID，用于请求接口数据
  priceId: string;
  // 是否在价格设置模态框内显示
  inSettingModal?: boolean;
  // 添加容器样式属性，允许自定义样式
  containerStyle?: React.CSSProperties;
}

// 定义接口返回数据类型
interface PriceHistoryItem {
  saleDate: string;
  salePrice: number;
}

/**
 * 价格策略历史记录图表组件
 */
const PriceHistoryChart: React.FC<PriceHistoryChartProps> = ({ 
  priceId,
  containerStyle,
  inSettingModal = false
}) => {
  // 时间维度选择，默认按月
  const [timeRange, setTimeRange] = useState('按月');
  // 存储接口返回的历史价格数据
  const [historyPriceData, setHistoryPriceData] = useState<{ date: string; price: number }[]>([]);
  // 加载状态
  const [loading, setLoading] = useState(false);
  
  // 获取默认日期范围：按月显示最近 7 个月，按天显示最近 7 天
  const getDefaultDateRange = (type: string): [string, string] => {
    const endDate = dayjs();
    let startDate;
    
    if (type === '按月') {
      startDate = endDate.subtract(6, 'month').startOf('month');
      return [
        startDate.format('YYYY-MM'),
        endDate.format('YYYY-MM')
      ];
    } else {
      startDate = endDate.subtract(6, 'day');
      return [
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD')
      ];
    }
  };

  // 日期范围
  const [localDateRange, setLocalDateRange] = useState<[string, string]>(getDefaultDateRange(timeRange));

  // 当选择的时间维度变化时，更新日期范围
  useEffect(() => {
    const newDateRange = getDefaultDateRange(timeRange);
    setLocalDateRange(newDateRange);
    
    // 请求接口数据
    fetchHistoryPriceData(newDateRange, timeRange);
  }, [timeRange, priceId]);

  // 组件加载时请求数据
  useEffect(() => {
    fetchHistoryPriceData(localDateRange, timeRange);
  }, [priceId]);

  // 请求历史价格数据
  const fetchHistoryPriceData = async (dates: [string, string], timeRangeType: string) => {
    if (!priceId) return;

    setLoading(true);
    try {
      const dateType = timeRangeType === '按月' ? 'month' : 'day';
      const params = {
        dateType,
        priceId,
        saleStartDate: dates[0],
        saleEndDate: dates[1]
      };

      const response = await apiQueryHistoryPriceDynamicsList(params);
      
      if (response && response.data) {
        // 确保 response.data 是数组
        const dataArray = Array.isArray(response.data) ? response.data : [];
        
        // 将接口返回的数据格式转换为组件需要的格式
        const formattedData = dataArray.map((item: PriceHistoryItem) => ({
          date: item.saleDate,
          price: item.salePrice
        }));
        
        setHistoryPriceData(formattedData);
      } else {
        setHistoryPriceData([]);
      }
    } catch (error) {
      console.error('获取历史价格数据失败：', error);
      setHistoryPriceData([]);
    } finally {
      setLoading(false);
    }
  };
  
  // 处理日期格式化，按月时显示年月，按天时显示年月日
  const formatDate = (date: string) => {
    if (timeRange === '按月') {
      return dayjs(date).format('YYYY.MM');
    } else {
      return dayjs(date).format('YYYY.MM.DD');
    }
  };

  // 筛选符合日期范围的数据
  const filterDataByDateRange = () => {
    if (historyPriceData.length === 0) return [];
    
    const startDate = timeRange === '按月' 
      ? dayjs(localDateRange[0]).startOf('month') 
      : dayjs(localDateRange[0]);
    const endDate = timeRange === '按月' 
      ? dayjs(localDateRange[1]).endOf('month') 
      : dayjs(localDateRange[1]);
    
    return historyPriceData
      .filter(item => {
        const itemDate = dayjs(item.date);
        return itemDate.isAfter(startDate) && itemDate.isBefore(endDate) || 
               itemDate.isSame(startDate) || 
               itemDate.isSame(endDate);
      })
      .map(item => ({ 
        date: formatDate(item.date), 
        price: item.price 
      }));
  };

  // 使用传入的数据，并根据日期范围进行筛选
  const filteredData = filterDataByDateRange();
  
  // 构建图表配置
  const chartOptions = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>每日平均价：￥${param.value}`;
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      textStyle: {
        color: '#fff'
      },
      confine: true
    },
    grid: {
      left: '6%',
      right: '10%',
      bottom: '3%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: filteredData.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#eaeaea'
        }
      },
      axisLabel: {
        color: '#666',
        rotate: 0,
      }
    },
    yAxis: {
      type: 'value',
      name: '每日平均价/元',
      nameLocation: 'middle',
      nameGap: 40,
      nameRotate: 90,
      nameTextStyle: {
        color: '#666',
        padding: [0, 0, 0, 0],
        align: 'center',
        verticalAlign: 'middle'
      },
      axisLabel: {
        formatter: '{value}',
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#eaeaea'
        }
      }
    },
    series: [
      {
        name: '每日平均价',
        type: 'line',
        data: filteredData.map(item => item.price),
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: true,
        itemStyle: {
          color: '#1890ff'
        },
        lineStyle: {
          width: 2,
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.2)'
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0.01)'
              }
            ]
          }
        }
      }
    ]
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates: any, dateStrings: [string, string]) => {
    if (dates) {
      setLocalDateRange(dateStrings);
      fetchHistoryPriceData(dateStrings, timeRange);
    }
  };

  // 处理时间维度变化
  const handleTimeRangeChange = (e: any) => {
    const newTimeRange = e.target.value;
    setTimeRange(newTimeRange);
  };

  // 根据是否在模态框内显示确定容器样式
  const containerStyleToUse = inSettingModal ? 
    styles.priceHistoryContainer : 
    { ...styles.container, ...containerStyle };

  return (
    <div style={containerStyleToUse}>
      <div style={styles.toolbar}>
        <Radio.Group
          value={timeRange}
          onChange={handleTimeRangeChange}
          buttonStyle="solid"
          size="small"
        >
          <Radio.Button value="按天">按天</Radio.Button>
          <Radio.Button value="按月">按月</Radio.Button>
        </Radio.Group>

        <RangePicker
          size="small"
          picker={timeRange === '按月' ? 'month' : 'date'}
          format={timeRange === '按月' ? 'YYYY-MM' : 'YYYY-MM-DD'}
          placeholder={['开始日期', '结束日期']}
          value={[
            localDateRange[0] ? dayjs(localDateRange[0]) : null,
            localDateRange[1] ? dayjs(localDateRange[1]) : null,
          ]}
          onChange={handleDateRangeChange}
          style={styles.dateRangePicker}
        />
      </div>

      {loading ? (
        <div style={styles.chartContainer}>
          <Spin />
        </div>
      ) : filteredData.length > 0 ? (
        <div style={styles.chartContainer}>
          <Chart options={chartOptions} />
        </div>
      ) : (
        <div style={styles.emptyContainer}>
          <Empty description="暂无历史价格数据" />
        </div>
      )}
    </div>
  );
};

export default PriceHistoryChart; 