/**
 * 详情弹窗组件
 * @param title  标题
 * @param visible/setVisible   开关
 * @param isLoading  骨架屏
 * @param columnsInitial  模板
 * @param dataSource  数据 {isEnable:禁启用}
 * @param onEnable  禁启用按钮 (成功交互函数 (),失败交互函数 (提示)) => {}
 * @param onDelete  删除按钮
 * @param onUpdate  编辑按钮
 * */
import { ModelWidth } from '@/common/utils/enum';
import { getUniqueId } from '@/common/utils/tool';
import type { ProDescriptionsProps } from '@ant-design/pro-components';
import ProDescriptions from '@ant-design/pro-descriptions';
import { Divider, Modal } from 'antd';
import type { ModalProps } from 'antd/es/modal/interface';
import React from 'react';
import PrefixTitle from '../PrefixTitle';

export type DetailsPopColumns<T = Record<string, any>> = ProDescriptionsProps<T>;
// export type DetailsPopColumns<T = Record<string, any>> = {
//   title: string;
//   columns: ProDescriptionsItemProps<T, 'text'>[];
// };

interface Porps extends ModalProps {
  title: string | React.ReactNode;
  visible: boolean;
  isLoading: boolean;
  setVisible: any;
  columnsInitial: any[];
  dataSource: any;
  onEnable?: any;
  onDelete?: any;
  onUpdate?: any;
  enableTitle?: string;
  limit?: {
    openClose?: string;
    delete?: string;
    edit?: string;
  };
  width?: any; // 弹窗宽
}

const DetailsPop = ({
  title,
  visible,
  isLoading,
  setVisible,
  columnsInitial,
  dataSource,
  onEnable,
  onDelete,
  onUpdate,
  limit, //是否具有权限
  width,
  enableTitle,
  ...modelProps
}: Porps) => {
  const [columnsList, setColumnsList] = React.useState<any>([]);
  // 初始化函数
  React.useEffect(() => {
    // 自动优化新增 key
    columnsInitial.map((item, index) => {
      item.columns.map((itemChild: any, indexChild: any) => {
        columnsInitial[index].columns[indexChild].key = itemChild.dataIndex;
      });
      // if (item.hideInList) columnsInitial.splice(index, 1)
    });
    setColumnsList(columnsInitial);
  }, [columnsInitial]);
  // 监听函数
  React.useEffect(() => {
    // 打开弹窗
    if (visible) {
    }
  }, [visible]);
  return (
    <Modal
      width={width || ModelWidth.md}
      title={title}
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      footer={false}
      {...modelProps}
    >
      <div style={{ minHeight: '337px' }}>
        {columnsList.map((item: any, index: any) => (
          <>
            <ProDescriptions
              column={{ xs: 1, sm: 2, lg: 2, md: 2, xl: 2, xxl: 2 }}
              {...item}
              loading={index === 0 && isLoading}
              title={item.title && <PrefixTitle>{item.title} </PrefixTitle>}
              dataSource={dataSource}
              columns={item.columns}
              key={getUniqueId()}
              style={{ display: isLoading ? 'none' : 'block' }}
              contentStyle={{ display: 'block' }}
            />
            {index < columnsList.length - 1 && !isLoading && <Divider />}
          </>
        ))}
      </div>
    </Modal>
  );
};

export default DetailsPop;
