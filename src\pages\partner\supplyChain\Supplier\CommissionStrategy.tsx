/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-25 11:30:28
 * @LastEditTime: 2023-07-20 11:08:56
 * @LastEditors: zhangfengfei
 */
/*
 * @Author: zhangfengfei
 * @Date: 2022-07-20 17:05:29
 * @LastEditTime: 2022-07-25 10:14:01
 * @LastEditors: zhangfengfei
 */
import { tableConfig } from '@/common/utils/config';
import { ticketTypeEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import type { ModalState } from '@/hooks/useModal';
import { getProductCommissionList } from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import { isNil } from 'lodash';
import type { FC } from 'react';
import { useModel } from '@umijs/max';

interface CommissionStrategyProps {
  modalState: ModalState;
  currentItem?: any;
}

const CommissionStrategy: FC<CommissionStrategyProps> = ({
  modalState: { visible, setVisible },
  currentItem: supplierItem,
}) => {
  const { initialState } = useModel('@@initialState');
  const { coId = '', coName = '' } = initialState?.currentCompany || {};

  const tableListReq = async (params: API.ProductCommissionListParams) => {
    const { data } = await getProductCommissionList(params);
    addOperationLogRequest({
      action: 'info',
      content: `查看【${supplierItem.coName}】佣金策略`,
    });

    return {
      data: data,
      total: data.length,
    };
  };

  const columns: ProColumns<API.ProductCommissionListItem>[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
      search: false,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '产品类型',
      dataIndex: 'unitType',
      search: false,
      valueEnum: { 0: '组合票', 1: '单票', 2: '权益卡' },
    },
    {
      title: '票种',
      dataIndex: 'goodsType',
      valueEnum: ticketTypeEnum,
      search: false,
    },
    {
      title: '分时预约时间',
      dataIndex: 'timeBeginTime',
      search: false,
      renderText: (_, record) =>
        record?.timeShareBeginTime
          ? `${record.timeShareBeginTime} 至 ${record.timeShareEndTime}`
          : '-',
    },
    {
      title: '单买价格',
      dataIndex: 'overallDiscount',
      search: false,
      renderText: (text, record) => {
        if (isNil(record.marketPrice)) {
          return '-';
        }
        const price = (record.overallDiscount * record.marketPrice) / 100;
        return `${((record.salePrice / price) * 100 || 0).toFixed(0)}%/${record.salePrice}`;
      },
    },
    {
      title: '组合销售',
      dataIndex: 'isCompose',
      search: false,
      renderText: (_, record) => (record.isCompose ? '是' : '否'),
    },
    {
      title: '组合价格',
      search: false,
      dataIndex: 'composePrice',
      render: (text, record) => {
        if (!record.isCompose || isNil(record.marketPrice)) {
          return '-';
        }
        const price = (record.overallDiscount * record.marketPrice) / 100;
        return `${((record.composePrice / price) * 100).toFixed(0)}%/${record.composePrice}`;
      },
    },
    {
      title: '佣金比例',
      dataIndex: 'commissionRate',
      search: false,
      renderText: (_, record) => `${record.commissionRate}%`,
    },
  ];

  return (
    <Modal
      width={1500}
      title={'佣金策略'}
      open={visible}
      maskClosable={false}
      destroyOnClose
      onCancel={() => setVisible(false)}
      footer={false}
    >
      <ProTable<API.ProductCommissionListItem, API.ProductCommissionListParams>
        {...tableConfig}
        params={{
          distributorId: coId,
          supplierId: supplierItem?.coId,
        }}
        pagination={false}
        request={tableListReq}
        columns={columns}
      />
    </Modal>
  );
};

export default CommissionStrategy;
