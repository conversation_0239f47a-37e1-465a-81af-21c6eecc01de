import Delete from '@/common/components/Delete';
import Disabled from '@/common/components/Disabled';
import ImageUpload from '@/common/components/ImageUpload';
import ProModal from '@/common/components/ProModal';
import useModal from '@/common/components/ProModal/useProModal';
import { tableConfig } from '@/common/utils/config';
import { GuideStepStatus, tourType } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getHashParams, removeStateFromUrl } from '@/common/utils/tool';
import { useGuide } from '@/hooks/useGuide';
import {
  addStore,
  deleteStore,
  editStore,
  enableStore,
  infoStore,
  pageStore,
  sortStore,
  sortStoreList,
} from '@/services/api/tour';
import { PlusOutlined } from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useRequest } from '@umijs/max';
import { Button, Select, Table } from 'antd';
import { useContext, useEffect, useRef, useState } from 'react';
import { TabKeyContext } from '../..';

export default ({ store: { value: storeId } }: any) => {
  const queryParams = getHashParams();
  const { updateGuideInfo } = useGuide();
  const modalState = useModal();
  const sortState = useModal();
  const actionRef = useRef<ActionType>();
  const [id, setId] = useState<string | null>();
  const [sortDataSource, setSortDataSource] = useState([]);
  // 排序列表数据接口
  const sortStoreListReq = useRequest(sortStoreList, {
    manual: true,
    onSuccess: setSortDataSource,
  });

  const tabKey = useContext(TabKeyContext);

  // 排序按钮
  const sortButtton = (
    <Button
      key="k2"
      type="primary"
      onClick={() => {
        sortStoreListReq.run({ storeId });
        sortState.setType('edit');
      }}
    >
      排序
    </Button>
  );
  // 新增按钮
  const addButton = (
    <Button
      key="k1"
      type="primary"
      onClick={() => {
        setId(null);
        modalState.setType('add');
      }}
    >
      <PlusOutlined /> 新增
    </Button>
  );
  // 设置列表数据
  const setSortData = (a: number, b: number) => {
    const sum = structuredClone(sortDataSource);
    sum.splice(b, 0, ...sum.splice(a, 1));
    setSortDataSource(sum);
  };
  // 设置排序列表
  const setSortList = async () => {
    const length = sortDataSource.length;
    const list = sortDataSource.map((item: any, index: number) => ({
      id: item.id,
      sort: length - index,
    }));
    await sortStore({ list });
    addOperationLogRequest({
      action: 'edit',
      module: tabKey,
      content: `编辑导览列表排序`,
    });
    actionRef?.current?.reload();
    return true;
  };

  const tableColumns: ProColumnType[] = [
    {
      title: '序号',
      valueType: 'index',
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '导览类型',
      dataIndex: 'type',
      search: false,
      valueEnum: tourType,
    },
    {
      title: '导览链接',
      dataIndex: 'navigationUrl',
      search: false,
    },
    {
      title: '启用状态',
      dataIndex: 'isEnable',
      search: false,
      render: (dom: any, entity: any) => (
        <Disabled
          access={true}
          status={dom == 2}
          params={{
            id: entity.id,
            isEnable: dom == 2 ? 1 : 2,
          }}
          request={async (params) => {
            const data = await enableStore(params);
            addOperationLogRequest({
              action: 'disable',
              module: tabKey,
              content: `${dom == 1 ? '启用' : '禁用'}【${entity.scenicName}】导览`,
            });
            return data;
          }}
          actionRef={actionRef}
        />
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_: any, entity: any) => [
        <a
          onClick={() => {
            setId(entity.id);
            modalState.setType('info');
            addOperationLogRequest({
              action: 'info',
              module: tabKey,
              content: `查看【${entity?.scenicName}】导览`,
            });
          }}
          key="k1"
        >
          查看
        </a>,
        <a
          onClick={() => {
            setId(entity.id);
            modalState.setType('edit');
          }}
          key="k2"
        >
          编辑
        </a>,
        <Delete
          key="k3"
          access={true}
          status={entity.isEnable == 2}
          params={{ id: entity.id }}
          request={async (params) => {
            const data = await deleteStore(params);
            addOperationLogRequest({
              action: 'del',
              module: tabKey,
              content: `删除【${entity.scenicName}】导览`,
            });
            return data;
          }}
          actionRef={actionRef}
        />,
      ],
    },
  ];
  const modalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '导览类型',
          dataIndex: 'type',
          valueEnum: tourType,
          initialValue: '1',
          convertValue: (value) => String(value ?? '') || value,
          fieldProps: {
            allowClear: false,
            disabled: modalState.type == 'edit',
          },
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '导览链接',
          dataIndex: 'navigationUrl',
          fieldProps: {
            disabled: modalState.type == 'edit',
          },
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '导览图片',
          dataIndex: 'publicizeImgUrl',
          formItemProps: { rules: [{ required: true }] },
          renderText: (text: any) => <ImageUpload defaultValue={text} readonly />,
          renderFormItem: (_, __, formRef) => (
            <ImageUpload
              defaultValue={formRef.getFieldValue('publicizeImgUrl')}
              size={10240}
              maxCount={1}
            />
          ),
        },
        {
          valueType: 'dependency',
          name: ['type'],
          columns: ({ type }) =>
            type == 2
              ? [
                  {
                    colProps: { xs: 24, sm: 12 },
                    title: '景区名称',
                    dataIndex: 'scenicName',
                    fieldProps: {
                      maxLength: 20,
                    },
                    formItemProps: { rules: [{ required: true }] },
                  },
                ]
              : [],
        },
      ],
    },
  ];
  const sortColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '',
          dataIndex: 'sort',
          colProps: { span: 24 },
          renderFormItem: () => (
            <Table
              rowKey="id"
              bordered
              pagination={false}
              columns={[
                {
                  title: '景区名称',
                  dataIndex: 'scenicName',
                },
                {
                  title: '当前排序',
                  render: (_: any, __: any, index: number) => (
                    <Select
                      value={index + 1}
                      options={sortStoreListReq.data.map((___: any, i: any) => ({
                        label: i + 1,
                        value: i + 1,
                      }))}
                      onChange={(value: number) => setSortData(index, value - 1)}
                    />
                  ),
                },
              ]}
              dataSource={sortDataSource}
              loading={sortStoreListReq.loading}
            />
          ),
        },
      ],
    },
  ];

  useEffect(() => {
    if (!modalState.type && queryParams?.operate) {
      history.pushState(null, null, removeStateFromUrl('operate'));
    }
  }, [modalState.type]);
  useEffect(() => {
    if (queryParams?.operate === 'addTour') {
      setId(null);
      modalState.setType('add');
    }
  }, [storeId]);

  return (
    <>
      <ProTable
        {...tableConfig}
        actionRef={actionRef}
        columns={tableColumns}
        toolBarRender={() => [sortButtton, addButton]}
        params={{ storeId }}
        request={pageStore}
      />
      <ProModal
        {...modalState}
        title="导览"
        actionRef={actionRef}
        columns={modalColumns}
        params={{ id, storeId }}
        infoRequest={infoStore}
        addRequest={async (params) => {
          const data = await addStore(params);
          if (queryParams?.operate) {
            history.pushState(null, null, removeStateFromUrl('operate'));
          }
          // 更新引导
          updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_5 });
          addOperationLogRequest({
            action: 'add',
            module: tabKey,
            content: `新增【${params.scenicName}】导览`,
          });
          return data;
        }}
        editRequest={async (params) => {
          const data = await editStore(params);
          addOperationLogRequest({
            action: 'edit',
            module: tabKey,
            content: `编辑【${params.scenicName}】导览`,
          });
          return data;
        }}
      />
      <ProModal
        {...sortState}
        title="排序"
        actionRef={actionRef}
        columns={sortColumns}
        params={{}}
        dataSource={{}}
        onFinish={setSortList}
      />
    </>
  );
};
