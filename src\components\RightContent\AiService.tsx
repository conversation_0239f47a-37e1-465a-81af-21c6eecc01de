import { getUniqueId, markdownToHtml } from '@/common/utils/tool';
import {
  apiChatCompletion,
  apiGetChatRecords,
  apiGetKnowledgeIds,
  apiSaveChatRecord,
  sendMyToken,
} from '@/services/api/service';
import { CloseOutlined, CustomerServiceOutlined, SendOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Drawer, Input, List, message, Select } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import styles from './AiService.module.less';
import {
  BuyTickMessage,
  GetEchartsData,
  GetInitialMessage,
  TicketMessage,
} from './components/AiMessage';

interface Message {
  id: string;
  content: string;
  time: string;
  sender: 'user' | 'ai';
  status?: 'sending' | 'sent' | 'error' | 'thinking';
  nowShow?: number;
}

const ThinkingAnimation: React.FC = () => {
  return (
    <div className={styles.thinkingAnimation}>
      <span className={styles.dot} />
      <span className={styles.dot} />
      <span className={styles.dot} />
    </div>
  );
};

const AiService: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [knowledgeFileIds, setKnowledgeFileIds] = useState<string[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [historyMessages, setHistoryMessages] = useState<Message[]>([]);
  const [moreVal, setMoreVal] = useState(null);
  const [needMessageShow, setNeedMessageShow] = useState(1);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isInputFromChild, setIsInputFromChild] = useState(false);
  const [aiToken, setAiToken] = useState(null);

  const { initialState, loading } = useModel('@@initialState');
  const { coId = '', coName = '' } = initialState?.currentCompany || {};
  const { userId } = initialState.userInfo;

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      // 使用更可靠的方式滚动到底部，不使用平滑滚动
      const container = document.querySelector(`.${styles.messageList}`);
      if (container) {
        setTimeout(() => {
          container.scrollTop = container.scrollHeight;
          console.log('滚动到底部', container.scrollHeight);
        }, 0); // 减少延迟时间
      }
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    console.log(open, coId, messages.length, messages);
    if (open && coId && messages.length === 0) {
      sendMyToken({ commerceCompanyId: coId }).then((res) => {
        if (res.code === 20000) {
          setAiToken(res.data);
          sessionStorage.setItem('x-extra-info', res.data);
        }
      });
      fetchChatHistory();
    }
  }, [open, coId]);

  // 单独函数返回带交互的初始消息
  const fetchChatHistory = async () => {
    try {
      const response = await apiGetChatRecords({
        businessId: coId,
        pageSize: 100,
      });
      const records = response.data?.records || [];
      if (records?.length > 0) {
        const historyMessages = records.reverse().map((record: any) => ({
          id: getUniqueId(),
          content: record.messageBody,
          sender: record.roleId ? 'user' : 'ai',
          status: 'sent',
        }));

        setHistoryMessages([...historyMessages]);
        // 默认不显示历史记录
        setShowHistory(false);
        // 只显示欢迎消息
        setMessages([
          {
            id: '1',
            content: '',
            time: new Date().toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
            }),
            sender: 'ai',
            status: 'sent',
            nowShow: 0,
          },
        ]);
      } else {
        setMessages([
          {
            id: '1',
            content: '',
            time: new Date().toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
            }),
            sender: 'ai',
            status: 'sent',
            nowShow: 0,
          },
        ]);
      }
      setTimeout(() => {
        scrollToBottom();
      }, 200);
    } catch (error) {
      console.error('获取聊天历史记录失败：', error);
    }
  };
  // 切换历史记录的显示状态
  const toggleHistory = () => {
    if (showHistory) {
      // 如果当前显示历史记录，则只保留当前会话的消息
      const currentSessionMessages = messages.filter(
        (msg) => !historyMessages.some((historyMsg) => historyMsg.id === msg.id),
      );
      setMessages(
        currentSessionMessages.length > 0
          ? currentSessionMessages
          : [
              {
                id: '1',
                content: '您好！有什么可以帮您？',
                time: new Date().toLocaleTimeString('zh-CN', {
                  hour: '2-digit',
                  minute: '2-digit',
                }),
                sender: 'ai',
                status: 'sent',
              },
            ],
      );
      setShowHistory(false);
    } else {
      // 如果当前隐藏历史记录，则将历史记录拼接到当前消息的上方
      const currentSessionMessages = messages.filter(
        (msg) => !historyMessages.some((historyMsg) => historyMsg.id === msg.id),
      );
      setMessages([...historyMessages, ...currentSessionMessages]);
      setShowHistory(true);
    }
    setTimeout(() => {
      scrollToBottom();
    }, 200);
  };

  const showDrawer = async () => {
    try {
      // businessId 是 慧旅云
      const businessId = 8888;

      // 获取知识库文件 ID 列表
      const response = await apiGetKnowledgeIds({ businessId });
      if (response.code === 20000) {
        setKnowledgeFileIds(response.data || []);
      } else {
        message.error('获取知识库文件列表失败');
      }
    } catch (error) {
      message.error('获取知识库文件列表失败');
      console.error('获取知识库文件列表失败：', error);
    }
    setOpen(true);
  };

  const handleSend = async () => {
    console.log(input.trim(), 66);

    if (!input.trim()) return;
    if (isSending) return;

    const newMessage: Message = {
      id: getUniqueId(),
      content: input,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      sender: 'user',
      status: 'sending',
    };

    setMessages((prev) => [...prev, newMessage]);
    setInput('');
    setIsSending(true);

    try {
      // 更新用户消息状态为已发送
      setMessages((prev) =>
        prev.map((msg) => (msg.id === newMessage.id ? { ...msg, status: 'sent' } : msg)),
      );

      // 保存用户发送的消息记录
      await apiSaveChatRecord({
        messageBody: newMessage.content,
        businessId: coId,
        roleId: userId, // 用户发送消息时，roleId 传景区 id
      });

      // 添加思考中的消息
      const thinkingMessage: Message = {
        id: getUniqueId(),
        content: '',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
        sender: 'ai',
        status: 'thinking',
      };
      setMessages((prev) => [...prev, thinkingMessage]);

      // 创建中止控制器
      const abortController = new AbortController();

      // 记录累积的响应内容
      let accumulatedContent = '';

      // 调用 AI 对话接口（使用新的回调方式）
      await apiChatCompletion(
        {
          chatid: getUniqueId(), // 使用唯一 ID 代替时间戳
          docids: knowledgeFileIds,
          question: input,
        },
        {
          onMessage: (data) => {
            // 处理接收到的数据
            let content = '';
            if (typeof data === 'string') {
              content = data;
            } else if (data && data.text) {
              content = data.text;
            }

            // 忽略 ping 消息
            if (content && !content.includes('ping -')) {
              // 处理内容，去除最后一个\r
              const cleanContent = content.replace(/\r$/, '');
              // 更新累积内容
              accumulatedContent += cleanContent;

              // 更新 UI
              setMessages((prev) => {
                return prev.map((msg) =>
                  msg.id === thinkingMessage.id
                    ? {
                        ...msg,
                        content: accumulatedContent,
                        status: 'sent',
                      }
                    : msg,
                );
              });
            }
          },
          onError: (error) => {
            console.error('流式消息处理错误：', error);
            if (!accumulatedContent) {
              setMessages((prev) =>
                prev.map((msg) =>
                  msg.id === thinkingMessage.id
                    ? {
                        ...msg,
                        content: '抱歉，发生了错误，请稍后再试。',
                        status: 'error',
                      }
                    : msg,
                ),
              );
            }
          },
          onClose: () => {
            console.log('聊天记录===');
            console.log(accumulatedContent);

            // 如果没有收到任何内容，显示提示信息
            if (!accumulatedContent) {
              setMessages((prev) =>
                prev.map((msg) =>
                  msg.id === thinkingMessage.id
                    ? {
                        ...msg,
                        content: '抱歉，没有收到回复，请稍后再试。',
                        status: 'error',
                      }
                    : msg,
                ),
              );
            }

            // 保存 AI 回复的消息记录
            if (accumulatedContent) {
              apiSaveChatRecord({
                // accountNumber: ,
                messageBody: accumulatedContent,
                businessId: coId,
                // AI 回复不传 roleId
              });
            }
          },
        },
        abortController,
      );
    } catch (error) {
      setMessages((prev) =>
        prev.map((msg) => (msg.id === newMessage.id ? { ...msg, status: 'error' } : msg)),
      );
      message.error('发送消息失败，请重试');
      console.error('AI 对话失败：', error);
    } finally {
      setIsSending(false);
    }
  };

  const handleChildMessage = (msg: string) => {
    setIsInputFromChild(true);
    setInput(msg);
  };

  useEffect(() => {
    if (input && isInputFromChild) {
      handleSend();
      setIsInputFromChild(false); // 重置标志
    }
  }, [input]);

  const returnContent = (num: number) => {
    const arr = ['功能咨询', '功能查找', '数据检索', '库存预警', '价格策略'];
    return arr[num - 1];
  };

  useEffect(() => {
    console.log(moreVal);
    if (moreVal) {
      const newMessage1: Message = {
        id: getUniqueId(),
        content: returnContent(moreVal),
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
        sender: 'user',
        status: 'sending',
      };
      const newMessage2: Message = {
        id: getUniqueId(),
        content: '',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
        sender: 'ai',
        status: 'sending',
        nowShow: +moreVal,
      };
      setMessages((prev) => [...prev, moreVal != 0 && newMessage1, newMessage2]);
    }
  }, [moreVal]);

  const onClose = () => {
    setOpen(false);
  };

  const returnJsonMsg = (content) => {
    const parseVal = JSON.parse(content);
    if (parseVal.type === 'FUNCTION_SEARCH') {
      // 功能查找
      return (
        <BuyTickMessage
          confrimText={'已为你找到快捷入口，点击下面按钮直接跳转：'}
          buyList={[{ text: '点我跳转', url: parseVal.url }]}
        />
      );
    } else if (parseVal.type === 'INVENTORY_ALERT') {
      // 库存预警
      const { conditionValue, notificationChannels, userName, url } = parseVal;
      let str = '';
      if (notificationChannels.includes(0)) str += '发送站内信提醒';
      if (notificationChannels.includes(1)) str += '发送短信提醒';
      const list = [
        `执行条件：商品库存小于${conditionValue}`,
        `执行操作：${str}`,
        `接收人：${userName}`,
      ];
      return (
        <TicketMessage
          text={
            '好的，下方是任务预览，可点击修改对应的内容。配置完成后点击"执行"即可创建库存预警。'
          }
          list={list}
          url={url}
        />
      );
    } else if (parseVal.type === 'PRICING_STRATEGY') {
      // 价格粗略
      const { commissionRate, salePrice, ticketName, url } = parseVal;
      const list = [
        `商品名称：${ticketName}`,
        `单买价格：${salePrice}`,
        `组合销售：否`,
        `佣金比例：${commissionRate}%`,
      ];
      return (
        <TicketMessage
          text={
            '好的，下方是任务预览，可点击修改对应的内容。配置完成后点击"执行"即可创建价格策略。'
          }
          list={list}
          url={url}
        />
      );
    } else if (parseVal.type === 'DATA_RETRIEVAL') {
      // 数据检索
      const { value, detail, beginTime, endTime, description } = parseVal;
      return (
        <GetEchartsData
          value={value}
          detail={detail}
          beginTime={beginTime}
          endTime={endTime}
          description={description}
        />
      );
    } else if ('error' in parseVal || 'errorMsg' in parseVal) {
      return <BuyTickMessage confrimText={parseVal.error || parseVal.errorMsg } buyList={[]} />;
    }
  };

  const isValidJSON = (str1) => {
    // // 快速排除明显不符合 JSON 格式的字符串
    if (typeof str1 !== 'string') return false;
    const str = str1.trim();
    // str = str.trim();
    if (!str) return false; // 空字符串不是有效的 JSON
    if (!/^[\[\{]/.test(str) || !/[\]\}]$/.test(str)) return false; // 不以 [ 或 { 开头，或以 ] 或 } 结尾

    // 使用 JSON.parse() 进行精确验证
    try {
      const parsed = JSON.parse(str);
      // 确保解析结果是对象或数组（基本的 JSON 结构）
      return typeof parsed === 'object' && parsed !== null;
    } catch (e) {
      return false;
    }
  };

  return (
    <>
      <div className={styles.iconContainer}>
        {/* <img
          onClick={showDrawer}
          src={aiServiceImage}
          alt="AI服务"
          className={styles.aiServiceIcon}
        /> */}
        <span style={{ cursor: 'pointer' }} onClick={showDrawer}>
          <CustomerServiceOutlined /> 客服
        </span>
      </div>
      <Drawer
        onClose={onClose}
        open={open}
        placement="right"
        width={400}
        className={styles.drawer}
        headerStyle={{ display: 'none' }}
        zIndex={9999}
      >
        <div className={styles.chatContainer}>
          <div className={styles.header}>
            <span className={styles.title}>智能客服</span>
            <CloseOutlined className={styles.close} onClick={onClose} />
          </div>
          {historyMessages.length > 0 && !showHistory && (
            <div
              className={styles.historyToggle}
              onClick={toggleHistory}
              style={{
                textAlign: 'center',
                padding: '8px',
                cursor: 'pointer',
                color: '#999',
                marginBottom: '8px',
              }}
            >
              点击显示历史记录
            </div>
          )}
          <List
            dataSource={messages}
            renderItem={(item) => (
              <List.Item
                className={`${styles.messageItem} ${
                  item.sender === 'user' ? styles.userMessage : styles.aiMessage
                }`}
              >
                <div className={styles.messageContent}>
                  {item.status === 'thinking' ? (
                    <div className={styles.messageText}>
                      <ThinkingAnimation />
                    </div>
                  ) : item?.nowShow >= 0 ? (
                    <>
                      <GetInitialMessage
                        onMessageClick={handleChildMessage}
                        nowShow={item?.nowShow}
                      />
                    </>
                  ) : isValidJSON(item?.content) ? (
                    returnJsonMsg(item?.content)
                  ) : (
                    <div
                      className={styles.messageText}
                      dangerouslySetInnerHTML={{
                        __html: item.sender === 'ai' ? markdownToHtml(item.content) : item.content,
                      }}
                    />
                  )}
                </div>
              </List.Item>
            )}
            className={styles.messageList}
          />
          <div ref={messagesEndRef} />
          <div className={styles.inputContainer}>
            <div className={styles.newMsg}>
              <Select
                placeholder={'更多功能'}
                style={{ width: 130, height: 40 }}
                popupMatchSelectWidth={false}
                placement={'topLeft'}
                value={moreVal}
                onChange={(val) => {
                  setMoreVal(val);
                }}
                optionLabelProp="text"
                disabled={isSending}
                options={[
                  {
                    value: '1',
                    label: (
                      <div style={{ display: 'flex', flexDirection: 'column', lineHeight: 1.8 }}>
                        <span>功能咨询</span>
                        <span style={{ fontSize: '12px', color: '#999' }}>
                          帮助您了解产品功能说明和相关业务概念
                        </span>
                      </div>
                    ),
                    text:'更多功能'
                  },
                  {
                    value: '2',
                    label: (
                      <div style={{ display: 'flex', flexDirection: 'column', lineHeight: 1.8 }}>
                        <span>功能查找</span>
                        <span style={{ fontSize: '12px', color: '#999' }}>
                          帮助您快捷找到管理后台的特定功能页面
                        </span>
                      </div>
                    ),
                    text:'更多功能'
                  },
                  {
                    value: '3',
                    label: (
                      <div style={{ display: 'flex', flexDirection: 'column', lineHeight: 1.8 }}>
                        <span>数据检索</span>
                        <span style={{ fontSize: '12px', color: '#999' }}>
                          帮助您快捷查询订单、门票销售数据
                        </span>
                      </div>
                    ),
                    text:'更多功能'
                  },
                  {
                    value: '4',
                    label: (
                      <div style={{ display: 'flex', flexDirection: 'column', lineHeight: 1.8 }}>
                        <span>库存预警</span>
                        <span style={{ fontSize: '12px', color: '#999' }}>
                          帮助您快捷设置库存阈值消息提醒
                        </span>
                      </div>
                    ),
                    text:'更多功能'
                  },
                  {
                    value: '5',
                    label: (
                      <div style={{ display: 'flex', flexDirection: 'column', lineHeight: 1.8 }}>
                        <span>价格策略</span>
                        <span style={{ fontSize: '12px', color: '#999' }}>
                          帮助您快捷设置动态价格策略
                        </span>
                      </div>
                    ),
                    text:'更多功能'
                  },
                ]}
              />
            </div>
            <div className={styles.botIpt}>
              <Input.TextArea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="请输入您的问题..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault();
                    handleSend();
                  }
                }}
                className={styles.input}
              />
              <Button
                type="primary"
                onClick={handleSend}
                icon={<SendOutlined />}
                loading={isSending}
                className={styles.sendButton}
              >
                发送
              </Button>
            </div>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default AiService;
