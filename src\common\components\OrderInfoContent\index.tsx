/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-18 16:17:24
 * @LastEditTime: 2023-09-21 14:58:54
 * @LastEditors: zhangfengfei
 */

import { tableConfig } from '@/common/utils/config';
import {
  equipmentTypeEnum,
  orderTypeEnum,
  payTypeEnum,
  productTypeEnum,
  saleChannelEnum,
  ticketStatusEnum,
  ticketTypeEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { formatTime } from '@/common/utils/tool';
import { showTicketModal } from '@/global';
import { apiOderPageParticularsList } from '@/services/api/erp';
import { SafetyCertificateOutlined, SolutionOutlined, UserOutlined } from '@ant-design/icons';
import ProDescriptions from '@ant-design/pro-descriptions';
import { ModalForm } from '@ant-design/pro-form';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Descriptions, Divider, Space, Steps, Table } from 'antd';
import { isEmpty } from 'lodash';
import type { FC } from 'react';
import { useRef, useState } from 'react';
import Tickets from './Tickets';

const { Step } = Steps;

interface OrderDetailProps {
  orderId: string;
  orderType: string;
}

const OrderDetail: FC<OrderDetailProps> = ({ orderId, orderType }) => {
  //票务详情
  const [inItTicketInfo, setTicketInfo] = useState<Record<string, any>[]>([]);
  //当前订单详情
  const [orderParticular, setOrderParticular] = useState({});
  //订单详情状态
  const [orderParticulaStatus, setOrderParticularStatus] = useState({});
  const [total, setTotal] = useState(0);

  const actionRef = useRef<ActionType>();

  //获取当前订单详情列表
  const getOrderDetail = async (params) => {
    const { data } = await apiOderPageParticularsList(params);
    addOperationLogRequest({
      action: 'info',
      content: `查看前结算佣金账单【${orderId}】`,
    });
    const { order, ticketInfoList, orderStatus, orderTravelCard = [], checkInfoList = [] } = data;
    setOrderParticular(order);
    setOrderParticularStatus(orderStatus);

    // 权益卡
    if (orderType === 'JQTC') {
      // 数据统一
      const data = orderTravelCard.map((item) => {
        const { productName, productType, identityName, identity } = item;
        return {
          ...item,
          proName: productName,
          proType: productType,
          buyer: identityName,
          buyerId: identity,
        };
      });

      setTicketInfo(data);
      return {
        data,
      };
    }
    // 普通票
    setTicketInfo(ticketInfoList.data);
    setTotal(ticketInfoList.total);
    return {
      ...ticketInfoList,
      data: (ticketInfoList.data ?? []).map((item, index) => {
        const current = checkInfoList?.find((i) => i.id === item.id);
        return {
          ...item,
          key: index,
          terminalName: current?.terminalName,
          checkPointName: current?.checkPointName,
          checkType: current?.checkType,
        };
      }),
    };
  };

  // 合计
  const [countNum, setCount] = useState<number>(0);
  const [chainModalVisit, setChainModalVisit] = useState(false);

  //创建订单
  const createStatusArray = [];
  //支付中
  const payStatusArray = [];
  //出票
  const ticketStatusArray = [];
  //使用
  // const checkStatusArray = [];
  //退款
  const refundStatusArray = [];
  //用户取消订单
  const userStatusArray = [];

  for (const k in orderParticulaStatus) {
    if (k == 'createTime') {
      createStatusArray.push({ name: '创建订单', time: orderParticular.createTime });
    }
    if (k == 'orderStatus') {
      //已取消
      if (orderParticulaStatus.orderStatus == 13 || orderParticular.orderStatus == 14) {
        userStatusArray.push({ name: '用户取消订单', time: orderParticular.modifyTime });
        if (orderParticulaStatus.payStatus == 20) {
          // payStatusArray.push({ name: '支付中', time: orderParticulaStatus.payStatus })
          payStatusArray.push({ name: '已取消', time: orderParticular.modifyTime });
        }
      } else if (orderParticulaStatus.orderStatus == 12) {
        payStatusArray.push({ name: '订单超时失效', time: orderParticulaStatus.orderTime });
      } else if (orderParticulaStatus.payStatus == 20) {
        payStatusArray.push({ name: '支付中', time: orderParticulaStatus.payTime });
        if (orderParticulaStatus.createStatus == 12) {
          createStatusArray.push({
            name: '订单超时失效',
            time: orderParticular.modifyTime,
          });
        }
      }
      //支付成功
      else if (orderParticulaStatus.payStatus == 21) {
        payStatusArray.push({ name: '支付成功', time: orderParticulaStatus.payTime });
        if (orderParticulaStatus.ticketStatus == 30) {
          ticketStatusArray.push({ name: '出票成功', time: orderParticulaStatus.ticketTime });
        } else if (orderParticulaStatus.ticketStatus == 31) {
          ticketStatusArray.push({ name: '出票失败', time: orderParticulaStatus.ticketTime });
          if (orderParticulaStatus.refundStatus == 51) {
            refundStatusArray.push({ name: '退款成功', time: orderParticulaStatus.refundTime });
          }
        }
      }

      if (orderParticulaStatus.refundStatus == 50) {
        refundStatusArray.push({ name: '退款中', time: orderParticulaStatus.refundTime });
      } else if (orderParticulaStatus.refundStatus == 51) {
        refundStatusArray.push({ name: '退款成功', time: orderParticulaStatus.refundTime });
      } else if (orderParticulaStatus.refundStatus == 52) {
        refundStatusArray.push({ name: '退款失败', time: orderParticulaStatus.refundTime });
      }
    }
  }

  //票务信息
  const ticketColumns: any = [
    {
      title: '票号',
      dataIndex: 'id',
      renderText: (text, record: any) => {
        if (!text) {
          return '-';
        }
        const newDom = text.replace(text.substring(2, text.length - 4), '****');

        return (
          <Tickets onTooltipId={text} orderParticular={orderParticular}>
            {newDom}
          </Tickets>
        );
      },
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
      ellipsis: true,
    },

    {
      title: '类型',
      dataIndex: 'proType',
      valueEnum: productTypeEnum,
    },
    {
      title: '票种',
      dataIndex: 'type',

      valueEnum: ticketTypeEnum,
    },
    {
      title: '人数',
      dataIndex: 'playerNum',
    },
    {
      title: '已核销总次数',
      dataIndex: 'checkedNum',
    },
    {
      title: '姓名/身份证',
      dataIndex: 'realNameList',
      width: 150,
      renderText: (text: any[]) => {
        if (isEmpty(text || [])) {
          return '-';
        }
        const { idCardName, idCardNumber } = text[0] || {};
        return (
          <Space direction="vertical" size="small" align="center">
            <span>{idCardName}</span>
            <span>{idCardNumber}</span>
            {text.length > 1 && <a onClick={() => showTicketModal(text)}>查看所有</a>}
          </Space>
        );
      },
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '检票点 / 检票设备 ',
      dataIndex: 'terminalName',
      valueEnum: equipmentTypeEnum,
      renderText: (text: any, { checkPointName, terminalName, checkType }: any) => {
        let name;
        //检票类型：0-在线检票，1-离线检票，2-后台人工检票
        if (checkType == '2') {
          name = orderParticular?.username;
        } else {
          name = terminalName;
        }
        return `${checkPointName || '-'} / ${name || '-'}`;
      },
    },
    {
      title: '售价',
      dataIndex: 'productPrice',
    },
    {
      title: '入园时间',
      dataIndex: 'enterTime',
      key: 'enterTime',
    },
    {
      title: '分时预约',
      dataIndex: 'enterBeginTime',
      render: (dom, record) => {
        return (
          <span>
            {record.enterBeginTime}-{record.enterEndTime}
          </span>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: ticketStatusEnum,
    },

    {
      title: '权益卡 ID',
      dataIndex: 'rightsId',
    },
    {
      title: '佣金（元）',
      dataIndex: 'commissionAmount',
      key: 'commissionAmount',
      align: 'right',
      render: (dom, record) => (record.commissionAmount * 1).toFixed(2),
    },
  ];

  const array = [
    ...createStatusArray,
    ...payStatusArray,
    ...ticketStatusArray,
    // ...checkStatusArray,
    ...refundStatusArray,
    ...userStatusArray,
  ];

  //NFT 流传记录
  const NFTcolumns = [
    {
      title: '交易 ID',
      dataIndex: 'proName',
      // hideInTable:true,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: 'Proposal Hash',
      dataIndex: 'proName1',
      // hideInTable:true,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '交易发起方',
      dataIndex: 'proName2',
      // hideInTable:true,
      hideInSearch: true,
    },
    {
      title: '交易接收方',
      dataIndex: 'proName3',
      // hideInTable:true,
      hideInSearch: true,
    },
    {
      title: '时间',
      dataIndex: 'proName4',
      // hideInTable:true,
      hideInSearch: true,
    },
  ];
  //NFT 流传记录数据
  const NFTData = [
    {
      proName: 'c765e920f161fb5394887913c7bd40e37e08c5b70197597abc2009e56b374527',
      proName1: 'c765e920f161fb5394887913c7bd40e37e08c5b70197597abc2009e56b374527',
      proName2: '0x0',
      proName3: 'user1',
      proName4: '2022-05-21 16:41:00',
    },
    {
      proName: 'c765e920f161fb5394887913c7bd40e37e08c5b70197597abc2009e56b374527',
      proName1: 'c765e920f161fb5394887913c7bd40e37e08c5b70197597abc2009e56b374527',
      proName2: 'user1',
      proName3: 'user2',
      proName4: '2022-05-23 15:03:06',
    },
  ];

  return (
    <>
      {' '}
      {/* <Skeleton active loading={loading}> */}
      <h3>当前订单状态：{orderTypeEnum[orderParticular.orderStatus]}</h3>
      <Steps
        style={{ margin: '20px auto' }}
        size="small"
        current={array.length !== 0 ? array.length : 0}
      >
        {orderParticulaStatus.createStatus !== null
          ? createStatusArray.map((item, index) => {
              return (
                <Step
                  key={index}
                  icon={<SolutionOutlined />}
                  title={item.name}
                  description={formatTime(item.time)}
                />
              );
            })
          : ''}
        {userStatusArray.map((item, index) => {
          return (
            <Step
              key={index}
              icon={<UserOutlined />}
              title={item.name}
              description={formatTime(item.time)}
            />
          );
        })}
        {orderParticulaStatus.payStatus !== null
          ? payStatusArray.map((item, index) => {
              return (
                <Step
                  key="index"
                  icon={<SafetyCertificateOutlined />}
                  title={item.name}
                  description={formatTime(item.time)}
                />
              );
            })
          : ''}
        {orderParticulaStatus.ticketStatus !== null
          ? ticketStatusArray.map((item, index) => {
              return (
                <Step
                  key={index}
                  title={item.name}
                  // icon={<SafetyOutlined />}
                  // icon={<SafetyOutlined />}
                  description={formatTime(item.time)}
                />
              );
            })
          : ''}

        {refundStatusArray.map((item, index) => {
          return (
            <Step
              key={index}
              icon={<SafetyCertificateOutlined />}
              title={item.name}
              description={formatTime(item.time)}
            />
          );
        })}
      </Steps>
      <Divider />
      <Descriptions title="基本信息" className="basicFont">
        <Descriptions.Item label="订单号">
          {orderParticular.orderId ? orderParticular.orderId : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="服务商">
          {orderParticular.serviceProviderName ? orderParticular.serviceProviderName : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="购票终端">
          {saleChannelEnum[orderParticular.sourceType]
            ? saleChannelEnum[orderParticular.sourceType]
            : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="代理商">
          {orderParticular.agentName ? orderParticular.agentName : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="支付时间">
          {orderParticular.payTime ? formatTime(orderParticular.payTime) : '-'}
        </Descriptions.Item>

        <Descriptions.Item label="购票数量">{total}</Descriptions.Item>

        <Descriptions.Item label="实付金额（元）">
          {orderParticular.totalAmount ? (orderParticular.totalAmount * 1).toFixed(2) : '-'}
        </Descriptions.Item>

        <Descriptions.Item label="结算订单号">
          {orderParticular.tradeNo ? orderParticular.tradeNo : '-'}
        </Descriptions.Item>

        <Descriptions.Item label="支付方式">
          {payTypeEnum[orderParticular.payType] ? payTypeEnum[orderParticular.payType] : '-'}
        </Descriptions.Item>

        <Descriptions.Item label="下单时间">
          {orderParticular.createTime ? formatTime(orderParticular.createTime) : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="佣金（元）">
          {orderParticular.commissionAmount
            ? (orderParticular.commissionAmount * 1).toFixed(2)
            : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="登录账号">
          {orderParticular.username ? orderParticular.username : '-'}
        </Descriptions.Item>

        {/* <Descriptions.Item label="售票类型">
         {orderParticular.distributorName ? orderParticular.distributorName : '-'}
       </Descriptions.Item> */}
      </Descriptions>
      <Divider />
      <Descriptions title="领票人信息" className="basicFont">
        <Descriptions.Item label="联系人">
          {orderParticular.pilotName ? orderParticular.pilotName : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="联系人电话">
          {orderParticular.pilotPhone ? orderParticular.pilotPhone : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="联系人身份证">
          {orderParticular.pilotIdentity ? orderParticular.pilotIdentity : '-'}
        </Descriptions.Item>
      </Descriptions>
      <Divider />
      <h3 className="titleFont">票务信息</h3>
      <ProTable
        {...tableConfig}
        className="order_InfoData"
        bordered={true}
        actionRef={actionRef}
        params={{ orderId }}
        columns={ticketColumns}
        request={getOrderDetail}
        pagination={
          orderType === 'JQTC'
            ? false
            : {
                defaultPageSize: 10,
              }
        }
        options={false}
        rowKey="key"
        search={false}
        summary={() => {
          let count = 0;
          (inItTicketInfo || []).map((item: any) => {
            count = count + item.productPrice;
          });
          setCount(count);

          return (
            <>
              <Table.Summary.Row>
                <Table.Summary.Cell index={1} colSpan={ticketColumns.length}>
                  合计：{countNum.toFixed(2)}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </>
          );
        }}
      />
      <ModalForm
        title="上链信息"
        visible={chainModalVisit}
        width={modelWidth.md}
        submitter={false}
        onFinish={async (val) => {
          console.log(val);
        }}
        onVisibleChange={(val) => {
          setChainModalVisit(val);
        }}
      >
        <ProDescriptions column={2}>
          <ProDescriptions.Item label="Token ID">hqskgy01</ProDescriptions.Item>
          <ProDescriptions.Item label="发行组织">chan-hqsk-ticket</ProDescriptions.Item>
          <ProDescriptions.Item label="联盟通道">200,000,000</ProDescriptions.Item>
        </ProDescriptions>
        NFT流转记录：
        <br /> <br />
        <ProTable
          bordered
          rowKey="id"
          options={false}
          search={false}
          toolBarRender={false}
          dataSource={NFTData}
          columns={NFTcolumns}
        />
      </ModalForm>
    </>
  );
};

export default OrderDetail;
