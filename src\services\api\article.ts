/**
 * 店铺导览
 */
import { request } from '@umijs/max';
import { scenicHost } from '.';
const url = (v: string) => scenicHost + v;

/** 新增文章 */
export function addArticle(data: any) {
  if (data.articleSource == 1) {
    //内链时清链接
    data.externalUrl = '';
  } else {
    //外链时清内容
    data.articleContent = '';
  }

  return request(url('/article/add'), {
    method: 'POST',
    data: {
      ...data,
      enableState: 2, // 1 禁用，2 启用
      recommendState: 1, //启用默认开，推荐默认关
    },
  });
}
/** 删除文章 */
export function deleteArticle(params: any) {
  return request(url('/article/delete/' + params.id), {
    method: 'DELETE',
  });
}
/** 禁用文章 */
export function enableArticle(data: any) {
  return request(url('/article/enable'), {
    method: 'PUT',
    data,
  });
}
/** 查看文章 */
export function infoArticle(params: any) {
  return request(url('/article/info'), { params });
}
/** 文章分页 */
export async function pageArticle(params: any) {
  const { data, code } = await request(url('/article/page'), { params });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total,
  };
}
/** 修改文章 */
export function editArticle(data: any) {
  if (data.articleSource == 1) {
    //内链时清链接
    data.externalUrl = '';
  } else {
    //外链时清内容
    data.articleContent = '';
  }
  return request(url('/article/update'), {
    method: 'PUT',
    data,
  });
}

/** 获取文章排序列表 */
export async function sortListArticle(params: any) {
  return request(url('/article/sort/list'), { params });
}

/** 修改文章排序 */
export async function editSortArticle(params: any) {
  const { msg, code } = await request(url('/article/batch/sort'), {
    method: 'PUT',
    skipCommonParams: true,
    data: params,
  });
  return {
    msg: msg,
    success: code == 20000,
  };
}
