/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-24 17:23:49
 * @LastEditTime: 2022-06-10 14:17:56
 * @LastEditors: zhangfengfei
 */

import { tableConfig } from '@/common/utils/config';
import { BusinessTypeEnum, EntryTypeEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getUniqueId } from '@/common/utils/tool';
import type { ModalState } from '@/hooks/useModal';
import { getBillDetail } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import ProDescriptions from '@ant-design/pro-descriptions';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Modal } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useState } from 'react';
import { useModel } from '@umijs/max';

interface BillDetailModalProps {
  currentItem?: API.CreditAccountItem;
  modalState: ModalState;
}

/**授信账户 - 收支明细 */
const BillDetailModal: FC<BillDetailModalProps> = ({
  currentItem,
  modalState: { visible, setVisible },
}) => {
  const { initialState } = useModel('@@initialState');
  const { settlementId = '' } = initialState?.currentCompany || {};

  const onCancel = () => setVisible(false);

  // 附加属性
  const [otherAttribute, setOtherAttribute] = useState<{ income: number; outcome: number }>();

  const tableListReq = async (params: API.BillDetailParams) => {
    const { data } = await getBillDetail(params);
    addOperationLogRequest({
      action: 'info',
      content: `查看【${currentItem?.provider?.legalName}】收支明细`,
    });

    setOtherAttribute(data.otherAttribute);
    return {
      data: data.page,
      total: data.totalNumberOfResults,
    };
  };

  const list = [
    {
      label: '指定消费商家',
      content: currentItem?.provider?.legalName,
    },
    // {
    //   label: '授信状态',
    //   content: currentItem?.status,
    // },
    {
      label: '信用余额（元）',
      content: currentItem?.balance,
    },
    {
      label: '合计收入（元）',
      content: otherAttribute?.income,
    },
    {
      label: '合计支出（元）',
      content: otherAttribute?.outcome,
    },
  ];

  const columns: ProColumns<any>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      hideInTable: true,
    },
    {
      title: '日期',
      dataIndex: 'creationDate',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '收/支',
      dataIndex: 'entryType',
      valueType: 'select',
      valueEnum: EntryTypeEnum,
    },

    {
      title: '业务类型',
      dataIndex: 'businessType',
      valueType: 'select',
      valueEnum: BusinessTypeEnum,
    },
    {
      title: '源单号',
      dataIndex: 'outTradeNo',
    },
    {
      title: '金额（元）',
      dataIndex: 'amount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '完成后余额（元）',
      dataIndex: 'afterBalance',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  return (
    <Modal
      width={1200}
      title={'信用收支明细'}
      visible={visible}
      maskClosable
      destroyOnClose
      onCancel={onCancel}
      footer={<Button onClick={onCancel}>返回</Button>}
    >
      <ProDescriptions style={{ padding: '0 24px' }} column={3}>
        {list.map((item) => (
          <ProDescriptions.Item key={item.label} label={item.label}>
            {item.content ?? '-'}
          </ProDescriptions.Item>
        ))}
      </ProDescriptions>
      <ProTable<API.BillDetailItem, API.BillDetailParams>
        {...tableConfig}
        style={{ marginTop: 32 }}
        rowKey={getUniqueId}
        options={false}
        search={false}
        params={{
          merchantId: settlementId,
          creditAccountId: currentItem?.id || '',
        }}
        pagination={{
          defaultPageSize: 10,
        }}
        request={tableListReq}
        columns={columns}
      />
    </Modal>
  );
};

export default BillDetailModal;
