import ImageUpload from '@/common/components/ImageUpload';
import MDEditor from '@/common/components/MDEditor';
import { tableConfig } from '@/common/utils/config';
import { productTypeEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getStoreGoodsTypeText } from '@/common/utils/tool';
import useModal from '@/hooks/useModal';
import StrategyModal from '@/pages/partner/saleChannel/PriceStrategy/StrategyModal';
import { getPriceStrategyByAgent } from '@/services/api/distribution';
import {
  apiComposeGoodsDetail,
  apiModifyComposeGoods,
  apiStoreGoodsAdd,
} from '@/services/api/store';
import { ArrowLeftOutlined } from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import ProForm from '@ant-design/pro-form';
import type { ProFormColumnsType } from '@ant-design/pro-form/lib/components/SchemaForm';
import SchemaForm from '@ant-design/pro-form/lib/components/SchemaForm';
import { FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Button, message, Tag } from 'antd';
import { useContext, useEffect, useState } from 'react';
import { TabKeyContext } from '..';
import GoodsDetail from '../components/ProductManage/GoodsDetail';
import Two from '../components/ProductManage/Two';
import styles from './index.less';

// 移除 id
let PriceIds: any = [];

export default ({ type, id, goodsRef, storeId, setType }: any) => {
  const getItemWidth = () => (document.querySelector('main')?.clientWidth || 80) - 80;
  const [itemWidth, setItemWidth] = useState(getItemWidth());
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [allPrice, setAllPrice] = useState<any>(0);
  const [twoDataSource, setTwoDataSource] = useState<any>([]); // 内嵌表格数据
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const [goodsItem, setGoodsItem] = useState<any>();
  const strategyModalState = useModal();

  // 商品详情
  const [goodsDetailData, setGoodsDetailData] = useState<any>({ id: '', isEnable: 0 });
  const [goodsDetailVisible, setGoodsDetailVisible] = useState<boolean>(false);
  const [goodsDetailLoading, setGoodsDetailLoading] = useState<boolean>(false);

  const editColumns: ProFormColumnsType<any>[] = [
    {
      title: '基础信息',
      valueType: 'group',
      columns: [
        {
          width: 'md',
          title: '组合商品名称',
          dataIndex: 'name',
          formItemProps: {
            rules: [{ required: true, max: 100 }],
          },
        },
        {
          width: 'md',
          title: '最大可提前预订天数',
          dataIndex: 'maxBookDays',
          valueType: 'digit',
          fieldProps: {
            min: 0,
            max: 9999,
            precision: 0,
          },
          formItemProps: {
            rules: [{ required: true }],
          },
        },
        {
          width: 'md',
          title: '集合使用',
          dataIndex: 'composeTimeSwitch',
          valueType: 'switch',
          tooltip: (
            <>
              <div>
                集合使用开关 - 关：所有组合票可分别进行选定日期适合配置一人出游多个景区的联票
              </div>
              <div>
                集合使用开关 - 开：所有组合票只可在同天使用适合配置多人同时出游一个景区的套票
              </div>
            </>
          ),
        },
        {
          width: itemWidth,
          title: '组合商品图片',
          dataIndex: 'picUrl',
          tooltip: (
            <>
              <span>最多可上传 9 张图片，每张最</span>
              <div>大为 100 M，支持 jpg、png 格式</div>
            </>
          ),
          renderFormItem: () => (
            <ProForm.Item name="picUrl">
              <ImageUpload defaultValue={dataSource?.picUrl || ''} maxCount={9} />
            </ProForm.Item>
          ),
        },
      ],
    },
    {
      title: '组合信息',
      valueType: 'group',
      columns: [
        {
          width: '100%',
          title: '',
          renderFormItem: () => (
            <EditableProTable
              {...tableConfig}
              defaultSize="small"
              rowKey="priceId"
              search={false}
              toolBarRender={() => [
                <Button
                  key="Button"
                  type="primary"
                  onClick={() => {
                    setTwoVisible(true);
                  }}
                >
                  导入商品
                </Button>,
              ]}
              columns={twoColumnsEdit}
              value={twoDataSource}
              recordCreatorProps={false}
              editable={{
                type: 'multiple',
                editableKeys,
                deleteText: <span className="del">删除</span>,
                actionRender: (row, config, defaultDoms) => {
                  return [
                    <a
                      key="show"
                      onClick={async () => {
                        // 商品详情
                        setGoodsDetailLoading(true);
                        setGoodsDetailVisible(true);
                        try {
                          // [普通票/旅游卡] 商品数据
                          const { data } = await getPriceStrategyByAgent({
                            unitId: row?.unitId,
                            distributorId: row?.supplierId,
                            priceId: row?.priceId,
                          });
                          const _datas = {
                            ...data,
                            id: data?.storeGoodsId,
                            ...(data?.price?.price || {}),
                          };
                          setGoodsDetailData(_datas);
                          setGoodsDetailLoading(false);
                        } catch (error) {
                          console.log(error);
                        }
                      }}
                    >
                      查看
                    </a>,
                    defaultDoms.delete,
                  ];
                },
                onValuesChange: (record, recordList) => {
                  // 设置总价
                  let allPrice = 0;
                  recordList.map((item: any) => {
                    allPrice += item.composePrice * (item.num ? item.num : 0);
                  });
                  setAllPrice(Math.round(allPrice * 100) / 100);
                  setTwoDataSource(recordList);
                },
                onChange: (e) => {
                  setEditableRowKeys(e);
                },
              }}
              footer={() => (
                <div className={styles.composeGoodsFooter}>总售价（元）：{allPrice}</div>
              )}
            />
          ),
        },
        {
          title: '预订须知',
          dataIndex: 'note',
          width: 688,
          renderFormItem: () => (
            <ProForm.Item name="note">
              <MDEditor />
            </ProForm.Item>
          ),
        },
      ],
    },
  ];
  const twoColumns: ProColumns[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      editable: false,
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
      search: false,
      editable: false,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      editable: false,
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      valueEnum: productTypeEnum,
      editable: false,
    },
    {
      title: '票种',
      dataIndex: 'goodsType',
      hideInSearch: true,
      editable: false,
      renderText: (text, entity) =>
        getStoreGoodsTypeText(entity.unitType, entity.storeGoodsType, entity.goodsType),
    },
    {
      title: '分时时段',
      search: false,
      editable: false,
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '购买有效时间',
      search: false,
      editable: false,
      render: (_, entity: any) =>
        entity.purchaseBeginTime && entity.purchaseEndTime
          ? entity.purchaseBeginTime + ' 至 ' + entity.purchaseEndTime
          : '-',
    },
    {
      title: '入园有效时间',
      editable: false,
      render: (_: any, record) =>
        record.dayBegin ? (
          <span>
            {record.dayBegin} 至 {record.dayEnd}
          </span>
        ) : (
          '-'
        ),
    },
    {
      title: '组合售价（元）',
      dataIndex: 'composePrice',
      editable: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '佣金比例（%）',
      dataIndex: 'commissionRate',
      editable: false,
      renderText: (dom) => (dom == 0 ? '-' : dom),
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      editable: false,
    },
    {
      title: '可用库存',
      dataIndex: 'totalNumber',
      editable: false,
    },
    {
      title: '数量',
      dataIndex: 'num',
      valueType: 'digit',
      fixed: 'right',
      fieldProps: (_: any, { entity }: any) => {
        // 是否允许成团人数控制（1：是）
        const { minPeople, maxPeople, isPeopleNumber } = entity;
        // 最大人数
        let max = entity.ticketNum || undefined;
        if (isPeopleNumber && maxPeople < entity.ticketNum) {
          max = maxPeople;
        }
        return {
          min: isPeopleNumber ? minPeople : 0,
          max,
          precision: 0,
        };
      },
      formItemProps: (form, { entity }) => {
        const { minPeople, isPeopleNumber } = entity;
        return {
          initialValue: isPeopleNumber ? minPeople : undefined,
        };
      },
    },
  ];
  const twoColumnsEdit = [
    ...twoColumns,
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
    },
  ];
  const [twoVisible, setTwoVisible] = useState<boolean>(false);

  useEffect(() => {
    const dom: any = document.querySelector('main');
    const observer = new ResizeObserver(() => setItemWidth(getItemWidth()));
    observer.observe(dom);
    return () => {
      observer.unobserve(dom);
    };
  }, []);

  const tabKey = useContext(TabKeyContext);

  return (
    <ProCard
      title={
        <>
          <ArrowLeftOutlined
            className={styles.backIcon}
            onClick={() => {
              setType('');
            }}
          />
          {type == 'edit' ? '编辑' : '新增'}组合商品
        </>
      }
      headerBordered
    >
      <SchemaForm
        layoutType="Form"
        columns={editColumns}
        params={id ? { id } : undefined}
        request={async (params) => {
          const { data } = await apiComposeGoodsDetail(params);
          setDataSource(data);
          setAllPrice(data.sellingPrice);
          //查询组合票的剩余库存的字段与新增时的字段表达不同，这里做一个转换
          data.data.forEach((item: any) => {
            item.totalNumber = item.stockQuantity;
          });
          // 编辑
          PriceIds = data.data.map((item: any) => item.priceId);
          setTwoDataSource(data.data);
          setEditableRowKeys(PriceIds);
          return data;
        }}
        submitter={{
          searchConfig: {
            submitText: '确定',
          },
          render: (_, dom) => (
            <FooterToolbar>
              {[<Button onClick={() => history.back()}>取消</Button>, dom[1]]}
            </FooterToolbar>
          ),
        }}
        onFinish={async (val) => {
          try {
            const list = twoDataSource.map((item: any) => {
              if (!item.num) {
                message.info('请输入组合商品数量');
                throw new Error('');
              }
              return {
                number: item.num,
                priceId: item.priceId,
              };
            });

            if (type == 'edit') {
              const delPriceIds = PriceIds.filter((item: any) => {
                let b = true;
                list.forEach((element: any) => {
                  if (item == element) {
                    b = false;
                  }
                });
                return b;
              });
              // 修改
              await apiModifyComposeGoods({
                delPriceIds,
                id,
                list,
                ...val,
              });

              addOperationLogRequest({
                action: 'edit',
                module: tabKey,
                content: `编辑【${val.name}】商品详情`,
              });

              message.success('修改成功');
            } else {
              // 新增
              await apiStoreGoodsAdd({
                composeGoods: {
                  list,
                  ...val,
                },
                storeId,
              });
              addOperationLogRequest({
                action: 'add',
                module: tabKey,
                content: `新增组合商品【${val.name}】`,
              });

              message.success('导入成功');
            }
            setType('');

            goodsRef?.current?.reload();
          } catch (error) {}
        }}
      />
      {/* 佣金策略 */}
      <StrategyModal
        goodsItem={goodsItem}
        modalState={strategyModalState}
        showInfo={false}
        showGroup={false}
      />
      {/* 选择可组合商品 */}
      <Two
        visible={twoVisible}
        setVisible={setTwoVisible}
        editableKeys={editableKeys}
        onfinish={(val: any) => {
          const data = [...twoDataSource, ...val];
          console.log('TTTTTTTTTTTTTTT');
          console.log(data);
          const totals: any = {};
          data.forEach((e) => {
            if (!totals[e.proId]) totals[e.proId] = 0;
            totals[e.proId]++;
          });
          data.forEach((e) => {
            e.ticketNum = Math.floor(e.totalNumber / totals[e.proId]);
          });

          setTwoDataSource(data);
          setEditableRowKeys(data.map((item: any) => item.priceId));
        }}
      />

      {/* 商品详情 */}
      <GoodsDetail
        visible={goodsDetailVisible}
        loading={goodsDetailLoading}
        setVisible={setGoodsDetailVisible}
        dataSource={goodsDetailData}
      />
    </ProCard>
  );
};
