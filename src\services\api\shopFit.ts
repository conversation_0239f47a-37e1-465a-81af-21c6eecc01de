/**
 * 店铺装修
 */
import { request } from '@umijs/max';
import { scenicHost } from '.';
const url = (v: string) => scenicHost + '/store/design' + v;

/** 删除页面 */
export function deleteFit(params: any) {
  return request(url('/delete/' + params.id), {
    method: 'DELETE',
  });
}
/** 批量删除页面 */
export function batchDeleteFit(ids: any) {
  return request(url('/batch/delete/' + ids), {
    method: 'DELETE',
  });
}
/** 页面分页 */
export async function pageFit(params: any) {
  const { data, code } = await request(url('/page'), { params });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total,
  };
}
/** 页面状态 */
export function stateFit(data: any) {
  return request(url('/state'), {
    method: 'PUT',
    data,
  });
}
