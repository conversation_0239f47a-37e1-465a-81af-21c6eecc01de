import Delete from '@/common/components/Delete';
import Disabled from '@/common/components/Disabled';
import EditPop from '@/common/components/EditPop';
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { SaleGroupTabKeyContext } from '@/pages/partner/saleChannel/Group';
import {
  apiAgentAdd,
  apiAgentGroupAdd,
  apiAgentGroupApplicationList,
  apiAgentGroupDel,
  apiAgentGroupEdit,
  apiAgentGroupList,
  apiAgentGroupStatus,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { But<PERSON>, Space, Tooltip, message } from 'antd';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Access, useAccess, useModel, useRequest } from '@umijs/max';
import Distributor from './Distributor';

const TableList = () => {
  const { initialState }: { initialState: any } = useModel('@@initialState');
  const { coId, settlementId } = initialState?.currentCompany;

  const saleGroupTabKey = useContext(SaleGroupTabKeyContext);

  // 【收款场景】下拉列表
  const downListReq = useRequest(apiAgentGroupApplicationList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      return (res.data || []).map((item: any) => ({
        value: item.code,
        label: item.name,
      }));
    },
  });

  const collectionEnum = useMemo(() => {
    return downListReq.data?.reduce((prev, cur) => {
      prev[cur.value] = cur.label;
      return prev;
    }, {});
  }, [downListReq.data]);

  const logList = [
    {
      dataIndex: 'name',
      title: '分组名称',
    },
    {
      dataIndex: 'collectionCode',
      title: '收款场景',
      valueEnum: collectionEnum,
    },
  ];

  const access = useAccess();

  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [peopleNumberB, setPeopleNumberB] = useState(false);
  const [peopleNumberA, setPeopleNumberA] = useState(false);
  const [groupId, setGroupId] = useState('');
  const [groupName, setGroupName] = useState('');
  // 【分销商列表】数据绑定
  const [distribVisible, setDistribVisible] = useState<boolean>(false);
  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      title: '分组名称',
      dataIndex: 'name',
      render: (dom, entity) => [
        dom,
        entity.id == 10000 ? (
          <Tooltip title="该分组面向普通代理商，请谨慎配置产品">
            {' '}
            <QuestionCircleOutlined />
          </Tooltip>
        ) : (
          ''
        ),
      ],
    },
    {
      title: '代理商数量',
      dataIndex: 'agentAmount',
      hideInSearch: true,
      render: (dom, entity) =>
        entity.id != 10000 ? (
          <a
            onClick={() => {
              setGroupId(entity.id);
              setGroupName(entity.name);
              setDistribVisible(true);
            }}
          >
            {dom}
          </a>
        ) : (
          '-'
        ),
    },
    {
      title: '商品配置数量',
      dataIndex: 'agentProductAmount',
      hideInSearch: true,
    },

    {
      title: '收款场景',
      dataIndex: 'collectionCode',
      hideInSearch: true,
      valueType: 'select',
      fieldProps: {
        loading: downListReq.loading,
        options: downListReq.data,
      },
    },
    {
      title: '授权状态',
      dataIndex: 'status',
      hideInSearch: true,
      renderText: (text, entity: any) =>
        entity.id != 10000 ? (
          <Disabled
            access={access.canSupplierGroup_agentGroupGpenClose}
            status={text == 1}
            params={{ id: entity.id, status: text == 1 ? 2 : 1 }}
            request={async (params) => {
              const data = await apiAgentGroupStatus(params);

              addOperationLogRequest({
                action: 'disable',
                module: saleGroupTabKey,
                content: `${text == 1 ? '禁用' : '启用'}【${entity.name}】`,
              });

              return data;
            }}
            actionRef={actionRef}
          />
        ) : (
          '-'
        ),
    },
    {
      width: 140,
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, entity) => (
        <Space size="large">
          <Access accessible={access.canSupplierGroup_agentGroupEdit}>
            <a
              onClick={async () => {
                entity.isEnable = entity.status == 1 ? 1 : 0;
                setGroupId(entity.id);
                setDataSource(entity);
                // 编辑
                setEditVisible(true);
              }}
            >
              编辑
            </a>
          </Access>
          <Delete
            access={access.canSupplierGroup_agentGroupDelete && dataSource.id != 10000}
            status={entity.status == 1}
            params={entity.id}
            request={async (params) => {
              const data = await apiAgentGroupDel(params);

              addOperationLogRequest({
                action: 'del',
                module: saleGroupTabKey,
                content: `删除【${entity.name}】`,
              });

              return data;
            }}
            actionRef={actionRef}
          />
        </Space>
      ),
    },
  ];
  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const editColumns = [
    {
      columns: [
        {
          title: '分组名称',
          dataIndex: 'name',
          formItemProps: {
            rules: [{ required: true, max: 30 }],
            // style: {
            //   height: '350px',
            // },
          },
          fieldProps: { disabled: dataSource.id == 10000 },
        },
        {
          title: '收款场景',
          dataIndex: 'collectionCode',
          valueType: 'select',
          fieldProps: {
            loading: downListReq.loading,
            options: downListReq.data,
          },
          formItemProps: {
            rules: [{ required: true }],
          },
        },
      ],
    },
  ];
  // 分销商表单
  const editFxsColumns: ProFormColumnsType<any>[] = [
    {
      columns: [
        {
          title: '类型',
          dataIndex: 'agentType',
          valueType: 'radio',
          valueEnum: { 1: '公司', 2: '个人' },
          fieldProps: {
            onChange: (e: any) => {
              if (e.target.value == 1) {
                setPeopleNumberB(false);
                setPeopleNumberA(true);
              } else {
                setPeopleNumberB(true);
                setPeopleNumberA(false);
              }
            },
            rules: [{ required: true }],
          },
          formItemProps: {
            rules: [{ required: true }],
          },
        },
        {
          hideInForm: !peopleNumberA,
          title: '统一社会信用代码',
          dataIndex: 'coCode',
          formItemProps: {
            rules: [
              { required: true },
              {
                pattern: /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/g,
                message: '请输入正确的 18 位统一社会信用代码',
              },
            ],
          },
        },
        {
          hideInForm: !peopleNumberB,
          title: '手机号',
          dataIndex: 'phone',
          formItemProps: {
            rules: [{ required: true }],
          },
        },
        // {
        //   title: '结算时间',
        //   formItemProps: {
        //     rules: [{ required: true }],
        //   },
        //   renderFormItem: (entry, text, form) => (
        //     <SettlementCycle
        //       radioName={'settlementType'}
        //       dateName={'settlementCycle'}
        //       radioLabel={''}
        //       labelCol={{ span: 0 }}
        //       wrapperCol={{ span: 24 }}
        //       formRef={form}
        //     />
        //   ),
        // },
      ],
    },
  ];

  useEffect(() => {
    downListReq.run({ merchantId: settlementId });
  }, []);

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key="addAccess" accessible={access.canSupplierGroup_agentGroupInsert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                // 初始化表单
                setDataSource({ id: '', isEnable: 0 });
                setEditVisible(true);
              }}
            >
              <PlusOutlined /> 新建
            </Button>
          </Access>,
        ]}
        params={{ distributorId: coId }}
        request={apiAgentGroupList}
        columns={columns}
      />

      {/* 添加/修改分组 */}
      <EditPop
        title="代理商分组"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/修改
        onFinish={async (val: any) => {
          if (dataSource.id) val.id = dataSource.id;
          const msgType = val.id ? '修改' : '添加';
          const hide = message.loading('正在' + msgType);
          val.distributorId = coId;
          try {
            if (dataSource.id) {
              await apiAgentGroupEdit({ ...val });

              addOperationLogRequest({
                action: 'edit',
                module: saleGroupTabKey,
                changeConfig: {
                  list: logList,
                  beforeData: dataSource,
                  afterData: val,
                },
                content: `修改【${val.name}】`,
              });
            } else {
              await apiAgentGroupAdd({ ...val });
              addOperationLogRequest({
                action: 'add',
                module: saleGroupTabKey,
                content: `新增【${val.name}】`,
              });
            }
            message.success(msgType + '成功');
            // 关闭弹窗并刷新列表
            setEditVisible(false);
            actionRef?.current?.reload();
          } catch (error) {}
          hide();
        }}
      />

      {/* 添加分销商 */}
      <EditPop
        title="代理商"
        visible={isModalVisible}
        setVisible={setIsModalVisible}
        columns={editFxsColumns}
        dataSource={dataSource}
        // 新增/修改
        onFinish={async (val: any) => {
          const hide = message.loading('正在添加');
          val.groupId = groupId;
          val.agentType = val.agentType * 1;
          try {
            await apiAgentAdd({
              ...val,
            });
            setIsModalVisible(false);
            actionRef.current?.reload();
            message.success('添加成功');
          } catch (error) {}
          hide();
        }}
      />

      {/* 分销商列表 */}
      <Distributor
        visible={distribVisible}
        setVisible={setDistribVisible}
        groupId={groupId}
        groupName={groupName}
        upActionRef={actionRef}
      />
    </>
  );
};

export default TableList;
