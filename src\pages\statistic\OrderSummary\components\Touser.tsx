/*
 * @FilePath: Touser.tsx
 * @Author: chentianya<PERSON>
 * @Date: 2022-09-29 15:25:39
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-09-30 18:01:01
 * Copyright: 2022 xxxTech CO.,LTD. All Rights Reserved.
 * @Descripttion:
 */
import { Tabs } from 'antd';
import React, { useState } from 'react';
import Byshop from './Byshop';
import Bysupplier from './Bysupplier';

const Touser: React.FC<Record<string, any>> = (props) => {
  // TAP list
  const tapList: Record<string, string>[] = [
    {
      title: '按店铺',
    },
    {
      title: '按供应商',
    },
  ];
  const [index, setIndex] = useState<number | string>(0);
  const onChange = async (key: string) => {
    setIndex(key);
  };
  return (
    <>
      <Tabs
        tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
        defaultActiveKey="0"
        onChange={onChange}
      >
        <Tabs.TabPane tab={tapList[0].title} key={0}>
          {index == 0 ? (
            <Byshop
              columnsDetail={props.columnsDetail}
              showList={props.showList}
              supplierList={props.supplierList}
            />
          ) : (
            ''
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab={tapList[1].title} key={1}>
          {index == 1 ? <Bysupplier /> : ''}
        </Tabs.TabPane>
      </Tabs>
    </>
  );
};

export default Touser;
