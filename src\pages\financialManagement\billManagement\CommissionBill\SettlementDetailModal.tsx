/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-24 16:35:25
 * @LastEditTime: 2023-09-27 10:56:50
 * @LastEditors: zhangfengfei
 */

import { tableConfig } from '@/common/utils/config';
import { payTypeEnum, saleChannelEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { downloadBlobFile } from '@/common/utils/tool';
import type { ModalState } from '@/hooks/useModal';
import { exportBillDetailDetail, getBalanceAssignmentDetail } from '@/services/api/billManage';
import type { API } from '@/services/api/typings';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useModel, useRequest } from '@umijs/max';

interface SettlementDetailModalProps {
  currentItem?: API.AgentBillItem;
  modalState: ModalState;
}

/** 佣金账单-结算明细 */
const SettlementDetailModal: FC<SettlementDetailModalProps> = ({
  currentItem,
  modalState: { visible, setVisible },
}) => {
  const { initialState } = useModel('@@initialState');
  const { settlementId = '' } = initialState?.currentCompany || {};

  const exportReq = useRequest(exportBillDetailDetail, {
    manual: true,
    formatResult: (res) => {
      downloadBlobFile(res, `结算单${currentItem?.billId || ''}明细.xls`, 'msexcel');
    },
  });
  const onExport = () => {
    if (currentItem)
      exportReq.run({
        id: currentItem.billId,
      });
  };

  const onCancel = () => setVisible(false);

  const tableListReq = async (params: { id: string }) => {
    const { data } = await getBalanceAssignmentDetail(params);
    addOperationLogRequest({
      action: 'info',
      content: `查看后结算佣金账单【${currentItem?.billId}】`,
    });
    return {
      data,
    };
  };

  const columns: ProColumns<API.AgentBillDetailItem>[] = [
    {
      title: '结算单号',
      dataIndex: 'tradeNo',
    },
    {
      title: '创建时间',
      dataIndex: 'payTime',
      renderText: (dom: any) => {
        if (dom) {
          return <span>{dayjs(dom).format('YYYY-MM-DD HH:mm:ss')}</span>;
        }
        return '-';
      },
    },
    {
      title: '购买终端',
      dataIndex: 'sourceType',
      valueType: 'select',
      valueEnum: saleChannelEnum,
    },
    {
      title: '对应订单号',
      dataIndex: 'orderId',
    },
    {
      title: '买家账号',
      dataIndex: 'userId',
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      valueType: 'select',
      valueEnum: payTypeEnum,
    },

    {
      title: '票号',
      dataIndex: 'ticketNumber',
    },
    {
      title: '佣金费率(%)',
      dataIndex: 'commissionRate',
    },
    // {
    //   title: '固定佣金（元）',
    //   dataIndex: 'commissionAmount',
    // },
    {
      title: '价格（元）',
      dataIndex: 'price',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '分佣金额（元）',
      dataIndex: 'actualComAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  return (
    <Modal
      width={1360}
      title={'结算明细详情'}
      visible={visible}
      maskClosable
      destroyOnClose
      onCancel={onCancel}
      confirmLoading={exportReq.loading}
      okText={'导出'}
      onOk={onExport}
    >
      <ProTable<API.AgentBillDetailItem, API.AgentBillDetailParams>
        {...tableConfig}
        rowKey="billId"
        headerTitle={`经销商名称：${currentItem?.name || '-'}`}
        options={false}
        search={false}
        params={{ id: currentItem?.billId || '' }}
        pagination={{
          defaultPageSize: 10,
        }}
        request={tableListReq}
        columns={columns}
      />
    </Modal>
  );
};

export default SettlementDetailModal;
