import { ProCard } from '@ant-design/pro-components';
import { useState } from 'react';
import ImproveGuide from './ImproveGuide';
import NewbieTask from './NewbieTask';

const Guide = () => {
  const [key, setKey] = useState<string>('newbieTask');

  const TabItems = [
    {
      access: true,
      key: 'newbieTask',
      label: '新手任务',
      children: <NewbieTask />,
    },
    {
      access: true,
      key: 'improveGuide',
      label: '提升建议',
      children: <ImproveGuide />,
    },
  ];

  return (
    <ProCard
      tabs={{
        items: TabItems,
        activeKey: key,
        onChange: setKey,
        destroyInactiveTabPane: true,
      }}
    />
  );
};

export default Guide;
