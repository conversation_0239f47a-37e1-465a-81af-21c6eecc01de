/*
 * @FilePath: Bysupplier.tsx
 * @Author: chent<PERSON><PERSON><PERSON>
 * @Date: 2022-09-29 15:23:37
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-10-27 20:53:07
 * Copyright: 2022 xxxTech CO.,LTD. All Rights Reserved.
 * @Descripttion:
 */
import { tableConfig } from '@/common/utils/config';
import {
  baseProductTypeEnum,
  productTypeEnum,
  ticketStatusEnum,
  ticketTypeEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { DataMaskTypeEnum } from '@/common/utils/tool';
import DataMask from '@/components/DataMask';
import { useMask } from '@/hooks/useMask';
import { ticketDetails } from '@/services/api/datareport';
import {
  byDistributor,
  distributorReceiveTicket,
  distributorRetreatTicket,
} from '@/services/api/goods';
import type { ActionType, ProColumns, RequestData } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Descriptions, Modal, Tooltip } from 'antd';
import dayjs from 'dayjs';
import QRCode from 'qrcode.react';
import React, { useRef, useState } from 'react';
import { useModel } from '@umijs/max';
import styles from '../index.less';
import Vessel from './Vessel';

const Bysupplier: React.FC = () => {
  interface IHeadInfo {
    label: string;
    value: string | number;
  }
  const actionRef = useRef<ActionType>();

  const types = [
    { title: '出票列表', inlayTitle: '出票' },
    { title: '退票列表', inlayTitle: '退票' },
  ];

  const { initialState }: any = useModel('@@initialState');

  const [visible, setVisible] = useState<boolean>(false);
  const [distributor, setDistributor] = useState<Record<string, any>>({});
  const [index, setIndex] = useState<number>(0);
  const [total, setTotal] = useState<number>(0); // 总数
  const [amount, setAmount] = useState<Record<string, any>[]>([]); //合计

  /** 详情模块 */
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [detailsWidth, setDetailsWidth] = useState<number>(modelWidth.md);
  const [rowInfo, setRowInfo] = useState<Record<string, any>>({});
  const [dates, setDates] = useState<Record<string, any>[] | null>(null); //总表时间
  const [headInfo, setHeadInfo] = useState<IHeadInfo[]>([]); // 弹窗头部信息

  // 添加数据脱敏相关 hook
  const [handleDetailsMaskChange, maskDetailsDataFn] = useMask();

  //弹框
  const circumstance = (item: any, i: number, type: number) => {
    const items = item.goodsList[i];
    console.log('circumstance', items.pro_type);
    const headInfoArr: IHeadInfo[] = [
      { label: '商品名称：', value: items.product_sku_name },
      { label: '票种：', value: ticketTypeEnum[items.ticket_type] },
      { label: '产品名称：', value: items.product_name },
      { label: '类型：', value: baseProductTypeEnum[items.pro_type * 1] },
      { label: '分时预约：', value: items.time_share },
      { label: '景区名称：', value: items.scenic_name },
      { label: '服务商：', value: items.service_provider_name },
      { label: '供应商：', value: items.distributor_name },
    ];
    setDistributor(items);
    setHeadInfo(headInfoArr);
    setIndex(type);
    setVisible(true);
  };
  const columns: ProColumns<ActionType>[] = [
    {
      title: '支付 / 退款日期',
      initialValue: [dayjs().startOf('months'), dayjs()],

      hideInForm: false,
      dataIndex: 'dates',
      key: 'dates',
      hideInSearch: false,
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '供应商',
      hideInForm: false,
      dataIndex: 'distributor_name',
      key: 'distributor_name',
      hideInSearch: false,
      hideInTable: false,
      render: (_, item: any) => <>{item.distributor_name || '-'}</>,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsList',
      key: 'product_sku_name',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.goodsList.map((n: any, i: number) => {
              return (
                <div key={i} style={n.product_sku_name === '合计' ? { fontWeight: '600' } : {}}>
                  {n.product_sku_name || '-'}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '票种',
      dataIndex: 'goodsList',
      key: 'ticket_type',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.goodsList.map((n: any, i: number) => {
              return (
                <div key={i} style={n.product_sku_name === '合计' ? { visibility: 'hidden' } : {}}>
                  {ticketTypeEnum[n.ticket_type] || '-'}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '产品名称',
      dataIndex: 'goodsList',
      key: 'product_name',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.goodsList.map((n: any, i: number) => {
              return (
                <div key={i} style={n.product_sku_name === '合计' ? { visibility: 'hidden' } : {}}>
                  {n.product_name || '-'}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '类型',
      dataIndex: 'goodsList',
      key: 'pro_type',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.goodsList.map((n: any, i: number) => {
              return (
                <div key={i} style={n.product_sku_name === '合计' ? { visibility: 'hidden' } : {}}>
                  {baseProductTypeEnum[n.pro_type * 1] || '-'}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '分时预约',
      dataIndex: 'goodsList',
      key: 'time_share',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.goodsList.map((n: any, i: number) => {
              return (
                <div key={i} style={n.product_sku_name === '合计' ? { visibility: 'hidden' } : {}}>
                  {n.time_share || '-'}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '出票数',
      dataIndex: 'goodsList',
      key: 'chu_tickets',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.goodsList.map((n: any, i: number) => {
              return n.product_sku_name === '合计' ? (
                <div key={i}>{n.chu_tickets * 1}</div>
              ) : (
                <div
                  style={{ color: '#1890ff', cursor: 'pointer' }}
                  onClick={() => {
                    circumstance(item, i, 0);
                  }}
                  key={i}
                >
                  {n.chu_tickets * 1}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '实付金额',
      dataIndex: 'goodsList',
      key: 'pay_amounts',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.goodsList.map((n: any, i: number) => {
              return <div key={i}>{n.pay_amounts * 1}</div>;
            })}
          </div>
        );
      },
    },
    {
      title: '退票数',
      dataIndex: 'goodsList',
      key: 're_ticket',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.goodsList.map((n: any, i: number) => {
              return n.product_sku_name === '合计' ? (
                <div key={i}>{n.re_ticket}</div>
              ) : (
                <div
                  style={{ color: '#1890ff', cursor: 'pointer' }}
                  onClick={() => {
                    circumstance(item, i, 1);
                  }}
                  key={i}
                >
                  {n.re_ticket * 1}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '退款金额',
      dataIndex: 'goodsList',
      key: 're_amounts',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.goodsList.map((n: any, i: number) => {
              return <div key={i}>{n.re_amounts * 1}</div>;
            })}
          </div>
        );
      },
    },
  ];
  // 弹窗列表选项
  const details = async (item: any) => {
    console.log(item, 'item');
    //门票详情
    try {
      const { code, data } = await ticketDetails(item.ticket_number);
      if (code == 20000) {
        setRowInfo(data);
      }
      setDetailsVisible(true);
    } catch (err) {
      console.log(err);
    }
  };

  const childColumns: ProColumns<ActionType>[][] = [
    [
      //出票
      {
        title: '票号',
        key: 'ticket_number',
        dataIndex: 'ticket_number',
        render: (_: any, item: any) => {
          if (!item.ticket_number) {
            return '-';
          }
          const ticket_number = item.ticket_number?.replace(
            item.ticket_number.substring(2, item.ticket_number.length - 4),
            '****',
          );
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              <Tooltip title={item.ticket_number}>{ticket_number}</Tooltip>
            </div>
          );
        },
      },
      {
        title: '订单号',
        key: 'order_id',
        dataIndex: 'order_id',
      },
      {
        title: '入园时间',
        key: 'day',
        dataIndex: 'day',
        valueType: 'dateRange',
        hideInSearch: true,
        render: (_, item: any) => <>{item.day.split('.')[0]}</>,
      },
      {
        title: '实付金额',
        key: 'product_price',
        dataIndex: 'product_price',
        hideInSearch: true,
      },
      {
        title: '佣金',
        key: 'actual_com_amount',
        dataIndex: 'actual_com_amount',
        hideInSearch: true,
      },
      {
        title: '支付时间',
        key: 'paytime',
        dataIndex: 'paytime',
        valueType: 'dateRange',
        initialValue: dates,
        render: (_, item: any) => <>{item.paytime.split('.')[0]}</>,
      },
    ],
    [
      //退票
      {
        title: '票号',
        key: 'ticket_number',
        dataIndex: 'ticket_number',
        render: (_: any, item: any) => {
          if (!item.ticket_number) {
            return '-';
          }
          const ticket_number = item.ticket_number?.replace(
            item.ticket_number.substring(2, item.ticket_number.length - 4),
            '****',
          );
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              <Tooltip title={item.ticket_number}>{ticket_number}</Tooltip>
            </div>
          );
        },
      },
      {
        title: '退单号',
        key: 'order_id',
        dataIndex: 'order_id',
      },
      {
        title: '入园时间',
        key: 'day',
        dataIndex: 'day',
        valueType: 'dateRange',
        hideInSearch: true,
        render: (_, item: any) => <>{item.day.split('.')[0]}</>,
      },
      {
        title: '退款金额',
        key: 'product_price',
        dataIndex: 'product_price',
        hideInSearch: true,
      },
      {
        title: '退佣金额',
        key: 'actual_com_amount',
        dataIndex: 'actual_com_amount',
        hideInSearch: true,
      },
      {
        title: '退款时间',
        key: 'paytime',
        dataIndex: 'paytime',
        valueType: 'dateRange',
        initialValue: dates,
        render: (_, item: any) => <>{item.paytime.split('.')[0]}</>,
      },
    ],
  ];

  const request =
    (fn: any, type: string) =>
    async (q: Record<string, any>): Promise<Partial<RequestData<ActionType>>> => {
      console.log('q', q);
      const params: any = {};
      if (type == 'receiveTicket') {
        params.start_year_month_day_pay = q.paytime ? q.paytime[0] : null;
        params.end_year_month_day_pay = q.paytime ? q.paytime[1] : null;
        params.order_id = q.order_id ? q.order_id : null;
      } else if (type == 'retreatTicket') {
        params.start_year_month_day = q.paytime ? q.paytime[0] : null;
        params.end_year_month_day = q.paytime ? q.paytime[1] : null;
        params.refund_id = q.order_id ? q.order_id : null;
      }
      const obj = {
        conf: {
          order_product_id: distributor.order_product_id,
          agent_id: initialState.currentCompany.coId,
          product_name: distributor.product_name,
          ticket_type: distributor.ticket_type,
          ticket_number: q.ticket_number ? q.ticket_number : null,
          pro_type: distributor.pro_type,
          time_share: distributor.time_share,
          scenic_name: distributor.scenic_name,
          service_provider_id: distributor.service_provider_id,
          distributor_id: distributor.distributor_id,
          m: q.pageSize, //条数
          n: q.current, //页码
          ...params,
        },
      };

      const data: any = [];
      try {
        const { result } = await fn(obj);
        setAmount([]);
        setTotal(0);
        result.map((_n: any, i: any) => {
          if (_n.ticket_number == '合计') {
            let amount: Record<string, any>[] = [];
            if (type == 'receiveTicket') {
              amount = [
                { label: '实付金额：', value: `${(_n.product_price * 1).toFixed(2)}` },
                { label: '佣金：', value: `${(_n.actual_com_amount * 1).toFixed(2)}` },
              ];
            }
            if (type == 'retreatTicket') {
              amount = [
                { label: '退款金额：', value: `${(_n.product_price * 1).toFixed(2)}` },
                { label: '退佣金额：', value: `${(_n.actual_com_amount * 1).toFixed(2)}` },
              ];
            }
            setAmount(amount);
            if (_n.total != 0) {
              setTotal(_n.total);
            }
          } else {
            data.push(_n);
          }
        });
      } catch (err) {
        console.log(err);
      }
      return await new Promise((resolve, reject) => resolve({ data, total: total }));
    };

  const requests: any[] = [
    request(distributorReceiveTicket, 'receiveTicket'),
    request(distributorRetreatTicket, 'retreatTicket'),
  ];

  return (
    <>
      <ProTable<ActionType, ProColumns>
        {...tableConfig}
        bordered
        actionRef={actionRef}
        rowKey="key"
        // options={{ setting: false, density: false }}
        // search={{ labelWidth: 'auto' }}
        request={async (params: Record<string, any>): Promise<Partial<RequestData<any>>> => {
          const obj: Record<string, any> = {
            conf: {
              order_product_id: null,
              agent_id: initialState.currentCompany.coId,
              distributor_name: params.distributor_name ? params.distributor_name : null,
              product_name: null,
              ticket_type: null,
              time_share: null,
              scenic_id: null,
              service_provider_id: null,
              distributor_id: null,
              order_id: null,
              start_year_month_day: params.dates ? params.dates[0] : null,
              end_year_month_day: params.dates ? params.dates[1] : null,
            },
          };
          setDates(params.dates ? params.dates : null);
          const { result } = await byDistributor(obj);
          addOperationLogRequest({
            action: 'info',
            content: '查看面向用户订单 - 按供应商汇总',
          });
          const data: any = {
            success: true,
            total: 0,
          };
          const clash = {};
          // 根据供应商 id 把数据分组，便于计算合计
          result.forEach((e: Record<string, any>) => {
            if (!clash[e.distributor_id]) {
              clash[e.distributor_id] = [];
            }
            clash[e.distributor_id].push(e);
          });
          const arr: any = [];
          // 把需要计算合计的字段单独分开，便于计算和在列表中展示
          Object.values(clash).forEach((items: any) => {
            // 同一组基本数据都一样，区别在于代理商有多个，所以只需要拿到数组第一条作为基本数据，再拼接需要合并数组即可
            arr.push({
              distributor_name: items[0].distributor_name,
              distributor_id: items[0].distributor_id,
              goodsList: items,
            });
          });
          // 前端计算合计
          arr.forEach((e: Record<string, any>) => {
            e.goodsList.push({
              product_sku_name: '合计',
              chu_tickets: e.goodsList
                .map((i: Record<string, any>) => i.chu_tickets * 1)
                .reduce((c: number, v: number) => c + v),
              pay_amounts: e.goodsList
                .map((i: Record<string, any>) => i.pay_amounts * 1)
                .reduce((c: number, v: number) => c + v)
                .toFixed(2),
              re_ticket: e.goodsList
                .map((i: Record<string, any>) => i.re_ticket * 1)
                .reduce((c: number, v: number) => c + v),
              re_amounts: e.goodsList
                .map((i: Record<string, any>) => i.re_amounts * 1)
                .reduce((c: number, v: number) => c + v)
                .toFixed(2),
            });
          });
          data.data = arr;
          data.total = arr.length;
          return await new Promise((resolve, reject) => resolve(data));
        }}
        columns={columns}
      />

      {/* 弹窗 */}
      <Vessel
        type={types[index]?.title}
        width={modelWidth.xl}
        total={total}
        visible={visible}
        setVisible={setVisible}
        columns={childColumns[index]}
        amount={amount}
        request={requests[index]}
        headInfo={headInfo}
        setAmount={setAmount}
      />

      {/* 细节弹窗  */}
      <Modal
        title={
          <>
            <span>{`${types[index]?.inlayTitle}详情`}</span>
            <DataMask
              onDataMaskChange={handleDetailsMaskChange}
              logContent="查看【面向用户-供应商-票详情】用户隐私信息"
            />
          </>
        }
        visible={detailsVisible}
        width={detailsWidth}
        destroyOnClose={true}
        footer={
          <>
            <Button onClick={() => setDetailsVisible(false)}>取消</Button>
          </>
        }
        onCancel={async () => {
          setDetailsVisible(false);
        }}
      >
        {/* 门票详情 */}
        <Descriptions title="基础信息" column={2}>
          <Descriptions.Item label="票号">{rowInfo.id}</Descriptions.Item>
          <Descriptions.Item label="服务商名称">{rowInfo.providerName}</Descriptions.Item>
          <Descriptions.Item label="产品名称">{rowInfo.proName}</Descriptions.Item>
          <Descriptions.Item label="产品类型">{productTypeEnum[rowInfo.proType]}</Descriptions.Item>
          <Descriptions.Item label="商品名称">{rowInfo.goodsName}</Descriptions.Item>
          <Descriptions.Item label="票种">{ticketTypeEnum[rowInfo.goodsType]}</Descriptions.Item>
          <Descriptions.Item label="购票人姓名">
            {maskDetailsDataFn(rowInfo.buyerName, DataMaskTypeEnum.NAME)}
          </Descriptions.Item>
          <Descriptions.Item label="购票人身份证号">
            {maskDetailsDataFn(rowInfo.buyerId, DataMaskTypeEnum.ID_CARD)}
          </Descriptions.Item>
          <Descriptions.Item label="入园时间">{rowInfo.enterDate}</Descriptions.Item>
          <Descriptions.Item label="状态">{ticketStatusEnum[rowInfo.status]}</Descriptions.Item>
          <Descriptions.Item label="出票时间">{rowInfo.createTime}</Descriptions.Item>
          <Descriptions.Item label="订单号">{rowInfo.orderId}</Descriptions.Item>
          <Descriptions.Item label="二维码">
            <QRCode
              style={{ margin: '0px 0px', position: 'relative' }}
              value={rowInfo.printStr} //value 参数为生成二维码的链接 我这里是由后端返回
              size={120} //二维码的宽高尺寸
              fgColor="#000000" //二维码的颜色
            />
          </Descriptions.Item>
          <Descriptions.Item label="景区名称">{rowInfo.scenicName}</Descriptions.Item>
        </Descriptions>
      </Modal>
    </>
  );
};

export default Bysupplier;
