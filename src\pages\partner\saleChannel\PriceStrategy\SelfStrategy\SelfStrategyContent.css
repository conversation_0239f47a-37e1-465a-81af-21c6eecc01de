.container {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
}

.section-indicator {
  width: 4px;
  height: 20px;
  background-color: #1890ff;
  border-radius: 4px;
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-right: 12px;
}

.tag-enabled {
  display: flex;
  align-items: center;
  background-color: #E6F7FF;
  border-radius: 20px;
  padding: 2px 12px;
  font-size: 12px;
  color: #1890ff;
  height: 24px;
}

.tag-disabled {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 20px;
  padding: 2px 12px;
  font-size: 12px;
  color: #999999;
  height: 24px;
}

.tag-dot {
  width: 6px;
  height: 6px;
  background-color: #1890ff;
  border-radius: 50%;
  margin-right: 6px;
  display: inline-block;
}

.tag-disabled .tag-dot {
  background-color: #999999;
}

.edit-link {
  color: #1890ff;
  cursor: pointer;
}

.strategy-content {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.price-update-alert {
  display: flex;
  align-items: center;
  cursor: pointer;
  background: #FFF7E6;
  padding: 2px 12px;
  border-radius: 16px;
  height: 24px;
  font-size: 12px;
}

.alert-icon {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #FAAD14;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 8px;
  font-size: 12px;
  font-weight: bold;
}

.alert-text {
  color: #000000;
  margin-right: 5px;
}

.calendar-container {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.calendar-nav {
  display: flex;
  align-items: center;
}

.calendar-date {
  margin: 0 8px;
}

.calendar-actions button {
  
  margin-left: 8px;
}

/* 新增日历布局样式 */
.calendar-layout {
  display: flex;
}

/* 表格样式 */
.calendar-table {
  width: 100%;
  border-collapse: collapse;
  border-top: 1px solid #f0f0f0;
  margin-bottom: 16px;
  table-layout: fixed;
}

.calendar-table th {
  padding: 8px;
  text-align: right;
  border: 1px solid #f0f0f0;
  border-top: none;
  height: 41px;
}

.calendar-table td {
  border: 1px solid #f0f0f0;
  padding: 8px;
  height: 100px;
  vertical-align: top;
}

/* 左侧边栏样式 */
.sidebar-cell {
  background-color: #f9f9f9;
  width: 120px;
  border-right: 1px solid #f0f0f0;
}

.sidebar-header {
  height: 100%;
}

.sidebar-row {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sidebar-switch {
  height: 32px;
  color: #666;
  font-size: 14px;
  margin-top: 24px;
}

.sidebar-price {
  height: 32px;
  color: #666;
  font-size: 14px;
  margin-top: 8px;
}

/* 日历单元格样式 */
.calendar-cell {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.date-number {
  text-align: right;
  font-size: 16px;
  font-weight: 500;
  height: 24px;
}

.other-month {
  color: #d9d9d9;
  font-weight: normal;
}

.today {
  background-color: #e6f7ff;
  border-radius: 4px;
  font-weight: bold;
}

.cell-switch {
  text-align: center;
}

.cell-price {
  height: 32px;
  text-align: center;
  color: #1890ff;
  font-size: 14px;
}

/* 价格悬停效果样式 */
.price-wrapper {
  position: relative;
  cursor: pointer;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}

.price-text {
  display: block;
}

.price-hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 4px;
}

.full-cell-hover-overlay {
  position: absolute;
  top: -72px; /* 向上扩展覆盖整个单元格 */
  left: -8px; /* 调整左边距 */
  width: calc(100% + 16px); /* 调整宽度 */
  height: calc(100% + 80px); /* 调整高度覆盖整个单元格 */
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 4px;
  z-index: 1;
}

.price-wrapper:hover .price-hover-overlay,
.price-wrapper:hover .full-cell-hover-overlay {
  opacity: 1;
}

.setting-text {
  font-size: 14px;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.strategy-config {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
}

.float-right {
  margin-left: auto;
}

.combo-price-container {
  margin-top: 16px;
}

.combo-price-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.combo-price-info .ant-typography {
  margin-right: 8px;
}

.combo-price-info .edit-btn {
  padding: 0;
  margin-left: auto;
}

/* 组合销售弹窗样式 */
.combo-sale-container {
  margin-bottom: 16px;
}

.combo-sale-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.combo-sale-switch {
  margin-left: 8px;
}

.combo-sale-badge {
  margin-left: 10px;
  background-color: #FFF7E6;
  border-radius: 50px;
  padding: 2px 10px;
  color: #FF9800;
  font-weight: bold;
}

.combo-sale-field {
  margin-bottom: 10px;
}

.combo-sale-label {
  display: inline-block;
  width: 80px;
}

.combo-sale-input {
  width: 180px;
}

/* 添加日期选择器的样式 */
.calendar-date-picker {
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  color: #333;
  width: auto !important;
  text-align: center;
}

.calendar-date-picker .ant-picker-input {
  display: flex;
  justify-content: center;
}

.calendar-date-picker .ant-picker-input > input {
  cursor: pointer;
  text-align: center;
}

/* 自定义月份选择下拉样式 */
.month-picker-dropdown .ant-picker-panel {
  border-radius: 8px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}

.month-picker-dropdown .ant-picker-header {
  border-bottom: none;
  padding: 12px 8px;
}

.month-picker-dropdown .ant-picker-header-view {
  font-weight: bold;
}

.month-picker-dropdown .ant-picker-content {
  padding: 0 8px 8px;
}

.month-picker-dropdown .ant-picker-cell {
  padding: 4px 0;
}

.month-picker-dropdown .ant-picker-cell-in-view {
  color: #333;
}

.month-picker-dropdown .ant-picker-cell-selected .ant-picker-cell-inner {
  background-color: #1890ff;
  color: #fff;
  border-radius: 4px;
}

.month-picker-dropdown .ant-picker-cell-inner {
  padding: 4px 0;
  border-radius: 4px;
}

.month-picker-dropdown .ant-picker-cell:hover .ant-picker-cell-inner {
  background-color: #f5f5f5;
}

/* 日历禁用状态 */
.disabled-calendar .calendar-cell.disabled {
  cursor: not-allowed;
  opacity: 0.8;
}

.disabled-calendar .price-hover-overlay {
  display: none;
}

/* 过去日期样式 */
.calendar-cell.past-date {
  background-color: #f9f9f9;
}

.calendar-cell.past-date .cell-switch .ant-switch {
  opacity: 0.5;
  cursor: not-allowed;
}

.calendar-cell.past-date .price-text.past-price {
  color: #999;
}

.calendar-cell.past-date .price-hover-overlay {
  display: none;
} 