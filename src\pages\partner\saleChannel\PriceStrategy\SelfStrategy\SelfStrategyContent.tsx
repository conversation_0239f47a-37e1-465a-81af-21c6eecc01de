import React, { useRef, useState, useEffect } from 'react';
import {
  Typography,
  Row,
  Col,
  Space,
  Tag,
  Tooltip,
  Button,
  Calendar,
  Badge,
  Switch,
  Modal,
  Input,
  DatePicker,
  Table,
  InputNumber,
} from 'antd';
import {
  InfoCircleOutlined,
  LeftOutlined,
  RightOutlined,
  MoreOutlined,
  EditOutlined,
} from '@ant-design/icons';
import './SelfStrategyContent.css';
import useModal from '@/hooks/useModal';
import DynamicPriceStrategyModal from './DynamicPriceStrategyModal';
import PriceSettingModal from './PriceSettingModal';
import { useModel } from '@umijs/max';
import type { ActionType } from '@ant-design/pro-table';
import dayjs from 'dayjs';
import type { API } from '@/services/api/typings';
import { toggleSelfPriceEnable } from '@/services/api/distribution';
import { ticketTypeEnum } from '@/common/utils/enum';
import { ceil } from 'lodash';
import GoodsTable from './components/GoodsTable';

const { Text } = Typography;

/**
 * 日历日期项目接口
 */
interface CalendarDateItem {
  date: dayjs.Moment;
  day: number;
  month: 'prev' | 'current' | 'next';
  isToday: boolean;
}

/**
 * 销售日期动态列表项目接口
 */
interface SaleDateDynamicsItem {
  salePrice: number; // 单买价格
  isEnable: boolean; // 开关状态
  saleDate: string; // 日期，格式为 YYYY-MM-DD
  agentPriceId: string; // 价格 ID
  id: string; // 主键 ID
}

/**
 * 模拟接口数据
 */
interface StrategyData {
  isEnabled: boolean;
  weatherStrategy: {
    isEnabled: boolean;
    condition: string;
    changeRate: number;
    isIncrease: boolean;
  };
  salesStrategy: {
    isEnabled: boolean;
    condition: string;
    changeRate: number;
    isIncrease: boolean;
  };
  costPrinciple: {
    isEnabled: boolean;
    content: string;
  };
  updateMethod: {
    type: string;
  };
}

/**
 * 模拟接口数据请求函数
 */
const fetchMockStrategyData = (): Promise<StrategyData> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        isEnabled: false,
        weatherStrategy: {
          isEnabled: true,
          condition: '当平均气温大于等于 35℃时',
          changeRate: 20,
          isIncrease: true,
        },
        salesStrategy: {
          isEnabled: true,
          condition: '当日销售量为日常销量的 15% 时',
          changeRate: 20,
          isIncrease: false,
        },
        costPrinciple: {
          isEnabled: true,
          content: '价格不低于进货价 20%',
        },
        updateMethod: {
          type: '手动更新',
        },
      });
    }, 500);
  });
};
// 组件 Props 类型
interface NormalPriceContentProps {
  priceData?: API.SelfPriceStrategyItem & {
    price: {
      saleDateDynamicsList?: SaleDateDynamicsItem[];
    };
  };
  onSubmit?: (data: API.SelfPriceStrategyItem) => void;
}
/**
 * 直销价格策略内容区域组件
 */
const SelfStrategyContent: React.FC<NormalPriceContentProps> = ({ priceData, onSubmit }) => {
  // 添加 modal 状态控制
  const dynamicPriceStrategyModalState = useModal();
  // 添加 actionRef 用于刷新数据
  const actionRef = useRef<ActionType>();
  // 获取当前商品信息
  const { initialState } = useModel('@@initialState');
  // 从本地存储获取商品信息
  const goodsItem = React.useMemo(() => {
    const cachedItem = localStorage.getItem('priceStrategyItem');
    return cachedItem ? JSON.parse(cachedItem) : null;
  }, []);

  // 添加策略数据状态
  const [strategyData, setStrategyData] = useState<StrategyData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // 添加日历当前月份状态
  const [currentDate, setCurrentDate] = useState<dayjs.Moment>(dayjs());

  // 添加日历编辑模式状态
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  // 添加价格设置弹窗状态
  const [priceModalVisible, setPriceModalVisible] = useState<boolean>(false);
  const [selectedDate, setSelectedDate] = useState<dayjs.Moment | null>(null);

  // 添加组合销售弹窗状态
  const [comboSaleModalVisible, setComboSaleModalVisible] = useState<boolean>(false);
  const [isComboEnabled, setIsComboEnabled] = useState<boolean>(false);

  // 组合价格
  const [combinedPrice, setCombinedPrice] = useState<number>(0);
  const [combinedPricePercent, setCombinedPricePercent] = useState<number>(100);

  // 添加弹窗临时状态变量
  const [tempIsComboEnabled, setTempIsComboEnabled] = useState<boolean>(false);
  const [tempCombinedPrice, setTempCombinedPrice] = useState<number>(0);
  const [tempCombinedPricePercent, setTempCombinedPricePercent] = useState<number>(100);

  // 编辑状态
  const [isEditing, setIsEditing] = useState(true);

  // 添加销售日期动态列表状态
  const [saleDateDynamics, setSaleDateDynamics] = useState<SaleDateDynamicsItem[]>([]);
  // 添加加载状态
  const [switchLoading, setSwitchLoading] = useState<Record<string, boolean>>({});

  // 添加批量设置弹窗状态
  const [batchSettingVisible, setBatchSettingVisible] = useState<boolean>(false);

  // 添加批量设置时使用的默认时间类型
  const [priceModalTimeType, setPriceModalTimeType] = useState<'按星期' | '按日期'>('按日期');

  // 获取模拟数据
  useEffect(() => {
    setLoading(true);
    fetchMockStrategyData()
      .then((data) => {
        setStrategyData(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // 处理销售日期动态列表数据
  useEffect(() => {
    console.log('priceData', priceData);
    const saleDateDynamicsList = priceData?.price?.saleDateDynamicsList || [];
    setSaleDateDynamics(saleDateDynamicsList);
  }, [priceData]);

  // 初始化组合销售数据
  useEffect(() => {
    if (goodsItem && priceData && priceData.price) {
      const marketPrice = goodsItem.marketPrice || 0;
      const { isCompose, composePrice } = priceData.price;
      const composeDiscount = (priceData.price as any).composeDiscount;

      // 设置组合销售启用状态
      setIsComboEnabled(isCompose || false);

      if (composePrice) {
        setCombinedPrice(composePrice);
        // 计算折扣百分比
        const discount = Math.round((composePrice / marketPrice) * 100);
        setCombinedPricePercent(discount);
      } else if (composeDiscount) {
        setCombinedPricePercent(composeDiscount * 100);
        setCombinedPrice(parseFloat((marketPrice * composeDiscount).toFixed(2)));
      } else {
        setCombinedPrice(marketPrice);
        setCombinedPricePercent(100);
      }
    }
  }, [goodsItem, priceData]);

  // 根据日期获取销售日期动态项
  const getSaleDateDynamicItem = (date: dayjs.Moment): SaleDateDynamicsItem | undefined => {
    const dateStr = date.format('YYYY-MM-DD');
    return saleDateDynamics.find((item) => item.saleDate === dateStr);
  };

  // 处理月份切换
  const handlePrevMonth = () => {
    setCurrentDate(currentDate.clone().subtract(1, 'month'));
  };

  const handleNextMonth = () => {
    setCurrentDate(currentDate.clone().add(1, 'month'));
  };

  // 格式化显示日期
  const formattedDate = currentDate.format('YYYY 年 MM 月');

  // 生成日历数据
  const generateCalendarData = () => {
    const year = currentDate.year();
    const month = currentDate.month() + 1; // 0-11

    // 获取当月的第一天和最后一天
    const firstDayOfMonth = dayjs([year, month, 1]);
    const lastDayOfMonth = dayjs(firstDayOfMonth).endOf('month');
    console.log('当月的第一天', firstDayOfMonth.format('YYYY-MM-DD'));
    console.log('当月的最后一天', lastDayOfMonth.format('YYYY-MM-DD'));
    // 获取当月天数
    const daysInMonth = lastDayOfMonth.date();

    // 获取当月第一天是星期几 (0-6, 0 是周日)
    const firstDayOfWeek = firstDayOfMonth.day();

    // 获取上个月的信息
    const prevMonth = month === 0 ? 11 : month - 1;
    const prevYear = month === 0 ? year - 1 : year;
    const daysInPrevMonth = dayjs([prevYear, prevMonth]).daysInMonth();

    // 获取下个月的信息
    const nextMonth = month === 11 ? 0 : month + 1;
    const nextYear = month === 11 ? year + 1 : year;

    // 生成日历数据（6 行 7 列的二维数组）
    const calendarData: CalendarDateItem[][] = [];
    let day = 1;
    let nextMonthDay = 1;

    // 通常日历显示 6 周，确保完整显示当月和相邻月份的日期
    for (let week = 0; week < 6; week++) {
      const weekData: CalendarDateItem[] = [];

      for (let dayOfWeek = 0; dayOfWeek < 7; dayOfWeek++) {
        // 第一周的情况
        if (week === 0 && dayOfWeek < firstDayOfWeek) {
          // 显示上个月的日期
          const prevMonthDate = daysInPrevMonth - (firstDayOfWeek - dayOfWeek - 1);
          weekData.push({
            date: dayjs([prevYear, prevMonth, prevMonthDate]),
            day: prevMonthDate,
            month: 'prev',
            isToday: dayjs([prevYear, prevMonth, prevMonthDate]).isSame(dayjs(), 'day'),
          });
        }
        // 当月日期用完的情况
        else if (day > daysInMonth) {
          // 显示下个月的日期
          weekData.push({
            date: dayjs([nextYear, nextMonth, nextMonthDay]),
            day: nextMonthDay,
            month: 'next',
            isToday: dayjs([nextYear, nextMonth, nextMonthDay]).isSame(dayjs(), 'day'),
          });
          nextMonthDay++;
        }
        // 当月的日期
        else {
          weekData.push({
            date: dayjs([year, month, day]),
            day: day,
            month: 'current',
            isToday: dayjs([year, month, day]).isSame(dayjs(), 'day'),
          });
          day++;
        }
      }

      calendarData.push(weekData);

      // 如果已经显示了所有当月日期且下个月的日期也已经占满一行，可以提前结束
      if (day > daysInMonth && week >= 4) {
        // 检查是否最后一行全是下个月的日期
        const allNextMonth = weekData.every((date) => date.month === 'next');
        if (allNextMonth) {
          break;
        }
      }
    }

    return calendarData;
  };

  // 获取日历数据
  const calendarData = generateCalendarData();

  // 处理价格设置弹窗打开
  const handleOpenPriceModal = (date: dayjs.Moment) => {
    setSelectedDate(date);
    setPriceModalTimeType('按日期');

    // 获取选中日期的价格信息
    const dynamicItem = getSaleDateDynamicItem(date);
    console.log('dynamicItem===');
    console.log(dynamicItem);
    // 创建临时价格数据对象
    const tempPriceData: API.SelfPriceStrategyItem = {
      priceId: '',
      price: {
        commissionRate: 0,
        composePrice: 0,
        isCompose: false,
        salePrice: 0,
        // 如果有价格信息，添加到临时对象中
        ...(dynamicItem
          ? {
              salePrice: dynamicItem.salePrice,
              saleDateDynamicsList: [dynamicItem],
            }
          : {
              // 如果没有价格信息，添加空的动态价格列表
              saleDateDynamicsList: [],
            }),
      } as any,
    };

    // // 更新价格数据状态
    // if (onSubmit) {
    //   onSubmit(tempPriceData);
    // }

    setPriceModalVisible(true);
  };

  // 处理价格设置弹窗关闭
  const handleClosePriceModal = () => {
    setPriceModalVisible(false);
    setSelectedDate(null);
  };

  // 处理组合销售弹窗打开
  const handleOpenComboSaleModal = () => {
    setIsEditing(true);
    // 初始化弹窗临时状态为当前值
    setTempIsComboEnabled(isComboEnabled);
    setTempCombinedPrice(combinedPrice);
    setTempCombinedPricePercent(combinedPricePercent);
    setComboSaleModalVisible(true);
  };

  // 处理组合销售弹窗关闭
  const handleCloseComboSaleModal = () => {
    setComboSaleModalVisible(false);
  };

  // 处理组合价格变化 - 更新临时状态
  const handleCombinedPriceChange = (value: number | null) => {
    if (value === null) return;

    setTempCombinedPrice(value);

    // 计算折扣百分比
    if (goodsItem?.marketPrice > 0) {
      const discount = Math.round((value / goodsItem.marketPrice) * 100);
      setTempCombinedPricePercent(discount);
    }
  };

  // 处理组合价格百分比变化 - 更新临时状态
  const handleCombinedPercentChange = (value: number | null) => {
    if (value === null) return;

    setTempCombinedPricePercent(value);

    // 计算实际价格
    if (goodsItem?.marketPrice) {
      const price = parseFloat(((goodsItem.marketPrice * value) / 100).toFixed(2));
      setTempCombinedPrice(price);
    }
  };

  // 处理组合销售弹窗确认
  const handleConfirmComboSale = () => {
    // 从临时状态更新到主状态
    setIsComboEnabled(tempIsComboEnabled);
    setCombinedPrice(tempCombinedPrice);
    setCombinedPricePercent(tempCombinedPricePercent);

    // 价格数据
    const priceData: any = {
      isCompose: tempIsComboEnabled,
    };

    // 只有在启用组合销售时才添加组合价格相关字段
    if (tempIsComboEnabled) {
      priceData.composePrice = tempCombinedPrice;
      priceData.composeDiscount = ceil(tempCombinedPricePercent / 100, 4);
      priceData.commissionRate = goodsItem?.commissionRate || 0;
    }

    // 调用父组件传递的 onSubmit 回调
    if (onSubmit) {
      onSubmit({
        priceId: '',
        price: priceData,
      });
    }

    setComboSaleModalVisible(false);
  };

  console.log('priceDatdddda', priceData);

  useEffect(() => {
    console.log('priceData', priceData);
    const saleDateDynamicsList = priceData?.price.saleDateDynamicsList;
  }, [priceData]);

  // 处理编辑模式切换
  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
  };

  // 检查日期是否为过去的日期
  const isPastDate = (date: dayjs.Moment): boolean => {
    const today = dayjs().startOf('day');
    return date.isBefore(today);
  };

  // 模拟更新开关状态的接口
  const mockUpdateSwitchStatus = (
    item: SaleDateDynamicsItem,
    newStatus: boolean,
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('更新开关状态：', item.id, '新状态：', newStatus);
        resolve(true);
      }, 500);
    });
  };

  // 处理开关状态变更
  const handleSwitchChange = async (item: SaleDateDynamicsItem, checked: boolean) => {
    if (!item || !isEditMode) return;

    // 设置对应项的加载状态
    setSwitchLoading((prev) => ({ ...prev, [item.id]: true }));

    try {
      // 调用模拟接口
      const success = await toggleSelfPriceEnable({
        agentPriceDynamicsId: item.id,
        isEnable: checked,
      });

      if (success) {
        // 更新本地状态
        const updatedDynamics = saleDateDynamics.map((dynamic) => {
          if (dynamic.id === item.id) {
            return { ...dynamic, isEnable: checked };
          }
          return dynamic;
        });

        setSaleDateDynamics(updatedDynamics);
      }
    } catch (error) {
      console.error('更新开关状态失败：', error);
    } finally {
      // 清除加载状态
      setSwitchLoading((prev) => ({ ...prev, [item.id]: false }));
    }
  };

  // 处理批量设置弹窗打开
  const handleOpenBatchSetting = () => {
    setPriceModalTimeType('按星期');
    setPriceModalVisible(true);
  };

  // 处理批量设置弹窗关闭
  const handleCloseBatchSetting = () => {
    setBatchSettingVisible(false);
  };

  // 计算组合价格的价格区间
  const calculatePriceRange = (goodsItem) => {
    if (!goodsItem) return { beginPrice: 0, endPrice: 0 };

    const marketPrice = goodsItem.marketPrice || 0;
    const beginDiscount = goodsItem.beginDiscount || 0;
    const endDiscount = goodsItem.endDiscount || 100;

    const beginPrice = parseFloat(((beginDiscount * marketPrice) / 100).toFixed(2));
    const endPrice = parseFloat(((endDiscount * marketPrice) / 100).toFixed(2));

    return { beginPrice, endPrice };
  };

  return (
    <div>
      {false && (
        <div>
          <div className="section-header">
            <div className="section-title-wrapper">
              <div className="section-indicator" />
              <Text strong className="section-title">
                动态定价
              </Text>
              {!loading && strategyData && (
                <div className={`tag-${strategyData.isEnabled ? 'enabled' : 'disabled'}`}>
                  <span className="tag-dot" />
                  {strategyData.isEnabled ? '启用中' : '已禁用'}
                </div>
              )}
            </div>
            <div className="section-title-wrapper">
              <Text
                className="edit-link"
                onClick={() => {
                  // 打开编辑弹窗
                  dynamicPriceStrategyModalState.setTypeWithVisible('update');
                }}
              >
                编辑
              </Text>
            </div>
          </div>
          <div className="strategy-content">
            <Row gutter={[24, 16]}>
              <Col span={6}>
                <div>
                  <Space align="center">
                    <Text strong>天气策略</Text>
                    <Tooltip title="好天气提价，坏天气降价或优惠">
                      <InfoCircleOutlined style={{ color: '#bfbfbf', fontSize: '14px' }} />
                    </Tooltip>
                  </Space>
                  <div style={{ marginTop: 8 }}>
                    {!loading && strategyData && strategyData.weatherStrategy.isEnabled ? (
                      <>
                        <Text>
                          {strategyData.weatherStrategy.condition}，
                          {strategyData.weatherStrategy.isIncrease ? '上涨' : '下降'}{' '}
                        </Text>
                        <Text
                          style={{
                            color: strategyData.weatherStrategy.isIncrease ? '#52c41a' : '#ff4d4f',
                          }}
                        >
                          {strategyData.weatherStrategy.changeRate}%
                        </Text>
                      </>
                    ) : (
                      <Text>-</Text>
                    )}
                  </div>
                </div>
              </Col>

              <Col span={6}>
                <div>
                  <Space align="center">
                    <Text strong>日常销量策略</Text>
                    <Tooltip title="接近客流上限提价，游客量少时降价，动态调整客流">
                      <InfoCircleOutlined style={{ color: '#bfbfbf', fontSize: '14px' }} />
                    </Tooltip>
                  </Space>
                  <div style={{ marginTop: 8 }}>
                    {!loading && strategyData && strategyData.salesStrategy.isEnabled ? (
                      <>
                        <Text>
                          {strategyData.salesStrategy.condition}，
                          {strategyData.salesStrategy.isIncrease ? '上涨' : '下降'}{' '}
                        </Text>
                        <Text
                          style={{
                            color: strategyData.salesStrategy.isIncrease ? '#52c41a' : '#ff4d4f',
                          }}
                        >
                          {strategyData.salesStrategy.changeRate}%
                        </Text>
                      </>
                    ) : (
                      <Text>-</Text>
                    )}
                  </div>
                </div>
              </Col>

              <Col span={6}>
                <div>
                  <Text strong>成本原则</Text>
                  <div style={{ marginTop: 8 }}>
                    {!loading && strategyData && strategyData.costPrinciple.isEnabled ? (
                      <Text>{strategyData.costPrinciple.content}</Text>
                    ) : (
                      <Text>-</Text>
                    )}
                  </div>
                </div>
              </Col>

              <Col span={6}>
                <div>
                  <Text strong>更新方式</Text>
                  <div style={{ marginTop: 8 }}>
                    {!loading && strategyData && <Text>{strategyData.updateMethod.type}</Text>}
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        </div>
      )}

      {/* 价格概览部分 */}
      <div className="container">
        <div className="section-header">
          <div className="section-title-wrapper">
            <div className="section-indicator" />
            <Text strong className="section-title">
              价格概览
            </Text>
            {/* <div className="price-update-alert">
              <div className="alert-icon"></div>
              <Text className="alert-text">有 1 条定价更新需确认</Text>
              <RightOutlined style={{ fontSize: '12px' }} />
            </div> */}
          </div>
          <div className="section-title-wrapper" />
        </div>

        {/* 日历占位部分 */}
        <div className={`calendar-container ${!isEditMode ? 'disabled-calendar' : ''}`}>
          <div className="calendar-header">
            <div className="calendar-nav">
              <Button icon={<LeftOutlined />} type="text" onClick={handlePrevMonth} />
              <DatePicker
                picker="month"
                format="YYYY年MM月"
                value={currentDate}
                onChange={(date) => date && setCurrentDate(date)}
                suffixIcon={null}
                allowClear={false}
                bordered={false}
                inputReadOnly={true}
                className="calendar-date-picker"
                popupClassName="month-picker-dropdown"
                getPopupContainer={(triggerNode) => triggerNode.parentNode as HTMLElement}
                placement="bottomLeft"
              />
              <Button icon={<RightOutlined />} type="text" onClick={handleNextMonth} />
            </div>
            <div className="calendar-actions">
              {isEditMode ? (
                <>
                  <Button
                    type="primary"
                    ghost
                    style={{ marginRight: '8px' }}
                    onClick={handleOpenBatchSetting}
                  >
                    批量设置
                  </Button>
                  <Button type="primary" onClick={toggleEditMode}>
                    完成
                  </Button>
                </>
              ) : (
                <Button type="text" style={{ color: '#1890ff' }} onClick={toggleEditMode}>
                  编辑
                </Button>
              )}
            </div>
          </div>

          {/* 日历占位表格 */}
          <div className="calendar-layout">
            <table className="calendar-table">
              <thead>
                <tr>
                  <th className="sidebar-cell">
                    <div className="sidebar-header" />
                  </th>
                  <th>日</th>
                  <th>一</th>
                  <th>二</th>
                  <th>三</th>
                  <th>四</th>
                  <th>五</th>
                  <th>六</th>
                </tr>
              </thead>
              <tbody>
                {calendarData.map((week, weekIndex) => (
                  <tr key={`week-${weekIndex}`}>
                    <td className="sidebar-cell">
                      <div className="sidebar-row">
                        <div className="sidebar-switch">开关</div>
                        <div className="sidebar-price">单买价格 (元)</div>
                      </div>
                    </td>
                    {week.map((date: CalendarDateItem, dayIndex) => {
                      const dynamicItem = getSaleDateDynamicItem(date.date);
                      const hasPrice = dynamicItem && dynamicItem.salePrice !== undefined;
                      const isLoading = dynamicItem ? switchLoading[dynamicItem.id] : false;
                      const isPast = isPastDate(date.date);
                      const isCurrentMonth = date.month === 'current';

                      return (
                        <td key={`day-${weekIndex}-${dayIndex}`}>
                          <div
                            className={`calendar-cell ${isPast ? 'past-date' : ''} ${
                              !isEditMode ? 'disabled' : ''
                            }`}
                          >
                            <div
                              className={`date-number ${
                                date.month !== 'current' ? 'other-month' : ''
                              } ${date.isToday ? 'today' : ''}`}
                            >
                              {date.date.format('DD')}
                            </div>
                            <div className="cell-switch">
                              {isCurrentMonth && dynamicItem && (
                                <Switch
                                  size="small"
                                  checked={dynamicItem.isEnable}
                                  loading={isLoading}
                                  disabled={!isEditMode || isPast}
                                  onChange={(checked) => handleSwitchChange(dynamicItem, checked)}
                                />
                              )}
                            </div>
                            <div className="cell-price">
                              {isCurrentMonth && (
                                <div className="price-wrapper">
                                  {hasPrice && (
                                    <span className={`price-text ${isPast ? 'past-price' : ''}`}>
                                      {dynamicItem.salePrice.toFixed(2)}
                                    </span>
                                  )}
                                  {(() => {
                                    return (
                                      isEditMode &&
                                      !isPast && (
                                        <div
                                          className={
                                            hasPrice
                                              ? 'price-hover-overlay'
                                              : 'full-cell-hover-overlay'
                                          }
                                          onClick={() => handleOpenPriceModal(date.date)}
                                        >
                                          <span className="setting-text">设置价格</span>
                                        </div>
                                      )
                                    );
                                  })()}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div className="strategy-config">
        组合销售：{isComboEnabled ? `${combinedPrice.toFixed(2)} 元` : '未启用'}{' '}
        <a className="edit-link float-right" onClick={handleOpenComboSaleModal}>
          编辑
        </a>
      </div>

      {goodsItem && (
        <DynamicPriceStrategyModal
          goodsItem={goodsItem}
          modalState={dynamicPriceStrategyModalState}
          actionRef={actionRef}
          onFinish={() => {
            // 刷新数据
            actionRef?.current?.reload();
            // 可以添加其他刷新逻辑
            window.location.reload();
          }}
        />
      )}

      {/* 价格设置弹窗 */}
      <PriceSettingModal
        visible={priceModalVisible}
        onCancel={handleClosePriceModal}
        selectedDate={selectedDate}
        priceData={
          priceData || {
            priceId: '',
            price: { commissionRate: 0, composePrice: 0, isCompose: false, salePrice: 0 },
          }
        }
        onSubmit={onSubmit}
        defaultTimeType={priceModalTimeType}
      />

      {/* 组合销售设置弹窗 */}
      <Modal
        title="编辑组合销售"
        open={comboSaleModalVisible}
        onCancel={handleCloseComboSaleModal}
        footer={[
          <Button key="cancel" onClick={handleCloseComboSaleModal}>
            取 消
          </Button>,
          <Button key="submit" type="primary" onClick={handleConfirmComboSale}>
            确 定
          </Button>,
        ]}
        width={700}
      >
        {/* 使用 GoodsTable 组件替换原有表格 */}
        <div style={{ marginBottom: 16 }}>
          {goodsItem && (
            <GoodsTable
              goodsItem={goodsItem}
              beginPrice={calculatePriceRange(goodsItem).beginPrice}
              endPrice={calculatePriceRange(goodsItem).endPrice}
            />
          )}
        </div>

        <div style={{ background: '#f9f9f9', padding: '16px 24px', borderRadius: '4px' }}>
          <div style={{ marginTop: 8 }}>
            <Space align="center" size={8}>
              <Text strong>
                <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
                组合销售：
              </Text>
              <Switch
                checked={tempIsComboEnabled}
                onChange={setTempIsComboEnabled}
                className={tempIsComboEnabled ? 'ant-switch-checked' : ''}
                checkedChildren="启用"
                unCheckedChildren="禁用"
              />
            </Space>
          </div>

          <div style={{ marginTop: 16 }}>
            <Text style={{ marginRight: 8 }}>
              <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
              组合价格：
            </Text>
            <Space align="center" size={8}>
              <InputNumber
                style={{ width: 160 }}
                value={tempCombinedPrice}
                onChange={handleCombinedPriceChange}
                min={
                  goodsItem?.beginDiscount
                    ? (goodsItem?.beginDiscount * goodsItem?.marketPrice) / 100
                    : 0
                }
                max={
                  goodsItem?.endDiscount
                    ? (goodsItem?.endDiscount * goodsItem?.marketPrice) / 100
                    : undefined
                }
                precision={2}
                addonAfter="元"
                disabled={!tempIsComboEnabled}
              />
              <Text>即市场标准价</Text>
              <InputNumber
                style={{ width: 160 }}
                value={tempCombinedPricePercent}
                onChange={handleCombinedPercentChange}
                min={goodsItem?.beginDiscount || 0}
                max={goodsItem?.endDiscount || 100}
                precision={0}
                addonAfter="%"
                disabled={!tempIsComboEnabled}
              />
            </Space>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default SelfStrategyContent;
