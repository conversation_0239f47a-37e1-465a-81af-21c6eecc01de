module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // 例如允许中文或长度规则等
    'subject-empty': [2, 'never'],
    'header-max-length': [2, 'always', 100],
    'subject-case': [0], // 允许中文或大小写自由
    // 关键规则：subject 必须以 -数字 结尾
    'header-pattern': [2, 'always', '^(feat|fix|docs|style|refactor|perf|test|chore|revert): .+ #\\d+$'],
    'type-enum': [2, 'always', ['feat', 'fix', 'docs', 'style', 'refactor', 'perf', 'test', 'chore', 'revert']],
  },
  plugins: [
    {
      rules: {
        'header-pattern': require('./header-pattern.cjs'),
      },
    },
  ],
};
