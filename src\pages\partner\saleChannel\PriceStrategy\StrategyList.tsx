/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-20 17:44:50
 * @LastEditTime: 2025-06-11 18:30:26
 * @LastEditors: 李悍宇 zhou<PERSON>@hqyatu.com
 */

import { tableConfig } from '@/common/utils/config';
import { GuideStepStatus } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { useGuide } from '@/hooks/useGuide';
import useModal from '@/hooks/useModal';
import {
  addPriceStrategy,
  apiSetFunctionEnable,
  deletePriceStrategy,
  getConfigPriceStrategy,
  getSelfPriceStrategy,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { ArrowLeftOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { But<PERSON>, Card, message, Modal, Popconfirm, Space, Switch, Tabs, Tour } from 'antd';
import { ceil, isNil } from 'lodash';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useModel, useParams, useRequest } from '@umijs/max';
import PriceSuggestionModal from './PriceSuggestionModal';
import NormalPriceContent from './SelfStrategy/NormalPriceContent';
import SelfStrategyContent from './SelfStrategy/SelfStrategyContent';
import StrategyModal from './StrategyModal';

// 扩展 API 类型
declare module '@/services/api/typings' {
  namespace API {
    interface PriceType {
      saleDateDynamicsList?: any[];
      isFunctionEnable?: boolean;
    }
  }
}

export type StrategyListItemType = API.PriceStrategyInfo & {
  group: API.ConfigPriceStrategyListItem['group'];
  priceId: string;
  unitId?: string;
};

const StrategyList: FC = () => {
  const { id } = useParams<{ id: string }>();
  const { initialState } = useModel('@@initialState');
  const { coId = '', coName = '' } = initialState?.currentCompany || {};
  const actionRef = useRef<ActionType>();
  const [tabKey, setTabKey] = useState<'agent' | 'self'>('self');
  const [isTopSwitchEnabled, setIsTopSwitchEnabled] = useState<boolean>(false);
  const [goodsItem, setGoodsItem] = useState<API.PriceStrategyListItem>();
  const [currentItem, setCurrentItem] = useState<StrategyListItemType>();
  const [selfPriceData, setSelfPriceData] = useState<API.SelfPriceStrategyItem>();
  const { updateGuideInfo } = useGuide();

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const tabValue = searchParams.get('tab') || 'self';

  // 漫游式引导相关状态
  const [tourOpen, setTourOpen] = useState<boolean>(false);
  const versionSwitchRef = useRef<HTMLDivElement>(null);

  const strategyModalState = useModal();
  const priceSuggestionModalState = useModal();

  const init = () => {
    // 从本地存储或其他缓存中获取商品信息
    const cachedItem = localStorage.getItem('priceStrategyItem');
    if (cachedItem) {
      const parsedItem = JSON.parse(cachedItem);
      setGoodsItem(parsedItem);
      // 请求直销价格策略
      getSelfPriceStrategy({
        distributorId: coId,
        unitId: parsedItem.unitId,
      }).then(({ data }) => {
        console.log(data, 'data-self');
        setSelfPriceData(data);
        // 检查 price 对象中是否有 isFunctionEnable 属性
        const isFunctionEnable =
          data?.price && 'isFunctionEnable' in data.price ? data.price.isFunctionEnable : false;
        if (isFunctionEnable) {
          setIsTopSwitchEnabled(true);
        }

        // 检查是否需要显示引导
        const hasSeenTour = localStorage.getItem('dynamicPricingTourSeen');
        if (!hasSeenTour) {
          setTourOpen(true);
        }
      });
    }
  };
  // 从缓存中读取商品信息
  useEffect(init, [id]);

  useEffect(() => {
    setTabKey(tabValue as any);
    if (tabValue === 'agent') {
      strategyModalState.setTypeWithVisible('add');
    }
  }, []);

  // 屎一样的后端数据结构
  const tableListReq = async (params: API.ConfigPriceStrategyParams) => {
    if (!goodsItem) return { data: [] };
    // 代理
    if (tabKey === 'agent') {
      const { data } = await getConfigPriceStrategy(params);
      return {
        data: data.map((i) => ({
          ...i.info.price,
          group: i.group,
          priceId: i.info.priceId,
        })),
      };
    }
    // 直销
    const { data } = await getSelfPriceStrategy(params);
    if (isNil(data)) {
      return {
        data: [],
      };
    } else {
      return {
        data: [{ ...data.price, priceId: data.priceId }],
      };
    }
  };

  const deletePriceStrategyReq = useRequest(deletePriceStrategy, {
    manual: true,
    onSuccess(data, params) {
      if (goodsItem) {
        addOperationLogRequest({
          action: 'del',
          content: `删除【${goodsItem.name}】代理价格策略`,
        });
      }

      message.success('删除成功');
      actionRef?.current?.reload();
    },
  });

  const submitSelfPrice = (data: API.SelfPriceStrategyItem) => {
    message.loading('保存中...');
    if (!data || !data.price || !goodsItem) return;

    const priceData = data.price;

    // 操作日志数据
    const logList = [
      {
        title: '单买价格（元）',
        dataIndex: 'salePrice',
        align: 'right',
        renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      },
      {
        title: '组合销售',
        dataIndex: 'isCompose',
        valueEnum: {
          true: '是',
          false: '否',
        },
      },
      {
        title: '组合价格（元）',
        dataIndex: 'composePrice',
        align: 'right',
        renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      },
    ];

    // 获取当前价格数据用于对比
    const currentPriceData = selfPriceData?.price || ({} as API.PriceType);

    const changeConfig = {
      list: logList,
      beforeData: {
        salePrice: currentPriceData.salePrice || 0,
        isCompose: currentPriceData.isCompose || false,
        composePrice: currentPriceData.composePrice || 0,
      },
      afterData: {
        salePrice: priceData.salePrice || 0,
        isCompose: priceData.isCompose || false,
        composePrice: priceData.composePrice || 0,
      },
    };

    // 设置是否新版本
    // @ts-ignore - 添加 isFunctionEnable 属性
    priceData.isFunctionEnable = isTopSwitchEnabled;

    const submitData = {
      distributorId: coId,
      groupId: [],
      price: priceData,
      goodsId: goodsItem?.goodsId,
      unitId: goodsItem.unitId,
    };

    // 提交价格策略
    addPriceStrategy(submitData as API.AddPriceStrategyParams, true).then(() => {
      message.success('价格设置已保存');
      // 更新引导
      updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_2 });

      // 添加操作日志
      addOperationLogRequest({
        action: 'edit',
        changeConfig,
        content: `编辑【${goodsItem.name}】直销价格策略`,
      });

      init();

      // // 保存成功后刷新数据
      // setSelfPriceData({
      //   priceId: data.priceId || '',
      //   price: priceData
      // });
      message.destroy();
    });
  };

  // 处理版本切换
  const handleVersionChange = (checked: boolean) => {
    if (checked) {
      // 切换到新版本
      Modal.confirm({
        title: '确定切换至新版本？',
        content: '切换版本后，旧版功能定价将失效，请确保新版功能配置。',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          const { saleDateDynamicsList = [] } = selfPriceData?.price || {};
          // 如果存在动态定价，则直接保存新版本开启状态
          if (saleDateDynamicsList.length > 0) {
            // 调用价格策略设置开关接口
            apiSetFunctionEnable({
              agentPriceId: selfPriceData?.priceId || '',
              isEnable: true,
            }).then(() => {
              message.success('已切换至新版本');
            });
          }
          setIsTopSwitchEnabled(true);
        },
      });
    } else {
      // 切换到旧版本
      Modal.confirm({
        title: '确定切换至旧版本？',
        content:
          '切换版本后，将自动恢复至旧版功能定价模式，新版本配置保留但暂不生效，请检查旧版参数是否当前运营需求。',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          const { priceId } = selfPriceData || {};
          // 如果存在单买价格，则直接保存新版本关闭状态
          if (priceId) {
            // 调用价格策略设置开关接口
            apiSetFunctionEnable({
              agentPriceId: selfPriceData?.priceId || '',
              isEnable: false,
            }).then(() => {
              message.success('已切换至旧版本');
            });
          }
          setIsTopSwitchEnabled(false);
        },
      });
    }
  };

  // 处理引导关闭
  const handleTourClose = () => {
    setTourOpen(false);
    localStorage.setItem('dynamicPricingTourSeen', 'true');
  };

  // 引导步骤配置
  const tourSteps = [
    {
      title: '【动态定价功能已上线】您的门票价格将更智能！',
      description: (
        <>
          <p>• 支持按淡旺季、实时天气灵活调整票价，平衡客流与收益</p>
          <p>• 实时监控客流变化和历史数据，数据看板助您快速决策</p>
          <p>• 价格浮动区间需符合政策要求，系统自动执行</p>
        </>
      ),
      target: () => versionSwitchRef.current,
      nextButtonProps: {
        children: '知道了',
      },
    },
  ];

  const columns: ProColumns<StrategyListItemType>[] = [
    {
      title: '单买价格',
      dataIndex: 'discount',
      renderText: (text, record) => {
        if (isNil(goodsItem?.marketPrice)) {
          return '-';
        }
        return `${record.discount * 100 || 0}%/${
          record?.salePrice ? ceil(record?.salePrice, 2) : record?.salePrice
        }`;
      },
    },
    {
      title: '组合销售',
      dataIndex: 'isCompose',
      renderText: (_, record) => (record.isCompose ? '是' : '否'),
    },
    {
      title: '组合价格',
      dataIndex: 'composeDiscount',
      renderText: (text, record) => {
        if (!record?.isCompose || isNil(goodsItem?.marketPrice)) {
          return '-';
        }
        return `${(record.composeDiscount * 100).toFixed(0)}%/${record?.composePrice}`;
      },
    },
    {
      title: '佣金比例',
      dataIndex: 'commissionRate',
      renderText: (_, record) => `${record.commissionRate}%`,
    },
    {
      title: '适用分组',
      dataIndex: 'group',
      renderText: (_, record) => {
        return (record.group ?? []).map((item) => item.name).join('，') || '-';
      },
    },

    {
      width: 120,
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      renderText: (_, record) => (
        <Space>
          <a
            onClick={() => {
              setCurrentItem(record);
              strategyModalState.setTypeWithVisible('update');
            }}
          >
            编辑
          </a>

          <Popconfirm
            title="确定删除？"
            onConfirm={() => {
              deletePriceStrategyReq.run({ id: record.priceId });
            }}
          >
            <a style={{ color: 'red' }}>删除</a>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Button
        type="link"
        icon={<ArrowLeftOutlined />}
        onClick={() => window.history.back()}
        style={{
          padding: 0,
          marginBottom: 16,
        }}
      >
        设置价格策略
      </Button>

      {goodsItem && (
        <>
          <div
            className="tabs-container"
            style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
          >
            <Tabs
              activeKey={tabKey}
              onChange={(activeKey) => {
                setTabKey(activeKey as 'agent' | 'self');
                // 切换 tab 时关闭引导
                setTourOpen(false);
                if (activeKey === 'agent') {
                  actionRef.current?.reload();
                }
              }}
              items={[
                {
                  label: '直销',
                  key: 'self',
                },
                {
                  label: '代理',
                  key: 'agent',
                },
              ]}
              style={{ marginBottom: 16 }}
            />
            {tabKey === 'self' && (
              <div style={{ marginBottom: 16 }} ref={versionSwitchRef}>
                <span>使用新版本：</span>
                <Switch checked={isTopSwitchEnabled} onChange={handleVersionChange} />
              </div>
            )}
          </div>

          {tabKey === 'agent' ? (
            <ProTable
              {...tableConfig}
              actionRef={actionRef}
              search={false}
              params={{
                distributorId: coId,
                unitId: goodsItem.unitId,
              }}
              toolBarRender={() => {
                return [
                  <Button
                    key="add"
                    icon={<PlusOutlined />}
                    type="primary"
                    onClick={() => {
                      strategyModalState.setTypeWithVisible('add');
                      setCurrentItem(undefined);
                    }}
                  >
                    新增
                  </Button>,
                ];
              }}
              pagination={false}
              request={tableListReq}
              columns={columns}
            />
          ) : isTopSwitchEnabled ? (
            <SelfStrategyContent priceData={selfPriceData} onSubmit={submitSelfPrice} />
          ) : (
            <NormalPriceContent priceData={selfPriceData} onSubmit={submitSelfPrice} />
          )}

          <StrategyModal
            goodsItem={goodsItem}
            currentItem={currentItem}
            modalState={strategyModalState}
            showGroup={tabKey === 'agent'}
            showRate={tabKey === 'agent'}
            actionRef={actionRef}
            onFinish={() => {
              actionRef?.current?.reload();
            }}
          />
          <PriceSuggestionModal goodsItem={goodsItem} modalState={priceSuggestionModalState} />

          {/* 漫游式引导组件 */}
          <Tour
            open={tourOpen}
            onClose={handleTourClose}
            steps={tourSteps}
            type="primary"
            mask={false}
            placement="bottomLeft"
            closeIcon={null}
          />
        </>
      )}
    </Card>
  );
};

export default StrategyList;
