import ChainModal from '@/common/components/ChainModal';
import DetailsPop from '@/common/components/DetailsPop';
import Export, { columnsSet } from '@/common/components/Export';
import useExport from '@/common/components/Export/useExport';
import Tag from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import {
  chainStatusEnum,
  orderTicketTypeEnum,
  orderTypeEnum,
  orderTypeSearch,
  payTypeEnum,
  productTypeEnum,
  saleChannelEnum,
  ticketStatusEnum,
  ticketTypeEnum,
  whetherEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { DataMaskTypeEnum, getUniqueId } from '@/common/utils/tool';
import Blockchain from '@/components/Blockchain';
import DataMask from '@/components/DataMask';
import { showTicketModal } from '@/global';
import { useMask } from '@/hooks/useMask';
import { apiGroupTicketOrder, apiStoreOrderInfo, apiStoreOrderList } from '@/services/api/store';
import { CopyOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import ProDescriptions from '@ant-design/pro-descriptions';
import { ModalForm } from '@ant-design/pro-form';
import { Space, Table, message } from 'antd';
import type { ColumnType } from 'antd/es/table';
import copy from 'copy-to-clipboard';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import { useContext, useState } from 'react';
import { TabKeyContext } from '../..';
import styles from '../../index.less';
// const orderTypeEnum = {
//   11: '已取消',
//   20: '待付款',
//   // 21: '支付成功',
//   22: '支付失败',
//   30: '已支付',
// };
export default ({
  // visible,
  // setVisible,
  storeId,
}: {
  // visible: boolean;
  // setVisible: Function;
  storeId: string;
}) => {
  // const [copyObj, setCopyObj] = useState({})
  const [handleListMaskChange, maskListDataFn] = useMask();

  const [handleDetailsMaskChange, maskDetailsDataFn] = useMask();
  const columns: ProColumns<any>[] = [
    {
      width: 170,
      title: '父单号',
      dataIndex: 'orderGroupId',
      fixed: 'left',
      // copyable: true,
      // ellipsis: true,
      render: (dom: any) => (
        <>
          <a
            title={dom}
            onClick={async () => {
              try {
                setLoading2(true);
                setDetailsVisible2(true);
                const { data } = await apiGroupTicketOrder({ orderId: dom });
                setDataSource2(data);
                setLoading2(false);
              } catch (error) {}
            }}
          >
            {dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}
          </a>
          <CopyOutlined
            style={{ marginLeft: '4px', color: '#1890ff' }}
            onClick={() => {
              copy(dom);
              message.success('已复制');
            }}
          />
        </>
      ),
    },
    {
      width: 170,
      title: '子订单号',
      dataIndex: 'orderId',
      fixed: 'left',
      // copyable: true,
      // ellipsis: true,
      render: (dom: any, entity: any, index: any, __: any, schema: any) => (
        <>
          <a
            title={dom}
            onClick={async () => {
              try {
                setLoading(true);
                if (entity.orderType == 'JQTC') {
                  setDetailsVisibleCard(true);
                  const { data } = await apiStoreOrderInfo({
                    current: 1,
                    pageSize: 999,
                    orderId: dom,
                  });
                  setDataSource({
                    ...data.order,
                    // ...data.orderStatus,
                    ticketInfo: data.orderTravelCard,
                  });
                } else {
                  setDetailsVisible(true);
                  const { data } = await apiStoreOrderInfo({
                    current: 1,
                    pageSize: 999,
                    orderId: dom,
                  });
                  setDataSource({
                    ...data.order,
                    // ...data.orderStatus,
                    ticketInfo: data.ticketInfoList.data,
                  });
                }
                setLoading(false);
              } catch (error) {}
            }}
          >
            {dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}
          </a>
          <span
            style={{ marginLeft: '4px', cursor: 'pointer' }}
            onClick={(e) => {
              // console.log(index, schema.dataIndex);
              // setCopyObj(!copyObj[index + schema.dataIndex])
              copy(dom);
              message.success('已复制');
            }}
          >
            <CopyOutlined style={{ color: '#1890ff' }} />
            {/* {copyObj[index + schema.dataIndex]
              ? <CheckOutlined style={{ color: '#52c41a' }} />
              : <CopyOutlined style={{ color: '#1890ff' }} />} */}
          </span>
        </>
      ),
    },
    {
      title: '订单类型',
      dataIndex: 'orderType',
      valueEnum: orderTicketTypeEnum,
      search: false,
    },
    {
      width: 80,
      title: '订单状态',
      dataIndex: 'orderStatus',
      valueEnum: orderTypeSearch,
      renderText: (dom: number) => orderTypeEnum[dom],
    },

    {
      title: '支付时间',
      dataIndex: 'payTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          console.log(value);
          return {
            payStartTime: value[0],
            payEndTime: value[1],
          };
        },
      },
    },
    {
      title: '支付时间',
      dataIndex: 'payTime',
      valueType: 'dateTime',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '售票终端类型',
      dataIndex: 'sourceType',
      render: (dom: any) => saleChannelEnum[dom],
      hideInSearch: true,
    },
    {
      title: '售票设备名称',
      dataIndex: 'equipmentName',
      search: false,
    },
    {
      title: '下单时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            createStartTime: value[0],
            createEndTime: value[1],
          };
        },
      },
    },
    {
      title: '下单时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      renderText: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),

      width: 150,
      search: false,
    },
    {
      title: '登录账号',
      dataIndex: 'username',
    },
    {
      title: '手机号',
      width: 150,
      dataIndex: 'userPhone',
      renderText: (text: string) => maskListDataFn(text, DataMaskTypeEnum.PHONE),
    },
    {
      title: '交易上链',
      dataIndex: 'isChainOrder',
      valueEnum: chainStatusEnum,
      renderText: (dom) => <Tag type="chainStatus" value={dom} />,
    },
    {
      title: '支付金额（元）',
      dataIndex: 'payAmount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '操作',
      dataIndex: '_option',
      valueType: 'option',
      fixed: 'right',
      width: 70,
      render: (_, entity: any) =>
        entity.txId ? (
          <ChainModal
            chainData={{
              txId: entity.txId,
            }}
          />
        ) : (
          '-'
        ),
    },
  ];

  //查看区块链交易数据
  const [vray, setVray] = useState(false); //渲染
  const [cochainVisible, setCochainVisible] = useState(false);
  const [cochainDataDataSource, setCochainDataDataSource] = useState<any>({});
  const cochainColumns = [
    {
      title: '区块链账号',
      columns: [
        {
          title: '发行方',
          dataIndex: 'fromAccount',
          span: 5,
          render: (dom: any) => dom.length > 0 && dom.join('、'),
        },
        {
          title: '发送方',
          dataIndex: 'issuerAccount',
          span: 5,
          render: (dom: any) => dom.length > 0 && dom.join('、'),
        },
      ],
    },
    {
      title: '存证信息',
      columns: [
        {
          title: '存证时间',
          dataIndex: 'savedAt',
          span: 5,
        },
        {
          title: `存 证 号`,
          dataIndex: 'certId',
          span: 5,
        },

        {
          title: '存证内容',
          dataIndex: 'certContent',
          span: 5,
          render: (dom: any) => {
            return (
              <>
                <div className={!vray ? styles.dom : ''}>{dom}</div>
                <div
                  style={{ textAlign: 'right', cursor: 'pointer', color: '#1890ff' }}
                  onClick={() => setVray(!vray)}
                >
                  {vray ? '收起 ' : '展开 '}
                  {vray ? <UpOutlined /> : <DownOutlined />}
                </div>
              </>
            );
          },
        },
        {
          title: '交易哈希',
          dataIndex: 'txId',
          span: 5,
        },
      ],
    },
  ];

  // 【详情】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [detailsVisibleCard, setDetailsVisibleCard] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [detailsVisible2, setDetailsVisible2] = useState<boolean>(false);
  const [isLoading2, setLoading2] = useState<boolean>(true);
  const [dataSource2, setDataSource2] = useState<any>({ id: '', isEnable: 0 });
  const columns2 = [
    {
      width: 154,
      title: '票号',
      dataIndex: 'id',
      renderText: (dom: any) => {
        if (isEmpty(dom)) {
          return '-';
        }
        return (
          <>
            <span title={dom}>{dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}</span>
            <CopyOutlined
              style={{ color: '#1890ff' }}
              onClick={() => {
                copy(dom);
                message.success('已复制');
              }}
            />
          </>
        );
      },
    },

    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      render: (dom: any) => productTypeEnum[dom],
    },
    {
      title: '票种',
      dataIndex: 'type',
      render: (dom: any) => ticketTypeEnum[dom],
    },

    {
      title: '人数',
      dataIndex: 'playerNum',
    },
    {
      title: '已核销总次数',
      dataIndex: 'checkedNum',
    },
    {
      title: '姓名/身份证',
      dataIndex: 'realNameList',
      width: 150,
      renderText: (text: any[]) => {
        if (isEmpty(text || [])) {
          return '-';
        }
        const { idCardName, idCardNumber } = text[0] || {};
        return (
          <Space direction="vertical" size="small" align="center">
            <span>{maskDetailsDataFn(idCardName, DataMaskTypeEnum.NAME)}</span>
            <span>{maskDetailsDataFn(idCardNumber, DataMaskTypeEnum.ID_CARD)}</span>
            {text.length > 1 && (
              <a
                onClick={() => {
                  // 对数据进行掩码处理后再传入
                  const maskedData = text.map((item) => ({
                    ...item,
                    idCardName: maskDetailsDataFn(item.idCardName, DataMaskTypeEnum.NAME),
                    idCardNumber: maskDetailsDataFn(item.idCardNumber, DataMaskTypeEnum.ID_CARD),
                  }));
                  showTicketModal(maskedData);
                }}
              >
                查看所有
              </a>
            )}
          </Space>
        );
      },
    },
    {
      title: '出票时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
    },
    // {
    //   title: '退票时间',
    //   dataIndex: 'refundTime',
    //   valueType: 'dateTime',
    // },
    {
      title: '入园时间',
      dataIndex: 'enterTime',
      valueType: 'dateTime',
    },
    {
      title: '权益 ID',
      dataIndex: 'rightsId',
    },
    {
      title: '金额（元）',
      dataIndex: 'productPrice',
      align: 'right',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    // {
    //   title: '佣金（元）',
    //   // dataIndex: 'actualAmount',
    // },
    {
      title: '佣金（元）',
      dataIndex: 'actualAmount',
      align: 'right',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      // render: (dom, record) => {
      //   let count = 0;
      //   dom.forEach((e) => {
      //     count = count + e.actualAmount;
      //   });
      //   return count;
      // },
    },
    {
      title: '数字资产',
      dataIndex: 'isChain',
      render: (text: number) => whetherEnum[text] || '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (dom: any) => ticketStatusEnum[dom],
    },

    // {
    //   title: '上链信息',
    //   dataIndex: 'createTime2',
    //   editable: true,
    //   hideInSearch: true,
    //   render:()=>{
    //     return <a onClick={onExamine}>查看</a>
    //   }
    // },
  ];
  const columns3 = [
    {
      title: '联系人',
      dataIndex: 'pilotName',
      render: (_, record) => {
        return maskDetailsDataFn(record.pilotName, DataMaskTypeEnum.NAME);
      },
    },
    {
      title: '联系人手机号',
      dataIndex: 'pilotPhone',
      render: (_, record) => {
        return maskDetailsDataFn(record.pilotPhone, DataMaskTypeEnum.PHONE);
      },
    },
    {
      title: '联系人身份证',
      dataIndex: 'pilotIdentity',
      render: (_, record) => {
        return maskDetailsDataFn(record.pilotIdentity, DataMaskTypeEnum.ID_CARD);
      },
    },
  ];
  const columnsDetail = [
    {
      title: '基本信息',
      columns: [
        {
          title: '子订单号',
          dataIndex: 'orderId',
        },
        {
          title: '结算单号',
          dataIndex: 'tradeNo',
        },
        {
          title: '订单类型',
          dataIndex: 'orderType',
          valueEnum: orderTicketTypeEnum,
        },
        {
          title: '订单状态',
          dataIndex: 'orderStatus',
          valueEnum: orderTypeEnum,
        },
        {
          title: '交易上链',
          dataIndex: 'isChainOrder',
          valueEnum: chainStatusEnum,
        },
        {
          title: '支付方式',
          dataIndex: 'payType',
          valueEnum: payTypeEnum,
        },

        {
          title: '下单时间',
          dataIndex: 'createTime',
          renderText: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),

          valueType: 'dateTime',
        },
        {
          title: '登录账号',
          dataIndex: 'username',
        },
        {
          title: '购票终端',
          dataIndex: 'sourceType',
          render: (dom: any) => saleChannelEnum[dom],
        },
        {
          title: '支付金额（元）',
          dataIndex: 'payAmount',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
        {
          title: '佣金（元）',
          dataIndex: 'commissionAmount',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
      ],
    },
    {
      title: '票务信息',
      className: 'no-bgColor',
      columns: [
        {
          title: '',
          dataIndex: 'ticketInfo',
          render: (dom: any) => {
            const _summaryFields = ['playerNum', 'checkedNum', 'productPrice', 'actualAmount'];
            const _summaryDatas: any = {
              id: '合计：',
              scenicName: '',
              proName: '',
              proType: '',
              type: '',
              playerNum: 0,
              checkedNum: 0,
              realNameList: '',
              createTime: '',
              enterTime: '',
              rightsId: '',
              productPrice: 0,
              actualAmount: 0,
              isChain: '',
              status: '',
            };
            (dom || []).forEach((e) => {
              _summaryFields.forEach((_field) => {
                _summaryDatas[_field] += e?.[_field] || 0;
              });
            });
            return (
              <ProTable
                {...tableConfig}
                columns={columns2}
                dataSource={dom}
                bordered
                size="middle"
                search={false}
                pagination={false}
                options={false}
                summary={(e) => {
                  return (
                    <>
                      <Table.Summary.Row>
                        {Object.keys(_summaryDatas).map((key, i) => {
                          return (
                            <Table.Summary.Cell
                              index={1}
                              key={key}
                              className={styles.amount}
                              align={
                                key == 'productPrice' || key == 'actualAmount' || key == 'id'
                                  ? 'right'
                                  : 'left'
                              }
                            >
                              {key == 'productPrice' || key == 'actualAmount'
                                ? Math.round(_summaryDatas[key] * 100) / 100
                                : _summaryDatas[key]}
                            </Table.Summary.Cell>
                          );
                        })}
                      </Table.Summary.Row>
                    </>
                  );
                }}
              />
            );
          },
        },
      ],
    },
    {
      title: '领票人信息',
      className: 'no-bgColor',
      columns: [
        {
          title: '',
          dataIndex: 'basicFont',
          render: (dom: any, record) => (
            <Table
              rowKey={getUniqueId()}
              columns={columns3}
              dataSource={[record]}
              bordered
              size="middle"
              pagination={false}
            />
          ),
        },
      ],
    },
  ];
  const tableColumns2Card: ColumnType<any>[] = [
    {
      width: 154,
      title: '权益卡号',
      dataIndex: 'cardId',
      render: (dom: any) => (
        <>
          <span title={dom}>{dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}</span>
          <CopyOutlined
            style={{ color: '#1890ff' }}
            onClick={() => {
              copy(dom);
              message.success('已复制');
            }}
          />
        </>
      ),
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '权益卡名称',
      dataIndex: 'productName',
    },
    {
      title: '购卡人姓名',
      dataIndex: 'identityName',
      render: (text) => maskDetailsDataFn(text, DataMaskTypeEnum.NAME),
    },
    {
      title: '购卡人身份证',
      dataIndex: 'identity',
      render: (text) => maskDetailsDataFn(text, DataMaskTypeEnum.ID_CARD),
    },
    {
      title: '金额（元）',
      dataIndex: 'productPrice',
      align: 'right',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '佣金（元）',
      dataIndex: 'actualAmount',
      align: 'right',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];
  const columnsDetailCard = [
    {
      title: '基本信息',
      columns: [
        {
          title: '子订单号',
          dataIndex: 'orderId',
        },
        {
          title: '结算单号',
          dataIndex: 'tradeNo',
        },
        {
          title: '订单类型',
          dataIndex: 'orderType',
          valueEnum: orderTicketTypeEnum,
        },
        {
          title: '订单状态',
          dataIndex: 'orderStatus',
          valueEnum: orderTypeEnum,
        },
        {
          title: '支付方式',
          dataIndex: 'payType',
          valueEnum: payTypeEnum,
        },

        {
          title: '下单时间',
          dataIndex: 'createTime',
          renderText: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),

          valueType: 'dateTime',
        },
        {
          title: '登录账号',
          dataIndex: 'username',
        },
        {
          title: '购票终端',
          dataIndex: 'sourceType',
          render: (dom: any) => saleChannelEnum[dom],
        },
        {
          title: '出卡时间',
          dataIndex: 'payTime',
          valueType: 'dateTime',
        },
        {
          title: '支付金额（元）',
          dataIndex: 'payAmount',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
        {
          title: '佣金（元）',
          dataIndex: 'actualAmount',
          valueType: 'digit',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
      ],
    },
    {
      title: '权益卡信息',
      columns: [
        {
          width: 1152,
          title: '',
          dataIndex: 'ticketInfo',
          render: (dom: any) => (
            <Table
              columns={tableColumns2Card}
              dataSource={dom}
              bordered
              size="middle"
              pagination={false}
            />
          ),
        },
      ],
    },
    {
      title: '领票人信息',
      columns: [
        {
          width: 1152,
          title: '',
          dataIndex: 'basicFont',
          render: (dom: any, record) => (
            <Table
              columns={columns3}
              dataSource={[record]}
              bordered
              size="middle"
              pagination={false}
            />
          ),
        },
      ],
    },
  ];
  const columnsDetail2 = [
    {
      title: '基本信息',
      columns: [
        {
          title: '父订单号',
          dataIndex: 'orderGroupId',
        },
        {
          title: '订单状态',
          dataIndex: 'orderStatus',
          valueEnum: orderTypeEnum,
        },
        {
          title: '订单类型',
          dataIndex: 'orderType',
          valueEnum: orderTicketTypeEnum,
        },
        {
          title: '交易上链',
          dataIndex: 'isChain',
          valueEnum: chainStatusEnum,
        },
        {
          title: '订单金额',
          dataIndex: 'totalAmount',
        },
        {
          title: '登录账号',
          // dataIndex: 'username',
          dataIndex: 'userId',
        },
        {
          title: '下单时间',
          dataIndex: 'createTime',
          valueType: 'dateTime',
        },
        {
          title: '支付方式',
          dataIndex: 'payType',
          valueEnum: payTypeEnum,
        },
        {
          title: '支付时间',
          dataIndex: 'payTime',
          valueType: 'dateTime',
        },

        {
          title: '购票终端',
          dataIndex: 'sourceType',
          valueEnum: saleChannelEnum,
        },
        {
          title: '结算单号',
          dataIndex: 'tradeNo',
          render: (_, record) => {
            if (record.orderInfo.length > 0) {
              return record.orderInfo[0].order.tradeNo;
            } else {
              return '-';
            }
          },
        },
        {
          title: '支付金额（元）',
          dataIndex: 'payAmount',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
      ],
    },
  ];
  //NFT 流传记录
  const NFTcolumns = [
    {
      title: '交易 ID',
      dataIndex: 'proName',
      // hideInTable:true,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: 'Proposal Hash',
      dataIndex: 'proName1',
      // hideInTable:true,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '交易发起方',
      dataIndex: 'proName2',
      // hideInTable:true,
      hideInSearch: true,
    },
    {
      title: '交易接收方',
      dataIndex: 'proName3',
      // hideInTable:true,
      hideInSearch: true,
    },
    {
      title: '时间',
      dataIndex: 'proName4',
      // hideInTable:true,
      hideInSearch: true,
    },
  ];
  //NFT 流传记录数据
  const NFTData = [
    {
      proName: 'c765e920f161fb5394887913c7bd40e37e08c5b70197597abc2009e56b374527',
      proName1: 'c765e920f161fb5394887913c7bd40e37e08c5b70197597abc2009e56b374527',
      proName2: '0x0',
      proName3: 'user1',
      proName4: '2022-05-21 16:41:00',
    },
    {
      proName: 'c765e920f161fb5394887913c7bd40e37e08c5b70197597abc2009e56b374527',
      proName1: 'c765e920f161fb5394887913c7bd40e37e08c5b70197597abc2009e56b374527',
      proName2: 'user1',
      proName3: 'user2',
      proName4: '2022-05-23 15:03:06',
    },
  ];
  const [cochainModalVisit, setCochainModalVisit] = useState(false);

  const tabKey = useContext(TabKeyContext);
  const exportState = useExport({
    columns,
    modulePath: 'E-commerce_MyShopOrder',
    params: { storeId },
  });

  return (
    <>
      <ProTable
        {...tableConfig}
        params={{ storeId }}
        headerTitle={
          <DataMask
            onDataMaskChange={handleListMaskChange}
            logContent="查看【订单管理列表】用户隐私信息"
          />
        }
        request={async (params) => {
          const { data } = await apiStoreOrderList(params);
          addOperationLogRequest({
            action: 'info',
            module: tabKey,
            content: `查看订单管理列表`,
          });
          return {
            data: data.records || [],
            total: data.total,
          };
        }}
        columns={columns}
        formRef={exportState.formRef}
        columnsState={columnsSet(exportState)}
        toolBarRender={() => [<Export key="export" {...exportState} />]}
      />
      {/* 查看区块链交易记录 */}
      <Blockchain
        cochainVisible={cochainVisible}
        setCochainVisible={setCochainVisible}
        cochainColumns={cochainColumns}
        cochainDataDataSource={cochainDataDataSource}
      />
      {/* 子订单 */}
      <DetailsPop
        width={1200}
        title={
          <>
            <span>子订单详情</span>
            <DataMask
              onDataMaskChange={handleDetailsMaskChange}
              logContent="查看【子订单详情】用户隐私信息"
            />
          </>
        }
        visible={detailsVisible}
        isLoading={isLoading}
        setVisible={setDetailsVisible}
        columnsInitial={columnsDetail}
        dataSource={dataSource}
      />
      {/* 子订单（权益卡） */}
      <DetailsPop
        width={1200}
        title={
          <>
            <span>子订单详情</span>
            <DataMask
              onDataMaskChange={handleDetailsMaskChange}
              logContent="查看【子订单详情】用户隐私信息"
            />
          </>
        }
        visible={detailsVisibleCard}
        isLoading={isLoading}
        setVisible={setDetailsVisibleCard}
        columnsInitial={columnsDetailCard}
        dataSource={dataSource}
      />
      {/* 父订单 */}
      <DetailsPop
        // width={1200}
        title={
          <>
            <span>父订单详情</span>
            <DataMask
              onDataMaskChange={handleDetailsMaskChange}
              logContent="查看【父订单详情】用户隐私信息"
            />
          </>
        }
        visible={detailsVisible2}
        isLoading={isLoading2}
        setVisible={setDetailsVisible2}
        columnsInitial={columnsDetail2}
        dataSource={dataSource2}
      />
      {/* 上链信息 */}
      <ModalForm
        title="上链信息"
        visible={cochainModalVisit}
        width={modelWidth.md}
        // formRef={formObj}
        // form={formRef}
        // form={formObj}
        // initialValues={initialValues}
        submitter={false}
        onFinish={async (val) => {
          console.log(val);
        }}
        onVisibleChange={(val) => {
          setCochainModalVisit(val);
          // if (!val) {
          //   setInitialValues({});
          // }
        }}
      >
        <ProDescriptions column={2}>
          <ProDescriptions.Item label="Token ID">hqskgy01</ProDescriptions.Item>
          <ProDescriptions.Item label="发行组织">chan-hqsk-ticket</ProDescriptions.Item>
          <ProDescriptions.Item label="联盟通道">200,000,000</ProDescriptions.Item>
        </ProDescriptions>
        NFT流转记录：
        <br /> <br />
        <ProTable
          // actionRef={actionRef}
          {...tableConfig}
          bordered
          rowKey="id"
          options={false}
          search={false}
          toolBarRender={null}
          // params={{ scenicId }}
          // request={apiOrderTicketCheck}
          dataSource={NFTData}
          columns={NFTcolumns}
        />
      </ModalForm>
    </>
  );
};
