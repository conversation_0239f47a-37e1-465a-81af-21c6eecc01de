import { ticketTypeEnum } from '@/common/utils/enum';
import { getAgentSellStatistic } from '@/services/api/performanceStatistics';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { But<PERSON>, Card } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import PerformanceGraph from './components/PerformanceGraph';
const currentCompanyId = localStorage.getItem('currentCompanyId');
const Agent: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [resetFlag, setResetFlag] = useState(0); // 新增重置标志
  const [graphData, setGraphData] = useState<any>({
    id: 'g2',
    name: '总绩效',
    label: 0,
    rate: 1,
    status: 'B',
    variableValue: undefined, // 不展示该值
    children: [],
  });

  const columns: ProColumns<any>[] = [
    {
      title: '排名',
      dataIndex: '_index', // 内部索引字段
      valueType: 'index',
      width: 60,
      align: 'center',
      fixed: 'left', // 可选：固定在左侧
    },
    {
      title: '时间',
      dataIndex: 'date',
      valueType: 'dateRange',
      initialValue: [
        dayjs().clone().subtract(30, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      hideInTable: true,
      fieldProps: {
        presets: [
          // 使用函数返回动态时间范围
          {
            label: '昨天',
            value: () => [dayjs().subtract(1, 'days').startOf('day'), dayjs().endOf('day')],
          },
          {
            label: '近7天',
            value: () => [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
          },
          {
            label: '近30天',
            value: () => [dayjs().subtract(30, 'days').startOf('day'), dayjs().endOf('day')],
          },
        ],
      },
      search: {
        transform: (value) => {
          return {
            startDate: value[0],
            endDate: value[1],
          };
        },
      },
    },
    {
      title: '产品',
      dataIndex: 'productName',
      hideInTable: true,
    },
    {
      title: '商品',
      dataIndex: 'goodsName',
      hideInTable: true,
    },
    {
      title: '票种',
      dataIndex: 'ticketType',
      hideInTable: true,
      valueEnum: ticketTypeEnum,
    },
    {
      title: '代理商名称',
      dataIndex: 'agentName',
      hideInSearch: true,
    },
    {
      title: '所属分组',
      dataIndex: 'agentGroupName',
      hideInSearch: true,
    },
    {
      title: '实际销售人数',
      dataIndex: 'actualPeople',
      hideInSearch: true,
    },
    {
      title: '实际售票⾦额(元)',
      dataIndex: 'actualSellAmount',
      hideInSearch: true,
      tooltip: '统计时间内，代理商C端门票销售支付成功且未发生退票的门票售票总金额',
    },
    {
      title: '实际售票数',
      dataIndex: 'actualSellNum',
      hideInSearch: true,
      tooltip: '统计时间内，代理商C端门票销售支付成功且未发生退票的门票售票总数量',
    },
    {
      title: '售票单数',
      dataIndex: 'sellNum',
      hideInSearch: true,
      tooltip: '统计时间内，代理商C端门票销售支付成功的订单总数',
    },
    {
      title: '退票率',
      dataIndex: 'refundRate',
      hideInSearch: true,
      tooltip: '统计时间内，代理商C端门票销售支付成功的退单总数占订单总数的百分比',
    },
  ];
  function transformData(data) {
    // 计算总绩效数据（actualSellAmount总和）
    const totalActualSellAmount = data.reduce((sum, item) => sum + item.actualSellAmount, 0);

    // 按代理商分组名称分组
    const groupMap = data.reduce((acc, item) => {
      const groupName = item.agentGroupName;
      if (!acc[groupName]) {
        acc[groupName] = {
          actualSellSum: 0, // 分组实际售票金额总和
          items: [], // 分组内代理商列表
        };
      }
      acc[groupName].actualSellSum += item.actualSellAmount;
      acc[groupName].items.push(item);
      return acc;
    }, {});

    // 生成二级分组节点
    const children = Object.entries(groupMap).map(([groupName, groupData], index) => {
      // 生成三级代理商节点
      const groupChildren = groupData.items.map((item, childIndex) => ({
        id: `g2-${index + 1}-${childIndex + 1}`,
        name: item.agentName, // 代理商名称
        count: item.actualSellNum, // 实际售票数
        label: item.actualSellAmount.toFixed(2), // 单个代理商实际售票金额
        children: [],
        rate: 1,
        status: 'B',
        variableValue: undefined, // 不展示该值
      }));

      return {
        id: `g2-${index + 1}`,
        name: groupName, // 代理商分组名称
        count: groupData.items.reduce((sum, item) => sum + item.actualSellNum, 0), // 分组总实际售票数
        label: groupData.actualSellSum.toFixed(2), // 分组实际售票金额总和
        children: groupChildren,
        rate: 1,
        status: 'B',
        variableValue: undefined, // 不展示该值
      };
    });

    // 返回最终结构
    return {
      id: 'g2',
      name: '总绩效',
      count: data.reduce((sum, item) => sum + item.actualSellNum, 0), // 总实际售票数
      label: totalActualSellAmount.toFixed(2), // 总实际售票金额
      children: children,
      rate: 1,
      status: 'B',
      variableValue: undefined, // 不展示该值
    };
  }

  return (
    <>
      <style>{`
        .ant-pro-query-filter.ant-pro-query-filter {
          margin-left: -40px;
        }
      `}</style>
      <ProTable
        style={{ width: '100%' }}
        actionRef={actionRef}
        rowKey={(record: any) => record.agentId}
        columns={columns}
        headerTitle={'绩效排行'}
        tableRender={(_, defaultDom) => (
          <div>
            {/* 插入自定义卡片 */}
            <Card
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <div>
                    <span style={{ fontSize: '16px' }}>绩效统计</span>
                    <span style={{ marginLeft: '8px', fontSize: '14px', color: '#ccc' }}>
                      （实际售票金额/元）
                    </span>
                  </div>
                  <Button onClick={() => setResetFlag((v) => v + 1)}>回到默认视图</Button>
                </div>
              }
              style={{ margin: '16px 0', borderRadius: 8 }}
              headStyle={{ border: 'none' }}
            >
              <PerformanceGraph data={graphData} resetFlag={resetFlag} />
            </Card>

            {/* 默认表格渲染 */}
            {defaultDom}
          </div>
        )}
        request={async (params) => {
          const param = {
            startDate: params.startDate,
            endDate: params.endDate,
            productName: params.productName,
            goodsName: params.goodsName,
            ticketType: params.ticketType,
            supplierId: currentCompanyId,
          };
          try {
            const res = await getAgentSellStatistic(param);
            const transformedData = transformData(res.data);
            console.log(transformedData);

            setGraphData(transformedData);
            return {
              data: res?.data || [],
              total: res.data.length,
              success: true,
            };
          } catch (error) {
            return {
              data: [],
              total: 0,
            };
          }
        }}
      />
    </>
  );
};

export default Agent;
