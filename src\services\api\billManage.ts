/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-06 14:25:35
 * @LastEditTime: 2023-01-09 10:45:20
 * @LastEditors: Please set LastEditors
 */

import { request } from '@umijs/max';
import { scenicHost } from '.';
import type { API, ResponseData } from './typings';

// 后端 list 接口格式
interface ResponseListData<T> {
  code: number;
  data: {
    current: number;
    pageSize: number;
    total: number;
    records: T;
  };
  msg: string;
}

/** 获取代理商账单 list */
export async function getAgentBillList(
  params: API.AgentBillListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.AgentBillItem[]>>(`${scenicHost}/bill/agentBillPageList`, {
    method: 'GET',
    params,
    ...options,
  });
}
/** 获取代理商账单前结算 list */
export async function getAgentBillPreList(
  params: API.AgentBillListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.AgentBillPreItem[]>>(
    `${scenicHost}/order/settlementPageList`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}
/** 获取账单明细 */
export async function getAgentBillDetail(
  params: API.AgentBillDetailParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.AgentBillDetailItem[]>>(`${scenicHost}/bill/billDetail`, {
    method: 'GET',
    params,
    ...options,
  });
}
/** 获取账单明细（下游分销商） */
export async function getComissionBillDetail(
  params: API.AgentBillDetailParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.AgentBillDetailItem[]>>(
    `${scenicHost}/bill/comissionBillDetail`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}
/** 获取账单明细（佣金明细） */
export async function getBalanceAssignmentDetail(
  params: API.AgentBillDetailParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.AgentBillDetailItem[]>>(
    `${scenicHost}/bill/balanceAssignmentDetail`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/** 获取经销商账单 list */
export async function getSellerBillList(
  params: API.SellerBillListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.SellerBillItem[]>>(`${scenicHost}/bill/sellerBillPageList`, {
    method: 'GET',
    params,
    ...options,
  });
}

/** 经销商账单确认 */
export async function confirmSellerBill(params: { id: string }, options: Record<string, any> = {}) {
  return request<ResponseData<null>>(`${scenicHost}/bill/confirmBill/${params.id}`, {
    method: 'PUT',
    ...options,
  });
}

/** 结算的 结算单明细导出 */
export async function exportSettlementDetail(
  params: { creditSettlementBillId: string; merchantId: string },
  options: Record<string, any> = {},
) {
  return request<ResponseData<Blob>>(`${scenicHost}/paypal/export/creditSettlementBillItems`, {
    method: 'GET',
    params,
    responseType: 'blob',
    skipErrorHandler: true,
    ...options,
  });
}

/**分销商下拉列表 */
export async function getDistributorList(
  params: { distributorId: string },
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.DistributorListItem[]>>(
    `${scenicHost}/distribution/distribution/downDistributorList`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/**代理商下拉列表 */
export async function getAgentList(
  params: { distributorId: string },
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.AgentListItem[]>>(`${scenicHost}/ticketAgent/agentsList`, {
    method: 'GET',
    params,
    ...options,
  });
}

// 创建账单，统计商户之间未结算的订单
export async function addSettlement(
  params: API.AddSettlementParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<string>>(`${scenicHost}/bill/billMerchant`, {
    method: 'POST',
    data: params,
    ...options,
  });
}

// 查询过期票账单
export async function checkOverdueTicket(
  params: API.ChcekOverdueTicketParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.ChcekOverdueTicketItem>>(`${scenicHost}/bill/overdueTicket`, {
    method: 'GET',
    params,
    ...options,
  });
}

export async function exportBillDetailDetail(
  params: { id: string },
  options: Record<string, any> = {},
) {
  return request<ResponseData<Blob>>(`${scenicHost}/bill/export/billDetail`, {
    method: 'GET',
    params,
    responseType: 'blob',
    skipErrorHandler: true,
    ...options,
  });
}
