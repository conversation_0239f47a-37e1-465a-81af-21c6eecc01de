/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-14 14:17:08
 * @LastEditTime: 2023-02-01 13:47:25
 * @LastEditors: Please set LastEditors
 */

import Export, { columnsSet } from '@/common/components/Export';
import useExport from '@/common/components/Export/useExport';
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { apiStoreCommission } from '@/services/api/store';
import ProForm, { ProFormDependency, ProFormSelect } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { DatePicker, Space, Tabs } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useContext, useRef, useState } from 'react';
import { TabKeyContext } from '../..';
import BillModal from './BillModal';

const { RangePicker } = DatePicker;

// 前/后结算
export type TabKeyType = '1' | '0';
// 分佣/退佣明细
export type BillType = 'divide' | 'return';

interface CommissionStatementProps {
  // visible: boolean;
  // setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  storeId: string;
}
// 比较急 ts 没写 后续有时间优化
/**
 * @description: 佣金报表
 */
const CommissionStatement: FC<CommissionStatementProps> = ({ storeId }) => {
  const [tabKey, setTabKey] = useState<TabKeyType>('1');
  const [bill, setBill] = useState<BillType>('divide');

  const [totalAmount, setToTalAmount] = useState();

  const actionRef = useRef<ActionType>();
  const [timeRange, setTimeRange] = useState<{
    startDate: string;
    endDate: string;
    dateType: string;
  }>();
  const [modalVisible, setModalVisible] = useState(false);
  const [tableListItem, setTableListItem] = useState<any>();

  const tabKeyValue = useContext(TabKeyContext);

  const tableListRequest = async (params: any) => {
    // 数据处理
    const { dateType, date, ...rest } = params;
    const format = dateType === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM';
    const range = {
      startDate: dayjs(date[0]).format(format),
      endDate: dayjs(date[1]).format(format),
      dateType,
    };
    const { data } = await apiStoreCommission({
      ...rest,
      ...range,
      storeId,
    });

    addOperationLogRequest({
      action: 'info',
      module: tabKeyValue,
      content: rest.settlementType == 1 ? '查看前结算佣金报表' : '查看后结算佣金报表',
    });

    setTimeRange(range);
    setToTalAmount(data.totalAmount);
    return {
      data: data.records,
    };
  };

  const columns: ProColumns<any>[] = [
    {
      title: '日期',
      dataIndex: 'date',
      search: false,
    },
    {
      title: '经销商',
      dataIndex: 'supplierName',
    },
    {
      title: '日期',
      hideInTable: true,
      renderFormItem: () => (
        <Space>
          <ProFormSelect
            name="dateType"
            formItemProps={{
              style: { width: '120px' },
            }}
            initialValue="date"
            options={[
              { value: 'date', label: '按日期' },
              { value: 'month', label: '按月份' },
            ]}
          />
          <ProFormDependency name={['dateType']}>
            {({ dateType }) => {
              const format = dateType === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM';
              return (
                <ProForm.Item name="date" initialValue={[dayjs().subtract(7, 'days'), dayjs()]}>
                  <RangePicker style={{ width: 250 }} picker={dateType} />
                </ProForm.Item>
              );
            }}
          </ProFormDependency>
        </Space>
      ),
    },
    {
      title: '分佣数量（张）',
      dataIndex: 'sellNum',
      search: false,
    },
    {
      title: '退佣数量（张）',
      dataIndex: 'refundNum',
      search: false,
      hideInTable: tabKey === '0',
    },
    {
      title: '分佣金额汇总（元）',
      dataIndex: 'sellAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      search: false,
    },
    {
      title: '退佣金额汇总（元）',
      dataIndex: 'refundAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      search: false,
      hideInTable: tabKey === '0',
    },
    {
      title: '实际分得佣金（元）',
      dataIndex: 'amount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      search: false,
    },
    {
      width: 180,
      title: '操作',
      dataIndex: '_option',
      valueType: 'option',
      render: (_, entity) => (
        <Space
          onClick={() => {
            setTableListItem(entity);
          }}
        >
          <a
            onClick={() => {
              setModalVisible(true);
              setBill('divide');
            }}
          >
            分佣明细
          </a>
          {tabKey === '1' && (
            <a
              onClick={() => {
                setModalVisible(true);
                setBill('return');
              }}
            >
              退佣明细
            </a>
          )}
        </Space>
      ),
    },
  ];

  const exportState = useExport({
    columns,
    modulePath: { 1: 'E-commerce_MyShopCommissionFront', 0: 'E-commerce_MyShopCommissionLater' }[
      tabKey
    ],
    params: { storeId, settlementType: tabKey, date: null, ...timeRange },
  });

  return (
    <
      // width={1200}
      // title={'佣金报表'}
      // visible={visible}
      // destroyOnClose
      // footer={
      //   <>
      //     <Button
      //       onClick={() => {
      //         setVisible(false);
      //       }}
      //     >
      //       取消
      //     </Button>
      //   </>
      // }
      // onCancel={() => {
      //   setVisible(false);
      // }}
    >
      <Tabs
        style={{ background: '#fff' }}
        tabBarStyle={{ padding: '0 24px', margin: '0' }}
        onChange={(activeKey: any) => {
          setTabKey(activeKey);
          // actionRef?.current?.reload();
        }}
        items={[
          {
            label: '前结算',
            key: '1',
          },
          {
            label: '后结算',
            key: '0',
          },
        ]}
      />
      <ProTable<{ storeId: string }, any>
        {...tableConfig}
        key={tabKey}
        // toolBarRender={() => [<span key="total">总计佣金金额：999 元</span>]}
        // options={false}
        actionRef={actionRef}
        columns={columns}
        pagination={false}
        params={{ storeId, settlementType: tabKey }}
        formRef={exportState.formRef}
        columnsState={columnsSet(exportState)}
        toolBarRender={() => [<Export key="export" {...exportState} />]}
        // search={{
        //   labelWidth: 'auto',
        // }}
        // toolbar={{
        //   menu: {
        //     type: 'tab',
        //     activeKey: tabKey,
        //     items: [
        //       {
        //         label: '前结算',
        //         key: '1',
        //       },
        //       {
        //         label: '后结算',
        //         key: '0',
        //       },
        //     ],
        //     onChange: (key: any) => {
        //       setTabKey(key);
        //     },
        //   },
        // }}
        request={tableListRequest}
      />
      <div style={{ textAlign: 'right', padding: 20 }}>
        <span>
          {tabKey === '1' ? '前结算' : '后结算'}佣金总额：{totalAmount ?? '-'}元
        </span>
      </div>
      {storeId && (
        <BillModal
          visible={modalVisible}
          setVisible={setModalVisible}
          currentItem={tableListItem}
          settlementType={tabKey}
          bill={bill}
          timeRange={timeRange}
        />
      )}
    </>
  );
};

export default CommissionStatement;
