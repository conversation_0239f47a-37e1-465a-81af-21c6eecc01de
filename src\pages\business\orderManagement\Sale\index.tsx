import {
  apiControlConfMessage,
  apiDeleteOrder,
  apiOderPageList,
  apiScenicControlList,
} from '@/services/api/erp';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Tag as AntdTag, Col, Row, Space, message } from 'antd';
import React, { useRef, useState } from 'react';
// import Qrcode from './components/Qrcode';
import ChainModal from '@/common/components/ChainModal';
import Export, { columnsSet } from '@/common/components/Export';
import useExport from '@/common/components/Export/useExport';
import Tag from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import {
  chainStatusEnum,
  orderTicketTypeEnum,
  orderTypeEnum,
  orderTypeSearch,
  payTypeEnum,
  saleChannelEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { DataMaskTypeEnum, formatTime } from '@/common/utils/tool';
import Blockchain from '@/components/Blockchain';
import DataMask from '@/components/DataMask';
import { useMask } from '@/hooks/useMask';
import useModal from '@/hooks/useModal';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import ProForm, { ModalForm, ProFormText } from '@ant-design/pro-form';
import { Access, useAccess, useModel } from '@umijs/max';
import OrderManagement from './OrderManagement';
import TemporalInterval from './TemporalInterval';
import styles from './index.less';

interface ScenicAreaDefault {
  value: any;
  flag: boolean;
  setStatus: boolean;
  inItTicketInfo: [];
  particularData: {};
  orderParticulaStatus: {};
  timePars: number;
}
let tirme: any = null;
let count: any;
let province: any;
// const {name,phone} =JSON.parse(localStorage.getItem('users'))
function ScenicArea(param: ScenicAreaDefault) {
  const access = useAccess();
  const [formObj] = ProForm.useForm();

  //服务商 ID
  const { initialState }: any = useModel('@@initialState');
  const { coId, coName, settlementId } = initialState?.currentCompany;
  const [modalVisit, setModalVisit] = useState(false);

  //查看区块链交易数据
  const [vray, setVray] = useState(false);
  const [cochainVisible, setCochainVisible] = useState(false);
  const [cochainDataDataSource, setCochainDataDataSource] = useState<any>({});

  const actionRef = useRef();

  //绑定详情订单状态
  const detailModal = useModal();
  const [currentRow, setCurrentRow] = useState<Record<string, any>>();

  //时间
  //时间状态
  // const [timePars, setTimePars] = useState({});

  //获取时间状态
  const [Time, setTime] = React.useState(0);
  const timeNume = (value: any) => {
    setTime(value);
  };
  //批量选择
  const [batchs, setBatchs] = useState([]);
  //配置 id
  const [controlConfId, setControlConfId] = useState(undefined);
  const [name, setName] = useState(undefined);
  const [phone, setPhone] = useState(undefined);
  const [isSend, setIsend] = useState(1);
  //批量删除
  const onDelects = async (val) => {
    const pars = {
      businessId: coId,
      idList: batchs,
    };
    try {
      const { data: val } = await apiScenicControlList(coId);
      console.log(val);
      setControlConfId(val[0].controlConfId);
      pars.controlConfId = val[0].controlConfId;
      const res = await apiDeleteOrder(pars);
      if (res.data !== null) {
        const { isAccess, name, phone } = res.data;
        setIsend(isAccess);
        setName(name);
        setPhone(phone);
        setModalVisit(true);
      } else {
        await apiDeleteOrder(pars);
        actionRef.current?.reload();
        message.success('删除成功');
      }
      console.log('pppppppppppppp', res);
    } catch (e) {
      console.error(e);
    }
    // setModalVisit(true);
    console.log('批量删除', val);
  };
  const onSubmit = async (val) => {
    clearInterval(tirme);
    console.log('batchs', val);
    // const pars = {
    //   verifyCode: val.code,
    //   controlConfId,
    //   businessId: coId,
    // };
    try {
      const pars2 = {
        businessId: coId,
        controlConfId,
        idList: [...batchs],
        verifyCode: val.code,
      };
      await apiDeleteOrder(pars2);
      formObj.setFieldsValue({ code: '' });
      actionRef.current?.reload();
      message.success('删除成功');
    } catch (e) {
      console.error(e);
    }
    setSendStatus(true);
    setRestStatus(true);
    // count=59
  };
  //发送短信状态
  const [sendStatus, setSendStatus] = useState(true);
  //重新发送短信状态
  const [restStatus, setRestStatus] = useState(true);
  //手机号验证状态
  const [phoneStatus, setPhoneStatus] = useState(true);
  const [num, setNum] = useState(59);
  const onSend = async () => {
    count = 59;
    // clearInterval(tirme);
    if (count == 59) {
      //防止多次发送短信
      setRestStatus(false);
      //是否发送短信和重新发送短信
      setSendStatus(false);
      try {
        if (isSend == 0) {
          //发送短信
          await apiControlConfMessage({ amount: batchs.length, businessId: coId, controlConfId });
          // 判断手机号不能为空
          setPhoneStatus(true);
        } else {
          message.info('无需发送短信验证码');
          // setResStatus(true)
          // setSendStatus(true)
          // clearInterval(tirme)
        }
      } catch (error) {
        console.error(error);
      }
    }
    tirme = setInterval(() => {
      count--;
      if (count == 0) {
        //关闭定时器
        clearInterval(tirme);
        //防止多次发送短信
        setRestStatus(true);
        //是否发送短信和重新发送短信
        setSendStatus(false);
      } else {
        // setSendStatus(true);
      }
      //计时数
      setNum(count);
    }, 1000);
    //计时数
    setNum(count);
  };

  const [handleListMaskChange, maskListDataFn] = useMask();

  //订单管理信息
  const columns: ProColumns<any>[] = [
    {
      title: '订单号',
      dataIndex: 'orderId',
      fixed: 'left',
      width: 200,
      key: 'orderId',
      fieldProps: {
        placeholder: '请输入订单号',
      },
      render: (dom: any, record: any) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(record);
              detailModal.setVisible(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '订单类型',
      width: 100,
      dataIndex: 'orderType',
      valueEnum: orderTicketTypeEnum,
    },
    {
      title: `服务商`,
      dataIndex: 'serviceProviderName',
      key: 'serviceProviderName',
      width: 150,
      search: false,
      render: (dom: any, record: any) => {
        return <span>{dom}</span>;
      },
      // transform: (date: any) => {
      //   return {
      //     serviceProviderId: date,
      //   };
      // },
      // renderFormItem: () => {
      //   return <Serviceprovider />;
      // },
    },
    {
      title: '购票数量',
      dataIndex: 'num',
      search: false,
      width: 100,
      key: 'num',
    },

    {
      title: '票号',
      key: 'ticketNumber',
      dataIndex: 'ticketNumber',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入票号',
      },
    },
    {
      title: '售票终端类型',
      dataIndex: 'sourceType',
      render: (dom: any) => saleChannelEnum[dom],
      hideInSearch: true,
    },
    {
      title: '售票设备名称',
      dataIndex: 'equipmentName',
      search: false,
    },
    {
      title: '代理商',
      dataIndex: 'agentName',
      width: 150,
      key: 'agentName',
      fieldProps: {
        placeholder: '请输入代理商',
      },
    },
    {
      title: '结算单号',
      width: 150,
      dataIndex: 'tradeNo',
      key: 'tradeNo',
      search: false,
    },

    {
      title: '支付方式',
      width: 150,
      dataIndex: 'payType',
      search: false,
      key: 'payType',
      valueEnum: payTypeEnum,
    },

    {
      title: '下单时间',
      dataIndex: 'createTime',
      width: 150,
      search: false,
      key: 'createTime',
      // valueType: 'date',
      // render: (dom: any) => {
      //   return <span>{dayjs(dom).format('YYYY-MM-DD HH:mm:ss')}</span>
      // }
    },

    {
      title: '支付时间',
      dataIndex: 'payTime',
      width: 150,
      search: false,
      key: 'payTime',
      // render: (dom: any) => {
      //   return <span>{dayjs(dom).format('YYYY-MM-DD HH:mm:ss')}</span>
      // }
    },
    {
      title: '订单状态',
      width: 150,
      dataIndex: 'orderStatus',
      valueEnum: orderTypeSearch,
      renderText: (dom: number) => <AntdTag color="blue">{orderTypeEnum[dom]}</AntdTag>,
    },
    {
      title: '登录账号',
      width: 150,
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '手机号',
      width: 150,
      dataIndex: 'userPhone',
      fieldProps: {
        placeholder: '请输入手机号',
      },
      renderText: (text: string) => maskListDataFn(text, DataMaskTypeEnum.PHONE),
    },
    {
      title: '订单金额（元）',
      dataIndex: 'totalAmount',
      search: false,
      width: 150,
      key: 'totalAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '佣金（元）',
      width: 100,
      dataIndex: 'commissionAmount',
      key: 'commissionAmount',
      hideInSearch: true,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '交易上链',
      width: 150,
      dataIndex: 'isChainOfOrder',
      valueType: 'select',
      valueEnum: chainStatusEnum,
      renderText: (dom) => <Tag type="chainStatus" value={dom} />,
    },
    {
      title: '操作',
      dataIndex: '_option',
      fixed: 'right',
      width: 80,
      valueType: 'option',
      render: (_: any, entity: any) => {
        return (
          <>
            {entity.txId ? (
              <ChainModal
                chainData={{
                  txId: entity.txId,
                }}
              />
            ) : (
              '-'
            )}
          </>
        );
      },
    },
    {
      title: '日期区间',
      // key: 'dateRange',
      dataIndex: 'created_at',
      key: 'created_at',
      valueType: 'dateTimeRange',
      hideInTable: true,
      transform: (date: any) => {
        // setTimePars({
        //   startTime: date[0],
        //   endTime: date[1]
        // });
        return {
          startTime: date[0],
          endTime: date[1],
        };
      },
      renderFormItem: () => {
        return <TemporalInterval timeNume={timeNume} />;
      },
    },
    {
      title: '产品名称',
      key: 'productName',
      dataIndex: 'productName',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入产品名称',
      },
    },
  ];
  //订单列表 和 查询表单
  const orderData = async (params: any) => {
    //服务商 id
    const distributorId = initialState?.currentCompany?.coId;
    const pars = { ...params, searchType: Time, distributorId };
    // setOrderListStatus(pars)
    try {
      const orderList: any = await apiOderPageList(pars);

      const orderListData = orderList.data?.records;
      // pars = { ...params }
      // setOrderListData(orderListData)
      console.log('订单列表', orderListData, pars);
      orderListData.map((e: any) => {
        //格式化时间
        e.payTime = formatTime(e.payTime);
        e.createTime = formatTime(e.createTime);
      });

      return {
        data: orderListData,
        success: true,
        total: orderList.data?.total,
      };
    } catch (e) {
      console.error(e);
      return {};
    }
  };

  const cochainColumns = [
    {
      title: '区块链账号',
      columns: [
        {
          title: '发行方',
          dataIndex: 'fromAccount',
          span: 5,
          render: (dom: any) => dom.length > 0 && dom.join('、'),
        },
        {
          title: '发送方',
          dataIndex: 'issuerAccount',
          span: 5,
          render: (dom: any) => dom.length > 0 && dom.join('、'),
        },
      ],
    },
    {
      title: '存证信息',
      columns: [
        {
          title: '存证时间',
          dataIndex: 'savedAt',
          span: 5,
        },
        {
          title: `存 证 号`,
          dataIndex: 'certId',
          span: 5,
        },

        {
          title: '存证内容',
          dataIndex: 'certContent',
          span: 5,
          render: (dom: any) => {
            return (
              <>
                <div className={!vray ? styles.dom : ''}>{dom}</div>
                <div
                  style={{ textAlign: 'right', cursor: 'pointer', color: '#1890ff' }}
                  onClick={() => setVray(!vray)}
                >
                  {vray ? '收起 ' : '展开 '}
                  {vray ? <UpOutlined /> : <DownOutlined />}
                </div>
              </>
            );
          },
        },
        {
          title: '交易哈希',
          dataIndex: 'txId',
          span: 5,
        },
      ],
    },
  ];

  const defaultColConfig = {
    xs: 24,
    sm: 24,
    md: 24,
    lg: 8,
    xl: 8,
    xxl: 8,
  };
  const exportState = useExport({
    columns,
    modulePath: 'E-commerce_OrderManage',
    params: { searchType: Time, distributorId: initialState?.currentCompany?.coId },
  });
  return (
    <div>
      <>
        <OrderManagement modalState={detailModal} currentRow={currentRow} />
        <>
          <ProTable
            {...tableConfig}
            columns={columns}
            actionRef={actionRef}
            pagination={{
              defaultPageSize: 10,
            }}
            headerTitle={
              <DataMask
                onDataMaskChange={handleListMaskChange}
                logContent="查看【库存销售记录列表】用户隐私信息"
              />
            }
            request={async (params) => orderData(params)}
            formRef={exportState.formRef}
            columnsState={columnsSet(exportState)}
            toolBarRender={() => [<Export key="export" {...exportState} />]}
            editable={{
              type: 'multiple',
            }}
            // options={{ density: false, setting: false }}
            // scroll={{ x: 1300 }}
            rowKey="orderId"
            // search={{
            //   labelWidth: 120,
            //   collapseRender: false,
            //   collapsed: false,
            //   span: defaultColConfig,
            // }}
            dateFormatter="string"
            form={
              {
                // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
                // syncToUrl: (values) => {
                //   return {
                //     ...values,
                //     created_at: [values.startTime, values.endTime],
                //   };
                //   // return values;
                // },
              }
            }
            //批量
            rowSelection={{
              defaultSelectedRowKeys: [],
            }}
            tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => {
              setBatchs(selectedRowKeys);
              return (
                <>
                  <Space size={24}>
                    <span>
                      已选 {selectedRowKeys.length} 项
                      <a style={{ marginLeft: 8 }} onClick={onCleanSelected}>
                        取消选择
                      </a>
                    </span>
                  </Space>
                </>
              );
            }}
            tableAlertOptionRender={() => {
              return (
                <Access accessible={access.canOrderManage_delete}>
                  <Space size={16}>
                    <a onClick={() => onDelects(batchs)}>批量删除</a>
                  </Space>
                </Access>
              );
            }}
          />
        </>
        {/* 查看区块链交易记录 */}
        <Blockchain
          cochainVisible={cochainVisible}
          setCochainVisible={setCochainVisible}
          cochainColumns={cochainColumns}
          cochainDataDataSource={cochainDataDataSource}
        />
        <ModalForm
          title="批量删除"
          visible={modalVisit}
          form={formObj}
          width={modelWidth.md - 200}
          // submitter={false}
          onFinish={async (value) => {
            onSubmit(value);
            return true;
          }}
          onVisibleChange={setModalVisit}
        >
          <Row gutter={[16, 16]} justify="center">
            <Row wrap>
              <Col push={2} span={20}>
                <Space>
                  <div>
                    此操作为敏感操作，需要复核员
                    <strong>
                      （{name}-{phone}）
                    </strong>
                    发送短信验证码审核通过
                  </div>
                </Space>
              </Col>
            </Row>
            <Row>
              <Col>
                <Space>
                  <ProFormText
                    width="lg"
                    name="code"
                    fieldProps={{
                      suffix: (
                        <>
                          {' '}
                          <button
                            style={{ border: 'none', background: '#fff' }}
                            disabled={restStatus ? false : true}
                            onClick={onSend}
                          >
                            {sendStatus ? '发送验证码' : num == 0 ? '重新发送' : `${num}  s`}
                          </button>
                        </>
                      ),
                    }}
                    placeholder="验证码"
                  />
                </Space>
              </Col>
            </Row>
          </Row>
        </ModalForm>
      </>
    </div>
  );
}
export default ScenicArea;
