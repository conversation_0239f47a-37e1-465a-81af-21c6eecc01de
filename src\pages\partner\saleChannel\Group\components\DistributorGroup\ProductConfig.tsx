/*
 * 配置产品弹窗
 * */
import { tableConfig } from '@/common/utils/config';
import { GuideStepStatus, productTypeEnum, ticketTypeEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { useGuide } from '@/hooks/useGuide';
import {
  getProductConfigListNew,
  getUnifyPullDownStock,
  saveProductConfig,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { LeftOutlined } from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-components';
import { ProCard } from '@ant-design/pro-components';
import type { EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import ProTable, { EditableProTable } from '@ant-design/pro-table';
import { useModel, useRequest } from '@umijs/max';
import { Button, Flex, InputNumber, Modal, Space, Tag, Typography, message } from 'antd';
import dayjs from 'dayjs';
import type { Key } from 'react';
import { useContext, useRef, useState } from 'react';
import { SaleGroupTabKeyContext } from '../..';
import GoodsTable from './GoodsTable';

const { Text } = Typography;

const ProductConfig = ({
  groupId,
  groupName,
  modalState,
}: {
  groupId: string;
  groupName: string;
  modalState: any;
}) => {
  const { initialState } = useModel('@@initialState');
  const { coId = '' } = initialState?.currentCompany || {};
  const { updateGuideInfo } = useGuide();
  const [selectedBatchItems, setSelectedBatchItems] = useState([]);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<EditableFormInstance<any>>();
  // 添加时的选择
  const sourceListRef = useRef<any[]>([]);
  // 数据绑定
  const [dataSource, setDataSource] = useState<API.ProductConfigListItem[]>([]);
  // 扩展项的 KEYS
  const [expandedRowKeys, setExpandedRowKeys] = useState<Key[]>([]);

  // 选择
  const [copyVisible, setCopyVisible] = useState(false);

  // 新增批量编辑相关状态
  const [batchEditing, setBatchEditing] = useState<boolean>(false);
  const [batchEditingRowKey, setBatchEditingRowKey] = useState<Key | null>(null);
  const [originalBatchData, setOriginalBatchData] = useState<any[]>([]);
  const [batchSaving, setBatchSaving] = useState<boolean>(false);
  const [newAddedBatchIds, setNewAddedBatchIds] = useState<Set<string>>(new Set());

  //景区名称下拉框数据
  const getSenicListReq = useRequest(getUnifyPullDownStock, {
    defaultParams: [{ getUnifyPullDown: coId, type: '1' }],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: key,
          label: res.data[key],
        };
      });
    },
  });
  //供应商名称下拉框数据
  const getSupplierListReq = useRequest(getUnifyPullDownStock, {
    defaultParams: [{ getUnifyPullDown: coId, type: '2' }],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: key,
          label: res.data[key],
        };
      });
    },
  });
  //产品名称下拉框数据
  const getProductListReq = useRequest(getUnifyPullDownStock, {
    defaultParams: [{ getUnifyPullDown: coId, type: '3' }],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: res.data[key],
          label: res.data[key],
        };
      });
    },
  });

  // 表格配置
  const tableColumns: ProColumns<API.ProductConfigListItem>[] = [
    {
      title: 'productId',
      dataIndex: 'productId',
      search: false,
      hideInTable: true,
    },
    {
      title: '库存批次号',
      width: 180,
      editable: false,
      dataIndex: 'batchId',
      search: true,
      hideInTable: true,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSupplierListReq.data,
        showSearch: true,
      },
    },
    {
      title: '商品名称',
      width: 180,
      editable: false,
      dataIndex: 'ticketGoodsName',
      fixed: 'left',
      search: true,
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      valueEnum: ticketTypeEnum,
      editable: false,
      search: true,
    },
    {
      title: '景区名称',
      dataIndex: 'scenicId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSenicListReq.data,
        showSearch: true,
      },
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      editable: false,
      search: false,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      search: false,
      editable: false,
    },
    {
      title: '产品名称',
      dataIndex: 'ticketProductName',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getProductListReq.data,
        showSearch: true,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'ticketProductName',
      search: false,
      editable: false,
    },
    {
      title: '产品类型',
      dataIndex: 'ticketProductType',
      valueEnum: productTypeEnum,
      editable: false,
    },
    {
      title: '数字资产',
      dataIndex: 'isDigit',
      search: false,
      editable: false,
      render: (val) => {
        return val == '1' ? '是' : '否';
      },
    },
    {
      title: '分时时段',
      dataIndex: 'beginTime',
      search: false,
      editable: false,
      render: (_, { timeShareVoList }) => {
        return (
          <span>
            {timeShareVoList?.length
              ? (timeShareVoList || [])?.map((_item) => (
                  <Tag key={_item?.id}>
                    {_item?.beginTime} - {_item?.endTime}
                  </Tag>
                ))
              : '-'}
          </span>
        );
      },
    },
    {
      title: '特殊折扣率',
      search: false,
      dataIndex: 'overallDiscount',
      editable: false,
      render: (dom: any) => `${dom}%`,
    },
    {
      title: '分销折扣区间',
      search: false,
      editable: false,
      render: (_, { beginDiscount, endDiscount }) => `${beginDiscount}% - ${endDiscount}%`,
    },
    {
      title: '购买有效时间',
      hideInTable: true,
      dataIndex: 'time1',
      valueType: 'dateRange',
      editable: false,
      search: {
        transform: (value) => {
          return {
            purchaseBeginTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            purchaseEndTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '入园有效时间',
      hideInTable: true,
      valueType: 'dateRange',
      dataIndex: 'time2',
      editable: false,
      search: {
        transform: (value) => {
          return {
            dayBegin: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            dayEnd: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '可用库存',
      dataIndex: 'availableNumber',
      fixed: 'right',
      hideInSearch: true,
      editable: false,
    },
    {
      title: '销售价（元）',
      dataIndex: 'productAuthorizeMigrateSalePrice',
      fixed: 'right',
      hideInSearch: true,
      editable: false,
      render: (text, record) => {
        const isEditing = batchEditing && batchEditingRowKey === record.ticketGoodsId;

        // 批量编辑模式下显示可编辑输入框
        if (isEditing) {
          return (
            <InputNumber
              min={0}
              step={0.01}
              precision={2}
              value={text == '-' ? null : text}
              onChange={(value) => {
                // 更新数据源中所有批次的销售价
                setDataSource((prev) =>
                  prev.map((item) => {
                    if (item.ticketGoodsId === record.ticketGoodsId) {
                      // 同步更新当前商品下所有批次的销售价
                      const updatedBatchData = item.batchData?.map((batch) => ({
                        ...batch,
                        productAuthorizeMigrateSalePrice: value,
                      }));
                      return {
                        ...item,
                        productAuthorizeMigrateSalePrice: value,
                        batchData: updatedBatchData,
                      };
                    }
                    return item;
                  }),
                );
              }}
              disabled={batchSaving}
              style={{ width: 120 }}
            />
          );
        }
        return text != '-' ? text + '起' : text;
      },
    },
    {
      title: '批量操作',
      width: 220,
      fixed: 'right',
      valueType: 'option',
      render: (_, record) => {
        const isEditing = batchEditing && batchEditingRowKey === record.ticketGoodsId;

        if (isEditing) {
          return (
            <Space size="small">
              <Button
                type="link"
                loading={batchSaving}
                onClick={() => handleBatchSave(record)}
                style={{ color: '#1890ff' }}
              >
                批量保存
              </Button>
              <Button
                type="link"
                disabled={batchSaving}
                onClick={handleBatchCancel}
                style={{ color: 'red' }}
              >
                取消
              </Button>
            </Space>
          );
        }

        return (
          <Button
            type="link"
            onClick={() => {
              setExpandedRowKeys([record.ticketGoodsId]);
              handleBatchEdit(record);
            }}
          >
            批量编辑
          </Button>
        );
      },
    },
  ];

  // 调整批量编辑处理逻辑，确保初始展开时数据同步
  const handleBatchEdit = (record: any) => {
    // 保存原始数据（包含所有批次的销售价）
    setOriginalBatchData([...record.batchData]);
    // 初始化父表格显示的销售价（取第一个批次的价格）
    const firstBatchPrice = record.batchData[0]?.productAuthorizeMigrateSalePrice || 0;
    setDataSource((prev) =>
      prev.map((item) =>
        item.ticketGoodsId === record.ticketGoodsId
          ? { ...item, productAuthorizeMigrateSalePrice: firstBatchPrice }
          : item,
      ),
    );
    setBatchEditingRowKey(record.ticketGoodsId);
    setBatchEditing(true);
    // 强制展开当前行
    setExpandedRowKeys([record.ticketGoodsId]);
  };

  // 添加库存批次配置
  // 表格配置
  const selectTableColumns: ProColumns<API.ProductConfigListItem>[] = [
    {
      title: 'productId',
      dataIndex: 'productId',
      search: false,
      hideInTable: true,
    },
    {
      title: '库存批次号',
      width: 180,
      editable: false,
      dataIndex: 'batchId',
      search: true,
      hideInTable: true,
      render: (dom, record: any) => (
        <Space>
          <span>
            {dom}
            {record.isExchange == 1 && (
              <>
                {' '}
                <Tag color="blue">交易所</Tag>
              </>
            )}
          </span>
        </Space>
      ),
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSupplierListReq.data,
        showSearch: true,
      },
    },
    {
      title: '商品名称',
      width: 180,
      editable: false,
      dataIndex: 'ticketGoodsName',
      fixed: 'left',
      search: true,
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      valueEnum: ticketTypeEnum,
      editable: false,
      search: true,
    },
    {
      title: '景区名称',
      dataIndex: 'scenicId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSenicListReq.data,
        showSearch: true,
      },
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      editable: false,
      search: false,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      search: false,
      editable: false,
    },
    {
      title: '产品名称',
      dataIndex: 'ticketProductName',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getProductListReq.data,
        showSearch: true,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'ticketProductName',
      search: false,
      editable: false,
    },
    {
      title: '产品类型',
      dataIndex: 'ticketProductType',
      valueEnum: productTypeEnum,
      editable: false,
    },
    {
      title: '数字资产',
      dataIndex: 'isDigit',
      search: false,
      editable: false,
      render: (val) => {
        return val == '1' ? '是' : '否';
      },
    },
    {
      title: '分时时段',
      dataIndex: 'beginTime',
      search: false,
      editable: false,
      render: (_, { timeShareVoList }) => {
        return (
          <span>
            {timeShareVoList?.length
              ? (timeShareVoList || [])?.map((_item) => (
                  <Tag key={_item?.id}>
                    {_item?.beginTime} - {_item?.endTime}
                  </Tag>
                ))
              : '-'}
          </span>
        );
      },
    },
    {
      title: '特殊折扣率',
      search: false,
      dataIndex: 'overallDiscount',
      editable: false,
      render: (dom: any) => `${dom}%`,
    },
    {
      title: '分销折扣区间',
      search: false,
      editable: false,
      render: (_, { beginDiscount, endDiscount }) => `${beginDiscount}% - ${endDiscount}%`,
    },
    {
      title: '购买有效时间',
      hideInTable: true,
      dataIndex: 'time1',
      valueType: 'dateRange',
      editable: false,
      search: {
        transform: (value) => {
          return {
            purchaseBeginTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            purchaseEndTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '入园有效时间',
      hideInTable: true,
      valueType: 'dateRange',
      dataIndex: 'time2',
      editable: false,
      search: {
        transform: (value) => {
          return {
            dayBegin: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            dayEnd: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '可用库存',
      dataIndex: 'availableNumber',
      hideInSearch: true,
      editable: false,
    },
    {
      title: '销售价（元）',
      search: false,
      dataIndex: 'productAuthorizeMigrateSalePrice',
      editable: false,
    },
  ];

  // 修改批量保存处理函数
  const handleBatchSave = async (record: any) => {
    try {
      setBatchSaving(true);

      // 获取当前展开行的所有批次数据
      const currentBatchData = dataSource.find(
        (item) => item.ticketGoodsId === batchEditingRowKey,
      )?.batchData;

      if (!currentBatchData || currentBatchData.length === 0) {
        message.warning('没有可编辑的数据');
        return;
      }

      // 收集所有需要保存的批次数据
      const batchesToSave: any[] = [];
      const newBatchIdsToClear: string[] = []; // 记录需要清除的新增批次ID

      currentBatchData.forEach((batch) => {
        // 只保存有修改的项
        const originalBatch = originalBatchData.find((b) => b.batchId === batch.batchId);

        if (
          originalBatch &&
          batch.productAuthorizeMigrateSalePrice !== originalBatch.productAuthorizeMigrateSalePrice
        ) {
          batchesToSave.push({
            ...batch,
            productId: record.ticketProductId,
            salePrice: batch.productAuthorizeMigrateSalePrice,
            configGoodsDetail: [
              {
                goodsId: record.ticketGoodsId,
                goodsName: record.ticketGoodsName,
              },
            ],
          });
        }

        // 如果是新增批次，记录需要清除的ID
        if (newAddedBatchIds.has(batch.batchId)) {
          newBatchIdsToClear.push(batch.batchId);
        }
      });

      if (batchesToSave.length === 0) {
        message.warning('没有需要保存的修改');
        setBatchEditing(false);
        setBatchEditingRowKey(null);
        return;
      }

      // 一次性保存所有修改的数据
      await actionSaveProductConfig(batchesToSave);

      // 保存成功后清除这些批次的新增标记
      newBatchIdsToClear.forEach((batchId) => {
        clearNewBatch(batchId);
      });

      // message.success(`批量保存成功，共更新了 ${batchesToSave.length} 条记录`);
      setBatchEditing(false);
      setBatchEditingRowKey(null);
      actionRef.current?.reload();
    } catch (error) {
      console.error('批量保存失败:', error);
      message.error('批量保存失败');
    } finally {
      setBatchSaving(false);
    }
  };

  // 批量取消处理函数
  const handleBatchCancel = () => {
    // 还原数据
    setDataSource((prev) =>
      prev.map((item) => {
        if (item.ticketGoodsId === batchEditingRowKey) {
          return {
            ...item,
            batchData: [...originalBatchData],
          };
        }
        return item;
      }),
    );

    setBatchEditing(false);
    setBatchEditingRowKey(null);
    setOriginalBatchData([]);
  };

  /**
   * 分销商分组 - 配置产品 修改
   */
  const actionSaveProductConfig = async (
    records: (API.ProductConfigListItem & { index?: number | undefined })[],
  ) => {
    // 批量保存接口调用
    await saveProductConfig({
      groupId,
      list: records, // 传递整个数组
    });

    // 更新引导
    updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_2 });

    addOperationLogRequest({
      action: 'edit',
      module: saleGroupTabKey,
      content: `编辑【${groupName}】销售授权`,
    });
    // actionRef?.current?.reload();
    message.success('保存成功');
  };

  /**
   * 分销商分组 - 配置产品 删除
   */
  const actionDeleteProductConfig = async (record: API.ProductConfigListItem) => {
    const result: any = await saveProductConfig({
      groupId,
      batchId: record.batchId,
    });
    const success = result.code == 20000;
    if (success) {
      addOperationLogRequest({
        action: 'del',
        module: saleGroupTabKey,
        content: `删除【${groupName}】销售授权`,
      });
    }
    return success;
    // return success ? Promise.resolve() : Promise.reject();
  };
  const saleGroupTabKey = useContext(SaleGroupTabKeyContext);

  /**
   * 处理数据的方法：根据selectedBatchItems与data的匹配情况，生成新数据
   * @param {Array} data 原始数据数组
   * @param {Array} selectedBatchItems 待合并的批次数据数组
   * @returns {Array} 处理后的新数据数组和需要展开的行ID
   */
  function processData(data, selectedBatchItems) {
    // 深拷贝原数据，避免修改原始数据
    const newData = JSON.parse(JSON.stringify(data));
    const targetBatches = JSON.parse(JSON.stringify(selectedBatchItems));

    // 边界处理：如果selectedBatchItems为空，直接返回原数据副本
    if (targetBatches.length === 0) {
      return { newData, expandedKey: null };
    }

    // 获取selectedBatchItems第一项的parentGoods及目标ticketGoodsId
    const firstParentGoods = targetBatches[0].parentGoods;
    if (!firstParentGoods || !firstParentGoods.ticketGoodsId) {
      return { newData, expandedKey: null }; // 若parentGoods或其ticketGoodsId不存在，返回原数据
    }
    const targetGoodsId = firstParentGoods.ticketGoodsId;

    // 在data中查找ticketGoodsId匹配的项
    const matchedItemIndex = newData.findIndex((item) => item.ticketGoodsId === targetGoodsId);

    let expandedKey = null;

    if (matchedItemIndex !== -1) {
      // 情况1：找到匹配项，合并batchData并去重
      const matchedItem = newData[matchedItemIndex];

      // 从原数组中移除匹配项
      newData.splice(matchedItemIndex, 1);

      // 处理原batchData（若为null则转为空数组）
      const originalBatches = matchedItem.batchData ? [...matchedItem.batchData] : [];

      // 提取targetBatches中的batch部分（去除parentGoods引用）
      const batchItems = targetBatches.map((batch) => ({
        ...batch,
        parentGoods: undefined, // 移除parentGoods引用，避免循环结构
      }));

      // 合并原batchData与提取的batchItems
      const combinedBatches = [...originalBatches, ...batchItems];

      // 根据batchId去重（保留首次出现的项）
      const batchIdSet = new Set();
      const uniqueBatches = combinedBatches.filter((batch) => {
        if (!batchIdSet.has(batch.batchId)) {
          batchIdSet.add(batch.batchId);
          return true;
        }
        return false;
      });

      // 更新匹配项的batchData
      matchedItem.batchData = uniqueBatches;
      // 关闭编辑状态
      matchedItem.editable = false;

      // 将处理后的项插入到数组首位
      newData.unshift(matchedItem);
      // 设置需要展开的行ID
      expandedKey = matchedItem.ticketGoodsId;
    } else {
      // 情况2：未找到匹配项，将parentGoods插入到data最前面
      // 为parentGoods设置正确的batchData（使用targetBatches）
      const newParentGoods = {
        ...firstParentGoods,
        batchData: targetBatches.map((batch) => ({
          ...batch,
          parentGoods: undefined, // 移除parentGoods引用，避免循环结构
        })),
        editable: false, // 确保新增项默认关闭编辑状态
      };

      // 插入到数组首位
      newData.unshift(newParentGoods);
      // 设置需要展开的行ID
      expandedKey = newParentGoods.ticketGoodsId;
    }

    // 关闭所有数据的编辑状态
    newData.forEach((item) => {
      item.editable = false;
    });

    if (expandedKey) {
      const newBatchIds = new Set(selectedBatchItems.map((item) => item.batchId));
      setNewAddedBatchIds(newBatchIds);
    }

    return { newData, expandedKey };
  }

  // 清除批次标记的函数
  const clearNewBatch = (batchId: string) => {
    setNewAddedBatchIds((prev) => {
      const newSet = new Set(prev);
      newSet.delete(batchId);
      return newSet;
    });
  };

  return (
    <>
      <ProCard
        title={
          <div
            className="flex align-items-center primary-color pointer"
            onClick={() => modalState.setType(null)}
          >
            <LeftOutlined style={{ marginRight: 10 }} />
            销售授权
          </div>
        }
        headerBordered
      >
        <EditableProTable<API.ProductConfigListItem, any>
          style={{ width: '100%' }}
          {...tableConfig}
          rowKey="ticketGoodsId"
          params={{ distributorId: coId, groupId, saleAuthorizeBool: true }}
          columns={tableColumns}
          actionRef={actionRef}
          editableFormRef={formRef}
          postData={(data: any[]) => {
            if (selectedBatchItems.length > 0) {
              // 处理数据并获取需要展开的行ID
              const { newData, expandedKey } = processData(data, selectedBatchItems);
              const pageSize = actionRef?.current?.pageInfo?.pageSize ?? 10;

              // 把数据量改为当前设置的每页数据页，防数据越界后导致分页功能失效
              if (newData.length > pageSize) {
                newData.length = pageSize;
              }

              // 设置数据源
              setDataSource(newData);

              // 设置展开行，只展开新置顶的数据，收起其他所有行
              if (expandedKey) {
                setExpandedRowKeys([expandedKey]);
              } else {
                setExpandedRowKeys([]);
              }

              setSelectedBatchItems([]);
              return newData;
            }

            // 关闭所有编辑状态
            const closedEditData = data.map((item) => ({ ...item, editable: false }));
            setDataSource(closedEditData);
            return closedEditData;
          }}
          recordCreatorProps={false}
          pagination={{
            defaultPageSize: 10,
          }}
          toolBarRender={() => {
            return [
              <Button
                type="primary"
                key="save"
                onClick={async () => {
                  setCopyVisible(true);
                }}
              >
                添加
              </Button>,
            ];
          }}
          editable={{
            type: 'multiple',
            deleteText: <></>,
            actionRender: (record, config, defaultDom) => {
              return (
                <Flex gap={24}>
                  {defaultDom.save}
                  <Button
                    type="link"
                    onClick={async () => {
                      setExpandedRowKeys((source) => {
                        return [...source].filter((i) => i != record?.batchId);
                      });
                      setDataSource((source) => {
                        const newData = [...source];
                        newData[record.index!!].editable = false;
                        return newData;
                      });
                      config?.cancelEditable?.(record?.batchId);
                    }}
                    style={{ padding: 0, height: 'auto' }}
                  >
                    取消
                  </Button>
                </Flex>
              );
            },
            onSave: async (key, record) => {
              // 调整为传入数组格式
              await actionSaveProductConfig([record]);
              setDataSource((source) => {
                const newData = [...source];
                newData[record.index!!].editable = false;
                return newData;
              });
            },
          }}
          expandable={{
            expandedRowRender: (record: any, index: any, indent, expanded) => {
              //这一步关键，关掉后清掉渲染，再开启再绘制才能刷新数据
              return expanded ? (
                <GoodsTable
                  products={dataSource}
                  setProduct={setDataSource}
                  index={index}
                  setDataSource={setDataSource}
                  formRef={formRef}
                  setExpandedRowKeys={setExpandedRowKeys}
                  actionDeleteProductConfig={actionDeleteProductConfig}
                  actionRef={actionRef}
                  actionSaveProductConfig={actionSaveProductConfig}
                  // 新增批量编辑相关属性
                  batchEditing={batchEditing && batchEditingRowKey === record.ticketGoodsId}
                  batchSaving={batchSaving}
                  originalBatchData={originalBatchData}
                  newAddedBatchIds={newAddedBatchIds}
                  clearNewBatch={clearNewBatch}
                />
              ) : (
                <></>
              );
            },
            showExpandColumn: true,
            expandedRowKeys: expandedRowKeys,
            onExpand: (expanded, record: any) => {
              //手动设置展开
              const source = [...expandedRowKeys];
              if (expanded) {
                source.push(record.ticketGoodsId);
              } else {
                source.splice(source.indexOf(record.ticketGoodsId), 1);
              }
              setExpandedRowKeys(source);
            },
          }}
          request={async (param: any) => {
            const { data, code } = await getProductConfigListNew(param);
            // 初始加载数据时关闭所有编辑状态
            const formattedData = data.data.map((item) => ({ ...item, editable: false }));
            return {
              data: formattedData,
              success: code == 20000,
              total: data.total,
            };
          }}
        />
      </ProCard>

      {/* 选择 */}
      <Modal
        width={1400}
        title="添加库存批次"
        visible={copyVisible}
        onCancel={() => {
          setCopyVisible(false);
          setSelectedBatchItems([]);
        }}
        footer={
          <Space>
            <Text strong>*添加后请填写销售价并保存，否则添加无效</Text>
            <Button
              onClick={() => {
                setCopyVisible(false);
                setSelectedBatchItems([]);
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              disabled={selectedBatchItems.length === 0}
              onClick={() => {
                actionRef?.current?.reload();
                setCopyVisible(false);
              }}
            >
              确定 ({selectedBatchItems.length})
            </Button>
          </Space>
        }
        destroyOnClose={true}
      >
        <ProTable
          {...tableConfig}
          rowKey="ticketGoodsId"
          params={{ distributorId: coId, groupId: groupId, saleAuthorizeBool: false }}
          request={async (params) => {
            const { data } = await getProductConfigListNew(params);
            return data;
          }}
          pagination={{
            defaultPageSize: 10,
          }}
          columns={selectTableColumns}
          options={false}
          expandable={{
            expandedRowRender: (record) => (
              <GoodsTableForSelection
                goods={record}
                selectedBatchItems={selectedBatchItems}
                setSelectedBatchItems={setSelectedBatchItems}
              />
            ),
            rowExpandable: (record) => record.batchData && record.batchData.length > 0,
            // 确保展开行时保留选择状态
            preserveExpandedRowKeys: true,
          }}
        />
      </Modal>
    </>
  );
};

// 新增组件：用于批次选择的GoodsTable
const GoodsTableForSelection = ({ goods, selectedBatchItems, setSelectedBatchItems }) => {
  const columns = [
    {
      title: '库存批次号',
      dataIndex: 'batchId',
      editable: false,
      render: (dom, record: any) => (
        <Space>
          <span>
            {dom}
            {record.isExchange == 1 && (
              <>
                {' '}
                <Tag color="blue">交易所</Tag>
              </>
            )}
          </span>
        </Space>
      ),
    },
    {
      title: '购买有效时间',
      dataIndex: 'purchaseTime',
      editable: false,
      render: (dom: any, record, index, __, schema: any) => (
        <span>{`${record.purchaseBeginTime}至${record.purchaseEndTime}`}</span>
      ),
    },
    {
      title: '入园有效时间',
      dataIndex: 'dayTime',
      editable: false,
      render: (dom: any, record, index, __, schema: any) => (
        <span>{`${record.dayBegin}至${record.dayEnd}`}</span>
      ),
    },
    {
      title: '可用库存',
      dataIndex: 'availableNumber',
      editable: false,
    },
    {
      title: '采购价（元）',
      dataIndex: 'purchasePrice',
      editable: false,
      renderText: (dom) => {
        const items = dom.map((price: any) => {
          const p = parseFloat(price) ? parseFloat(price).toFixed(2) : price;
          return <div>{p}</div>;
        });
        return <div>{items.length > 0 ? items : '-'}</div>;
      },
    },
    {
      title: '销售价（元）',
      dataIndex: 'productAuthorizeMigrateSalePrice',
      editable: false,
    },
  ];

  // 处理行选择变化
  const handleRowSelectionChange = (selectedRowKeys: React.Key[], selectedRows: any[]) => {
    // 构建包含父商品信息的批次数据
    const newGood = JSON.parse(JSON.stringify(goods));
    newGood.batchData = null;
    const formattedItems = selectedRows.map((row) => ({
      ...row,
      parentGoods: newGood,
    }));
    // 更新选中的批次
    setSelectedBatchItems(formattedItems);
  };

  // 判断行是否被选中
  const isRowSelected = (record: any) => {
    return selectedBatchItems.some(
      (item) =>
        item.batchId === record.batchId && item.parentGoods.ticketGoodsId === goods.ticketGoodsId,
    );
  };

  return (
    <EditableProTable
      {...tableConfig}
      columns={columns}
      rowKey="batchId"
      value={goods?.batchData}
      search={false}
      options={false}
      pagination={false}
      recordCreatorProps={false} // 禁止添加新行
      rowSelection={{
        type: 'checkbox',
        onChange: handleRowSelectionChange,
        selectedRowKeys: selectedBatchItems
          .filter((item) => item.parentGoods.ticketGoodsId === goods.ticketGoodsId)
          .map((item) => item.batchId),
        getCheckboxProps: (record) => ({
          checked: isRowSelected(record),
          disabled: record.isExchange == 1 || record.isAuthorization == 1,
        }),
      }}
    />
  );
};

export default ProductConfig;
