import guideIcon from '@/assets/guide.svg';
import NewbieTask from '@/pages/system/Guide/NewbieTask';
import { Divider, Drawer } from 'antd';
import { useState } from 'react';
import styles from './index.module.scss';

const GuideDrawer = () => {
  const [mode, setMode] = useState('icon');

  return (
    <>
      {mode === 'icon' ? (
        <div className={styles.guideIcon} onClick={() => setMode('drawer')}>
          <img src={guideIcon} />
          <Divider />
          <span>新手</span>
          <span>指南</span>
        </div>
      ) : (
        <Drawer
          title=""
          closable={false}
          open={mode == 'drawer'}
          onClose={() => setMode('icon')}
          width={408}
          rootClassName={styles.MessageDrawer}
          destroyOnClose
        >
          <NewbieTask type="collapse" onClose={() => setMode('icon')} />
        </Drawer>
      )}
    </>
  );
};

export default GuideDrawer;
