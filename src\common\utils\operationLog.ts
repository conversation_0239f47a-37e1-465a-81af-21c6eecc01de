import type { RouterTypes } from '@ant-design/pro-components';
import type { TabsProps } from 'antd';
import type { TreeSelectProps } from 'antd/lib/tree-select';
import { isEmpty, isEqual } from 'lodash';
import type { ReactNode } from 'react';
import { request } from '@umijs/max';
import Routes from '../../../config/routes';
import { getEnv } from '@/common/utils/getEnv';

export interface LogListItem {
  dataIndex: string;
  title: string;
  valueEnum?: Record<string, string>;
  renderText?: (val: any, record: Record<string, any>) => ReactNode | string;
  [key: string]: any;
}

interface OperationLogRequestParams {
  /**  操作 */
  action: keyof typeof actionType;
  /**  操作内容 */
  content: string;
  /**  操作变化内容 (新增、编辑时候传) */
  changeConfig?: {
    list: LogListItem[];
    beforeData?: Record<string, any>;
    afterData: Record<string, any>;
  };
  [key: string]: any;
}

// 操作日志 - 功能枚举
export const actionType = {
  add: '新增',
  edit: '编辑',
  del: '删除',
  info: '查看',
  disable: '启用/禁用',
  login: '登录',
  link: '跳转',
  audit: '审核',
  migrate: '迁移',
  invite: '邀请',
  copy: '复制',
  select: '选择',
  confirm: '确认',
  export: '导出',
};

export const detailActions: (keyof typeof actionType)[] = ['edit', 'migrate'];

// 额外的路由
const extraRoutes: Record<string, TabsProps['items']> = {
  // wR7hV1qS: saleGroupTabItems,
};

// 深度遍历筛选路由
export function deepMapRoutes(routes: RouterTypes<any>['route'][]) {
  const result: RouterTypes<any>['route'][] = [];

  routes.forEach((i) => {
    const { hideInMenu, id, name, path, layout, routes: childrenRoutes } = i || {};
    // 过滤路由的初始条件
    if (!hideInMenu && !layout && id) {
      if (isEmpty(childrenRoutes)) {
        const extraRoute = extraRoutes[id];
        result.push({
          path,
          id,
          name,
          title: name,
          value: id,
          ...(extraRoute
            ? {
                children: (extraRoute ?? []).map(({ key, label }) => ({
                  path: '',
                  id: key,
                  name: label,
                  title: label,
                  value: key,
                })),
              }
            : null),
        });
      } else {
        // 有子路由嵌套 递归
        const children = deepMapRoutes(childrenRoutes || []);
        result.push({
          path,
          id,
          name,
          title: name,
          value: id,
          children,
        });
      }
    }
  });

  return result;
}

// 根据地址栏路由找到 id
function findIDByPath(urlArr: string[], treeData: RouterTypes<any>['route'][]) {
  const url = urlArr.shift();
  const current = treeData.find((i) => i?.path === url);

  if (url && current) {
    if (current.children && !isEmpty(urlArr)) {
      return findIDByPath(urlArr, current.children);
    } else {
      return current.id;
    }
  }
  return null;
}

// 获取公共参数
function getCommonParams() {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const companyId = localStorage.getItem('currentCompanyId');
  // 路由
  const path = location.hash.split('?')[0].replace('#/', '').split('/');
  //  生成树
  const operationMenuTree = deepMapRoutes(Routes);
  const module = findIDByPath(structuredClone(path), operationMenuTree);
  return {
    module,
    project: companyId,
    app: getEnv().APPID,
    creator: userInfo.username || userInfo.phone,
    nickname: userInfo.nickname,
  };
}

// 根据 id 找到中文菜单
export function findNamePathByID(
  treeData: TreeSelectProps['treeData'] = [],
  id: string,
  currentPath: string[] = [],
): string | null {
  for (const item of treeData) {
    currentPath.push(item.title as string);

    if (item.value === id) {
      return currentPath.join('-');
    }
    if (!isEmpty(item.children)) {
      const result = findNamePathByID(item.children, id, currentPath);

      if (result) {
        return result;
      }
    }
    currentPath.pop();
  }

  return null;
}

// 新增操作日志请求
export function addOperationLogRequest(params: OperationLogRequestParams) {
  const { action, content, changeConfig, ...rest } = params;

  const changeData: any[] = [];
  // 编辑需处理数据变化
  if (changeConfig && detailActions.includes(action)) {
    const { afterData = {}, list = [], beforeData = {} } = changeConfig;
    console.log(beforeData, afterData);
    list.forEach(({ title, dataIndex, valueEnum = {}, renderText = () => {} }) => {
      const before =
        renderText(beforeData[dataIndex], beforeData) ??
        valueEnum[beforeData[dataIndex]] ??
        beforeData[dataIndex];
      const after =
        renderText(afterData[dataIndex], afterData) ??
        valueEnum[afterData[dataIndex]] ??
        afterData[dataIndex];
      //有变化 记录
      if ((before || after) && !isEqual(before, after)) {
        changeData.push({
          key: dataIndex,
          name: title,
          before,
          after,
        });
      }
    });
  }

  const commonParams = getCommonParams();

  return request(`${getEnv().OPERATION_HOST}/info`, {
    method: 'POST',
    data: {
      ...commonParams,
      function: action,
      content,
      contentDetails: JSON.stringify(changeData),
      ...rest,
    },
    skipErrorHandler: true,
  });
}
