import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import useModal from '@/hooks/useModal';
import {
  apiAgentInfo,
  apiAgentRelation,
  apiAgentShowCredentials,
  apiDealerInfo,
  apiRemoveDistributor,
} from '@/services/api/distribution';
import { ProTable, type ActionType, type ProColumns } from '@ant-design/pro-components';
import { useLocation } from '@umijs/max';
import { Modal, Popconfirm, Space, Tabs, message } from 'antd';
import Paragraph from 'antd/lib/typography/Paragraph';
import React, { useEffect, useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import CommissionStrategy from './CommissionStrategy';

let isClick = true;
const TableList: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const initialName = searchParams.get('type');
  const { coId = '' } = initialState?.currentCompany || {};
  const access = useAccess();
  const strategyState = useModal();
  const [tableItem, setTableItem] = useState<any>();

  const menuItem: { label: string; key: string }[] = [];
  if (access.canSupplierManagement_dealerSelect) {
    menuItem.push({
      label: '经销供应商',
      key: 'sell',
    });
  }
  if (access.canSupplierManagement_agentSelect) {
    menuItem.push({
      label: '代理供应商',
      key: 'agency',
    });
  }

  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      title: '供应商名称',
      dataIndex: 'coName',
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
      hideInSearch: true,
    },
    {
      title: '手机号码',
      dataIndex: 'coPhone',
      hideInSearch: true,
    },
    {
      title: '电子邮箱',
      dataIndex: 'coEmail',
      hideInSearch: true,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
      hideInSearch: true,
    },
    {
      title: '地址',
      dataIndex: 'coAddressInfo',
      hideInSearch: true,
      render: (_, item: any) => (
        <>{item.coProvinceName + item.coCityName + item.coAreaName + item.coAddressInfo || '-'}</>
      ),
    },
    {
      title: '商户账号',
      dataIndex: 'settlementName',
      hideInSearch: true,
    },
    {
      width: 120,
      title: '操作',
      valueType: 'option',
      hideInTable: !access.canSupplierManagement_dealerDeleteSuppliers,
      fixed: 'right',
      render: (_, entity) => [
        <Access key="access" accessible={access.canSupplierManagement_dealerDeleteSuppliers}>
          <a onClick={() => {}}>
            <Popconfirm
              placement="top"
              onConfirm={async () => {
                try {
                  await apiRemoveDistributor({
                    downDistributorId: coId,
                    upDistributorId: entity.coId,
                  });

                  addOperationLogRequest({
                    action: 'del',
                    content: `删除供应商【${entity.coName}】`,
                  });

                  message.success('删除成功');
                  actionRef.current?.reload();
                } catch (error) {}
              }}
              okText="是"
              cancelText="否"
              title={'确定删除吗？'}
            >
              <span style={{ color: 'red' }}>删除</span>
            </Popconfirm>
          </a>
        </Access>,
      ],
    },
  ];
  const columnsAgency: ProColumns[] = [
    {
      title: '供应商名称',
      dataIndex: 'coName',
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
      hideInSearch: true,
    },
    {
      title: '手机号码',
      dataIndex: 'coPhone',
      hideInSearch: true,
    },
    {
      title: '电子邮箱',
      dataIndex: 'coEmail',
      hideInSearch: true,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
      hideInSearch: true,
    },
    {
      title: '地址',
      dataIndex: 'coAddressInfo',
      hideInSearch: true,
      render: (_, item: any) => (
        <>{item.coProvinceName + item.coCityName + item.coAreaName + item.coAddressInfo || '-'}</>
      ),
    },
    {
      title: '商户账号',
      dataIndex: 'settlementName',
      hideInSearch: true,
    },
    {
      width: 120,
      title: '操作',
      key: 'option',
      valueType: 'option',
      hideInTable:
        !access.canSupplierManagement_agentDeleteSupplier &&
        !access.canSupplierManagement_agentSelectAppID,
      fixed: 'right',
      render: (_, entity) => (
        <Space size="large">
          <Access key="SelectAppID" accessible={access.canSupplierManagement_agentSelectAppID}>
            <a
              onClick={async () => {
                if (isClick) {
                  isClick = false;
                } else {
                  return;
                }
                try {
                  const { data } = await apiAgentShowCredentials({
                    upDistributorId: entity.coId,
                    downDistributorId: coId,
                    type: 2,
                  });
                  addOperationLogRequest({
                    action: 'info',
                    content: `查看【${entity.coName}】授权码信息`,
                  });
                  isClick = true;
                  if (!data.appId) {
                    message.info('该供应商未对当前企业生成授权码信息');
                    return;
                  }
                  Modal.info({
                    title: '授权码信息',
                    content: (
                      <Paragraph
                        copyable={{
                          text: `AppID: ${data.appId}; AppSecret: ${data.appSecret}`,
                          tooltips: ['复制', '已复制'],
                        }}
                      >
                        <div>
                          <span
                            style={{ width: '70px', textAlign: 'end', display: 'inline-block' }}
                          >
                            AppID:{' '}
                          </span>
                          <code style={{ marginLeft: '8px' }}>{data.appId}</code>
                        </div>
                        <span>
                          <span
                            style={{ width: '70px', textAlign: 'end', display: 'inline-block' }}
                          >
                            AppSecret:{' '}
                          </span>
                          <code style={{ marginLeft: '8px' }}>{data.appSecret}</code>
                        </span>
                      </Paragraph>
                    ),
                    okText: '关闭',
                    onOk() {},
                  });
                } catch (error) {}
              }}
            >
              查看授权码信息
            </a>
          </Access>
          <a
            key="commissionStrategy"
            onClick={() => {
              setTableItem(entity);
              strategyState.setTypeWithVisible('info');
            }}
          >
            佣金策略
          </a>
          <Access
            key="DeleteSupplier"
            accessible={access.canSupplierManagement_agentDeleteSupplier}
          >
            <a onClick={() => {}}>
              <Popconfirm
                placement="top"
                onConfirm={async () => {
                  try {
                    await apiAgentRelation({
                      downDistributorId: coId,
                      upDistributorId: entity.coId,
                    });

                    addOperationLogRequest({
                      action: 'del',
                      content: `删除【${entity.coName}】`,
                    });

                    message.success('移除成功');
                    actionRef?.current?.reload();
                  } catch (error) {}
                }}
                okText="是"
                cancelText="否"
                title={'确定删除吗？'}
              >
                <span style={{ color: 'red' }}>删除</span>
              </Popconfirm>
            </a>
          </Access>
        </Space>
      ),
    },
  ];
  const [columnsKey, setColumnsKey] = useState<any>(
    access.canSupplierManagement_dealerSelect ? 'sell' : 'agency',
  );

  useEffect(() => {
    if (initialName) {
      setColumnsKey(initialName);
    }
  }, [initialName]);

  return (
    <Access
      accessible={
        access.canSupplierManagement_dealerSelect || access.canSupplierManagement_agentSelect
      }
    >
      <Tabs
        style={{ background: '#fff' }}
        tabBarStyle={{ padding: '0 24px', margin: '0' }}
        activeKey={columnsKey}
        onChange={(activeKey) => {
          setColumnsKey(activeKey);
          actionRef?.current?.reload();
        }}
        items={menuItem}
      />
      <ProTable
        {...tableConfig}
        actionRef={actionRef}
        params={{ distributorId: coId, type: columnsKey == 'sell' ? 1 : 2 }}
        request={columnsKey == 'sell' ? apiDealerInfo : apiAgentInfo}
        columns={columnsKey == 'sell' ? columns : columnsAgency}
      />
      <CommissionStrategy modalState={strategyState} currentItem={tableItem} />
    </Access>
  );
};

export default TableList;
