import ChainModal from '@/common/components/ChainModal';
import DetailsPop from '@/common/components/DetailsPop';
import Export, { columnsSet } from '@/common/components/Export';
import useExport from '@/common/components/Export/useExport';
import Tag from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import {
  chainStatusEnum,
  orderTicketTypeEnum,
  orderTypeEnum,
  payTypeEnum,
  productTypeEnum,
  saleChannelEnum,
  ticketStatusEnum,
  ticketTypeEnum,
  whetherEnum,
} from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { DataMaskTypeEnum } from '@/common/utils/tool';
import Blockchain from '@/components/Blockchain';
import DataMask from '@/components/DataMask';
import { showTicketModal } from '@/global';
import { useMask } from '@/hooks/useMask';
import {
  apiStoreOrderInfo,
  apiStoreReturnOrderInfo,
  apiStoreReturnOrderList,
} from '@/services/api/store';
import { CopyOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Space, Table, message } from 'antd';
import copy from 'copy-to-clipboard';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import { useContext, useState } from 'react';
import { TabKeyContext } from '../..';
import styles from '../../index.less';

export default ({
  // visible,
  // setVisible,
  storeId,
}: {
  // visible: boolean;
  // setVisible: Function;
  storeId: string;
}) => {
  const tabKey = useContext(TabKeyContext);

  const [handleDetailsMaskChange, maskDetailsDataFn] = useMask();
  const [handleOrderDetailsMaskChange, maskOrderDetailsDataFn] = useMask();

  const columns: ProColumns<any>[] = [
    {
      width: 170,
      title: '退单号',
      dataIndex: 'refundId',
      render: (dom: any) => (
        <>
          <a
            title={dom}
            onClick={async () => {
              try {
                setLoading(true);
                setDetailsVisible(true);
                const { data } = await apiStoreReturnOrderInfo({ id: dom });
                console.log(data);

                setDataSource(data);
                setLoading(false);
              } catch (error) {}
            }}
          >
            {dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}
          </a>
          <CopyOutlined
            style={{ marginLeft: '4px', color: '#1890ff' }}
            onClick={() => {
              copy(dom);
              message.success('已复制');
            }}
          />
        </>
      ),
    },
    {
      title: '对应订单号',
      dataIndex: 'orderGroupId',
      hideInTable: true,
    },
    {
      width: 170,
      title: '对应订单号',
      dataIndex: 'orderId',
      search: false,
      render: (dom: any, entity: any, index: any, __: any, schema: any) => (
        <>
          <a
            title={dom}
            onClick={async () => {
              try {
                setLoading(true);
                if (entity.orderType == 'JQTC') {
                  setDetailsVisibleCard(true);
                  const { data } = await apiStoreOrderInfo({ orderId: dom });
                  setDataSource({
                    ...data.order,
                    // ...data.orderStatus,
                    ticketInfo: data.orderTravelCard,
                  });
                } else {
                  setDetailsVisible2(true);
                  const { data } = await apiStoreOrderInfo({ orderId: dom });
                  console.log('datadatadata', data);
                  setDataSource({
                    ...data.order,
                    // ...data.orderStatus,
                    ticketInfo: dom, //把定单 ID 向下传，因为其子窗口里还嵌有表格，相当于这个接口要调两次
                  });
                }
                setLoading(false);
              } catch (error) {}
            }}
          >
            {dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}
          </a>
          <span
            style={{ marginLeft: '4px', cursor: 'pointer' }}
            onClick={(e) => {
              // console.log(index, schema.dataIndex);
              // setCopyObj(!copyObj[index + schema.dataIndex])
              copy(dom);
              message.success('已复制');
            }}
          >
            <CopyOutlined style={{ color: '#1890ff' }} />
            {/* {copyObj[index + schema.dataIndex]
              ? <CheckOutlined style={{ color: '#52c41a' }} />
              : <CopyOutlined style={{ color: '#1890ff' }} />} */}
          </span>
        </>
      ),
    },
    {
      title: '申请日期',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
    },

    {
      title: '退款状态',
      dataIndex: 'refundStatus',
      valueEnum: orderTypeEnum,
      search: false,
    },
    {
      title: '售票终端类型',
      dataIndex: 'sourceType',
      render: (dom: any) => saleChannelEnum[dom],
      hideInSearch: true,
    },
    {
      title: '售票设备名称',
      dataIndex: 'equipmentName',
      search: false,
    },
    {
      title: '登录账号',
      dataIndex: 'username',
    },
    {
      title: '退单金额（元）',
      dataIndex: 'amount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      search: false,
    },
    {
      title: '手续费（元）',
      dataIndex: 'refundFee',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      search: false,
    },
    {
      title: '退款金额（元）',
      dataIndex: 'refundAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      search: false,
    },
    {
      title: '交易上链',
      dataIndex: 'isChainOrderRefund',
      valueEnum: chainStatusEnum,
      renderText: (dom) => <Tag type="chainStatus" value={dom} />,
    },
    {
      title: '操作',
      dataIndex: '_option',
      valueType: 'option',
      render: (_, entity: any) => {
        return (
          <>
            {entity.txId ? (
              <ChainModal
                chainData={{
                  txId: entity.txId,
                }}
              />
            ) : (
              '-'
            )}
          </>
        );
      },
    },
  ];

  //查看区块链交易数据
  const [vray, setVray] = useState(false); //渲染
  const [cochainVisible, setCochainVisible] = useState(false);
  const [cochainDataDataSource, setCochainDataDataSource] = useState<any>({});
  const cochainColumns = [
    {
      title: '区块链账号',
      columns: [
        {
          title: '发行方',
          dataIndex: 'fromAccount',
          span: 5,
          render: (dom: any) => dom.length > 0 && dom.join('、'),
        },
        {
          title: '接收方',
          dataIndex: 'issuerAccount',
          span: 5,
          render: (dom: any) => dom.length > 0 && dom.join('、'),
        },
      ],
    },
    {
      title: '存证信息',
      columns: [
        {
          title: '存证时间',
          dataIndex: 'savedAt',
          span: 5,
        },
        {
          title: `存 证 号`,
          dataIndex: 'certId',
          span: 5,
        },

        {
          title: '存证内容',
          dataIndex: 'certContent',
          span: 5,
          render: (dom: any) => {
            return (
              <>
                <div className={!vray ? styles.dom : ''}>{dom}</div>
                <div
                  style={{ textAlign: 'right', cursor: 'pointer', color: '#1890ff' }}
                  onClick={() => setVray(!vray)}
                >
                  {vray ? '收起 ' : '展开 '}
                  {vray ? <UpOutlined /> : <DownOutlined />}
                </div>
              </>
            );
          },
        },
        {
          title: '交易哈希',
          dataIndex: 'txId',
          span: 5,
        },
      ],
    },
  ];

  // 【详情】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [detailsVisible2, setDetailsVisible2] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [detailsVisibleCard, setDetailsVisibleCard] = useState<boolean>(false);
  const columns2 = [
    {
      width: 154,
      title: '票号',
      dataIndex: 'ticketNumber',
      render: (dom: any) => {
        if (isEmpty(dom)) {
          return '-';
        }
        return (
          <>
            <span title={dom}>{dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}</span>
            <CopyOutlined
              style={{ color: '#1890ff' }}
              onClick={() => {
                copy(dom);
                message.success('已复制');
              }}
            />
          </>
        );
      },
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      render: (dom: any) => productTypeEnum[dom],
    },
    {
      title: '票种',
      dataIndex: 'ticketType',
      render: (dom: any) => ticketTypeEnum[dom],
    },
    {
      title: '销票终端',
      dataIndex: 'sourceType',
      render: (dom: any) => saleChannelEnum[dom],
    },
    {
      title: '退款状态',
      dataIndex: 'refundStatus',
      render: (dom: any) => orderTypeEnum[dom],
    },

    {
      title: '人数',
      dataIndex: 'playerNum',
    },
    {
      title: '已核销总次数',
      dataIndex: 'checkedNum',
    },
    {
      title: '姓名/身份证',
      dataIndex: 'realNameList',
      width: 150,
      render: (text: any[]) => {
        if (isEmpty(text || [])) {
          return '-';
        }
        const { idCardName, idCardNumber } = text[0] || {};
        return (
          <Space direction="vertical" size="small" align="center">
            <span>{maskDetailsDataFn(idCardName, DataMaskTypeEnum.NAME)}</span>
            <span>{maskDetailsDataFn(idCardNumber, DataMaskTypeEnum.ID_CARD)}</span>
            {text.length > 1 && (
              <a
                onClick={() => {
                  const maskedData = text.map((item) => ({
                    ...item,
                    idCardName: maskDetailsDataFn(item.idCardName, DataMaskTypeEnum.NAME),
                    idCardNumber: maskDetailsDataFn(item.idCardNumber, DataMaskTypeEnum.ID_CARD),
                  }));
                  showTicketModal(maskedData);
                }}
              >
                查看所有
              </a>
            )}
          </Space>
        );
      },
    },
    {
      title: '入园时间',
      dataIndex: 'day',
      valueType: 'date',
    },
    {
      title: '权益 ID',
      dataIndex: 'rightsId',
      render: (dom: any) => dom || '-',
    },
    {
      title: '状态',
      dataIndex: 'ticketStatus',
      render: (dom: 0) => ticketStatusEnum[dom],
    },
    {
      title: '退款金额（元）',
      dataIndex: 'refundAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '手续费（元）',
      dataIndex: 'refundFee',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '数字资产',
      dataIndex: 'isChainTicket',
      // valueEnum: whetherEnum,
      render: (text: any) => whetherEnum[text] || '-',
    },
  ];
  const orderColumns = [
    {
      width: 154,
      title: '票号',
      dataIndex: 'id',
      render: (dom: any) => {
        if (isEmpty(dom)) {
          return '-';
        }
        return (
          <>
            <span title={dom}>{dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}</span>
            <CopyOutlined
              style={{ color: '#1890ff' }}
              onClick={() => {
                copy(dom);
                message.success('已复制');
              }}
            />
          </>
        );
      },
    },

    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      render: (dom: any) => productTypeEnum[dom],
    },
    {
      title: '票种',
      dataIndex: 'type',
      render: (dom: any) => ticketTypeEnum[dom],
    },

    {
      title: '人数',
      dataIndex: 'playerNum',
    },
    {
      title: '已核销总次数',
      dataIndex: 'checkedNum',
    },
    {
      title: '姓名/身份证',
      dataIndex: 'realNameList',
      width: 150,
      render: (text: any[]) => {
        if (isEmpty(text || [])) {
          return '-';
        }
        const { idCardName, idCardNumber } = text[0] || {};
        return (
          <Space direction="vertical" size="small" align="center">
            <span>{maskOrderDetailsDataFn(idCardName, DataMaskTypeEnum.NAME)}</span>
            <span>{maskOrderDetailsDataFn(idCardNumber, DataMaskTypeEnum.ID_CARD)}</span>
            {text.length > 1 && (
              <a
                onClick={() => {
                  const maskedData = text.map((item) => ({
                    ...item,
                    idCardName: maskOrderDetailsDataFn(item.idCardName, DataMaskTypeEnum.NAME),
                    idCardNumber: maskOrderDetailsDataFn(
                      item.idCardNumber,
                      DataMaskTypeEnum.ID_CARD,
                    ),
                  }));
                  showTicketModal(maskedData);
                }}
              >
                查看所有
              </a>
            )}
          </Space>
        );
      },
    },
    {
      title: '出票时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
    },
    // {
    //   title: '退票时间',
    //   dataIndex: 'refundTime',
    //   valueType: 'dateTime',
    // },
    {
      title: '入园时间',
      dataIndex: 'enterTime',
      valueType: 'dateTime',
    },
    {
      title: '权益 ID',
      dataIndex: 'rightsId',
    },
    {
      title: '金额（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '佣金（元）',
      dataIndex: 'actualAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      valueType: 'digit',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (dom: any) => ticketStatusEnum[dom],
    },
    {
      title: '数字资产',
      dataIndex: 'isChain',
      // valueEnum: whetherEnum,
      render: (text: any) => whetherEnum[text] || '-',
    },

    // {
    //   title: '上链信息',
    //   dataIndex: 'createTime2',
    //   editable: true,
    //   hideInSearch: true,
    //   render:()=>{
    //     return <a onClick={onExamine}>查看</a>
    //   }
    // },
  ];
  const columnsDetail = [
    {
      title: '基本信息',
      columns: [
        {
          title: '退单号',
          dataIndex: 'refundId',
        },
        {
          title: '退款金额（元）',
          dataIndex: 'refundAmount',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
        // {
        //   title: '退款类别',
        //   dataIndex: '',
        // },
        {
          title: '退款原因',
          dataIndex: 'remark',
        },
        {
          title: '退款失败原因',
          dataIndex: 'failMessage',
        },
        {
          title: '退款到账时间',
          dataIndex: 'refundTime',
          valueType: 'dateTime',
        },
        {
          title: '订单状态',
          dataIndex: 'refundStatus',
          valueEnum: orderTypeEnum,
        },
        {
          title: '结算单号',
          dataIndex: 'tradeNo',
        },
        {
          title: '代理商',
          dataIndex: 'distributorName',
        },
        {
          title: `服务商`,
          dataIndex: 'serviceProviderName',
        },
        {
          title: '登录账号',
          dataIndex: 'username',
        },
        {
          title: '下单时间',
          dataIndex: 'createTime',
          renderText: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
        },
        {
          title: '购票终端',
          dataIndex: 'sourceType',
          // hideInSearch: 'false'
          hideInSearch: true,
          valueType: 'select',
          key: 'sourceType',
          valueEnum: saleChannelEnum,
        },
        {
          title: '支付方式',
          dataIndex: 'payType',
          search: false,
          valueEnum: payTypeEnum,
        },
        {
          title: '订单金额（元）',
          dataIndex: 'payAmount',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
        {
          title: '交易上链',
          dataIndex: 'isChainOrderRefund',
          valueEnum: chainStatusEnum,
          renderText: (dom) => <Tag type="chainStatus" value={dom} />,
        },
        // {
        //   title: '佣金（元）',
        //   dataIndex: 'commissionAmount',
        // },
      ],
    },
    {
      title: '票务信息',
      columns: [
        {
          title: '',
          dataIndex: 'productList',
          render: (dom: any) => (
            <Table {...tableConfig} columns={columns2} dataSource={dom} bordered size="middle" />
          ),
        },
      ],
    },
    // {
    //   title: '票务信息',
    //   columns: [
    //     {
    //       title: '',
    //       dataIndex: 'productList',
    //       render: (dom: any) => (
    //         <Table columns={columns2} dataSource={dom} bordered size="middle" />
    //       ),
    //     },
    //   ],
    // },
  ];
  const columnsDetail2 = [
    {
      title: '基本信息',
      columns: [
        {
          title: '子订单号',
          dataIndex: 'orderId',
        },
        {
          title: '订单类型',
          dataIndex: 'orderType',
          valueEnum: orderTicketTypeEnum,
        },
        {
          title: '订单状态',
          dataIndex: 'orderStatus',
          valueEnum: orderTypeEnum,
        },
        {
          title: '结算单号',
          dataIndex: 'tradeNo',
        },
        {
          title: '代理商',
          dataIndex: 'distributorName',
        },
        {
          title: `服务商`,
          dataIndex: 'serviceProviderName',
        },
        {
          title: '登录账号',
          dataIndex: 'username',
        },
        {
          title: '下单时间',
          dataIndex: 'createTime',
          renderText: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
        },
        {
          title: '购票终端',
          dataIndex: 'sourceType',
          search: false,
          valueEnum: saleChannelEnum,
        },
        {
          title: '支付方式',
          dataIndex: 'payType',
          search: false,
          valueEnum: payTypeEnum,
        },
        {
          title: '订单金额（元）',
          dataIndex: 'payAmount',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
        {
          title: '佣金（元）',
          dataIndex: 'commissionAmount',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
          // render: (dom, record) => {
          //   let count = 0;
          //   dom.forEach((e) => {
          //     count = count + e.actualAmount;
          //   });
          //   return count;
          // },
        },
        {
          title: '交易上链',
          dataIndex: 'isChainOrder',
          valueEnum: chainStatusEnum,
          renderText: (dom) => <Tag type="chainStatus" value={dom} />,
        },
      ],
    },
    {
      title: '票务信息',
      className: 'no-bgColor',
      columns: [
        {
          title: '',
          dataIndex: 'ticketInfo',
          span: 3,
          renderText: (orderId: any) => (
            <ProTable
              {...tableConfig}
              columns={orderColumns}
              style={{ width: '100%' }}
              search={false}
              options={false}
              params={{ orderId }}
              pagination={{
                defaultPageSize: 10,
              }}
              request={async (params: any) => {
                if (!params.orderId) {
                  //防空值刷新
                  return {};
                }
                try {
                  const { data } = await apiStoreOrderInfo(params);
                  const ticketInfoList = data.ticketInfoList;
                  return {
                    total: ticketInfoList.total,
                    data: ticketInfoList.data,
                    success: true,
                  };
                } catch (error) {
                  return {};
                }
              }}
            />
          ),
        },
      ],
    },
  ];
  const columnsDetailCard = [
    {
      title: '基本信息',
      columns: [
        {
          title: '子订单号',
          dataIndex: 'orderId',
        },
        {
          title: '结算单号',
          dataIndex: 'tradeNo',
        },
        {
          title: '订单类型',
          dataIndex: 'orderType',
          valueEnum: orderTicketTypeEnum,
        },
        {
          title: '订单状态',
          dataIndex: 'orderStatus',
          valueEnum: orderTypeEnum,
        },
        {
          title: '支付方式',
          dataIndex: 'payType',
          valueEnum: payTypeEnum,
        },
        {
          title: '支付金额（元）',
          dataIndex: 'payAmount',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
        {
          title: '下单时间',
          dataIndex: 'createTime',
          valueType: 'dateTime',
          renderText: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
        },
        {
          title: '登录账号',
          dataIndex: 'username',
        },
        {
          title: '购票终端',
          dataIndex: 'sourceType',
          render: (dom: any) => saleChannelEnum[dom],
        },
        {
          title: '代理商',
          dataIndex: 'distributorName',
        },
        {
          title: '出卡时间',
          dataIndex: 'payTime',
          valueType: 'dateTime',
        },
        {
          title: '佣金（元）',
          dataIndex: 'commissionAmount',
          align: 'right',
          renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
        },
      ],
    },
    {
      title: '权益卡信息',
      columns: [
        {
          width: 1152,
          title: '',
          dataIndex: 'ticketInfo',
          render: (dom: any) => (
            <Table
              {...tableConfig}
              columns={columns2Card}
              dataSource={dom}
              bordered
              size="middle"
              pagination={false}
            />
          ),
        },
      ],
    },
    {
      title: '领票人信息',
      columns: [
        {
          width: 1152,
          title: '',
          dataIndex: 'basicFont',
          render: (dom: any, record) => (
            <Table
              columns={columns3}
              dataSource={[record]}
              bordered
              size="middle"
              pagination={false}
            />
          ),
        },
      ],
    },
  ];
  const columns2Card = [
    {
      width: 154,
      title: '权益卡号',
      dataIndex: 'cardId',
      render: (dom: any) => (
        <>
          <span title={dom}>{dom?.slice(0, 15) + (dom?.slice(15) ? '...' : '')}</span>
          <CopyOutlined
            style={{ color: '#1890ff' }}
            onClick={() => {
              copy(dom);
              message.success('已复制');
            }}
          />
        </>
      ),
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '权益卡名称',
      dataIndex: 'productName',
    },
    // {
    //   title: '产品类型',
    //   dataIndex: 'proType',
    //   render: (dom: any) => productTypeEnum[dom],
    // },
    // {
    //   title: '票种',
    //   dataIndex: 'type',
    //   render: (dom: any) => ticketTypeEnum[dom],
    // },

    {
      title: '购卡人姓名',
      dataIndex: 'identityName',
    },
    {
      title: '购卡人身份证',
      dataIndex: 'identity',
    },
    // {
    //   title: '出卡时间',
    //   dataIndex: 'payTime',
    //   valueType: 'dateTime',
    // },
    // {
    //   title: '退票时间',
    //   dataIndex: 'refundTime',
    //   valueType: 'dateTime',
    // },
    // {
    //   title: '入园时间',
    //   dataIndex: 'enterTime',
    //   valueType: 'dateTime',
    // },
    // {
    //   title: '状态',
    //   dataIndex: 'status',
    //   render: () => '已核销',
    // },
    // {
    //   title: '上链信息',
    //   dataIndex: 'createTime2',
    //   editable: true,
    //   hideInSearch: true,
    //   render:()=>{
    //     return <a onClick={onExamine}>查看</a>
    //   }
    // },
    {
      title: '金额（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '佣金（元）',
      dataIndex: 'commissionAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      // render: (dom, record) => {
      //   let count = 0;
      //   dom.forEach((e) => {
      //     count = count + e.actualAmount;
      //   });
      //   return count;
      // },
    },
  ];
  const columns3 = [
    {
      title: '联系人',
      dataIndex: 'pilotName',
      render: (_, record) => {
        if (record.pilotName !== '') {
          return record.pilotName;
        } else {
          return '-';
        }
      },
    },
    {
      title: '联系人手机号',
      dataIndex: 'pilotPhone',
      render: (_, record) => {
        if (record.pilotPhone !== '') {
          return record.pilotPhone;
        } else {
          return '-';
        }
      },
    },
    {
      title: '联系人身份证',
      dataIndex: 'pilotIdentity',
      render: (_, record) => {
        if (record.pilotIdentity !== '') {
          return record.pilotIdentity;
        } else {
          return '-';
        }
      },
    },
  ];
  const exportState = useExport({
    columns,
    modulePath: 'E-commerce_MyShopRefund',
    params: { storeId },
  });

  return (
    <>
      <ProTable
        {...tableConfig}
        params={{ storeId }}
        request={async (params) => {
          const data = await apiStoreReturnOrderList(params);

          addOperationLogRequest({
            action: 'info',
            module: tabKey,
            content: '查看退单管理列表',
          });

          return data;
        }}
        columns={columns}
        formRef={exportState.formRef}
        columnsState={columnsSet(exportState)}
        toolBarRender={() => [<Export key="export" {...exportState} />]}
      />
      {/* 查看区块链交易记录 */}
      <Blockchain
        cochainVisible={cochainVisible}
        setCochainVisible={setCochainVisible}
        cochainColumns={cochainColumns}
        cochainDataDataSource={cochainDataDataSource}
      />
      {/* 退单详情 */}
      <DetailsPop
        width={1200}
        title={
          <>
            <span>退单详情</span>
            <DataMask
              onDataMaskChange={handleDetailsMaskChange}
              logContent="查看【退单详情】用户隐私信息"
            />
          </>
        }
        visible={detailsVisible}
        isLoading={isLoading}
        setVisible={setDetailsVisible}
        columnsInitial={columnsDetail}
        dataSource={dataSource}
      />
      {/* 子订单 */}
      <DetailsPop
        width={1200}
        title={
          <>
            <span>子订单详情</span>
            <DataMask
              onDataMaskChange={handleOrderDetailsMaskChange}
              logContent="查看【退单子订单详情】用户隐私信息"
            />
          </>
        }
        visible={detailsVisible2}
        isLoading={isLoading}
        setVisible={setDetailsVisible2}
        columnsInitial={columnsDetail2}
        dataSource={dataSource}
      />
      {/* 子订单（权益卡） */}
      <DetailsPop
        width={1200}
        title="子订单详情"
        visible={detailsVisibleCard}
        isLoading={isLoading}
        setVisible={setDetailsVisibleCard}
        columnsInitial={columnsDetailCard}
        dataSource={dataSource}
      />
    </>
  );
};
