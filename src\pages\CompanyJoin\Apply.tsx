/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-16 11:00:36
 * @LastEditTime: 2023-04-06 10:17:58
 * @LastEditors: zhangfengfei
 */

import { Button, Space } from 'antd';
import qs from 'qs';
import type { FC } from 'react';
import { history, useLocation } from '@umijs/max';
import type { URLParams } from '.';

interface ApplyProps {}

const Apply: FC<ApplyProps> = () => {
  const { search } = useLocation();

  const { coName } = qs.parse(search, {
    ignoreQueryPrefix: true,
  }) as unknown as URLParams;

  const handleClick = () => {
    history.push(`/companyJoin/commit${search}`);
  };
  return (
    <Space
      direction="vertical"
      style={{
        alignItems: 'center',
      }}
    >
      <span>{coName}邀请你成为</span>
      <h2>{coName}下级分销商</h2>
      <Button type="primary" style={{ width: 216, marginTop: 30 }} onClick={handleClick}>
        去申请
      </Button>
    </Space>
  );
};

export default Apply;
