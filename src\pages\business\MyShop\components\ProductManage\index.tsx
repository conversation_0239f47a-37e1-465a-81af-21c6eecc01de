import DetailsPop from '@/common/components/DetailsPop';
import EditPop from '@/common/components/EditPop';
import ImageUpload from '@/common/components/ImageUpload';
import MDEditor from '@/common/components/MDEditor';
import { tableConfig } from '@/common/utils/config';
import {
  GuideStepStatus,
  StoreGoodsTypeEnum,
  TicketTypeEnum,
  productTypeEnum,
  whetherEnum,
} from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import {
  getHashParams,
  getStoreGoodsTypeText,
  getUniqueId,
  toValueEnum,
} from '@/common/utils/tool';
import { useGuide } from '@/hooks/useGuide';
import useModal from '@/hooks/useModal';
import StrategyModal from '@/pages/partner/saleChannel/PriceStrategy/StrategyModal';
import {
  apiComposeGoodsDetail,
  apiModifyComposeGoods,
  apiModifySimpleGoods,
  apiOfficialRecommend,
  apiOfficialRecommendEnable,
  apiOfficialRecommendList,
  apiQuerySimpleGoodsDetail,
  apiShopLabelList,
  apiShopLabelSet,
  apiStoreGoodsAdd,
  apiUpDownStoreGoods,
  deleteStoreGoods,
  getShopGoodsPageList,
} from '@/services/api/store';
import type { API } from '@/services/api/typings';
import { EllipsisOutlined, TagOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { EditableProTable, ProTable } from '@ant-design/pro-components';
import type { ProDescriptionsProps } from '@ant-design/pro-descriptions';
import type { ProFormColumnsType } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { Button, Dropdown, Modal, Popconfirm, Space, Switch, Table, Tag, message } from 'antd';
import { isEmpty } from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import { TabKeyContext } from '../..';
import GoodsDetail from './GoodsDetail';
import One from './One';
import Two from './Two';
import { getEnv } from '@/common/utils/getEnv';

export type ProDescriptionsGroup<T> = Omit<ProDescriptionsProps<T>, 'params' | 'request'>;

// 移除 id
let PriceIds: any = [];
let labelId: any = [];
let removeLabelId: any = [];

export default ({
  store: { value: storeId, label: storeName },
  setGoodsRef,
  setType,
  setId,
}: any) => {
  const { IMG_HOST } = getEnv();
  const currentCompanyId = localStorage.getItem('currentCompanyId'); //当前企业 ID
  const queryParams = getHashParams();
  const { updateGuideInfo } = useGuide();

  const [storeGoodsId, setStoreGoodsId] = useState();
  const actionRef = React.useRef<ActionType>();
  const [goodsItem, setGoodsItem] = useState<any>();
  const [stockVisible, setStockVisible] = useState<boolean>(false);
  const [stockDetailInfo, setStockDetailInfo] = useState<API.IDetailProps>({
    goodsName: '',
    timeShare: '',
    storeGoodsId: '',
  });
  const [officialEnable, setOfficialEnable] = useState<boolean>(false); //是否显示可以官网推荐按钮
  const [officialMV, setOfficialMV] = useState<boolean>(false); //是否显示官网推荐弹窗列表设置 MV=model visable
  const [officialDS, setOfficialDS] = useState<any[]>([]); //是否显示官网推荐弹窗列表设置 DS=dataSource
  const strategyModalState = useModal();
  const tabKey = useContext(TabKeyContext);

  const columns: ProColumns[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '类别',
      dataIndex: 'storeGoodsType',
      valueEnum: toValueEnum(StoreGoodsTypeEnum),
      hideInSearch: true,
    },
    {
      title: '票种',
      dataIndex: 'goodsType',
      hideInSearch: true,
      render: (text: any, { unitType, storeGoodsType, goodsType }) => {
        if (storeGoodsType === 2) {
          return text
            .split(',')
            .map((i: any) => TicketTypeEnum[i])
            .join('，');
        }
        return getStoreGoodsTypeText(unitType, storeGoodsType, goodsType);
      },
    },

    {
      title: '数字资产',
      dataIndex: 'isDigit',
      valueEnum: whetherEnum,
    },

    {
      title: '购买有效时间',
      dataIndex: 'purchaseTime',
      hideInSearch: true,
      render: (dom: any, record) =>
        record.purchaseBeginTime ? (
          <span>
            {record.purchaseBeginTime} 至 {record.purchaseEndTime}
          </span>
        ) : (
          '-'
        ),
    },
    {
      title: '入园有效时间',
      dataIndex: 'time',
      hideInSearch: true,
      render: (dom: any, record) =>
        record.dayBegin ? (
          <span>
            {record.dayBegin} 至 {record.dayEnd}
          </span>
        ) : (
          '-'
        ),
    },
    {
      title: '分时时段',
      hideInSearch: true,
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierNames',
      search: false,
    },
    {
      title: '可用库存',
      dataIndex: 'quantity',
      hideInSearch: true,
      align: 'right',
      valueType: 'digit',
    },
    {
      title: '用户售价（元）',
      dataIndex: 'sellingPrice',
      align: 'right',
      renderText: (dom) => {
        const result = parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom;
        return result + '起';
      },
      // valueType: 'digit',
      hideInSearch: true,
    },
    {
      title: '启用状态',
      dataIndex: 'goodsStatus',
      hideInSearch: true,
      fixed: 'right',
      render: (dom: any) => <Tag color={dom ? 'blue' : 'red'}>{dom ? '启用' : '禁用'}</Tag>,
    },
    {
      title: '上架状态',
      dataIndex: 'upDown',
      hideInSearch: true,
      fixed: 'right',
      render: (_: any, entity: any) => (
        <Switch
          checkedChildren="上架"
          unCheckedChildren="下架"
          checked={entity.upDown == 1}
          onChange={async () => {
            try {
              await apiUpDownStoreGoods({
                id: entity.storeGoodsId,
                type: entity.upDown == 1 ? 2 : 1,
              });
              if (entity.upDown != 1) {
                // 上架更新引导
                updateGuideInfo({
                  tabIndex: 1,
                  status: '官网商品管理',
                  systemId: 'HJY',
                  scenicId: entity.scenicId,
                });
                // 更新引导
                updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_4 });
              }
              message.success('已' + (entity.upDown == 1 ? '下架' : '上架'));
              addOperationLogRequest({
                action: 'disable',
                module: tabKey,
                content: `${entity.upDown == 1 ? '下架' : '上架'}商品【${entity.goodsName}】`,
              });

              actionRef?.current?.reload();
            } catch (error) {}
          }}
        />
      ),
    },

    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_, entity: any) => (
        <Space size="middle">
          {/* isWindow 字段 int 数值类型  仅窗口售票：1、是 2、否 */}
          {officialEnable && entity?.isWindow !== 1 ? (
            <a
              onClick={async () => {
                setOfficialMV(true);
                const { data } = await apiOfficialRecommendList({
                  coId: currentCompanyId,
                  storeGoodsId: entity.storeGoodsId,
                });
                //把弹窗需要的参数向下传递
                data.map((item: any) => {
                  item.goodsName = entity.goodsName;
                  item.upDown = entity.upDown;
                  item.goodsStatus = entity.goodsStatus;
                  item.storeGoodsId = entity.storeGoodsId;
                });
                setOfficialDS(data);
              }}
            >
              官网推荐
            </a>
          ) : (
            ''
          )}
          <a
            onClick={async () => {
              // 商品详情
              if (entity.storeGoodsType == 1) {
                // 商品
                setOneLoading(true);
                setOneDetailsVisible(true);
                try {
                  // [普通票/旅游卡] 商品数据
                  const { data } = await apiQuerySimpleGoodsDetail({
                    id: entity?.storeGoodsId,
                    isChain: entity?.isChain,
                    distributorId: entity?.distributorId,
                  });
                  data.id = data.storeGoodsId;
                  setOneDataSource(data);
                  setOneLoading(false);
                } catch (error) {
                  console.log(error);
                }
              } else {
                // 组合
                setTwoLoading(true);
                setTwoDetailsVisible(true);
                try {
                  const { data } = await apiComposeGoodsDetail({ id: entity.storeGoodsId });
                  setDataSource(data);
                  setAllPrice(data.sellingPrice);
                  setTwoLoading(false);
                } catch (error) {
                  console.log(error);
                }
              }

              addOperationLogRequest({
                action: 'info',
                module: tabKey,
                content: `查看【${entity.goodsName}】商品详情`,
              });
            }}
          >
            查看
          </a>
          <a
            onClick={async () => {
              if (entity.storeGoodsType == 1) {
                // 商品
                try {
                  // [普通票/旅游卡] 商品数据
                  const { data } = await apiQuerySimpleGoodsDetail({
                    id: entity.storeGoodsId,
                    isChain: entity.isChain,
                    distributorId: entity.distributorId,
                  });
                  data.id = data.storeGoodsId;
                  setOneDataSource(data);
                  // 编辑
                  setOneEditVisible(true);
                } catch (error) {
                  console.log(error);
                }
              } else {
                // 组合
                setType('edit');
                setId(entity.storeGoodsId);
              }
            }}
          >
            编辑
          </a>
          <Popconfirm
            title="是否删除该商品？"
            onConfirm={async () => {
              if (entity.upDown === 1) {
                message.warning('不能删除已上架的商品');
                return;
              }
              await deleteStoreGoods(entity.storeGoodsId);
              addOperationLogRequest({
                action: 'del',
                module: tabKey,
                content: `删除【${entity.goodsName}】商品`,
              });
              message.success('删除成功');
              actionRef?.current?.reloadAndRest();
            }}
          >
            <a style={{ color: 'red' }}>删除</a>
          </Popconfirm>
          <Dropdown
            menu={{
              items: [
                // {
                //   key: 1,
                //   label: (
                //     <a
                //       onClick={async () => {
                //         try {
                //           await apiRecommend({
                //             id: entity.storeGoodsId,
                //             isRecommend: !entity.isRecommend,
                //           });
                //           message.success('已' + (entity.isRecommend ? '取消推荐' : '设为推荐'));
                //           actionRef?.current?.reload();
                //         } catch (error) {}
                //       }}
                //     >
                //       {entity.isRecommend ? '取消推荐' : '设为推荐'}
                //     </a>
                //   ),
                // },
                {
                  key: 2,
                  label: (
                    <a
                      onClick={async () => {
                        try {
                          labelId = [];
                          removeLabelId = [];
                          setStoreGoodsId(entity.storeGoodsId);
                          const { data } = await apiShopLabelList({
                            storeId,
                            storeGoodsId: entity.storeGoodsId,
                          });
                          setTagData(data);
                          setTagVisible(true);
                        } catch (error) {}
                      }}
                    >
                      设置标签
                    </a>
                  ),
                },
                ...(entity.unitType == 1 || entity.unitType == 2
                  ? [
                      {
                        key: 3,
                        label: (
                          <a
                            key={getUniqueId()}
                            onClick={() => {
                              setGoodsItem(entity);
                              strategyModalState.setTypeWithVisible('info');
                            }}
                          >
                            商品佣金策略
                          </a>
                        ),
                      },
                    ]
                  : []),
              ],
            }}
          >
            <EllipsisOutlined />
          </Dropdown>
        </Space>
      ),
    },
  ];
  // 【选择商品】数据绑定
  const [oneVisible, setOneVisible] = useState<boolean>(false);
  // 【选择组合商品】数据绑定
  const [allPrice, setAllPrice] = useState<any>(0);
  const [twoVisible, setTwoVisible] = useState<boolean>(false);
  const [twoDataSource, setTwoDataSource] = useState<any>([]); // 内嵌表格数据
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);

  const twoColumns: ProColumns[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      editable: false,
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
      search: false,
      editable: false,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      editable: false,
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      valueEnum: productTypeEnum,
      editable: false,
    },
    {
      title: '票种',
      dataIndex: 'goodsType',
      hideInSearch: true,
      editable: false,
      renderText: (text, entity) =>
        getStoreGoodsTypeText(entity.unitType, entity.storeGoodsType, entity.goodsType),
    },
    {
      title: '分时时段',
      dataIndex: 'timeShareVoList',
      search: false,
      editable: false,
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id} style={{ marginBottom: 12 }}>
                {[item.beginTime, item.endTime].join('-')}
              </Tag>
            ))
          : '-';
      },
    },
    {
      title: '购买有效时间',
      search: false,
      editable: false,
      render: (_, entity: any) =>
        entity.purchaseBeginTime && entity.purchaseEndTime
          ? entity.purchaseBeginTime + ' 至 ' + entity.purchaseEndTime
          : '-',
    },
    {
      title: '入园有效时间',
      editable: false,
      render: (_: any, record) =>
        record.dayBegin ? (
          <span>
            {record.dayBegin} 至 {record.dayEnd}
          </span>
        ) : (
          '-'
        ),
    },
    {
      title: '组合售价（元）',
      dataIndex: 'composePrice',
      editable: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '佣金比例（%）',
      dataIndex: 'commissionRate',
      editable: false,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      editable: false,
    },
    {
      title: '剩余库存',
      dataIndex: 'stockQuantity',
      valueType: 'digit',
      editable: false,
    },
    {
      title: '数量',
      dataIndex: 'num',
      valueType: 'digit',
      fixed: 'right',
      fieldProps: (_: any, { entity }: any) => {
        // 是否允许成团人数控制（1：是）
        const { minPeople, maxPeople, isPeopleNumber } = entity;
        return {
          min: isPeopleNumber ? minPeople : 0,
          max: isPeopleNumber ? maxPeople : undefined,
          precision: 0,
        };
      },
      formItemProps: (form, { entity }) => {
        const { minPeople, isPeopleNumber } = entity;
        return {
          initialValue: isPeopleNumber ? minPeople : undefined,
        };
      },
    },
  ];
  const twoColumnsEdit = [
    ...twoColumns,
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
    },
  ];
  const [tagVisible, setTagVisible] = useState<boolean>(false);
  const [tagData, setTagData] = useState<any>([]);
  // 【商品详情】数据绑定
  const [oneDetailsVisible, setOneDetailsVisible] = useState<boolean>(false);
  const [isOneLoading, setOneLoading] = useState<boolean>(true);
  const [oneDataSource, setOneDataSource] = useState<any>({ id: '', isEnable: 0 });
  // 【商品编辑】数据绑定
  const [oneEditVisible, setOneEditVisible] = useState<boolean>(false);
  const oneEditColumns: ProFormColumnsType<any>[] = [
    {
      title: '基础信息',
      columns: [
        {
          title: '商品名称',
          dataIndex: 'goodsName',
          fieldProps: {
            disabled: true,
          },
          formItemProps: {
            rules: [{ required: true }],
          },
        },
        // {
        //   title: '类别',
        //   dataIndex: 'storeGoodsType',
        //   valueType: 'select',
        //   valueEnum: { 1: '单票' },
        //   fieldProps: {
        //     allowClear: false,
        //     disabled: true,
        //   },
        //   formItemProps: {
        //     rules: [{ required: true }],
        //   },
        // },
        {
          title: '商品图片',
          dataIndex: 'picUrl',
          tooltip: (
            <>
              <span>最多可上传 9 张图片，每张最</span>
              <div>大为 100M，支持 jpg、png 格式</div>
            </>
          ),
          renderFormItem: () => (
            <ProForm.Item name="picUrl">
              <ImageUpload defaultValue={oneDataSource?.picUrl || ''} maxCount={9} />
            </ProForm.Item>
          ),
        },
      ],
    },
    // {
    //   title: '价格信息',
    //   columns: [
    //     {
    //       title: '佣金类型',
    //       dataIndex: 'commissionType',
    //       valueEnum: { 0: '固定佣金', 1: '按比例' },
    //       fieldProps: {
    //         disabled: true,
    //       },
    //     },
    //     {
    //       title: '佣金比例（%）',
    //       dataIndex: 'commissionRate',
    //       fieldProps: {
    //         disabled: true,
    //       },
    //       hideInForm: oneDataSource.commissionType == 0,
    //     },
    //     {
    //       title: '佣金数值',
    //       dataIndex: 'commissionAmount',
    //       fieldProps: {
    //         disabled: true,
    //       },
    //       hideInForm: oneDataSource.commissionType == 1,
    //     },
    //     {
    //       title: '预订须知',
    //       dataIndex: 'note',
    //       width: 688,
    //       renderFormItem: () => (
    //         <ProForm.Item name="note">
    //           <MDEditor />
    //         </ProForm.Item>
    //       ),
    //     },
    //   ],
    // },
  ];
  // 【组合商品详情】数据绑定
  const [twoDetailsVisible, setTwoDetailsVisible] = useState<boolean>(false);
  const [isTwoLoading, setTwoLoading] = useState<boolean>(true);
  const twoDetailsColumns = [
    {
      title: '基础信息',
      columns: [
        {
          title: '组合商品名称',
          dataIndex: 'name',
          formItemProps: {
            rules: [{ required: true }],
          },
        },
        {
          span: 2,
          title: '最大可提前预订天数',
          dataIndex: 'maxBookDays',
        },
        {
          span: 2,
          title: '集合使用',
          dataIndex: 'composeTimeSwitch',
          render: (text: any) => (text ? '是' : '否'),
        },
        // {
        //   span: 2,
        //   title: '类别',
        //   dataIndex: 'storeGoodsType',
        //   render: () => '组合票',
        // },
        // {
        //   title: '票种',
        //   dataIndex: 'goodsType',
        //   render: (_: any, entity: any) =>
        //     getStoreGoodsTypeText(entity.unitType, entity.storeGoodsType, entity.goodsType),
        // },
        {
          title: '商品图片',
          dataIndex: 'picUrl',
          render: (dom: any) =>
            dom ? (
              dom
                .split(',')
                .map((item: any) => (
                  <img width={50} style={{ marginRight: '8px' }} src={IMG_HOST + item} />
                ))
            ) : (
              <img width={50} src={IMG_HOST + '-'} alt="" />
            ),
        },
      ],
    },
    {
      title: '组合信息',
      className: 'no-bgColor',
      columns: [
        {
          span: 3,
          title: '',
          dataIndex: 'data',
          render: (dom: any) => (
            <ProTable
              {...tableConfig}
              cardBordered
              search={false}
              options={false}
              pagination={false}
              columns={twoColumns}
              dataSource={dom}
            />
          ),
        },
        {
          title: '预订须知',
          dataIndex: 'note',
          render: (dom: any) => <MDEditor value={dom} readonly />,
        },
        {
          title: '总计',
          dataIndex: 'sellingPrice',
          render: (dom: any) => '¥ ' + dom,
        },
      ],
    },
  ];
  // 【组合商品编辑】数据绑定
  const editColumns: ProFormColumnsType<any>[] = [
    {
      title: '基础信息',
      columns: [
        {
          title: '组合商品名称',
          dataIndex: 'name',
          formItemProps: {
            rules: [{ required: true, max: 100 }],
          },
        },
        {
          title: '最大可提前预订天数',
          dataIndex: 'maxBookDays',
          valueType: 'digit',
          fieldProps: {
            min: 0,
            max: 9999,
            precision: 0,
          },
          formItemProps: {
            rules: [{ required: true }],
          },
        },
        // {
        //   title: '类别',
        //   dataIndex: 'uintType',
        //   valueType: 'select',
        //   valueEnum: { 2: '组合票' },
        //   initialValue: '2',
        //   fieldProps: {
        //     disabled: true,
        //     allowClear: false,
        //   },
        //   formItemProps: {
        //     rules: [{ required: true }],
        //   },
        // },
        {
          width: 688,
          title: '组合商品图片',
          tooltip: (
            <>
              <span>最多可上传 9 张图片，每张最</span>
              <div>大为 100 M，支持 jpg、png 格式</div>
            </>
          ),
          dataIndex: 'picUrl',
          renderFormItem: () => (
            <ProForm.Item name="picUrl">
              <ImageUpload defaultValue={dataSource?.picUrl || ''} maxCount={9} />
            </ProForm.Item>
          ),
        },
      ],
    },
    {
      title: '组合信息',
      columns: [
        {
          width: '100%',
          title: '',
          dataIndex: 'name',
          renderFormItem: () => (
            <EditableProTable
              {...tableConfig}
              defaultSize="small"
              rowKey="priceId"
              search={false}
              toolBarRender={() => [
                <Button
                  key="Button"
                  type="primary"
                  onClick={() => {
                    setTwoVisible(true);
                  }}
                >
                  导入商品
                </Button>,
              ]}
              columns={twoColumnsEdit}
              value={twoDataSource}
              recordCreatorProps={false}
              editable={{
                type: 'multiple',
                editableKeys,
                deleteText: '删除',
                actionRender: (row, config, defaultDoms) => {
                  return [
                    defaultDoms.delete,
                    <a
                      key="strategy"
                      onClick={() => {
                        setGoodsItem(row);
                        strategyModalState.setTypeWithVisible('info');
                      }}
                    >
                      商品佣金策略
                    </a>,
                  ];
                },
                onValuesChange: (record, recordList) => {
                  // 设置总价
                  let allPrice = 0;
                  recordList.map((item: any) => {
                    allPrice += item.composePrice * (item.num ? item.num : 0);
                  });
                  // allPrice = parseInt(allPrice.toFixed(2));
                  setAllPrice(Math.round(allPrice * 100) / 100);

                  setTwoDataSource(recordList);
                  // console.log(1, record, recordList, recordListAll);

                  // if (!record) {
                  //   // 移除状态
                  //   // 复制价格
                  //   recordList.map((item: any) => {
                  //     recordListAll.map((item2: any) => {
                  //       if (Key(item) == Key(item2)) {
                  //         item.realPrice = item2.realPrice;
                  //       }
                  //     });
                  //   });
                  // }
                  // recordListAll = recordList;
                  // setDataSource(recordListAll);

                  // // console.log(recordListAll);
                  // // console.log(recordList, dataSource);
                },
                onChange: (e) => {
                  setEditableRowKeys(e);
                  // PriceIds = e
                  // console.log(2, e);
                  // // 截取数据
                  // const sum = recordListAll.filter(
                  //   (item: any) => e.filter((itemC) => itemC == Key(item)).length > 0,
                  // );
                  // setDataSource(sum);
                  // setEditableRowKeys(e);
                },
              }}
            />
          ),
        },
        {
          title: '',
          dataIndex: 'allPrice',
          renderFormItem: () => '总计：¥ ' + allPrice,
        },
        // {
        //   title: '预订须知',
        //   dataIndex: 'note',
        //   width: 1152,
        //   renderFormItem: () => (
        //     <ProForm.Item name="note">
        //       <MDEditor />
        //     </ProForm.Item>
        //   ),
        // },
      ],
    },
  ];

  const officialColumns = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '推荐状态',
      dataIndex: 'officialRecommend',
      width: 154,
      render: (_: any, entity: any, index: number) => {
        //是否推荐到官网：1、官网不推荐 2、官网推荐
        return (
          <Switch
            checkedChildren="推荐"
            unCheckedChildren="默认"
            checked={entity.officialRecommend == 2}
            onChange={async () => {
              if (entity.upDown != 1 || !entity.goodsStatus) {
                message.warning('仅商品启用及上架状态下可打开官网推荐按钮');
                return;
              }
              const isRecommend = entity.officialRecommend == 2;

              try {
                await apiOfficialRecommend({
                  id: entity.id,
                  scenicId: entity.scenicId,
                  storeGoodsId: officialDS[index].storeGoodsId,
                  officialRecommend: isRecommend ? 1 : 2,
                });

                addOperationLogRequest({
                  action: 'disable',
                  module: tabKey,
                  content: isRecommend
                    ? `关闭【${entity.goodsName}】官网推荐`
                    : `官网推荐【${entity.goodsName}】`,
                });
                message.success('已' + (isRecommend ? '取消推荐' : '设为推荐'));
                //更新当前的弹窗表格值
                const { data } = await apiOfficialRecommendList({
                  coId: currentCompanyId,
                  storeGoodsId: entity.storeGoodsId,
                });
                //把弹窗需要的参数再补上，用于判定此开关是否可点
                data.map((item: any) => {
                  item.upDown = 1; //当开关能判定到这里时，此值一定是 1
                  item.goodsStatus = 1;
                  item.storeGoodsId = entity.storeGoodsId;
                });
                setOfficialDS(data);
              } catch (error) {}
            }}
          />
        );
      },
    },
  ];

  /**
   * 验证企业是否包含运营，有才显示官网推荐
   */
  const checkOfficialRecommendEnable = async () => {
    const { data } = await apiOfficialRecommendEnable(currentCompanyId);
    setOfficialEnable(data);
  };

  useEffect(() => {
    if (queryParams?.operate === 'addGoods') {
      setOneVisible(true);
    }
  }, [storeId]);

  useEffect(() => {
    checkOfficialRecommendEnable();
    setGoodsRef(actionRef);
  }, []);

  return (
    <>
      <ProTable<API.ShopGoodsPageListItem, API.ShopGoodsPageListParams>
        {...tableConfig}
        actionRef={actionRef}
        toolBarRender={() => [
          <Button
            key="Button"
            type="primary"
            onClick={() => {
              setOneVisible(true);
            }}
          >
            导入商品
          </Button>,
          <Button
            key="Button"
            type="primary"
            onClick={() => {
              // 初始化
              PriceIds = [];
              setDataSource({ id: '', isEnable: 0 });
              setTwoDataSource([]);
              setEditableRowKeys([]);
              setAllPrice(0);
              // setEditVisible(true);
              setType('add');
            }}
          >
            新增组合商品
          </Button>,
        ]}
        params={{ storeId }}
        request={async (params) => {
          try {
            const { data } = await getShopGoodsPageList(params);
            return data;
          } catch (error) {
            return { data: [], total: 0 };
          }
        }}
        columns={columns}
      />
      {/* 选择商品 */}
      <One
        visible={oneVisible}
        setVisible={(v) => {
          setOneVisible(v);
        }}
        storeId={storeId}
        actionRefUp={actionRef}
      />
      {/* 选择可组合商品 */}
      <Two
        visible={twoVisible}
        setVisible={setTwoVisible}
        storeId={storeId}
        editableKeys={editableKeys}
        actionRefUp={actionRef}
        onfinish={(val: any) => {
          const data = [...twoDataSource, ...val];
          setTwoDataSource(data);
          setEditableRowKeys(data.map((item: any) => item.priceId));
        }}
      />
      {/* 库存详情 */}
      {/* <StockDetail
        stockVisible={stockVisible}
        setStockVisible={setStockVisible}
        stockDetailInfo={stockDetailInfo}
      /> */}
      {/* 新增/编辑组合商品 */}
      <EditPop
        width={1500}
        title="组合商品"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/修改
        onFinish={async (val: any) => {
          // 保存
          try {
            const list = twoDataSource.map((item: any) => {
              if (!item.num) {
                message.info('请输入组合商品数量');
                throw new Error('');
              }
              return {
                number: item.num,
                priceId: item.priceId,
              };
            });

            if (dataSource.id) {
              const delPriceIds = PriceIds.filter((item: any) => {
                let b = true;
                list.forEach((element: any) => {
                  if (item == element) {
                    b = false;
                  }
                });
                return b;
              });

              // 修改
              await apiModifyComposeGoods({
                delPriceIds,
                id: dataSource.id,
                list,
                ...val,
              });
              message.success('修改成功');
            } else {
              // 新增
              await apiStoreGoodsAdd({
                composeGoods: {
                  list,
                  ...val,
                },
                storeId,
              });
              message.success('导入成功');
            }

            setEditVisible(false);
            actionRef?.current?.reload();
          } catch (error) {}
        }}
      />
      {/* 组合商品详情 */}
      <DetailsPop
        width={1200}
        column={{ xs: 1, sm: 2, xl: 3 }}
        title="组合商品详情"
        visible={twoDetailsVisible}
        isLoading={isTwoLoading}
        setVisible={setTwoDetailsVisible}
        columnsInitial={twoDetailsColumns}
        dataSource={dataSource}
      />
      {/* 商品详情 */}
      <GoodsDetail
        visible={oneDetailsVisible}
        loading={isOneLoading}
        setVisible={setOneDetailsVisible}
        dataSource={oneDataSource}
      />
      {/* 商品编辑 */}
      <EditPop
        title={oneDataSource.categoryType == 2 ? '权益卡商品' : '商品'}
        visible={oneEditVisible}
        setVisible={setOneEditVisible}
        columns={oneEditColumns}
        dataSource={oneDataSource}
        // 新增/修改
        onFinish={async (val: any) => {
          try {
            await apiModifySimpleGoods({
              id: oneDataSource.id,
              // name: val.name,
              picUrl: val.picUrl,
              // note: val.note,
              // priceId: oneDataSource.priceId,
            });

            addOperationLogRequest({
              action: 'edit',
              module: tabKey,
              content: `编辑【${oneDataSource.goodsName}】商品详情`,
            });

            message.success('修改成功');
            setOneEditVisible(false);
            actionRef?.current?.reload();
          } catch (error) {
            console.log(error);
          }
        }}
      />
      <StrategyModal
        goodsItem={goodsItem}
        modalState={strategyModalState}
        showInfo={false}
        showGroup={false}
      />

      {/* 设置标签 */}
      <Modal
        open={tagVisible}
        title="请选择标签"
        onCancel={() => {
          setTagVisible(false);
        }}
        onOk={async () => {
          if (isEmpty(labelId) && isEmpty(removeLabelId)) {
            setTagVisible(false);
            return;
          }
          const hide = message.loading('正在设置');
          try {
            await apiShopLabelSet({
              labelId,
              removeLabelId,
              storeGoodsId,
            });
            message.success('设置成功');
            setTagVisible(false);
          } catch (error) {}
          hide();
        }}
      >
        {tagData.map((item: any, index: any) => (
          <div key={index} style={{ margin: '8px 0 16px' }}>
            <Space style={{ marginBottom: '8px' }}>
              <TagOutlined />
              {item.name}
              <EllipsisOutlined />
            </Space>
            <div>
              {item.list.map((itemTag: any, indexTag: any) => (
                <Tag.CheckableTag
                  key={indexTag}
                  checked={itemTag.selected}
                  onChange={(checked: any) => {
                    const tagDataSum = JSON.parse(JSON.stringify(tagData));
                    tagDataSum[index].list[indexTag].selected = checked;
                    setTagData(tagDataSum);
                    if (checked) {
                      const i = removeLabelId.indexOf(itemTag.id);
                      if (i == -1) {
                        labelId.push(itemTag.id);
                      } else {
                        removeLabelId.splice(i, 1);
                      }
                    } else {
                      const i = labelId.indexOf(itemTag.id);
                      if (i == -1) {
                        removeLabelId.push(itemTag.id);
                      } else {
                        labelId.splice(i, 1);
                      }
                    }
                  }}
                >
                  {itemTag.name}
                </Tag.CheckableTag>
              ))}
            </div>
          </div>
        ))}
      </Modal>

      {/* 官网推荐设置弹窗 */}
      <Modal
        open={officialMV}
        title="官网推荐"
        onCancel={() => {
          setOfficialMV(false);
          actionRef?.current?.reload();
        }}
        footer={null}
      >
        <Table
          columns={officialColumns}
          dataSource={officialDS}
          bordered
          size="middle"
          pagination={false}
        />
      </Modal>
    </>
  );
};
