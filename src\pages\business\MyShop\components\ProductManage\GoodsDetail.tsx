import DetailsPop from '@/common/components/DetailsPop';
import MDEditor from '@/common/components/MDEditor';
import CheckRuleDetails from '@/common/components/RuleDetails/CheckRuleDetails';
import IssueRuleDetails from '@/common/components/RuleDetails/IssueRuleDetails';
import RetreatRuleDetails from '@/common/components/RuleDetails/RetreatRuleDetails';
import { productTypeEnum, ticketTypeEnum, weekEnum } from '@/common/utils/enum';
import type { ProDescriptionsProps } from '@ant-design/pro-components';
import { Tag } from 'antd';
import { isEmpty } from 'lodash';
import StockDetails from './StockInfo';
import { getEnv } from '@/common/utils/getEnv';
export type ProDescriptionsGroup<T> = Omit<ProDescriptionsProps<T>, 'params' | 'request'>;

interface Props {
  visible: boolean;
  loading: boolean;
  setVisible: any;
  dataSource: any;
}

const GoodsDetail = ({ visible, loading, setVisible, dataSource }: Props) => {
  const { IMG_HOST } = getEnv();
  const oneDetailsColumns: ProDescriptionsGroup<any>[] = [
    {
      title: '基础信息',
      columns: [
        {
          title: '所属景区',
          dataIndex: 'scenicName',
        },
        {
          title: '产品类型',
          dataIndex: 'proType',
          valueEnum: productTypeEnum,
        },
        {
          title: '所属产品名称',
          dataIndex: 'productName',
        },
        {
          title: '商品名称',
          dataIndex: 'goodsName',
        },
        {
          hideInDescriptions: dataSource?.categoryType == 2,
          title: '票种',
          dataIndex: 'type',
          valueEnum: ticketTypeEnum,
        },
        {
          hideInDescriptions: dataSource?.categoryType == 2,
          span: 2,
          title: '该星期下，不可售该票',
          dataIndex: 'restrictWeek',
          render: (dom: any) =>
            dom
              ? dom.split('#').map(
                  (item: any, index: any) =>
                    item && (
                      <Tag style={{ marginBottom: '8px' }} key={index}>
                        {weekEnum[item]}
                      </Tag>
                    ),
                )
              : '-',
        },
        {
          title: '商品图片',
          dataIndex: 'picUrl',
          render: (dom: any) =>
            dom ? (
              dom
                .split(',')
                .map((item: any) => (
                  <img width={50} style={{ marginRight: '8px' }} src={IMG_HOST + item} />
                ))
            ) : (
              <img width={50} src={IMG_HOST + '-'} alt="" />
            ),
        },
      ],
    },
    {
      title: '分时信息',
      columns: [
        {
          title: '分时预约',
          dataIndex: 'timeShareVoList',
          render: (_, { timeShareVoList }: any) => (isEmpty(timeShareVoList) ? '关' : '开'),
        },
        {
          title: '分时时段',
          dataIndex: 'timeShareVoList',
          render: (_, { timeShareVoList }: any) => {
            return timeShareVoList
              ? timeShareVoList.map((item: any) => (
                  <Tag key={item.id} style={{ marginBottom: 12 }}>
                    {[item.beginTime, item.endTime].join('-')}
                  </Tag>
                ))
              : '-';
          },
        },
      ],
    },
    {
      title: '价格信息',
      columns: [
        {
          title: '单独售价',
          dataIndex: 'salePrice',
          renderText: (text: any) => '¥ ' + text + '起',
        },
        {
          hideInDescriptions: dataSource?.categoryType == 2,
          title: '组合售价',
          dataIndex: 'composePrice',
          renderText: (text: any) => '¥ ' + text,
        },
        {
          title: '佣金比例',
          dataIndex: 'commissionRate',
          render: (dom: any, entity: any) => (entity.commissionRate ? dom + '%' : '-'),
        },
      ],
    },
    {
      title: '其他信息',
      columns: [
        {
          hideInDescriptions: dataSource?.categoryType == 2,
          title: '最小起订量',
          dataIndex: 'minPeople',
          // render: (dom: any, entity: any) => (entity.isPeopleNumber ? dom : '-'),
        },
        {
          hideInDescriptions: dataSource?.categoryType == 2,
          title: '单次最大预订量',
          dataIndex: 'maxPeople',
          // render: (dom: any, entity: any) => (entity.isPeopleNumber ? dom : '-'),
        },
        {
          title: '使用有效期',
          dataIndex: 'validityDay',
          render: (dom: any) => <span>游玩日期起 {dom} 天内有效</span>,
        },
      ],
    },
    {
      title: '规则信息',
      columns: [
        {
          title: '出票规则',
          dataIndex: 'issueName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                IssueRuleDetails.show(entity.issueId);
              }}
            >
              {dom}
            </a>
          ),
        },
        {
          hideInDescriptions: dataSource?.categoryType == 2,
          title: '检票规则',
          dataIndex: 'checkName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                CheckRuleDetails.show(entity.checkId);
              }}
            >
              {dom}
            </a>
          ),
        },
        {
          hideInDescriptions: dataSource?.categoryType == 2,
          title: '退票规则',
          dataIndex: 'retreatName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                RetreatRuleDetails.show(entity.retreatId);
              }}
            >
              {dom}
            </a>
          ),
        },
      ],
    },
    {
      title: '库存信息',
      className: 'no-bgColor',
      columns: [
        {
          span: 2,
          title: '',
          dataIndex: 'salesGoodsList',
          render: (dom: any) => dom && <StockDetails salesGoodsList={dom} />,
        },
      ],
    },
    {
      title: '说明信息',
      columns: [
        {
          span: 2,
          title: '预订须知',
          dataIndex: 'bookNotice',
          render: (dom: any) => <MDEditor value={dom} readonly />,
        },
        {
          span: 2,
          title: dataSource?.categoryType == 2 ? '购卡须知' : '入园须知',
          dataIndex: 'enterNotice',
          render: (dom: any) => <MDEditor value={dom} readonly />,
        },
      ],
    },
  ];

  return (
    <>
      {/* 商品详情 */}
      <DetailsPop
        title={dataSource?.categoryType == 2 ? '权益卡商品详情' : '商品详情'}
        visible={visible}
        isLoading={loading}
        setVisible={setVisible}
        columnsInitial={oneDetailsColumns}
        dataSource={dataSource}
      />
    </>
  );
};

export default GoodsDetail;
