<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 60 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 5备份</title>
    <defs>
        <linearGradient x1="97.7530992%" y1="0%" x2="2.24690083%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FD9F86" offset="0%"></stop>
            <stop stop-color="#EF6E53" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="97.7530992%" y1="0%" x2="2.9017319%" y2="99.3143575%" id="linearGradient-2">
            <stop stop-color="currentColor" stop-opacity="0.8" offset="0%"></stop>
            <stop stop-color="currentColor" offset="100%"></stop>
        </linearGradient>
        <path d="M42.2411138,39 L35.438675,39 C34.7850088,39 25.0927688,38.9261876 23.7315792,38.9261876 L17.101666,38.9261876 C15.7404765,38.9261876 14.6370143,37.8246227 14.6370143,36.4657735 L14.6370143,20.0056031 L13.1089302,20.0056031 C12.1138903,20.0728049 11.1962608,19.4688147 10.8660971,18.5293547 C10.4801407,17.6131036 10.6843424,16.5550217 11.383674,15.8475033 L28.1433059,0.691352414 C29.0985965,-0.230450805 30.6138811,-0.230450805 31.5691718,0.691352414 L48.0823385,15.798295 C48.7757156,16.504448 48.9782517,17.556373 48.5965165,18.4688153 C48.2147813,19.3812576 47.322954,19.9769108 46.3324357,19.980999 L44.6318261,19.980999 L44.6318261,36.6133983 C44.5932578,37.9152006 43.5451582,38.9614981 42.2411138,39 Z" id="path-3"></path>
        <filter x="-1.3%" y="-1.3%" width="102.6%" height="102.6%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.786576705 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <rect id="path-5" x="25" y="17" width="10" height="11" rx="3"></rect>
        <filter x="-105.0%" y="-77.3%" width="350.0%" height="327.3%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="2" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板备份" transform="translate(-91.000000, -712.000000)">
            <g id="编组-5备份" transform="translate(91.000000, 712.000000)">
                <rect id="矩形备份-3" opacity="0.337913876" x="0" y="0" width="60" height="60"></rect>
                <g id="编组" transform="translate(4.213036, 9.000000)">
                    <path d="M27.4763031,39.3100791 L22.2436579,39.3100791 C21.7408377,39.3100791 14.2852685,39.2533003 13.2381996,39.2533003 L8.13826634,39.2533003 C7.09119744,39.2533003 6.24238039,38.4059427 6.24238039,37.3606741 L6.24238039,24.6990046 L5.06693109,24.6990046 C4.30151579,24.7506983 3.59564689,24.2860904 3.34167488,23.5634288 C3.04478532,22.8586203 3.20186357,22.0447112 3.73981091,21.5004662 L16.6318354,9.84188864 C17.3666744,9.13280925 18.5322779,9.13280925 19.2671169,9.84188864 L31.9695528,21.4626137 C32.5029199,22.0058083 32.6587169,22.8149814 32.3650744,23.5168601 C32.0714319,24.2187388 31.3854109,24.6769335 30.6234738,24.6800783 L29.3153125,24.6800783 L29.3153125,37.4742316 C29.2856447,38.475618 28.4794142,39.2804623 27.4763031,39.3100791 Z" id="路径" fill="currentColor" transform="translate(17.852482, 24.310079) rotate(-14.000000) translate(-17.852482, -24.310079) "></path>
                    <g id="路径">
                        <use fill-opacity="0.1" fill="url(#linearGradient-2)" fill-rule="evenodd" xlink:href="#path-3"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    </g>
                    <g id="矩形" fill-rule="nonzero">
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        <use fill="#FFFFFF" xlink:href="#path-5"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>