import { LeftOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import styles from './index.less';

export default (props: any) => {
  return (
    props.open && (
      <ProCard
        title={
          <div className={styles.title} onClick={() => props.setOpen(false)}>
            <LeftOutlined />
            {props.title}
          </div>
        }
        headerBordered
      >
        {props.children}
      </ProCard>
    )
  );
};
