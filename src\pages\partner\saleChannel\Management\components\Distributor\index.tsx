import DetailsPop from '@/common/components/DetailsPop';
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getUniqueId } from '@/common/utils/tool';
import InviteModal from '@/components/InviteModal';
import { apiDistribuList, getDealerApplicationList } from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { UserAddOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button } from 'antd';
import { useContext, useEffect, useState } from 'react';
import { Access, useAccess, useModel, useRequest } from '@umijs/max';
import { SaleMangeTabKeyContext } from '../..';

const TableList = () => {
  const access = useAccess();
  const [inviteVisible, setInviteVisible] = useState(false);
  const saleMangeTabKey = useContext(SaleMangeTabKeyContext);

  const { initialState } = useModel('@@initialState'); //用于取景区 ID
  const { coId, settlementId } = initialState?.currentCompany;
  // 【表格】数据绑定

  // 【收款场景】下拉列表
  const downListReq = useRequest(getDealerApplicationList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      return (res.data || []).map((item: any) => ({
        value: item.code,
        label: item.name,
      }));
    },
  });

  const columnsTable: ProColumns[] = [
    {
      title: '经销商名称',
      dataIndex: 'coName',
    },
    {
      title: '分组名称',
      dataIndex: 'groupName',
      hideInSearch: true,
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
    },
    {
      title: '手机号码',
      dataIndex: 'coPhone',
    },
    {
      title: '电子邮箱',
      dataIndex: 'coEmail',
    },
    {
      title: '收款场景',
      dataIndex: 'collectionCode',
      hideInSearch: true,
      valueType: 'select',
      fieldProps: {
        loading: downListReq.loading,
        options: downListReq.data,
      },
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
      hideInSearch: true,
    },
    {
      width: 120,
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (dom: any, entity: any) => (
        <a
          onClick={() => {
            setLoading(true);
            setDetailsVisible(true);
            setDataSource(entity);
            setLoading(false);

            addOperationLogRequest({
              action: 'info',
              module: saleMangeTabKey,
              content: `查看【${entity.coName}】详情`,
            });
          }}
        >
          查看
        </a>
      ),
    },
  ];

  // 【详情】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const columnsDetail = [
    {
      title: '基本信息',
      columns: [
        {
          title: '经销商名称',
          dataIndex: 'coName',
        },
        {
          title: '统一组织机构代码',
          dataIndex: 'coCode',
        },
        {
          title: '企业名称',
          dataIndex: 'coName',
        },
        {
          title: '企业地址',
          dataIndex: 'coAddressInfo',
        },
      ],
    },
    {
      title: '联系信息',
      columns: [
        {
          title: '联系人',
          dataIndex: 'contactName',
        },
        {
          title: '手机号码',
          dataIndex: 'coPhone',
        },
        {
          title: '电子邮箱',
          dataIndex: 'coEmail',
        },
        {
          title: '用户账号',
          dataIndex: 'account',
        },
        {
          title: '用户密码',
          dataIndex: 'password',
        },
      ],
    },
  ];

  useEffect(() => {
    downListReq.run({ merchantId: settlementId });
  }, []);

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        params={{ distributorId: coId }}
        request={apiDistribuList}
        columns={columnsTable}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canAgentGroupManagement_invite}>
            <Button
              key="invite"
              type="primary"
              onClick={() => {
                setInviteVisible(true);
              }}
            >
              <UserAddOutlined /> 邀请
            </Button>
          </Access>,
        ]}
      />

      {/* 详情 */}
      <DetailsPop
        title="经销商详情"
        visible={detailsVisible}
        isLoading={isLoading}
        setVisible={setDetailsVisible}
        columnsInitial={columnsDetail}
        dataSource={dataSource}
      />

      {/* 邀请 */}
      <InviteModal visible={inviteVisible} setVisible={setInviteVisible} invitetype={'2'} />
    </>
  );
};

export default TableList;
