import { ExtensionCategory, Graph, iconfont, register, treeToGraphData } from '@antv/g6';
import { debounce } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { GREY_COLOR, TreeNode } from './TreeNode';

const PerformanceGraph = ({ data, resetFlag }) => {
  const graphRef = useRef<Graph>(null);
  const styleRef = useRef<HTMLStyleElement>();
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver>();
  const [containerSize, setContainerSize] = useState({
    width: 0,
    height: 300, // 默认高度
  });
  const isInitialRender = useRef(true); // 新增：标记初始渲染状态

  // 样式初始化（只执行一次）
  useEffect(() => {
    if (!styleRef.current) {
      const style = document.createElement('style');
      style.innerHTML = `@import url('${iconfont.css}');`;
      document.head.appendChild(style);
      styleRef.current = style;
    }
  }, []);

  // 容器尺寸监听
  useEffect(() => {
    if (!containerRef.current) return;

    const handleResize = debounce((entries: ResizeObserverEntry[]) => {
      const entry = entries[0];
      if (entry) {
        const { width, height } = entry.contentRect;
        setContainerSize({ width, height });
      }
    }, 300);

    resizeObserverRef.current = new ResizeObserver(handleResize);
    resizeObserverRef.current.observe(containerRef.current);

    return () => {
      resizeObserverRef.current?.disconnect();
    };
  }, []);

  useEffect(() => {
    if (graphRef.current && !graphRef.current.destroyed) {
      // 直接调用图表适应方法
      graphRef.current.fitView({ padding: 40 });
    }
  }, [resetFlag]);

  // 图表初始化及更新
  useEffect(() => {
    if (!containerRef.current || !containerSize.width) return;

    // 销毁旧图表
    const cleanupGraph = () => {
      if (graphRef.current && !graphRef.current.destroyed) {
        graphRef.current.destroy();
        graphRef.current = undefined;
      }
    };

    cleanupGraph();

    // 动态计算布局参数
    const dynamicIndent = Math.min(containerSize.width * 0.3, 300);
    const nodeWidth = Math.min(containerSize.width * 0.2, 202);

    // 注册自定义节点
    register(ExtensionCategory.NODE, 'tree-node', TreeNode);

    // 创建新图表（关键修改：调整fitView配置）
    const graph = new Graph({
      container: containerRef.current,
      data: treeToGraphData(data, {
        getNodeData: (datum, depth) => {
          if (!datum.style) datum.style = {};
          datum.style.collapsed = depth >= 2;
          if (!datum.children) return datum;
          const { children, ...restDatum } = datum;
          return { ...restDatum, children: children.map((child) => child.id) };
        },
      }),
      node: {
        type: 'tree-node',
        style: {
          size: [nodeWidth, 60],
          ports: [{ placement: 'left' }, { placement: 'right' }],
          radius: 4,
        },
      },
      edge: {
        type: 'cubic-horizontal',
        style: {
          stroke: GREY_COLOR,
        },
      },
      layout: {
        type: 'indented',
        direction: 'LR',
        dropCap: false,
        indent: dynamicIndent,
        getHeight: () => 60,
        preLayout: false,
      },
      behaviors: ['zoom-canvas', 'drag-canvas'],
      fitView: false, // 关闭自动fitView
      fitCenter: false, // 关闭自动居中
      autoPaint: true, // 启用自动绘制
    });

    // 渲染图表后手动调整一次视图
    graph.on('afterrender', () => {
      if (isInitialRender.current) {
        graph.fitView({ padding: 40 }); // 初始渲染时仅执行一次fitView
        isInitialRender.current = false;
      }
    });

    // 缓存实例
    graphRef.current = graph;
    graph.render();

    return () => {
      cleanupGraph();
      isInitialRender.current = true; // 重置初始状态
    };
  }, [data, containerSize]);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      if (styleRef.current) {
        document.head.removeChild(styleRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      style={{
        height: '45vh',
        width: '100%',
        minHeight: 300,
        overflow: 'hidden', // 防止滚动条干扰
      }}
    />
  );
};

export default PerformanceGraph;
