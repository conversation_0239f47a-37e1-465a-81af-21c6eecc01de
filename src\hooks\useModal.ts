/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-20 13:48:54
 * @LastEditTime: 2025-05-29 10:55:58
 * @LastEditors: 李悍宇 <EMAIL>
 */
import { getMessage } from '@/services/api/message';
import { useMemo, useState } from 'react';

export type ModalType = 'add' | 'update' | 'info';

interface useModalConfig {
  defaultVisible?: boolean;
  defaultType?: ModalType;
}

export interface ModalState {
  type: ModalType;
  setType: React.Dispatch<React.SetStateAction<ModalType>>;
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  toggle: () => void;
  setTypeWithVisible: (val: ModalType, show?: boolean) => void;
  getMessageRed: (enterpriseId: string, userId?: string) => void;
  unReadNum: number;
  setNum: (num: number) => void;
}

export default function useModal(config: useModalConfig = {}): ModalState {
  const { defaultType = 'add', defaultVisible = false } = config;
  const [type, setType] = useState<ModalType>(defaultType);
  const [visible, setVisible] = useState<boolean>(defaultVisible);
  const [unReadNum, setUnReadNum] = useState<number>(0);

  function toggle() {
    setVisible(!visible);
  }

  function setTypeWithVisible(val: ModalType) {
    setType(val);
    setVisible(true);
  }

  function setNum(Num) {
    setUnReadNum(Num);
  }

  const getMessageRed = async (enterpriseId, userId) => {
    const res = await getMessage({
      current: 1, // 传递当前页码
      pageSize: 1,
      status: '0',
      enterpriseId : enterpriseId || localStorage.getItem('currentCompanyId'),
      userId,
    });
    setUnReadNum(res.data.unredNum);
  };

  return useMemo(() => {
    return {
      type,
      setType,
      visible,
      setVisible,
      toggle,
      setTypeWithVisible,
      unReadNum,
      getMessageRed,
      setNum,
    };
  }, [type, visible, unReadNum]);
}
