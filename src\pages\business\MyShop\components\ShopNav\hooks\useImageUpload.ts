import { useState } from 'react';
import { message } from 'antd';
import type { RcFile } from 'antd/lib/upload';
import { scenicHost } from '@/services/api';
import { getEnv } from '@/common/utils/getEnv';

/**
 * 通用图片上传 Hook
 * @template T 上传标识符的类型，可以是 number、string 或其他类型
 */
export const useImageUpload = <T = string>(
  onImageUpload: (identifier: T, imageUrl: string) => void
) => {
  const { FILE_HOST } = getEnv();
  const [uploading, setUploading] = useState<T | null>(null);

  /**
   * 上传前的文件验证
   * @param file 要上传的文件
   * @returns 是否通过验证
   */
  const beforeUpload = (file: RcFile): boolean => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只允许上传 jpeg,png 格式的图片！');
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('图片必须小于10MB!');
    }
    return isJpgOrPng && isLt10M;
  };

  /**
   * 处理图片上传
   * @param identifier 上传标识符，用于区分不同的上传实例
   */
  const handleUpload = (identifier: T): void => {
    if (uploading !== null) return; // 防止重复上传

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/png, image/jpeg';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file && beforeUpload(file as RcFile)) {
        setUploading(identifier);
        const formData = new FormData();
        formData.append('file', file);

        // 使用fetch调用上传接口
        fetch(scenicHost + '/aws/uploadFile', {
          method: 'POST',
          body: formData,
        })
          .then((response) => response.json())
          .then((response) => {
            if (response.data?.path) {
              onImageUpload(identifier, FILE_HOST + response.data.path);
              message.success('图片上传成功');
            } else {
              message.error('上传失败，请重试');
            }
          })
          .catch(() => {
            message.error('上传失败，请重试');
          })
          .finally(() => {
            setUploading(null);
          });
      }
    };
    input.click();
  };

  return { 
    handleUpload, 
    uploading,
    /**
     * 检查指定标识符是否正在上传
     * @param identifier 要检查的标识符
     * @returns 是否正在上传
     */
    isUploading: (identifier: T) => uploading === identifier
  };
};

export default useImageUpload;
