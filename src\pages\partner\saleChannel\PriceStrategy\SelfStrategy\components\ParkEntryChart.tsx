import React, { useState, useEffect, useRef } from 'react';
import Chart from '@/components/Chart';
import { DatePicker, Radio, Empty, Spin } from 'antd';
import dayjs from 'dayjs';
import { apiParkEntryStatistics } from '@/services/api/distribution';

const { RangePicker } = DatePicker;

// 整合 CSS 样式为组件内部样式对象
const styles = {
  container: {
    padding: '16px',
    background: '#fff',
    borderRadius: '4px',
  },
  toolbar: {
    display: 'flex',
    justifyContent: 'flex-end',
    marginBottom: '16px',
    gap: '10px',
  },
  chartContainer: {
    height: '350px',
    position: 'relative' as 'relative',
  },
  emptyContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '300px',
    background: '#f9f9f9',
    borderRadius: '4px',
  },
  // 从 PriceSettingModal.css 整合的样式
  chartComponentContainer: {
    padding: '8px',
    borderLeft: '1px solid #f0f0f0',
    minHeight: '410px',
    display: 'flex',
    flexDirection: 'column' as 'column',
    overflowY: 'auto' as 'auto',
  },
  dateRangePicker: {
    width: '200px',
  },
  sectionTitle: {
    fontSize: '16px',
    fontWeight: 500,
  },
};

interface ParkEntryChartProps {
  // 商品 ID
  goodsId?: string;
  // 入园数据
  entryData?: { date: string; count: number; ratio?: number }[];
  // 日期范围
  dateRange?: [string, string];
  // 获取数据的回调函数
  onDateRangeChange?: (dateRange: [string, string], timeRange: string) => void;
  // 添加容器样式属性，允许自定义样式
  containerStyle?: React.CSSProperties;
  // 是否在模态框内显示
  inSettingModal?: boolean;
}

/**
 * 入园情况图表组件
 */
const ParkEntryChart: React.FC<ParkEntryChartProps> = ({ 
  goodsId,
  entryData = [], 
  dateRange, 
  onDateRangeChange,
  containerStyle,
  inSettingModal = false
}) => {
  // 时间维度选择，默认按月
  const [timeRange, setTimeRange] = useState('按月');
  // 加载状态
  const [loading, setLoading] = useState(false);
  // 图表数据
  const [chartData, setChartData] = useState<any[]>([]);
  // 使用 ref 来跟踪上一次的请求参数，避免重复请求
  const prevRequestRef = useRef<{
    goodsId?: string;
    dateRange: [string, string];
    timeRange: string;
  }>({
    goodsId: undefined,
    dateRange: ['', ''],
    timeRange: '',
  });
  
  // 获取默认日期范围：按月显示最近 7 个月，按天显示最近 7 天
  const getDefaultDateRange = (type: string): [string, string] => {
    const endDate = dayjs();
    let startDate;
    
    if (type === '按月') {
      startDate = endDate.subtract(6, 'month').startOf('month');
      return [
        startDate.format('YYYY-MM'),
        endDate.format('YYYY-MM')
      ];
    } else {
      startDate = endDate.subtract(6, 'day');
      return [
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD')
      ];
    }
  };

  // 日期范围
  const [localDateRange, setLocalDateRange] = useState<[string, string]>(
    dateRange || getDefaultDateRange(timeRange)
  );

  // 获取入园数据
  const fetchEntryData = async () => {
    if (!goodsId) return;
    
    // 检查是否与上次请求参数相同，如果相同则不重复请求
    const currentRequest = {
      goodsId,
      dateRange: localDateRange,
      timeRange,
    };
    
    if (
      prevRequestRef.current.goodsId === currentRequest.goodsId &&
      prevRequestRef.current.dateRange[0] === currentRequest.dateRange[0] &&
      prevRequestRef.current.dateRange[1] === currentRequest.dateRange[1] &&
      prevRequestRef.current.timeRange === currentRequest.timeRange
    ) {
      return;
    }
    
    // 更新上次请求参数
    prevRequestRef.current = currentRequest;
    
    setLoading(true);
    try {
      const params = {
        goodsId,
        saleStartDate: localDateRange[0],
        saleEndDate: localDateRange[1],
        dateType: timeRange === '按月' ? 'month' : 'day',
      };
      
      const { data } = await apiParkEntryStatistics(params);
      console.log('data---', data);
      if (data) {
        // 处理接口返回的数据
        const formattedData = data.map((item: any) => ({
          date: item.checkDate,
          count: parseInt(item.checkQuantity, 10),
          ratio: parseFloat(item.checkProportion.replace('%', ''))
        }));

        console.log('formattedData---', formattedData);
        setChartData(formattedData);
      } else {
        setChartData([]);
      }
    } catch (error) {
      console.error('获取入园数据失败：', error);
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };

  // 当父组件传入的日期范围变化时，更新本地日期范围
  useEffect(() => {
    if (dateRange) {
      setLocalDateRange(dateRange);
    }
  }, [dateRange]);

  // 使用单独的 effect 处理 entryData
  useEffect(() => {
    if (!goodsId && entryData && entryData.length > 0) {
      setChartData(entryData);
    }
  }, [goodsId, entryData]);

  // 当日期范围、时间维度或商品 ID 变化时，获取数据
  useEffect(() => {
    if (goodsId) {
      fetchEntryData();
    }
  }, [goodsId, localDateRange[0], localDateRange[1], timeRange]);
  
  // 处理日期格式化，按月时显示年月，按天时显示年月日
  const formatDate = (date: string) => {
    if (timeRange === '按月') {
      return dayjs(date).format('YYYY.MM');
    } else {
      return dayjs(date).format('YYYY.MM.DD');
    }
  };

  // 筛选符合日期范围的数据
  const filterDataByDateRange = () => {
    if (chartData.length === 0) return [];
    
    const startDate = timeRange === '按月' 
      ? dayjs(localDateRange[0]).startOf('month') 
      : dayjs(localDateRange[0]);
    const endDate = timeRange === '按月' 
      ? dayjs(localDateRange[1]).endOf('month') 
      : dayjs(localDateRange[1]);
    
    return chartData
      .filter(item => {
        const itemDate = dayjs(item.date);
        return itemDate.isAfter(startDate) && itemDate.isBefore(endDate) || 
               itemDate.isSame(startDate) || 
               itemDate.isSame(endDate);
      })
      .map(item => ({ 
        date: formatDate(item.date), 
        count: item.count,
        ratio: item.ratio || 0
      }));
  };

  // 使用传入的数据，并根据日期范围进行筛选
  const filteredData = filterDataByDateRange();
  
  // 构建图表配置
  const chartOptions = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>入园数：${param.value}人`;
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      textStyle: {
        color: '#fff'
      },
      confine: true
    },
    grid: {
      left: '6%',
      right: '4%',
      bottom: '3%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: filteredData.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#eaeaea'
        }
      },
      axisLabel: {
        color: '#666',
        rotate: 0,
      }
    },
    yAxis: {
      type: 'value',
      name: '入园数/人',
      nameLocation: 'middle',
      nameGap: 40,
      nameRotate: 90,
      nameTextStyle: {
        color: '#666',
        padding: [0, 0, 0, 0],
        align: 'center',
        verticalAlign: 'middle'
      },
      axisLabel: {
        formatter: '{value}',
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#eaeaea'
        }
      }
    },
    series: [
      {
        name: '入园数',
        type: 'bar',
        data: filteredData.map(item => item.count),
        barWidth: '40%',
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates: any, dateStrings: [string, string]) => {
    if (dates) {
      setLocalDateRange(dateStrings);
      
      // 通知父组件日期范围变化
      if (onDateRangeChange) {
        onDateRangeChange(dateStrings, timeRange);
      }
    }
  };

  // 处理时间维度变化
  const handleTimeRangeChange = (e: any) => {
    const newTimeRange = e.target.value;
    // 在这里直接更新日期范围，避免触发额外的 useEffect
    const newDateRange = getDefaultDateRange(newTimeRange);
    setLocalDateRange(newDateRange);
    
    // 通知父组件日期范围和时间维度变化
    if (onDateRangeChange) {
      onDateRangeChange(newDateRange, newTimeRange);
    }
    
    // 最后再设置时间范围，这样只会触发一次 fetchEntryData
    setTimeRange(newTimeRange);
  };

  // 根据是否在模态框内显示确定容器样式
  const containerStyleToUse = inSettingModal ? 
    styles.chartComponentContainer : 
    { ...styles.container, ...containerStyle };

  return (
    <div style={containerStyleToUse}>
      <div style={styles.toolbar}>
        <Radio.Group
          value={timeRange}
          onChange={handleTimeRangeChange}
          buttonStyle="solid"
          size="small"
        >
          <Radio.Button value="按天">按天</Radio.Button>
          <Radio.Button value="按月">按月</Radio.Button>
        </Radio.Group>

        <RangePicker
          size="small"
          picker={timeRange === '按月' ? 'month' : 'date'}
          format={timeRange === '按月' ? 'YYYY-MM' : 'YYYY-MM-DD'}
          placeholder={['开始日期', '结束日期']}
          value={[
            localDateRange[0] ? dayjs(localDateRange[0]) : null,
            localDateRange[1] ? dayjs(localDateRange[1]) : null,
          ]}
          onChange={handleDateRangeChange}
          style={styles.dateRangePicker}
        />
      </div>

      <Spin spinning={loading}>
        {filteredData.length > 0 ? (
          <div style={styles.chartContainer}>
            <Chart options={chartOptions} />
          </div>
        ) : (
          <div style={styles.emptyContainer}>
            <Empty description="暂无入园数据" />
          </div>
        )}
      </Spin>
    </div>
  );
};

export default ParkEntryChart; 