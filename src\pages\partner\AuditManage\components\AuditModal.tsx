/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-24 10:16:48
 * @LastEditTime: 2025-05-28 17:56:48
 * @LastEditors: 李悍宇 zhou<PERSON>@hqyatu.com
 */

import { ApplyStatusEnum, GuideStepStatus } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { useGuide } from '@/hooks/useGuide';
import type { ModalState } from '@/hooks/useModal';
import type { DownInUpDistrItem } from '@/services/api/auditManage';
import {
  agentGroupAdd,
  distributorGroupAdd,
  getAgentGroupList,
  getDistributorGroupList,
  getDownInUpAgent,
  getDownInUpDistr,
  updateAuditInfo,
} from '@/services/api/auditManage';
import type { API } from '@/services/api/typings';
import ProDescriptions from '@ant-design/pro-descriptions';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormDependency, ProFormSelect } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Input, Modal, Space, Typography, message } from 'antd';
import { isEmpty } from 'lodash';
import type { FC } from 'react';
import { useEffect, useReducer, useRef } from 'react';
import { Access, useAccess, useModel, useRequest } from '@umijs/max';

const { Text } = Typography;

const initData = {
  data: [],
  suffix: '',
};
function reducer(
  state: {
    suffix: string;
    data: DownInUpDistrItem[];
  },
  action: { type: 'agent' | 'distr' | 'default'; payload: DownInUpDistrItem[] },
) {
  const { type, payload } = action;

  switch (type) {
    case 'agent':
      return {
        ...state,
        data: payload,
        suffix: '代理商',
      };
    case 'distr':
      return {
        ...state,
        data: payload,
        suffix: '经销商',
      };

    default:
      return {
        data: [],
        suffix: '',
      };
  }
}

type AuditType = 'pass' | 'reject';

type AuditModalProps = ModalState & {
  currentItem?: API.AuditListItem;
  actionRef: React.MutableRefObject<ActionType | undefined>;
};
const AuditModal: FC<AuditModalProps> = ({ visible, type, currentItem, actionRef, setVisible }) => {
  const { initialState } = useModel('@@initialState');
  const formRef = useRef<ProFormInstance<any>>();
  const { username = '' } = initialState?.userInfo || {};
  const { coName = '', coId = '' } = initialState?.currentCompany || {};
  const reasonRef = useRef<string>('');
  // useState 一个对象也是一样的
  const [state, dispatch] = useReducer(reducer, initData);

  const { updateGuideInfo } = useGuide();

  const access = useAccess();

  // 分销商 B 添加到了分销商 A 的哪些经销商分组
  const getDownInUpDistrReq = useRequest(getDownInUpDistr, {
    manual: true,
    initialData: [],
    onSuccess(data = [], params) {
      if (data.length > 0) {
        dispatch({
          type: 'distr',
          payload: data,
        });
      }
    },
  });
  // 查询分销商 B 添加到了分销商 A 的哪些代理商分组
  const getDownInUpAgentReq = useRequest(getDownInUpAgent, {
    manual: true,
    initialData: [],
    onSuccess(data = [], params) {
      if (data.length > 0) {
        dispatch({
          type: 'agent',
          payload: data,
        });
      }
    },
  });

  // 代理商分组
  const agentGroupListReq = useRequest(getAgentGroupList, {
    manual: true,
    formatResult(res) {
      return (res.data ?? [])
        .filter((item) => item.id !== '10000')
        .map((item) => ({
          label: item.name,
          value: item.id,
        }));
    },
  });
  // 经销商分组
  const distributorGroupListReq = useRequest(getDistributorGroupList, {
    manual: true,
    formatResult(res) {
      return (res.data ?? []).map((item) => ({
        label: item.name,
        value: item.id,
      }));
    },
  });

  // 通过/拒绝审批
  const updateAuditReq = useRequest(
    (action: AuditType, type?: number) => {
      return updateAuditInfo({
        applyId: currentItem!.applyId,
        status: action === 'pass' ? ApplyStatusEnum.审核通过 : ApplyStatusEnum.已拒绝,
        phone: currentItem!.phone,
        applyCompanyName: currentItem!.coName,
        upCompanyName: coName,
        approvalUserName: username,
        note: reasonRef.current,
        coCode: currentItem?.coCode,
        type: type ? type : null,
      });
    },
    {
      manual: true,
      onSuccess(data, params) {
        addOperationLogRequest({
          action: 'audit',
          content: `${params[0] === 'pass' ? '通过' : '拒绝'}【${currentItem?.coName}】合作申请`,
        });
        message.success('操作成功');
        setVisible(false);
        reasonRef.current = '';
        actionRef.current?.reload();
        console.log(actionRef.current);
      },
    },
  );

  // 添加代理商/经销商进分组
  const groupAddReq = useRequest(
    ({ group, groupId }) => {
      const { coCode, phone } = currentItem!;
      if (group === 1) {
        // 区分个人和企业
        const agentType = coCode ? 1 : 2;
        const rest = agentType === 1 ? { coCode } : { phone };
        return agentGroupAdd({
          groupId,
          agentType,
          ...rest,
        });
      }
      return distributorGroupAdd({
        coCode,
        groupId,
      });
    },
    {
      manual: true,
      onSuccess(data, params: any) {
        message.success('添加成功');
        updateAuditReq.run('pass', params[0]?.group);
        // 更新引导
        updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_1 });
      },
    },
  );

  const columns: ProColumns<API.AuditListItem>[] = [
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
    },

    {
      title: '企业名称',
      dataIndex: 'coName',
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
    },
    {
      title: '商户账号',
      dataIndex: 'settlementName',
    },
  ];
  const passConfig = {
    width: 500,
    title: '该操作将同意该企业成为下级分销商，并短信通知用户',

    content: (
      <>
        <Space direction="vertical">
          <Text type="secondary">你还要继续吗？</Text>
          {!isEmpty(state.data) && (
            <Text type="warning">
              注：该企业已在{state.data.map((i) => i.name).join('、') + state.suffix}分组中
            </Text>
          )}

          <Text>请选择要添加到的分组：</Text>
        </Space>
        <ProForm
          formRef={formRef}
          layout="inline"
          submitter={false}
          onValuesChange={({ group }) => {
            if (group) {
              formRef.current?.setFieldsValue({
                groupId: undefined,
              });
            }
          }}
        >
          <ProFormSelect
            name="group"
            rules={[
              {
                required: true,
                message: '请选择分组',
              },
            ]}
            formItemProps={{
              style: {
                width: 150,
              },
            }}
            options={[
              {
                label: '代理商分组',
                disabled: (getDownInUpAgentReq.data?.length ?? 0) > 0,
                value: 1,
              },
              {
                label: '经销商分组',
                disabled: (getDownInUpDistrReq.data?.length ?? 0) > 0,
                value: 2,
              },
            ]}
          />
          <ProFormDependency name={['group']}>
            {({ group }) => {
              const options = [[], agentGroupListReq.data, distributorGroupListReq.data];
              return (
                <ProFormSelect
                  formItemProps={{
                    style: {
                      width: 150,
                    },
                  }}
                  rules={[
                    {
                      required: true,
                      message: '请选择',
                    },
                  ]}
                  name="groupId"
                  label=""
                  options={options[group]}
                />
              );
            }}
          </ProFormDependency>
        </ProForm>
      </>
    ),
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      const data = await formRef.current?.validateFields();
      groupAddReq.run(data);
    },
  };

  const rejectConfig = {
    width: 500,
    title: '该操作将驳回该用户的申请，并短信通知用户',
    content: (
      <>
        <Space direction="vertical">
          <Text type="secondary">你还要继续吗？</Text>
          <Text>请填写驳回原因：</Text>
        </Space>
        <Input.TextArea
          style={{ height: 100 }}
          onChange={({ target: { value } }) => {
            /**
             * @description 不要用 setState 去保存值，modal.confirm 是函数实现
             * @see https://github.com/ant-design/ant-design/issues/29291#issuecomment-775869367
             */
            reasonRef.current = value;
          }}
        />
      </>
    ),
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      updateAuditReq.run('reject');
    },
  };
  useEffect(() => {
    const { applyDistributorId = '', status } = currentItem || {};
    if (visible && status !== 2 && status !== 3) {
      dispatch({
        type: 'default',
        payload: [],
      });
      getDownInUpDistrReq.run({
        upDistributorId: coId,
        downDistributorId: applyDistributorId,
      });
      getDownInUpAgentReq.run({
        upDistributorId: coId,
        downDistributorId: applyDistributorId,
      });
      agentGroupListReq.run({
        distributorId: coId,
      });
      distributorGroupListReq.run({
        distributorId: coId,
      });
    }
  }, [visible]);

  return (
    <Modal
      title="审核详情页"
      width={800}
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      footer={
        <>
          {ApplyStatusEnum.待审核 === currentItem?.status && (
            <Access accessible={access.canCheckManagement_passReject}>
              <Button
                type="primary"
                loading={updateAuditReq.loading}
                onClick={() => {
                  Modal.confirm(passConfig);
                }}
              >
                通过
              </Button>
              <Button
                danger
                loading={updateAuditReq.loading}
                onClick={() => {
                  Modal.confirm(rejectConfig);
                }}
              >
                驳回
              </Button>
            </Access>
          )}
          <Button
            onClick={() => {
              setVisible(false);
            }}
          >
            关闭
          </Button>
        </>
      }
    >
      <ProDescriptions<API.AuditListItem>
        column={2}
        title="基本信息"
        layout="horizontal"
        dataSource={currentItem}
        columns={columns}
      />
    </Modal>
  );
};

export default AuditModal;
