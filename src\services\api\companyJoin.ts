/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-20 14:47:03
 * @LastEditTime: 2022-06-23 14:37:20
 * @LastEditors: zhang<PERSON><PERSON>i
 */

import { request } from '@umijs/max';
import { scenicHost } from '.';
import type { API, ResponseData } from './typings';

/** 获取分销商邀请链接凭证 */
export async function getInviteUrl(
  params: {
    distributorId: string;
    type: 1 | 2;
  },
  options: Record<string, any> = {},
) {
  return request<
    ResponseData<{
      code: string;
      validDate: string;
    }>
  >(`${scenicHost}/agentSupport/code`, {
    method: 'GET',
    params,
    ...options,
  });
}

// 企业列表（包含个人机构）
export async function getIMerchantList(options: Record<string, any> = {}) {
  return request<ResponseData<API.MerchantListItem[]>>(`${scenicHost}/co/merchantList`, {
    method: 'GET',
    ...options,
  });
}

// 企业详情
export async function getCoInfo(
  params: {
    id: string;
  },
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.CoInfo>>(`${scenicHost}/co/info/${params.id}`, {
    method: 'GET',
    params,
    ...options,
  });
}

// 个人详情
export async function getPersonInfo(params: { useId: string }) {
  return request<ResponseData<API.PersonInfo>>(`${scenicHost}/co/personalInfo`, {
    method: 'GET',
    params,
  });
}
// 提交申请
export async function submitApproval(
  params: {
    applyDistributorId: string;
    code: string;
    upDistributorId: string;
    type: string;
    userId: string;
  },
  options: Record<string, any> = {},
) {
  return request<ResponseData<string>>(`${scenicHost}/agentSupport/agentApply`, {
    method: 'POST',
    data: params,
    ...options,
  });
}
// 获取申请状态
export async function getApplyStatus(params: API.ApplyStatusParams) {
  return request<ResponseData<API.ApplyStatus>>(`${scenicHost}/agentSupport/agentApply`, {
    method: 'GET',
    params,
  });
}

// 获取实名信息
export async function getRealNameInfo(params: { userId: string }) {
  return request<ResponseData<API.RealNameInfo>>(
    `${scenicHost}/orgStructure/realNameInfo/${params.userId}`,
    {
      method: 'GET',
      params,
    },
  );
}

// 验证凭证是否有效 (用于判断链接是否有效)
export async function verifyCode(params: { distributorId: string; code: string }) {
  return request<ResponseData<boolean>>(`${scenicHost}/agentSupport/verifyCode`, {
    method: 'GET',
    params,
  });
}
