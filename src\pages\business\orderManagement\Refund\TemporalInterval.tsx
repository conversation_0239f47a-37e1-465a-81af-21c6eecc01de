import { useEffect } from 'react';

import { DatePicker, Input, Select } from 'antd';

import './index.less';
// import dayjs from 'dayjs';

export default function TemporalInterval(props: any) {
  const { onChange, timeNume } = props;
  //日期选择
  const selectHandle = (value: any) => {
    console.log(value);
    timeNume(value);
  };

  const getTime = (date: any, dateString: any) => {
    console.log('选择日期', date, dateString);
    onChange(date);
  };
  useEffect(() => {}, []);

  // const pix = (
  //   <Select
  //     defaultValue="0"
  //     onChange={(value) => {
  //       selectHandle(value);
  //     }}
  //   >
  //     <Select.Option value="0">下单时间</Select.Option>
  //     <Select.Option value="1">支付时间</Select.Option>
  //   </Select>
  // )
  return (
    <div>
      <Input.Group className="temporalInterval" style={{ display: 'flex' }}>
        <Select
          defaultValue="0"
          onChange={(value) => {
            selectHandle(value);
          }}
        >
          <Select.Option value="0">申请时间</Select.Option>
          <Select.Option value="1">入园时间</Select.Option>
          <Select.Option value="2">实际退款到账时间</Select.Option>
        </Select>
        <DatePicker.RangePicker
          onChange={(date, dateString) => getTime(date, dateString)}
          // style={{ width: '500px' }}
        />
      </Input.Group>
    </div>
  );
}
