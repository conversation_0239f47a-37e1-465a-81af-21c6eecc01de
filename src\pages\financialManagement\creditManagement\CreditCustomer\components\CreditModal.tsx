/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-23 14:20:19
 * @LastEditTime: 2023-06-12 15:06:10
 * @LastEditors: zhangfeng<PERSON>i
 */
import { addOperationLogRequest } from '@/common/utils/operationLog';
import type { ModalState } from '@/hooks/useModal';
import { updateCreditBalance } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import ProDescriptions from '@ant-design/pro-descriptions';
import type { ActionType } from '@ant-design/pro-table';
import { Form, InputNumber, message, Modal, Select, Space } from 'antd';
import { useForm } from 'antd/es/form/Form';
import type { DefaultOptionType } from 'antd/lib/select';
import type { FC } from 'react';
import { useEffect } from 'react';
import { useModel, useRequest } from '@umijs/max';

interface CreditModalProps {
  currentItem?: API.CreditAccountItem;
  modalState: ModalState;
  actionRef: React.MutableRefObject<ActionType | undefined>;
}

/**
 * @description 授信客户 - 授信
 * */
const CreditModal: FC<CreditModalProps> = ({
  currentItem,
  modalState: { visible, setVisible },
  actionRef,
}) => {
  const { initialState } = useModel('@@initialState');

  const settlementId = initialState?.currentCompany.settlementId || '';
  const [form] = useForm();

  const { loading, run } = useRequest(updateCreditBalance, {
    manual: true,
    onSuccess: () => {
      addOperationLogRequest({
        action: 'edit',
        content: `编辑授信账户【${currentItem?.consumer?.registrationName}】金额`,
      });
      message.success('操作成功');
      setVisible(false);
      actionRef?.current?.reload();
    },
  });

  const selectOptions: DefaultOptionType[] = [
    {
      label: '增加授信',
      value: 'add',
    },
    {
      label: '扣减授信',
      value: 'sub',
      disabled: (currentItem?.balance || 0) <= 0,
    },
  ];

  const onOk = async () => {
    if (currentItem) {
      const { amount, type } = await form.validateFields();
      const prefix = type === 'add' ? 1 : -1;
      run({
        id: currentItem.id,
        amount: (amount || 0) * prefix,
        merchantId: settlementId,
      });
    }
  };
  const onValuesChange = ({ type }: any, values: any) => {
    if (type) {
      form.setFieldsValue({
        amount: undefined,
      });
    }
  };

  const list = [
    {
      label: '授信客户',
      content: currentItem?.consumer?.legalName,
    },
    {
      label: '当前授信余额',
      content: currentItem?.balance,
    },
    {
      label: '调整授信金额（元）',
      content: (
        <Form
          form={form}
          initialValues={{ type: 'add' }}
          preserve={false}
          onValuesChange={onValuesChange}
        >
          <Space style={{ alignItems: 'start' }}>
            <Form.Item name="type" noStyle>
              <Select style={{ width: 120 }} options={selectOptions} />
            </Form.Item>
            <Form.Item
              name="amount"
              rules={[
                {
                  required: true,
                  message: '请输入授信金额',
                },
              ]}
              style={{ marginBottom: 0 }}
            >
              <InputNumber
                placeholder="请输入授信金额"
                max={form.getFieldValue('type') === 'sub' ? currentItem?.balance : undefined}
                precision={2}
                style={{ width: 150 }}
                min={0}
              />
            </Form.Item>
          </Space>
        </Form>
      ),
    },
  ];

  // 重置
  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible]);

  return (
    <Modal
      width={736}
      title={'授信'}
      visible={visible}
      maskClosable
      destroyOnClose
      onCancel={() => setVisible(false)}
      confirmLoading={loading}
      onOk={onOk}
    >
      <ProDescriptions column={1}>
        {list.map((item) => (
          <ProDescriptions.Item
            key={item.label}
            label={item.label}
            labelStyle={{
              width: 140,
            }}
          >
            {item.content}
          </ProDescriptions.Item>
        ))}
      </ProDescriptions>
    </Modal>
  );
};

export default CreditModal;
