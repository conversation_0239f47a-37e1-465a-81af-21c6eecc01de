/*
 * @FilePath: index.tsx
 * @Author: chentian<PERSON><PERSON>
 * @Date: 2022-10-14 13:43:21
 * @LastEditors: z<PERSON><PERSON><PERSON>i
 * @LastEditTime: 2023-09-15 18:34:26
 * Copyright: 2022 xxxTech CO.,LTD. All Rights Reserved.
 * @Descripttion:
 */

import { tableConfig } from '@/common/utils/config';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { formatTime } from '@/common/utils/tool';
import {
  addCashSettlementStatistics,
  getCashSettlementList,
  getUserList,
  updetaCashSettlementState,
} from '@/services/api/cashSettlement';
import type { API } from '@/services/api/typings';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Form, Modal, Popconfirm, Select, Space, Tag, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { Access, useAccess, useModel, useRequest } from '@umijs/max';
import StatementDetail from './components/statementDetail';
import { getEnv } from '@/common/utils/getEnv';
interface IStaffInfoProps {
  label: string;
  value: string;
}
const CashStatement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const settlementStateEnum: Record<number, string> = {
    1: '待结算',
    2: '已结算',
  };
  const [staffVisible, setStaffVisible] = useState<boolean>(false);
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [changeValue, setChangeValue] = useState<string>(''); // 选中的员工信息
  const [tableListItem, setTableListItem] = useState<API.ICashSettlementListResponse>();
  const [form] = Form.useForm();
  const { initialState } = useModel('@@initialState');
  const access = useAccess();

  // 查询现金账单列表
  const cashSettlementListReq = async (params: API.ICashLIstParams) => {
    const realParams = {
      ...params,
      providerId: initialState?.currentCompany?.coId || '',
    };
    const { data } = await getCashSettlementList(realParams);
    return {
      data: data.data,
      total: data.total,
    };
  };

  // 新增现金账单统计
  const addCashSettlementStatisticsReq = useRequest(addCashSettlementStatistics, {
    manual: true,
    onSuccess: (data) => {
      addOperationLogRequest({
        action: 'add',
        content: `生成现金账单【${data}】`,
      });

      message.success('添加成功');
      setChangeValue('');
      actionRef.current?.reload();
      setStaffVisible(false);
    },
    onError: () => {
      setChangeValue('');
      setStaffVisible(false);
    },
  });
  // 公司部门或者成员列表
  const departmentListReq = useRequest(getUserList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      const data = (res.data || []).map((item) => ({
        ...item,
        label: item.nickname,
        value: item.username,
      }));
      return data.map((item: any) => {
        return {
          label: `${item.nickname} ${item.username}`,
          value: `${item.nickname} ${item.username}`,
        };
      });
    },
  });

  // 选择框选中事件
  const handleOnSelectOk = () => {
    if (!changeValue) {
      message.warn('请选择要生成账单的员工');
      return;
    }
    const params = {
      staffName: changeValue.split(' ')[0],
      staffAccount: changeValue.split(' ')[1],
      providerId: initialState?.currentCompany?.coId || '',
    };
    addCashSettlementStatisticsReq.run(params);
  };

  // 修改账单结算状态
  const updateCashSettlementStateReq = useRequest(updetaCashSettlementState, {
    manual: true,
    onSuccess: () => {
      addOperationLogRequest({
        action: 'confirm',
        content: `确认现金账单【${tableListItem?.id}】金额`,
      });
      message.success('修改成功');
      actionRef.current?.reload();
    },
  });

  // 修改账单结算状态二次确认
  const onConfirm = async () => {
    if (tableListItem) {
      updateCashSettlementStateReq.run({
        id: tableListItem.id,
        settlementState: tableListItem.settlementState == 1 ? 2 : 1,
        settlementTime: formatTime(new Date().getTime()),
      });
    }
  };

  useEffect(() => {
    // 获取当前企业所有员工列表
    departmentListReq.run({
      companyId: initialState?.currentCompany?.coId || '',
      appId: getEnv().APPID || '',
      system: 2, // 禁用系统类型：1 景区系统，2 电商系统，3 慧旅云
    });
  }, []);

  const columns: ProColumns<API.ICashSettlementListResponse>[] = [
    {
      title: '单号',
      dataIndex: 'id',
      key: 'id',
      hideInSearch: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      search: false,
    },
    {
      title: '创建时间',
      key: 'time',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            createBeginTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD HH:mm:ss'),
            createEndTime: dayjs(value[1], 'YYYY-MM-DD').endOf('day').format('YYYY-MM-DD HH:mm:ss'),
          };
        },
      },
    },
    {
      title: '员工姓名',
      dataIndex: 'staffName',
      key: 'staffName',
      hideInSearch: false,
    },
    {
      title: '员工账号',
      dataIndex: 'staffAccount',
      key: 'staffAccount',
      hideInSearch: false,
    },
    {
      title: '统计时间',
      key: 'count_time',
      hideInSearch: true,
      render: (_, item: any) => <>{`${item.countBeginTime}~${item.createTime}`}</>,
    },
    {
      title: '账单金额（元）',
      dataIndex: 'payAmount',
      key: 'payAmount',
      hideInSearch: true,
      align: 'right',
      renderText: (text) => parseFloat(text).toFixed(2),
    },
    {
      title: '结算状态',
      dataIndex: 'settlementState',
      valueType: 'select',
      valueEnum: settlementStateEnum,
      renderText: (dom) => {
        const color = dom == 1 ? 'orange' : 'blue';
        return <Tag color={color}>{settlementStateEnum[dom]}</Tag>;
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      renderText: (_, record: any) => (
        <Space
          onClick={() => {
            setTableListItem(record);
          }}
        >
          <a
            onClick={() => {
              setDetailsVisible(true);
            }}
          >
            账单明细
          </a>
          {record.settlementState == 1 && (
            <Access key="confirm" accessible={access.canBillAccount_confirm}>
              <Popconfirm
                placement="top"
                title={'是否确认已结算全部现金？'}
                onConfirm={onConfirm}
                okButtonProps={{
                  loading: updateCashSettlementStateReq.loading,
                }}
              >
                <a>确定</a>
              </Popconfirm>
            </Access>
          )}
        </Space>
      ),
    },
  ];
  return (
    <>
      <ProTable<API.ICashSettlementListResponse, API.ICashLIstParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="id"
        request={cashSettlementListReq}
        toolBarRender={() => [
          <Access key="add" accessible={access.canBillAccount_insert}>
            <Button
              type="primary"
              key="cashStatement"
              onClick={() => {
                setStaffVisible(true);
              }}
            >
              生成现金账单
            </Button>
          </Access>,
        ]}
        columns={columns}
      />

      {/* 生成现金账单弹窗 */}
      <Modal
        width={modelWidth.md}
        title={'生成现金账单'}
        visible={staffVisible}
        destroyOnClose
        onCancel={() => setStaffVisible(false)}
        onOk={handleOnSelectOk}
      >
        <Form form={form} preserve={false}>
          <Form.Item label="选择员工账号">
            <Select
              showSearch
              placeholder="请输入员工姓名或员工账号"
              showArrow={false}
              options={departmentListReq.data}
              onChange={(e) => {
                setChangeValue(e);
              }}
            >
              {/* {options} */}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      {/* 账单明细 modal */}
      <StatementDetail
        visible={detailsVisible}
        setVisible={setDetailsVisible}
        tableListItem={tableListItem}
      />
    </>
  );
};

export default CashStatement;
