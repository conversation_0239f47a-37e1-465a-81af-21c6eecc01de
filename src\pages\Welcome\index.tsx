import { addOperationLogRequest } from '@/common/utils/operationLog';
import { copyText, getUniqueId } from '@/common/utils/tool';
import {
  filterBarData1,
  filterBarData2,
  filterLineData1,
  filterLineData2,
} from '@/components/BaseChart/filterData';
import type { IBarChartProps } from '@/components/BaseChart/index';
import BaseChart from '@/components/BaseChart/index';
import RefundModal from '@/components/RefundModal';
import useModal from '@/hooks/useModal';
import { getSalesRefundOrderPageList } from '@/services/api/distribution';
import { apiNoticeList } from '@/services/api/erp';
import { apiStoreList } from '@/services/api/store';
import { CloseOutlined } from '@ant-design/icons';
import { Alert, Button, Card, Col, Divider, Modal, Row, Typography, message } from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import React, { useEffect } from 'react';
import { useModel, useRequest } from '@umijs/max';
import styles from './index.less';
import { getEnv } from '@/common/utils/getEnv';

const { Title, Paragraph } = Typography;
const respon = {
  xs: 24,
  md: 12,
  xxl: 6,
};

export default (): React.ReactNode => {
  const { SCENIC_HOST, FINANCIAL_HOST, MERCHANT_HOST, HOUSE_URL } = getEnv();
  const { initialState }: any = useModel('@@initialState');
  const [scenicList, setScenicList] = React.useState<any>([]);
  const [noticeList, setNoticeList] = React.useState<any>([]);
  const [storeList, setStoreList] = React.useState<any>([]);
  const [detailsVisible, setDetailsVisible] = React.useState<any>(false);
  const [storeVisible, setStoreVisible] = React.useState<any>(false);

  const chartData: IBarChartProps[] = [
    {
      id: '1',
      type: 'bar',
      datePickerConfig: {
        picker: 'rangePicker',
        defaultValue: [dayjs().subtract(6, 'day'), dayjs()],
      },
      title: '订单/退订统计',
      filterDataFunc: filterBarData1,
      unit: '单',
    },
    {
      id: '2',
      title: '佣金统计',
      type: 'bar',
      datePickerConfig: {
        picker: 'rangePicker',
        defaultValue: [dayjs().subtract(6, 'day'), dayjs()],
      },
      filterDataFunc: filterBarData2,
      unit: '元',
    },
    {
      id: '3',
      type: 'line',
      datePickerConfig: {
        picker: 'rangePicker',
        defaultValue: [dayjs().subtract(6, 'day'), dayjs()],
      },
      title: '当前企业交易统计',
      unit: '元',
      filterDataFunc: filterLineData1,
    },
    {
      id: '4',
      type: 'line',
      datePickerConfig: {
        picker: 'rangePicker',
        defaultValue: [dayjs().subtract(6, 'day'), dayjs()],
      },
      title: '下级代理交易统计',
      unit: '元',
      filterDataFunc: filterLineData2,
    },
  ];
  // 消息通知
  const getNotice = () => {
    apiNoticeList({
      isEnable: 1,
      noticePosition: 2,
      receiveList: initialState?.currentCompany.coId,
    })
      .then((e: any) => {
        const nowTime = new Date();
        const list = e?.data?.records.filter(
          (notice: any) =>
            nowTime > new Date(notice.noticeDisplayBeginTime) &&
            nowTime < new Date(notice.noticeDisplayEndTime),
        );
        setNoticeList(list);
      })
      .catch((e) => {
        console.error(e);
      });
  };
  // 获取店铺列表
  const getStoreList = () => {
    apiStoreList({ id: initialState?.userInfo.userId })
      .then(({ data }) => {
        const list = data.map((e: any) => {
          return {
            title: e.name,
            url: `${HOUSE_URL}#/?storeId=${e.id}`,
          };
        });
        setStoreList(list);
      })
      .catch(() => {});
  };
  // 公告地址
  const onLinkUrl = (val: any) => {
    const url = val.includes('http') ? val : `http://${val}`; //如果链接地址不是 http 开头，则添加 http://
    window.open(url);
    console.log(val);
  };
  // 近七天销售退单
  const refundModalState = useModal();
  const getRefundPageListReq = useRequest(getSalesRefundOrderPageList, {
    manual: true,
    formatResult(res: any) {
      return res.data.data || res.data.records;
    },
  });
  // 卡片数据
  const data = [
    // {
    //   title: '近七天销售退单',
    //   key: 'order',
    //   content: getRefundPageListReq.data?.length ?? 0,
    //   button: (
    //     <div className="button" onClick={() => refundModalState.setVisible(true)}>
    //       查看
    //     </div>
    //   ),
    // },
    {
      title: '电商系统',
      key: 'exchange',
      content: '窗口售票',
      button: (
        <div
          className="button"
          onClick={() => {
            if (storeList?.length > 0) {
              if (storeList.length === 1) {
                addOperationLogRequest({
                  action: 'link',
                  content: '跳转窗口售票系统',
                });
                window.open(storeList[0].url);
              } else {
                setStoreVisible(true);
              }
            } else {
              message.warning('暂无售票窗口');
            }
          }}
        >
          前往
        </div>
      ),
      url: SCENIC_HOST,
      copyCallBack: () => {
        if (storeList?.length > 0) {
          if (storeList?.length === 1) {
            copyText(storeList[0].url);
          } else {
            setStoreVisible(true);
          }
        } else {
          message.warning('暂无售票窗口');
        }
      },
      goLinkCallBack: () => {
        if (storeList?.length > 0) {
          if (storeList?.length === 1) {
            window.open(storeList[0].url);
          } else {
            setStoreVisible(true);
          }
        } else {
          message.warning('暂无售票窗口');
        }
      },
    },
    {
      title: '景区系统',
      key: 'scenic',
      content: '票务管理、设备管理、销售管理',
      button: (
        <div
          className="button"
          onClick={() => {
            if (scenicList?.length > 0) {
              if (scenicList.length === 1) {
                addOperationLogRequest({
                  action: 'link',
                  content: '跳转慧景云',
                });
                window.open(scenicList[0].url);
              } else {
                setDetailsVisible(true);
              }
            } else {
              message.warning('暂无景区');
            }
          }}
        >
          前往
        </div>
      ),
      url: SCENIC_HOST,
      scenicList: [],
      copyCallBack: () => {
        if (scenicList?.length > 0) {
          if (scenicList?.length === 1) {
            copyText(scenicList[0].url);
          } else {
            setDetailsVisible(true);
          }
        } else {
          message.warning('暂无景区');
        }
      },
      goLinkCallBack: () => {
        if (scenicList?.length > 0) {
          if (scenicList?.length === 1) {
            window.open(scenicList[0].url);
          } else {
            setDetailsVisible(true);
          }
        } else {
          message.warning('暂无景区');
        }
      },
    },
    {
      title: '金融系统',
      key: 'financial',
      content: '信用申请、信用评估、预授信激活',
      button: (
        <div
          className="button"
          onClick={() => {
            if (initialState?.currentCompany.settlementStatus == 1) {
              addOperationLogRequest({
                action: 'link',
                content: '跳转金融系统',
              });
              window.open(FINANCIAL_HOST);
            } else {
              message.info('请完成企业开户认证后前往！');
            }
          }}
        >
          前往
        </div>
      ),
      url: SCENIC_HOST,
      copyCallBack: () => {
        copyText(FINANCIAL_HOST);
      },
      goLinkCallBack: () => {
        window.open(FINANCIAL_HOST);
      },
    },
    {
      title: '结算系统',
      key: 'pay',
      content: '结算交易记录',
      button: (
        <div
          className="button"
          onClick={() => {
            addOperationLogRequest({
              action: 'link',
              content: '跳转结算系统',
            });
            window.open(MERCHANT_HOST);
          }}
        >
          前往
        </div>
      ),
      url: SCENIC_HOST,
      copyCallBack: () => {
        copyText(MERCHANT_HOST);
      },
      goLinkCallBack: () => {
        window.open(MERCHANT_HOST);
      },
    },
  ];
  useEffect(() => {
    const itemArr: any = [];
    initialState?.scenicList?.forEach((e: any) => {
      // 筛选当前企业的景区
      if (e.coId === initialState?.currentCompany?.coId) {
        const obj = {
          title: e.scenicName,
          url: `${SCENIC_HOST}#/${e.uniqueIdentity}/welcome`,
        };
        itemArr.push(obj);
      }
    });
    setScenicList(itemArr);
    //获取公告信息
    getNotice();
    // 获取店铺列表
    getStoreList();
    getRefundPageListReq.run({
      pageSize: 999,
      sellerId: initialState?.currentCompany?.coId,
      beginTime: dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
      endTime: dayjs().format('YYYY-MM-DD'),
    });
  }, []);

  useEffect(() => {
    addOperationLogRequest({
      action: 'info',
      content: '查看首页',
    });
  }, []);

  return (
    <Row gutter={[0, 24]} className={styles.main}>
      {/* 卡片 */}
      <Col span={24}>
        <Card>
          {!!noticeList.length &&
            noticeList.map((e: any, i: any) => (
              <Alert
                key={i}
                message="公告"
                description={
                  <>
                    <div>{e.noticeContent}</div>
                    <br />
                    <a onClick={() => onLinkUrl(e.noticeUrl)}>
                      {e.noticeUrl !== '' ? '查看详情' : ''}
                    </a>
                  </>
                }
                type="info"
                closeText={e.isClose == '1' ? <CloseOutlined /> : ''}
                style={{ marginBottom: 20 }}
              />
            ))}
          <Row gutter={[20, 20]}>
            {data.map((item) => (
              <>
                <Col {...respon}>
                  <div className={`card card_${item.key}`}>
                    <div className="title">{item.title}</div>
                    <div className="content">{item.content}</div>
                    {item.button}
                    <div className="icon" />
                  </div>
                </Col>
              </>
            ))}
          </Row>
        </Card>
      </Col>
      {/* 图表 */}
      <Col span={24}>
        <Row gutter={[20, 20]}>
          {chartData.map(({ datePickerConfig, type, title, unit, filterDataFunc, id }) => (
            <>
              <Col xs={24} sm={24} xl={12}>
                <BaseChart
                  id={id}
                  datePickerConfig={datePickerConfig}
                  unit={unit}
                  type={type}
                  title={title}
                  filterDataFunc={filterDataFunc}
                />
              </Col>
            </>
          ))}
        </Row>
      </Col>
      {/* 景区列表弹窗 */}
      <Modal
        width={670}
        title="景区列表"
        visible={detailsVisible}
        onCancel={() => {
          setDetailsVisible(false);
        }}
        footer={null}
      >
        {scenicList.map((item: any, index: any) => (
          <div key={getUniqueId()}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Title level={5} style={{ margin: 0 }}>
                  {item.title}
                </Title>
                <Paragraph copyable style={{ margin: 0 }}>
                  {item.url}
                </Paragraph>
              </div>
              <Button
                type="primary"
                onClick={() => {
                  addOperationLogRequest({
                    action: 'link',
                    content: '跳转慧景云',
                  });
                  window.open(item.url);
                }}
              >
                前往
              </Button>
            </div>
            {index < scenicList.length - 1 && <Divider style={{ margin: '12px 0' }} />}
          </div>
        ))}
        {/* <List
          className="demo-loadmore-list"
          loading={false}
          itemLayout="horizontal"
          dataSource={scenicList}
          renderItem={(item: any) => (
            <List.Item
              actions={[
                <a
                  key="list-loadmore-copy"
                  onClick={() => {
                    copyText(item.url);
                  }}
                >
                  复制链接
                </a>,
                <a key="list-loadmore-edit" target="_blank" href={item.url} rel="noreferrer">
                  前往景区
                </a>,
              ]}
            >
              <Skeleton title={false} loading={false}>
                <List.Item.Meta title={item.title} description={item.url} />
              </Skeleton>
            </List.Item>
          )}
        /> */}
      </Modal>
      {/* 售票窗口列表弹窗 */}
      <Modal
        width={670}
        title="售票窗口列表"
        visible={storeVisible}
        onCancel={() => {
          setStoreVisible(false);
        }}
        footer={null}
      >
        {storeList.map((item: any, index: any) => (
          <div key={getUniqueId()}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Title level={5} style={{ margin: 0 }}>
                  {item.title}
                </Title>
                <Paragraph copyable style={{ margin: 0 }}>
                  {item.url}
                </Paragraph>
              </div>
              <Button
                type="primary"
                onClick={() => {
                  addOperationLogRequest({
                    action: 'link',
                    content: '跳转窗口售票系统',
                  });

                  window.open(item.url);
                }}
              >
                前往
              </Button>
            </div>
            {index < storeList.length - 1 && <Divider style={{ margin: '12px 0' }} />}
          </div>
        ))}
        {/* <List
          className="demo-loadmore-list"
          loading={false}
          itemLayout="horizontal"
          dataSource={storeList}
          renderItem={(item: any) => (
            <List.Item
              actions={[
                <a
                  key="list-loadmore-copy"
                  onClick={() => {
                    copyText(item.url);
                  }}
                >
                  复制链接
                </a>,
                <a key="list-loadmore-edit" target="_blank" href={item.url} rel="noreferrer">
                  前往窗口
                </a>,
              ]}
            >
              <Skeleton title={false} loading={false}>
                <List.Item.Meta title={item.title} description={item.url} />
              </Skeleton>
            </List.Item>
          )}
        /> */}
      </Modal>
      {/* 销售退单 */}
      <RefundModal modalState={refundModalState} dataSource={getRefundPageListReq.data || []} />
    </Row>
  );
};
