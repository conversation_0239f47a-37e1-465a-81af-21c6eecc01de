import { PageLoading } from '@ant-design/pro-layout';
import { history } from '@umijs/max';
import { parse } from 'querystring';
import React, { useEffect } from 'react';

const LoadingPage: React.FC = () => {
  useEffect(() => {
    const { hash } = history.location;
    const query = parse(hash.split('?')[1]);

    if (query.isReload == '1') {
      history.replace('/welcome');
    } else {
      history.replace('/loading?isReload=1');
      location.reload();
    }
  }, []);
  return <PageLoading />;
};

export default LoadingPage;
