import { SafetyCertificateOutlined, SolutionOutlined, UserOutlined } from '@ant-design/icons';
import ProTable from '@ant-design/pro-table';
import { Button, Descriptions, Divider, Modal, Skeleton, Space, Steps } from 'antd';
import { useEffect, useState } from 'react';
let refund = [];
let refundObj = {};
// import Qrcode from './Qrcode';

import Tag from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import {
  orderTypeEnum,
  payTypeEnum,
  productTypeEnum,
  saleChannelEnum,
  ticketTypeEnum,
  whetherEnum,
} from '@/common/utils/enum';
import { DataMaskTypeEnum, formatTime } from '@/common/utils/tool';
import DataMask from '@/components/DataMask';
import { showTicketModal } from '@/global';
import { useMask } from '@/hooks/useMask';
import { isEmpty } from 'lodash';
import './index.less';

const { Step } = Steps;

interface ManagementDefault {
  orderParticular: any;
  inItTicketInfoData: any;
  orderParticulaStatus: any;
  resStatus: boolean;
  flag: boolean;
  onClose: Function;
  unsubscribeMessageData: any;
  backOrderId: any;
}

function Management(props: ManagementDefault) {
  // const [QRcodestatus, setQRcodestatus] = useState(false);
  // const [QRcodestatusID, setQRcodestatusID] = useState();

  //二维码
  // const QRcode = (id: any) => {
  //   setQRcodestatus(true);
  //   setQRcodestatusID(id);
  // };

  //订单详情取消按钮
  // const onclose = (flag: boolean) => {
  //   setQRcodestatus(flag);
  // };

  const [handleDetailsMaskChange, maskDetailsDataFn] = useMask();

  //订单详情
  const {
    orderParticular,
    inItTicketInfoData,
    orderParticulaStatus,
    resStatus,
    unsubscribeMessageData,
    backOrderId,
  } = props;
  console.log('票务信息', inItTicketInfoData);
  console.log('基本信息', orderParticular);
  console.log('当前订单', orderParticulaStatus);
  console.log('退款订单', unsubscribeMessageData);

  //当前退订信息
  refund = unsubscribeMessageData.filter((item) => {
    return item.refundId == backOrderId;
  });
  refundObj = [...refund];

  // 合计
  const [countNum, setCount] = useState<number>(0);

  let reSum2 = 0;
  unsubscribeMessageData.map((item: any) => {
    reSum2 = item.refundAmount + reSum2;
    return reSum2;
  });

  inItTicketInfoData.forEach((item) => {
    refund[0]?.ticketInfo.forEach((item2) => {
      item.refundAmount = item2.refundAmount;
    });
  });
  console.log('********************', inItTicketInfoData);

  console.log('backOrderIdbackOrderId', refund, backOrderId, refundObj);

  const shutDown = () => {
    props.onClose(false);
  };

  useEffect(() => {}, []);

  //创建订单
  const createStatusArray = [];
  //支付中
  const payStatusArray = [];
  //出票
  const ticketStatusArray = [];
  //使用
  const checkStatusArray = [];
  //退款
  const refundStatusArray = [];
  //用户取消订单
  const userStatusArray = [];

  for (const k in orderParticulaStatus) {
    // switch (orderParticulaStatus[k]) {
    //   case '10':
    //     createStatusArray.push({ name: '创建订单', time: orderParticulaStatus.createStatus })
    //     break;
    //   case '13':
    //     createStatusArray.push({ name: '用户取消订单', time: orderParticulaStatus.createStatus })
    //     break;
    //   case '14':
    //     createStatusArray.push({ name: '系统取消订单', time: orderParticulaStatus.createStatus })
    //     break;
    //   case '20':
    //     payStatusArray.push({ name: '支付中', time: orderParticulaStatus.payStatus })
    //     break;
    //   case '21':
    //     payStatusArray.push({ name: '支付成功', time: orderParticulaStatus.payStatus })
    //     break;
    //   case '30':
    //     payStatusArray.push({ name: '出票成功', time: orderParticulaStatus.payStatus })
    //     break;
    //   default:

    //     break;
    // }

    if (k == 'createTime') {
      createStatusArray.push({ name: '创建订单', time: orderParticulaStatus.createTime });
    }
    if (k == 'orderStatus') {
      //已取消
      if (orderParticulaStatus.orderStatus == 13 || orderParticulaStatus.orderStatus == 14) {
        userStatusArray.push({ name: '用户取消订单', time: orderParticulaStatus.createTime });
        if (orderParticulaStatus.payStatus == 20) {
          // payStatusArray.push({ name: '支付中', time: orderParticulaStatus.payStatus })
          payStatusArray.push({ name: '已取消', time: orderParticulaStatus.createTime });
        }
      } else if (orderParticulaStatus.orderStatus == 12) {
        payStatusArray.push({ name: '订单超时失效', time: orderParticulaStatus.createTime });
      } else if (orderParticulaStatus.payStatus == 20) {
        payStatusArray.push({ name: '支付中', time: orderParticulaStatus.payTime });
        if (orderParticulaStatus.createStatus == 12) {
          createStatusArray.push({
            name: '订单超时失效',
            time: orderParticulaStatus.createTime,
          });
        }
      } else if (orderParticulaStatus.payStatus == 21) {
        payStatusArray.push({ name: '支付成功', time: orderParticulaStatus.payTime });
        if (orderParticulaStatus.ticketStatus == 30) {
          ticketStatusArray.push({ name: '出票成功', time: orderParticulaStatus.ticketTime });
        }
      }

      if (orderParticulaStatus.refundStatus == 50) {
        refundStatusArray.push({ name: '退款中', time: orderParticulaStatus.refundTime });
      } else if (orderParticulaStatus.refundStatus == 51) {
        refundStatusArray.push({ name: '退款成功', time: orderParticulaStatus.refundTime });
      } else if (orderParticulaStatus.refundStatus == 52) {
        refundStatusArray.push({ name: '退款失败', time: orderParticulaStatus.refundTime });
      }
    }
  }
  console.log('退款信息', refundStatusArray);

  //票务信息
  const stubHub: any = [
    // {
    //   title: '票号',
    //   dataIndex: 'id',
    //   editable: true,
    //   hideInSearch: true,
    //   width: '10%',
    //   render: (dom: any, record: any) => {
    //     const newDom = dom.replace(dom.substring(2, record.id.length - 4), '****');
    //     return (
    //       <>
    //         <Tickets onTooltipId={record.id} orderParticular={orderParticular}>
    //           {newDom}
    //         </Tickets>
    //       </>
    //     );
    //   },
    // },
    {
      title: '产品名称',
      dataIndex: 'proName',
      editable: true,
      hideInSearch: true,
      ellipsis: true,
      width: '10%',
      key: 'proName',
    },

    {
      title: '类型',
      dataIndex: 'proType',
      editable: true,
      hideInSearch: true,
      width: '10%',
      valueEnum: productTypeEnum,
    },
    {
      title: '票号',
      dataIndex: 'id',
      editable: true,
      hideInSearch: true,
      width: '9%',
      ellipsis: true,
    },
    {
      title: '票种',
      dataIndex: 'type',
      editable: true,
      hideInSearch: true,
      width: '10%',
      valueEnum: ticketTypeEnum,
    },
    {
      title: '数字资产',
      dataIndex: 'isChain',
      valueEnum: whetherEnum,
    },
    {
      title: '人数',
      dataIndex: 'playerNum',
    },
    {
      title: '已核销总次数',
      dataIndex: 'checkedNum',
    },
    {
      title: '姓名/身份证',
      dataIndex: 'realNameList',
      width: 150,
      renderText: (text: any[]) => {
        if (isEmpty(text || [])) {
          return '-';
        }
        const { idCardName, idCardNumber } = text[0];
        return (
          <Space direction="vertical" size="small" align="center">
            <span>{maskDetailsDataFn(idCardName, DataMaskTypeEnum.NAME)}</span>
            <span>{maskDetailsDataFn(idCardNumber, DataMaskTypeEnum.ID_CARD)}</span>
            {text.length > 1 && (
              <a
                onClick={() => {
                  const maskedData = text.map((item) => ({
                    ...item,
                    idCardName: maskDetailsDataFn(item.idCardName, DataMaskTypeEnum.NAME),
                    idCardNumber: maskDetailsDataFn(item.idCardNumber, DataMaskTypeEnum.ID_CARD),
                  }));
                  showTicketModal(maskedData);
                }}
              >
                查看所有
              </a>
            )}
          </Space>
        );
      },
    },

    // {
    //   title: '支付金额',
    //   dataIndex: 'realPrice',
    //   editable: true,
    //   hideInSearch: true,
    //   width: '10%',
    //   key: 'realPrice',
    // },
    // {
    //   title: '使用日期',
    //   dataIndex: 'enterDate',
    //   editable: true,
    //   hideInSearch: true,
    //   width: '10%',
    //   key: 'enterDate',
    //   // ellipsis: true,
    // },

    {
      title: '退票金额',
      dataIndex: 'refundAmount',
      editable: true,
      hideInSearch: true,
      width: '9%',
      ellipsis: true,
      key: 'refundAmount',
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      width: '5%',
      key: 'status',
      // ellipsis: true,
      editable: true,
      // width: '10%',
      // ellipsis: true,
      render: (dom: any, record: any) => {
        if (dom == 0) {
          return '未核销';
        } else if (dom == 1) {
          return '已核销';
        } else if (dom == 2) {
          return '已过期';
        } else if (dom == 3) {
          return '已完成';
        } else if (dom == 4) {
          return '已退票';
        } else {
          return '-';
        }
      },
    },
  ];

  const array = [
    ...createStatusArray,
    ...payStatusArray,
    ...ticketStatusArray,
    // ...checkStatusArray,
    ...refundStatusArray,
    ...userStatusArray,
  ];

  return (
    <>
      {/* <Qrcode flag={QRcodestatus} onClose={onclose} QRcodestatusID={QRcodestatusID} /> */}

      <Modal
        title={
          <>
            <span>退订详情</span>
            <DataMask
              onDataMaskChange={handleDetailsMaskChange}
              logContent="查看【退订详情】用户隐私信息"
            />
          </>
        }
        visible={props.flag}
        width={1200}
        footer={[
          <Button key="start" onClick={shutDown}>
            取消
          </Button>,
        ]}
        onCancel={() => {
          shutDown();
        }}
      >
        {!resStatus ? (
          <Skeleton active paragraph={{ rows: 4 }} />
        ) : (
          <>
            <h3>当前订单状态：{orderTypeEnum[orderParticular.orderStatus]}</h3>

            <Steps
              style={{ margin: '20px auto' }}
              size="small"
              // progressDot
              current={array.length !== 0 ? array.length : 0}
              // progressDot={true}
              // percent={3}
            >
              {orderParticulaStatus.createStatus !== null
                ? createStatusArray.map((item, index) => {
                    return (
                      <Step
                        key={index}
                        icon={<SolutionOutlined />}
                        title={item.name}
                        description={formatTime(item.time)}
                      />
                    );
                  })
                : ''}
              {userStatusArray.map((item, index) => {
                return (
                  <Step
                    key={index}
                    icon={<UserOutlined />}
                    title={item.name}
                    description={formatTime(item.time)}
                  />
                );
              })}
              {orderParticulaStatus.payStatus !== null
                ? payStatusArray.map((item, index) => {
                    return (
                      <Step
                        key="index"
                        icon={<SafetyCertificateOutlined />}
                        title={item.name}
                        description={formatTime(item.time)}
                      />
                    );
                  })
                : ''}
              {orderParticulaStatus.ticketStatus !== null
                ? ticketStatusArray.map((item, index) => {
                    return (
                      <Step
                        key={index}
                        title={item.name}
                        // icon={<SafetyOutlined />}
                        // icon={<SafetyOutlined />}
                        description={formatTime(item.time)}
                      />
                    );
                  })
                : ''}

              {refundStatusArray.map((item, index) => {
                return (
                  <Step
                    key={index}
                    icon={<SafetyCertificateOutlined />}
                    title={item.name}
                    description={formatTime(item.time)}
                  />
                );
              })}
            </Steps>
            <Divider />
            {refund.length > 0 ? (
              <Descriptions title="基础信息" className="basicFont">
                <Descriptions.Item label="退单号">{backOrderId}</Descriptions.Item>
                <Descriptions.Item label="订单号">
                  {refund[0]?.orderId ? refund[0]?.orderId : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="结算单号">
                  {refund[0].tradeNo ? refund[0].tradeNo : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="服务商">
                  {orderParticular.serviceProviderName ? orderParticular.serviceProviderName : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="购票终端">
                  {saleChannelEnum[orderParticular.sourceType]
                    ? saleChannelEnum[orderParticular.sourceType]
                    : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="代理商">
                  {orderParticular.distributorName ? orderParticular.distributorName : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="申请日期">
                  {refund[0].refundTime ? formatTime(refund[0].refundTime) : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="下单时间">
                  {refund[0].createTime ? formatTime(refund[0].createTime) : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="订单金额（元）">
                  {orderParticular.totalAmount ? (orderParticular.totalAmount * 1).toFixed(2) : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="佣金（元）">
                  {orderParticular.actualAmount
                    ? (orderParticular.actualAmount * 1).toFixed(2)
                    : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="支付方式">
                  {payTypeEnum[orderParticular.payType] ?? '-'}
                </Descriptions.Item>
                <Descriptions.Item label="退款原因">
                  {refund[0].remark ? refund[0].remark : '-'}
                </Descriptions.Item>

                <Descriptions.Item label="登录账号">
                  {orderParticular.username ? orderParticular.username : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="交易上链">
                  <Tag type="chainStatus" value={unsubscribeMessageData[0].isChainOrderRefund} />
                </Descriptions.Item>
              </Descriptions>
            ) : (
              '-'
            )}
            <Divider />
            {/* <Descriptions
              title="领票人信息"
              className="
basicFont"
            >
              <Descriptions.Item label="联系人">
                {orderParticular.pilotName ? orderParticular.pilotName : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系人电话">
                {orderParticular.pilotPhone ? orderParticular.pilotPhone : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系人身份证">
                {orderParticular.pilotIdentity ? orderParticular.pilotIdentity : '-'}
              </Descriptions.Item>
            </Descriptions>
            <Divider /> */}

            {inItTicketInfoData.length > 0 ? (
              <>
                <h3 className="titleFont">票务信息</h3>
                <ProTable
                  {...tableConfig}
                  className="order_InfoData"
                  bordered={true}
                  columns={stubHub}
                  dataSource={inItTicketInfoData}
                  pagination={false}
                  options={false}
                  rowKey="key"
                  search={false}
                  summary={() => {
                    console.log('初次渲染');
                    let count = 0;
                    (inItTicketInfoData || []).map((item: any) => {
                      count = count + item.realPrice;
                    });
                    setCount(count);

                    return <></>;
                  }}
                />
              </>
            ) : (
              ''
            )}

            {/*
            <h3 className="titleFont">退订信息</h3>
            <ProTable
              bordered={true}
              className="order_InfoData"
              columns={unsubscribe}
              dataSource={unsubscribeMessageData}
              options={false}
              rowKey="refundId"
              pagination={{
                // showQuickJumper: true,
                pageSize: 5,
                hideOnSinglePage: true,
              }}
              search={{
                collapsed: false,
                collapseRender: false,
                optionRender: false,
              }}
            /> */}
            {/* <Divider />
            <Row>
              <Col>
                <h3>审核日志</h3>
              </Col>
            </Row>
            <Row>
              <Col>时间：{dayjs(new Date()).format('YYYY-MM-DD HH:MM:SS')}</Col>
              &nbsp;&nbsp;
              <Col>
                <span> 审核人：吴 xx</span>&nbsp;&nbsp;<span>结论：审核通过 </span>{' '}
                <span>退款金额：{reSum2.toFixed(2)} 元</span>
              </Col>
            </Row> */}
          </>
        )}
      </Modal>
    </>
  );
}
export default Management;
