/*
 * @Author: xuanshuncong
 * @Date: 2022-10-08 15:49:54
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-10-27 20:22:45
 * @FilePath: financialStatement.ts
 */
import { getData } from '@/common/utils/tool';
import dayjs from 'dayjs';
import { request } from '@umijs/max';
import { getEnv } from '@/common/utils/getEnv';

const detail_part: string = 'scenic_ads_dw';
const { DATA_HOST } = getEnv();

// 财务总览
export async function apiIncomeOverviewList(params: any, options: Record<string, any> = {}) {
  //2024年7月31日 换新接口不再使用 _all 的逻辑
  return getData(
    await request<any>(`${DATA_HOST}/finance_in_out_total_overview/${detail_part}/`, {
      method: 'POST',
      params,
      ...options,
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

/**
 * 财务明细
 *
 * @deprecated 2024年7月31日 三个接口合为一个新接口
 */
export async function apiIncomeDetailList(params: any, options: Record<string, any> = {}) {
  // 查询开始时间在倒数 30 天内
  const isCurrentMonth =
    dayjs().startOf('days').subtract(30, 'days').unix() < dayjs(params.start_day).unix();
  return getData(
    await request<any>(
      `${DATA_HOST}/channel_agent_finance_detail${isCurrentMonth ? '' : '_all'}/${detail_part}/`,
      {
        method: 'POST',
        params,
        ...options,
        skipErrorHandler: true, //不走错误处理
      },
    ),
  );
}

/**
 * 财务明细总条数
 *
 * @deprecated 2024年7月31日 三个接口合为一个新接口
 */
export async function apiIncomeDetailTotal(params: any, options: Record<string, any> = {}) {
  // 查询开始时间在倒数 30 天内
  const isCurrentMonth =
    dayjs().startOf('days').subtract(30, 'days').unix() < dayjs(params.start_day).unix();
  return getData(
    await request<any>(
      `${DATA_HOST}/channel_agent_counts${isCurrentMonth ? '' : '_all'}/${detail_part}/`,
      {
        method: 'POST',
        params,
        ...options,
        skipErrorHandler: true, //不走错误处理
      },
    ),
  );
}

/**
 * 财务明细总金额
 *
 * @deprecated 2024年7月31日 三个接口合为一个新接口
 */
export async function apiIncomeDetailAmount(params: any, options: Record<string, any> = {}) {
  // 查询开始时间在倒数 30 天内
  const isCurrentMonth =
    dayjs().startOf('days').subtract(30, 'days').unix() < dayjs(params.start_day).unix();
  return getData(
    await request<any>(
      `${DATA_HOST}/channel_agent_amounts${isCurrentMonth ? '' : '_all'}/${detail_part}/`,
      {
        method: 'POST',
        params,
        ...options,
        skipErrorHandler: true, //不走错误处理
      },
    ),
  );
}

// 财务明细改为一个接品
export async function apiIncomeDetail(params: any, options: Record<string, any> = {}) {
  return getData(
    await request<any>(`${DATA_HOST}/finance_in_out_detail/${detail_part}/`, {
      method: 'POST',
      params,
      ...options,
      skipErrorHandler: true, //不走错误处理
    }),
  );
}
