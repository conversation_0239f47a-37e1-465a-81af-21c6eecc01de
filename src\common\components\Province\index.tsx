/*
 * 省市区组件
 * @param width 宽度
 * @param disabled 是否禁用
 * */
import { getProvince } from '@/services/api/erp';
import { Cascader } from 'antd';
import React from 'react';

const fetchProvince = async (params) => {
  try {
    const msg = await getProvince(params);
    return msg;
  } catch (error) {
    console.log(error);
  }
  return undefined;
};

const Province = ({ width, value, onChange, disabled }) => {
  const [options, setOptions] = React.useState([]);
  console.log('是否接收到了一个参数', value);
  // const [defaultValue, setDefaultValue] = React.useState([]); //默认值

  React.useEffect(() => {
    // 在此可以执行任何带副作用操作
    fetchProvince().then((res) => {
      if (res) {
        res.map((e) => {
          //第一级非叶子
          e.isLeaf = false;
        });
      }
      setOptions(res);
    });
    return () => {
      // 在组件卸载前执行
      // 在此做一些收尾工作，比如清除定时器/取消订阅等
    };
  }, []); // 如果指定的是 [], 回调函数只会在第一次 render() 后执行

  //给父级传数据
  const handleProviceData = (value, selectedOptions) => {
    console.log('省市区给父级传：');
    console.log(selectedOptions);

    onChange(value, selectedOptions);
  };
  const loadData = (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    console.log(selectedOptions);
    // load options lazily
    fetchProvince({ id: targetOption.addressId }).then((res) => {
      targetOption.loading = false;
      res.forEach((e) => {
        if (selectedOptions.length === 2) {
          //区没有下一级，是叶子
          e.isLeaf = true;
        } else {
          e.isLeaf = false;
        }
      });
      targetOption.children = res;
      setOptions([...options]);
    });
  };
  return (
    <Cascader
      style={{ width: width }}
      // defaultValue={value} //默认值由外部传进来
      value={value} //TODO:什么时候用defaultValue?什么时候用value设置默认值?
      options={options}
      disabled={disabled}
      onChange={handleProviceData}
      fieldNames={{ label: 'addressName', value: 'addressId', children: 'children' }}
      loadData={loadData}
      // changeOnSelect
      placeholder="请选择省市区"
    />
  );
};

export default Province;
