import theme_book_chun from '@/assets/theme-book-chun.jpg';
import theme_book_dong from '@/assets/theme-book-dong.jpg';
import theme_book_qiu from '@/assets/theme-book-qiu.jpg';
import theme_book_xia from '@/assets/theme-book-xia.jpg';
import theme_my_chun from '@/assets/theme-my-chun.jpg';
import theme_my_dong from '@/assets/theme-my-dong.jpg';
import theme_my_qiu from '@/assets/theme-my-qiu.jpg';
import theme_my_xia from '@/assets/theme-my-xia.jpg';
import theme_order_chun from '@/assets/theme-order-chun.jpg';
import theme_order_dong from '@/assets/theme-order-dong.jpg';
import theme_order_qiu from '@/assets/theme-order-qiu.jpg';
import theme_order_xia from '@/assets/theme-order-xia.jpg';
import { Button, Card } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';

// 色块
const ColorBlock = ({ color, active = false }) => {
  return (
    <div
      style={{
        display: 'flex',
        marginRight: '10px',
        padding: '2px',
        border: `1px solid  ${active ? '#1890ff' : 'transparent'}`,
        cursor: 'pointer',
      }}
    >
      <div
        style={{
          width: '30px',
          height: '30px',
          backgroundColor: color,
        }}
      />
    </div>
  );
};

export default ({ shopConfig, updateShopConfig }: any) => {
  const [value, setValue] = useState(1);
  const [activeColor, setActiveColor] = useState('#349FFF');
  const [customColor, setCustomColor] = useState('#000000');
  // 颜色选择器开关
  const [colorPickerVisible, setColorPickerVisible] = useState(false);

  const onChange = (e) => {
    setValue(e.target.value);
  };

  useEffect(() => {
    setActiveColor(shopConfig?.shopStyle?.color);
  }, [shopConfig]);

  const colorList = [
    {
      color: '#349FFF',
      gradation: ['#BBD9FF', '#EFF6FF'],
    },
    {
      color: '#F87054',
      gradation: ['#FFB59E', '#FFF5F2'],
    },
    {
      color: '#2DC996',
      gradation: ['#A7E6D2', '#F4FAF6'],
    },
    {
      color: '#FE9A35',
      gradation: ['#FFD39E', '#FFF6F2'],
    },
  ];

  const handleChangeColor = (color) => {
    setCustomColor(color.hex);
    setActiveColor(color.hex);
  };
  // let shopStyleDetail = {};

  // useEffect(() => {
  //   getStoreStyle({ storeId }).then((res) => {
  //     shopStyleDetail = res?.data;
  //     setActiveColor(shopStyleDetail?.style?.color);
  //   });
  // }, []);

  return (
    <div>
      <div
        style={{
          display: 'flex',
        }}
      >
        <div style={{ flex: 'none' }}>
          {activeColor === '#2DC996' && (
            <div>
              <img className={styles.template_img} src={theme_order_chun} alt="" />
              <img className={styles.template_img} src={theme_book_chun} alt="" />
              <img className={styles.template_img} src={theme_my_chun} alt="" />
            </div>
          )}
          {activeColor === '#F87054' && (
            <div>
              <img className={styles.template_img} src={theme_order_xia} alt="" />
              <img className={styles.template_img} src={theme_book_xia} alt="" />
              <img className={styles.template_img} src={theme_my_xia} alt="" />
            </div>
          )}
          {activeColor === '#FE9A35' && (
            <div>
              <img className={styles.template_img} src={theme_order_qiu} alt="" />
              <img className={styles.template_img} src={theme_book_qiu} alt="" />
              <img className={styles.template_img} src={theme_my_qiu} alt="" />
            </div>
          )}
          {activeColor === '#349FFF' && (
            <div>
              <img className={styles.template_img} src={theme_order_dong} alt="" />
              <img className={styles.template_img} src={theme_book_dong} alt="" />
              <img className={styles.template_img} src={theme_my_dong} alt="" />
            </div>
          )}
        </div>
        <div
          style={{
            width: '100px',
          }}
        />
        <Card title="主题配色" style={{ width: '100%' }}>
          <div
            style={{
              display: 'flex',
            }}
          >
            {colorList.map((item, index) => (
              <div
                key={index}
                onClick={() => {
                  setActiveColor(item.color);
                }}
              >
                <ColorBlock color={item.color} active={activeColor === item.color} />
              </div>
            ))}
          </div>
        </Card>
      </div>
      <div
        style={{
          marginTop: '20px',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Button
          type="primary"
          onClick={async () => {
            const shopStyle = colorList.find((item) => item.color === activeColor);
            updateShopConfig({
              shopStyle,
            });
          }}
        >
          保存
        </Button>
      </div>
    </div>
  );
};
