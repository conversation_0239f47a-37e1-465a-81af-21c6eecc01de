/*
 * @Author: z<PERSON><PERSON>fei
 * @Date: 2022-06-20 11:49:29
 * @LastEditTime: 2023-10-09 10:06:11
 * @LastEditors: zhangfengfei
 */

import Tag from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import { ApplyStatusEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import useModal from '@/hooks/useModal';
import { getAuditList } from '@/services/api/auditManage';
import type { API } from '@/services/api/typings';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { isString, pickBy } from 'lodash';
import type { FC } from 'react';
import { useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import AuditModal from './components/AuditModal';

type AuditManageProps = Record<string, never>;

/**
 * @description: 审核管理
 */
const AuditManage: FC<AuditManageProps> = () => {
  const access = useAccess();
  const { initialState } = useModel('@@initialState');
  const { coId = '' } = initialState?.currentCompany || {};
  const actionRef = useRef<ActionType>();
  const auditModal = useModal();

  const [tableItem, setTableItem] = useState<API.AuditListItem>();

  const tableListReq = async (params: API.AuditListParams) => {
    const { data } = await getAuditList(params);
    return data;
  };

  const columns: ProColumns<API.AuditListItem>[] = [
    {
      title: '申请 id',
      dataIndex: 'applyId',
      hideInTable: true,
      search: false,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
      copyable: true,
      search: false,
    },

    {
      title: '企业名称',
      dataIndex: 'coName',
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
    },

    {
      title: '手机号',
      dataIndex: 'phone',
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      valueEnum: pickBy(ApplyStatusEnum, isString),
      valueType: 'select',
      renderText: (dom) => <Tag type="auditStatus" value={dom} />,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      renderText: (_, record) => (
        <Access accessible={access.canCheckManagement_detail}>
          <a
            onClick={() => {
              setTableItem(record);
              auditModal.setTypeWithVisible('info');
              addOperationLogRequest({
                action: 'info',
                content: `查看【${record.coName}】合作申请详情`,
              });
            }}
          >
            查看详情
          </a>
        </Access>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.AuditListItem, API.AuditListParams>
        {...tableConfig}
        actionRef={actionRef}
        pagination={{
          defaultPageSize: 10,
        }}
        params={{ distributorId: coId }}
        request={tableListReq}
        columns={columns}
      />

      {/*查看详情 */}
      <AuditModal actionRef={actionRef} currentItem={tableItem} {...auditModal} />
    </>
  );
};

export default AuditManage;
