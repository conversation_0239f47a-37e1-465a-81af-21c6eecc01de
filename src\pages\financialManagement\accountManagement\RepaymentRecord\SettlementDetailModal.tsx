/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-24 17:54:25
 * @LastEditTime: 2022-06-10 14:18:42
 * @LastEditors: zhangfengfei
 */

import { OriginTypeEnum, SettlementStatusEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { downloadBlobFile } from '@/common/utils/tool';
import type { ModalState } from '@/hooks/useModal';
import { exportSettlementDetail } from '@/services/api/billManage';
import { getReturnRecordSettlementBillItems } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import ProDescriptions from '@ant-design/pro-descriptions';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useModel, useRequest } from '@umijs/max';

interface SettlementDetailModalProps {
  currentItem?: API.ConsumerSettlementItem;
  modalState: ModalState;
}

/**结算明细 */
const SettlementDetailModal: FC<SettlementDetailModalProps> = ({
  currentItem,
  modalState: { visible, setVisible },
}) => {
  const { initialState } = useModel('@@initialState');
  const { settlementId = '' } = initialState?.currentCompany || {};

  const exportReq = useRequest(exportSettlementDetail, {
    manual: true,
    formatResult: (res) => {
      downloadBlobFile(res, `结算单${currentItem?.id || ''}明细.xls`, 'msexcel');
    },
  });
  const onExport = () => {
    if (currentItem)
      exportReq.run({
        merchantId: settlementId,
        creditSettlementBillId: currentItem.id,
      });
  };

  const onCancel = () => setVisible(false);

  const tableListReq = async (params: API.ProviderBillDetailParams) => {
    const { data } = await getReturnRecordSettlementBillItems(params);
    addOperationLogRequest({
      action: 'info',
      content: `查看【${currentItem?.id}】结算明细`,
    });

    return {
      data: data.page,
      total: data.totalNumberOfResults,
    };
  };
  const list = [
    {
      label: '单号',
      content: currentItem?.id,
    },
    {
      label: '创建时间',
      content:
        currentItem?.creationDate && dayjs(currentItem.creationDate).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      label: '结算状态',
      content: currentItem && SettlementStatusEnum[currentItem.settlementStatus],
    },
    {
      label: '授信客户',
      content: currentItem?.provideLegalName,
    },
    {
      label: '应结算金额（元）',
      content: currentItem?.amount,
    },
    {
      label: '收款确认时间',
      content:
        currentItem?.payedDate && dayjs(currentItem?.payedDate).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  const columns: ProColumns<API.ProviderBillDetailItem>[] = [
    {
      title: '分录号',
      dataIndex: 'id',
    },
    {
      title: '交易号',
      dataIndex: 'tradeNo',
    },
    {
      title: '商品名称',
      dataIndex: 'description',
    },
    {
      title: '商户订单号',
      dataIndex: 'outTradeNo',
    },
    {
      title: '下单时间',
      dataIndex: 'createDate',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '供应商名称',
      dataIndex: 'sellerName',
    },
    {
      title: '分销商名称',
      dataIndex: 'buyerName',
    },
    {
      title: '付款完成日期',
      dataIndex: 'paymentDate',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '订单状态',
      dataIndex: 'status',
    },
    {
      title: '源单类型',
      dataIndex: 'originType',
      valueType: 'select',
      valueEnum: OriginTypeEnum,
    },
    {
      title: '结算金额（元）',
      dataIndex: 'amount',
    },
  ];

  return (
    <Modal
      width={1360}
      title={'结算明细详情'}
      visible={visible}
      maskClosable
      destroyOnClose
      onCancel={onCancel}
      okText={'导出'}
      confirmLoading={exportReq.loading}
      onOk={onExport}
      // footer={<Button onClick={onCancel}>返回</Button>}
    >
      <ProDescriptions style={{ padding: '0 24px' }} column={3}>
        {list.map((item) => (
          <ProDescriptions.Item key={item.label} label={item.label}>
            {item.content ?? '-'}
          </ProDescriptions.Item>
        ))}
      </ProDescriptions>
      <ProTable<API.ProviderBillDetailItem, API.ProviderBillDetailParams>
        style={{ marginTop: 32 }}
        rowKey="id"
        options={false}
        search={false}
        pagination={{
          defaultPageSize: 10,
        }}
        params={{
          merchantId: settlementId,
          creditSettlementBillId: currentItem?.id || '',
        }}
        request={tableListReq}
        columns={columns}
      />
    </Modal>
  );
};
export default SettlementDetailModal;
