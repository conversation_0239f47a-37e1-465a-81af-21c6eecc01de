/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-05 13:57:50
 * @LastEditTime: 2022-09-19 11:36:44
 * @LastEditors: zhang<PERSON>fei
 */

import { getAllMicroService, getCoScenicService } from '@/services/api/erp';
import { getCoListByScenicId } from '@/services/api/ticket';
import { useRequest } from '@umijs/max';

type MicroServiceParams = {
  scenicId?: string;
};

/**
 * @description: 微服务开通数据处理
 */
const useMicroService = ({ scenicId }: MicroServiceParams) => {
  // 查询系统所有微服务
  const allMicroServiceReq = useRequest(() => getAllMicroService({ type: [2] }), {
    manual: true,
    initialData: [],
  });

  // 查询景区所有企业的微服务
  const coMicroServiceReq = useRequest(() => getCoScenicService({ scenicId }), {
    ready: !!scenicId,
    manual: true,
    initialData: [],
  });

  // 查询企业列表
  const coInfoListReq = useRequest(() => getCoListByScenicId({ scenicId }), {
    ready: !!scenicId,
    manual: true,
    initialData: [],
  });

  // 初始数据准备
  const fetchData = () => {
    allMicroServiceReq.run();
    coMicroServiceReq.run();
    coInfoListReq.run();
  };

  // 基础服务
  const basicServiceList = allMicroServiceReq.data?.filter((item) => item?.type === 1) || [];
  // 增值服务
  const addedServiceList = allMicroServiceReq.data?.filter((item) => item?.type === 2) || [];

  const loading = coInfoListReq.loading || allMicroServiceReq.loading || coMicroServiceReq.loading;

  return {
    coInfoList: coInfoListReq.data || [],
    coMicroServiceList: coMicroServiceReq.data || [],
    basicServiceList,
    addedServiceList,
    loading,
    fetchData,
  };
};
export default useMicroService;
