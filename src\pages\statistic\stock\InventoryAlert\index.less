.flexB {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .line {
    width: 4px;
    height: 20px;
    background-color: #1890ff;
  }

  .title {
    font-weight: 700;
    font-size: 18px;
    margin-left: 10px;
  }
}

.receiversBox {
  display: flex;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
  gap: 6px 12px;

  .mytag {
    // height: 32px;
    padding: 3px 12px;
    font-size: 14px;
    display: flex;
    align-items: center;
    background: #F7F7F7;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.15);

    .text {
      margin-right: 12px;
    }

    .outLind {
      color: #C2C2C2;

      &:hover {
        color: #696969;
      }
    }
  }
}

.botBtn {
  box-shadow: 0px -2px 9px -1px rgba(208, 208, 208, 0.5);
  justify-content: center;
}