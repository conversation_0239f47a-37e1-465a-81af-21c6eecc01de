import { getTicket } from '@/services/api/chart';
import { Card, Spin } from 'antd';
import qs from 'qs';
import React, { useEffect, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';
import Chart from '../Chart';
import type { ChartDatePickerProps } from './ChartDatePicker';
import ChartDatePicker from './ChartDatePicker';
import { getBarOptions, getLineOptions } from './filterData';
import styles from './index.less';
import { getEnv } from '@/common/utils/getEnv';

const isProd = getEnv().ENV == 'prod' || 'canary';

const urlMap = {
  1: {
    date: '/order_refund_stat_d/scenic_ads_dw/',
    month: '/order_refund_stat_m/scenic_ads_dw/',
  },
  2: {
    date: '/commission_stat_d/scenic_ads_dw/',
    month: '/commission_stat_m/scenic_ads_dw/',
  },
  3: {
    date: '/cur_company_trade_stat_d/scenic_ads_dw/',
    month: '/cur_company_trade_stat_m/scenic_ads_dw/',
  },
  4: {
    date: '/low_level_proxy_trade_stat_d/scenic_ads_dw/',
    month: '/low_level_proxy_trade_stat_m/scenic_ads_dw/',
  },
};

export interface IBarChartProps {
  id: string;
  datePickerConfig: Omit<ChartDatePickerProps, 'onValuesChange'>;
  type: 'bar' | 'line';
  title: string;
  filterDataFunc: any;
  unit: string;
}
const BarChart: React.FC<IBarChartProps> = (props) => {
  const { datePickerConfig, id } = props;
  const { initialState } = useModel('@@initialState') || {};
  const { coId } = initialState?.currentCompany || {};

  const [option, setOption] = useState({});
  const getChartDataReq = useRequest(
    ({ sort, value }: ParamsType) => {
      // 处理后端地址 参数
      if (value) {
        const current = urlMap[id];
        const requestUrl = current[sort];
        const commonParams = {
          [id === '4' ? 'seller_id' : 'agent_id']: coId,
          update_frequency: 0,
        };
        let specialParams: Record<string, any>;
        // rangePicker
        if (Array.isArray(value)) {
          // 按日
          if (sort === 'date') {
            specialParams = {
              begin_year_month_day: value[0].format('YYYY-MM-DD'),
              end_year_month_day: value[1].format('YYYY-MM-DD'),
            };
          } else {
            // 按月
            specialParams = {
              begin_year_month: value[0].format('YYYY-MM'),
              end_year_month: value[1].format('YYYY-MM'),
            };
          }
        } else {
          specialParams = {
            params: value.format('YYYY-MM-DD'),
          };
        }
        const params = { ...commonParams, ...specialParams, update_frequency: '0.0' };
        return getTicket(
          requestUrl +
            qs.stringify(params, {
              addQueryPrefix: true,
            }),
        );
      }
      return;
    },
    {
      manual: true,
      formatResult({ code, data }) {
        if (code < 20000 || code >= 30000) return;

        const res = props.filterDataFunc(data.result || []);
        // console.log(res);

        if (props.type === 'bar') {
          setOption(getBarOptions(res, props.unit));
        } else if (props.type === 'line') {
          setOption(getLineOptions(res || [], props.unit));
        }
      },
    },
  );

  useEffect(() => {
    if (datePickerConfig.defaultValue) {
      getChartDataReq.run({
        sort: datePickerConfig.sort || 'date',
        value: datePickerConfig.defaultValue || null,
      });
    }
  }, []);

  return (
    <Card className={styles.chart}>
      <Spin spinning={getChartDataReq.loading}>
        {/* 标题 */}
        <div style={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center' }}>
          <span style={{ fontSize: 20, fontWeight: 'bold', minWidth: 160 }}>{props.title}</span>
          <div className={styles.control}>
            <ChartDatePicker
              {...datePickerConfig}
              onValuesChange={(sort, value) => {
                getChartDataReq.run({
                  sort,
                  value,
                });
              }}
            />
          </div>
        </div>
        {/* 图表 */}
        <Chart options={option} />
      </Spin>
    </Card>
  );
};

export default BarChart;
