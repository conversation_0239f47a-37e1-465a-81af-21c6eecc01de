<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 60 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>订单备份 5</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板备份" transform="translate(-467.000000, -224.000000)">
            <g id="订单备份-5" transform="translate(467.000000, 224.000000)">
                <rect id="矩形备份-8" opacity="0.548270089" x="0" y="0" width="60" height="60"></rect>
                <rect id="矩形" x="28" y="24" width="19" height="27" rx="4"></rect>
                <rect id="矩形" stroke="#14131F" stroke-width="4" x="16" y="17.075" width="29" height="33.925" rx="5"></rect>
                <line x1="22.25" y1="31.9875" x2="30.5" y2="31.9875" id="直线-8" stroke="#14131F" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></line>
                <line x1="22.25" y1="40.1875" x2="37.71875" y2="40.1875" id="直线-8备份" stroke="#14131F" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></line>
                <polyline id="直线-7" stroke="#14131F" stroke-width="4" stroke-linecap="round" points="22.765625 12 22.765625 17.2590586 22.765625 20.2"></polyline>
                <polyline id="直线-7备份" stroke="#14131F" stroke-width="4" stroke-linecap="round" points="39.265625 12 39.265625 17.2590586 39.265625 20.2"></polyline>
            </g>
        </g>
    </g>
</svg>