import ChainModal from '@/common/components/ChainModal';
import Export, { columnsSet } from '@/common/components/Export';
import useExport from '@/common/components/Export/useExport';
import ProModal from '@/common/components/ProModal';
import useProModal from '@/common/components/ProModal/useProModal';
import CheckRuleDetails from '@/common/components/RuleDetails/CheckRuleDetails';
import IssueRuleDetails from '@/common/components/RuleDetails/IssueRuleDetails';
import RetreatRuleDetails from '@/common/components/RuleDetails/RetreatRuleDetails';
import Subpage from '@/common/components/Subpage';
import useSubpage from '@/common/components/Subpage/useSubpage';
import Tags from '@/common/components/Tag';
import TimeStore from '@/common/components/TimeStore';
import { tableConfig } from '@/common/utils/config';
import {
  chainStatusEnum,
  orderTypeEnum,
  payTypeEnum,
  productTypeEnum,
  ticketStatusEnum,
  ticketTypeEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { formatTime, getUniqueId } from '@/common/utils/tool';
import { numberValidator } from '@/common/utils/validate';
import Blockchain from '@/components/Blockchain';
import TicketSaleDetail from '@/components/TicketSaleDetail';
import useModal from '@/hooks/useModal';
import {
  apiBcPay,
  apiCancellationOrder,
  apiDistributeBuyOrder,
  apiListDistributeBuyOrder,
  getUnifyPullDown,
} from '@/services/api/distribution';
import { getOrderGoodsDetails } from '@/services/api/ticket';
import {
  DownOutlined,
  SafetyCertificateOutlined,
  SolutionOutlined,
  UpOutlined,
} from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useLocation } from '@umijs/max';
import { Button, Card, Divider, List, Modal, Popconfirm, Steps, Tag, message } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import Table from 'antd/lib/table';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';
import Purchase from './components/purchase';
import PurchaseAdd from './components/purchaseAdd';

const payOrderId = '';

const TableList: React.FC = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const initialName = searchParams.get('name');
  // 进货二级
  const subpageState = useSubpage();
  // 退货二级
  const subpageState2 = useSubpage();
  // 分时库存
  const timeModalState = useProModal();
  const [timeModalData, setTimeModalData] = useState<any>({});
  const [lawsList, setLawsList] = useState<any>([]);
  const lawsColumns = [
    {
      title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join('-')}`,
      render: () => {},
    },
    {
      title: () => '采购数量',
      render: ({ purchaseNum = 0 }) => purchaseNum,
    },
    {
      title: () => '采购金额（元）',
      render: ({ purchaseAmount = 0 }) => purchaseAmount,
    },
  ];
  const timeModalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '入园有效时间',
          valueType: 'dateRange',
          dataIndex: 'dateRange',
          initialValue: [timeModalData.enterStartTime, timeModalData.enterEndTime],
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '',
          dataIndex: 'timeLaws',
          colProps: { span: 24 },
          renderFormItem: () => <TimeStore lawsColumns={lawsColumns} lawsList={lawsList} />,
        },
        {
          title: '总采购数量',
          dataIndex: 'num',
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '总采购金额（元）',
          dataIndex: 'price',
          fieldProps: {
            disabled: true,
          },
        },
      ],
    },
  ];

  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany;

  const [orderGroupId, setOrderGroupId] = useState('');
  const [currentRow, setCurrentRow] = useState<Record<string, any>>({});
  const [stockCertificate, setStockCertificate] = useState('');
  const [showbtn, setShowbtn] = useState(false);
  const [counts, setCounts] = useState(0);
  const modalState = useProModal();
  const getAllSuppliersReq = useRequest(getUnifyPullDown, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return res.data.map((name: any) => {
        return {
          value: name,
          label: name,
        };
      });
    },
  });
  const getProductListReq = useRequest(getUnifyPullDown, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return res.data.map((name: any) => {
        return {
          value: name,
          label: name,
        };
      });
    },
  });
  const getUsernameListReq = useRequest(getUnifyPullDown, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return res.data.map((name: any) => {
        return {
          value: name,
          label: name,
        };
      });
    },
  });
  useEffect(() => {
    if (coId) {
      getAllSuppliersReq.run({ getUnifyPullDown: coId, type: '2' });
      getProductListReq.run({ getUnifyPullDown: coId, type: '3' });
      getUsernameListReq.run({ getUnifyPullDown: coId, type: '4' });
    }
  }, [coId]);

  useEffect(() => {
    if (initialName && exportState.formRef.current) {
      // 设置供应商名称搜索值
      exportState.formRef.current.setFieldsValue({
        sellerName: initialName,
      });

      actionRef.current?.reload();
    }
  }, [coId, initialName]); // 确保 coId 变化时也会执行

  const closeOrder = async (orderId: string = '') => {
    const id = orderId.length == 0 ? orderGroupId : orderId;
    try {
      const res = await apiCancellationOrder({ orderId: id });
      message.success('取消订单成功');
      setDetailsVisible(false);
      actionRef?.current?.reload();
    } catch (e) {
      console.error(e);
      message.success('取消订单失败！');
    }
  };

  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const purchaseModalState = useModal();

  const columns: ProColumns[] = [
    {
      title: '采购订单号',
      dataIndex: 'orderId',
      fixed: 'left',
    },
    {
      title: '供应商名称',
      dataIndex: 'sellerName',
      valueType: 'select',
      fieldProps: {
        options: getAllSuppliersReq.data,
        showSearch: true,
      },
    },
    {
      title: '结算单号',
      dataIndex: 'tradeNo',
    },

    {
      title: '采购时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      hideInTable: true,
      initialValue: [dayjs().startOf('year'), dayjs().endOf('day')],
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },

    {
      title: '支付时间',
      dataIndex: 'payTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            payStartTime: value[0],
            payEndTime: value[1],
          };
        },
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: getProductListReq.data,
        showSearch: true,
      },
    },
    {
      title: '采购时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      render: (dom: any) => dayjs(dom).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '支付时间',
      dataIndex: 'payTime',
      hideInSearch: true,
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      valueEnum: payTypeEnum,
    },
    {
      title: '操作人',
      dataIndex: 'username',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getUsernameListReq.data,
        showSearch: true,
      },
    },
    {
      title: '操作人',
      dataIndex: 'username',
      hideInSearch: true,
    },
    {
      title: '采购总数',
      dataIndex: 'purchaseQuantity',
      hideInSearch: true,
    },
    {
      title: '采购总金额（元）',
      dataIndex: 'totalAmount',
      hideInSearch: true,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      hideInSearch: true,
      fixed: 'right',
      render: (dom) => <Tags type="orderStatus" value={dom} />,
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      width: 150,
      hideInTable: true,
      valueEnum: orderTypeEnum,
    },
    // {
    //   title: '交易上链',
    //   dataIndex: 'isChain',

    //   valueEnum: chainStatusEnum,
    //   hideInTable: true,
    // },
    {
      title: '交易上链',
      dataIndex: 'isChain',
      fixed: 'right',
      valueEnum: chainStatusEnum,
      renderText: (dom) => <Tags type="chainStatus" value={dom} />,
      search: false,
    },
    {
      // width: 360,
      title: '操作',
      dataIndex: '_option',
      valueType: 'option',
      fixed: 'right',
      render: (_, entity: any) => [
        <a
          key="optionDetails"
          onClick={async () => {
            setOrderGroupId(entity.orderGroupId);
            setCurrentRow(entity);
            if (entity.orderStatus == 10 || entity.orderStatus == 20) {
              setShowbtn(true);
            } else {
              setShowbtn(false);
            }

            modalState.setType('info');
          }}
        >
          查看
        </a>,
        entity.orderStatus == 10 || entity.orderStatus == 20 || entity.orderStatus == 21 ? (
          <Divider key="optionVertical" type="vertical" />
        ) : null,
        entity.txId ? (
          <ChainModal
            chainData={{
              txId: entity.txId,
            }}
          />
        ) : null,
      ],
    },
  ];

  //查看区块链交易数据
  const [txIds, setTxIds] = useState<any>([]);
  const [tokenIds, setTokenIds] = useState<any>([]);
  const [cochainVisible, setCochainVisible] = useState(false);
  const [cochainDataDataSource, setCochainDataDataSource] = useState<any>({});
  const cochainColumns = [
    {
      title: '区块链账号',
      columns: [
        {
          title: '发行方',
          dataIndex: 'fromAccount',
          span: 5,
          render: (dom: any) => dom,
        },
        {
          title: '接收方',
          dataIndex: 'toAccount',
          span: 5,
          render: (dom: any) => dom,
        },
      ],
    },
    {
      title: '存证信息',
      columns: [
        {
          title: '存证时间',
          dataIndex: 'transactionAt',
          span: 5,
        },
        {
          title: 'TokenID',
          dataIndex: 'tokenIds',
          span: 5,
          render: (dom: any) => {
            return dom && dom.data.length > 0
              ? dom?.data.map((item: any, index: number) => (
                  <>
                    <span>{item}</span>
                    <br />
                    {index + 1 == dom.data.length && dom.data.length > 1 ? (
                      <span
                        style={{ color: '#1890ff', cursor: 'pointer' }}
                        onClick={() => {
                          const obj = { ...cochainDataDataSource };
                          if (dom.unfold) {
                            obj.tokenIds = {
                              unfold: false,
                              data: cochainDataDataSource.tokenIds.data.slice(0, 5),
                            };
                            setCochainDataDataSource(obj);
                          } else {
                            obj.tokenIds = { unfold: true, data: tokenIds };
                            setCochainDataDataSource(obj);
                          }
                        }}
                      >
                        {dom.unfold ? '收起 ' : '展开 '}
                        {dom.unfold ? <UpOutlined /> : <DownOutlined />}
                      </span>
                    ) : (
                      ''
                    )}
                  </>
                ))
              : '-';
          },
        },
        {
          title: '交易哈希',
          dataIndex: 'txId',
          span: 5,
          render: (dom: any) => {
            return dom && dom.data.length > 0
              ? dom?.data.map((item: any, index: number) => (
                  <>
                    <span>{item}</span>
                    <br />
                    {index + 1 == dom.data.length && dom.data.length > 1 ? (
                      <span
                        style={{ color: '#1890ff', cursor: 'pointer' }}
                        onClick={() => {
                          const obj = { ...cochainDataDataSource };
                          if (dom.unfold) {
                            obj.txId = {
                              unfold: false,
                              data: cochainDataDataSource.txId.data.slice(0, 5),
                            };
                            setCochainDataDataSource(obj);
                          } else {
                            obj.txId = { unfold: true, data: txIds };
                            setCochainDataDataSource(obj);
                          }
                        }}
                      >
                        {dom.unfold ? '收起 ' : '展开 '}
                        {dom.unfold ? <UpOutlined /> : <DownOutlined />}
                      </span>
                    ) : (
                      ''
                    )}
                  </>
                ))
              : '-';
          },
        },
      ],
    },
  ];
  const { Step } = Steps;
  // 【列表】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);

  const columnsInitial: ProColumns<any>[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      hideInSearch: true,
      valueEnum: productTypeEnum,
    },
    {
      title: '入园日期',
      dataIndex: 'dayBegin',
      hideInSearch: true,
      render: (dom: any, entity: any) =>
        entity.noDateList ? (
          <a
            onClick={() => {
              setUnTimeDataSource(entity.noDateList);
              addOperationLogRequest({
                action: 'info',
                content: `查看【${currentRow?.orderId}】入园日期信息`,
              });
              setUnTimeVisible(true);
            }}
          >
            详情
          </a>
        ) : (
          '-'
        ),
    },
    {
      title: '进货总量',
      dataIndex: 'totalNum',
      hideInSearch: true,
      align: 'right',
      valueType: 'digit',
    },
    {
      title: '进货金额（元）',
      dataIndex: 'totalAmount',
      hideInSearch: true,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  // 分时数据绑定
  const [timeVisible, setTimeVisible] = useState<any>();
  // 分时序列 [表格序，列表序]
  const [timeTitle, setTimeTitle] = useState<any>();
  const [timeDataSource, setTimeDataSource] = useState<any>();
  const timeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '分时预约',
      dataIndex: 'timeShare',
    },

    {
      title: '进货数量',
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];
  // 非分时数据绑定
  const [unTimeVisible, setUnTimeVisible] = useState<boolean>(false);
  const [unTimeDataSource, setUnTimeDataSource] = useState<any>();
  const unTimeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '入园日期',
      dataIndex: 'dayBegin',
      hideInSearch: true,
      render: (dom: any, entity: any) => entity.dayBegin + ' 至 ' + entity.dayEnd,
    },
    {
      title: '进货数量',
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];
  const columnsPurchase: ProColumns[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      render: (dom: keyof typeof productTypeEnum) => <span>{productTypeEnum[dom]}</span>,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      render: (_, { goodsInfoVO }) => goodsInfoVO?.goodsName || '-',
    },
    {
      title: '票种',
      dataIndex: 'type',
      render: (_, { goodsInfoVO }) => <span>{ticketTypeEnum?.[goodsInfoVO?.type] || '-'}</span>,
    },
    {
      title: '库存批次号',
      width: 180,
      dataIndex: 'batchId',
      render: (dom, record) => (
        <span>
          {dom}
          {record.isExchange === 1 && <Tag color="blue">交易所</Tag>}
        </span>
      ),
    },
    {
      title: '购买有效时间',
      width: 200,
      dataIndex: 'buyStartTime',
      render: (dom, record) => (
        <span>
          {dayjs(record.buyStartTime).format('YYYY-MM-DD')}至
          {dayjs(record.buyEndTime).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: '入园有效时间',
      width: 200,
      dataIndex: 'dayBegin',
      render: (dom, record) => (
        <span>
          {dayjs(record.dayBegin).format('YYYY-MM-DD')}至{dayjs(record.dayEnd).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: '分时时段',
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList?.length
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '市场标准价（元）',
      width: 150,
      dataIndex: 'marketPrice',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '有效时长天数（天）',
      width: 150,
      dataIndex: 'validDay',
    },
    {
      title: '可入园天数',
      dataIndex: 'enterDay',
    },
    {
      title: '使用次数',
      width: 80,
      dataIndex: 'useTimes',
      render: (dom) => <span>每天{dom}次</span>,
    },
    {
      title: '采购数量',
      dataIndex: 'purchaseNum',
    },
    // {
    //   title: '退货数量',
    //   dataIndex: 'refundNum',
    // },
    {
      title: '单价（元）',
      dataIndex: 'unitPrice',
    },
    {
      title: '金额（元）',
      dataIndex: 'buyMoney',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, entity: any) =>
        entity.timeLaws && (
          <a
            key={getUniqueId()}
            onClick={() => {
              console.log(entity);

              setLawsList(entity.timeShareVoList);
              setTimeModalData({
                enterStartTime: entity.dayBegin,
                enterEndTime: entity.dayEnd,
                timeLaws: JSON.parse(entity.timeLaws),
                num: entity.purchaseNum,
                price: entity.buyMoney,
              });
              timeModalState.setType('edit');
            }}
          >
            分时详情
          </a>
        ),
    },
  ];
  const columnsSubPurchase: ColumnsType = [
    {
      title: '特殊折扣率',
      dataIndex: 'overallDiscount',
      render: (dom) => <span>{dom}%</span>,
    },
    {
      title: '分销折扣区间',
      width: 150,
      dataIndex: 'overallDiscount',
      render: (dom, record) => (
        <span>
          {record.beginDiscount}%-{record.endDiscount}%
        </span>
      ),
    },
    {
      title: '权益票',
      dataIndex: 'isRights',
      render: (dom) => <span>{dom == 1 ? '权益票' : '非权益票'}</span>,
    },
    {
      title: '数字资产',
      width: 80,
      dataIndex: 'isDigit',
      render: (dom) => <span>{dom == 1 ? '是' : '否'}</span>,
    },
    {
      title: '定价（元）',
      dataIndex: 'marketPrice',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '出票规则',
      dataIndex: 'issueName',
      render: (dom: any, entity: any) => (
        <a
          onClick={() => {
            IssueRuleDetails.show(entity.issueId);
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '检票规则',
      dataIndex: 'checkName',
      render: (dom: any, entity: any) => (
        <a
          onClick={() => {
            CheckRuleDetails.show(entity.checkId);
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '退票规则',
      dataIndex: 'retreatName',
      render: (dom: any, entity: any) => (
        <a
          onClick={() => {
            RetreatRuleDetails.show(entity.retreatId);
          }}
        >
          {dom}
        </a>
      ),
    },
  ];

  const orderGoodsDetailsReq = async (params: any) => {
    const { data } = await getOrderGoodsDetails(params);
    return {
      //添加分页信息
      total: data.total,
      data: data.data,
      success: true,
    };
  };

  const saleTicketColumns: ProColumns[] = [
    {
      title: '票号',
      dataIndex: 'ticketNumber',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '商品名称',
      dataIndex: 'productSkuName',
    },
    {
      title: '票种',
      dataIndex: 'ticketType',
      valueEnum: ticketTypeEnum,
    },
    {
      title: '数字资产',
      dataIndex: 'isDigit',
      search: false,
      render: (dom) => <span>{dom == 1 ? '是' : '否'}</span>,
    },
    {
      title: '库存批次号',
      dataIndex: 'batchId',
      formItemProps: {
        rules: [numberValidator()],
      },
      render: (dom: any, record, index, __, schema: any) => (
        <span>
          {dom} {record.isExchange === 1 && <Tag color="blue">交易所</Tag>}
        </span>
      ),
    },
    {
      title: '出票时间',
      dataIndex: 'cTime',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            ticketStartCreateTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            ticketEndCreateTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '出票时间',
      dataIndex: 'createTime',
      search: false,
    },
    {
      title: '预计入园时间',
      dataIndex: 'eTime',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            playStartCreateTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            playEndCreateTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '预计入园时间',
      dataIndex: 'enterTime',
      search: false,
    },
    {
      title: '分时时段',
      search: false,
      dataIndex: 'timeShareVoList',
      render: (_, item: any) =>
        item.timeShareBeginTime && item.timeShareEndTime ? (
          <Tag key={item.id}>{[item.timeShareBeginTime, item.timeShareEndTime].join('-')}</Tag>
        ) : (
          '-'
        ),
    },
    {
      title: '有效时长天数（天）',
      search: false,
      dataIndex: 'validDay',
    },
    {
      title: '可入园天数',
      search: false,
      dataIndex: 'enterDay',
    },
    {
      title: '使用次数',
      dataIndex: 'useTimes',
      search: false,
      render: (dom) => `每天${dom}次`,
    },
    {
      title: '人数',
      dataIndex: 'num',
      search: false,
    },
    {
      title: '采购单价（元）',
      dataIndex: 'purchasePrice',
      search: false,
      render: (dom: any) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额（元）',
      dataIndex: 'totalMoney',
      search: false,
      render: (dom: any) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '门票状态',
      dataIndex: 'ticketStatus',
      valueEnum: ticketStatusEnum,
    },
  ];

  const modalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '',
          colSize: 3,
          dataIndex: 'orderStatusFlow',
          render: (orderStatusFlow, record) => {
            const orderStatus: keyof typeof orderTypeEnum = record?.orderTab?.orderStatus;
            const orderTime = record?.orderStatusFlow?.orderTime;
            const payTime = record?.orderStatusFlow?.payTime;
            let current;
            if (orderTime) current = 0;
            if (payTime) current = 2;
            return (
              <div style={{ width: '100%' }}>
                <h3>当前订单状态：{orderTypeEnum[orderStatus]}</h3>
                <Steps style={{ margin: '20px auto' }} size="small" current={current}>
                  <Step
                    key={0}
                    icon={<SolutionOutlined />}
                    title={'创建订单'}
                    description={
                      <div>
                        <div>{dayjs(orderTime).format('YYYY-MM-DD')}</div>
                        <div>{dayjs(orderTime).format('HH:mm:ss')}</div>
                      </div>
                    }
                  />
                  <Step
                    key={1}
                    icon={<SafetyCertificateOutlined />}
                    title={'支付成功'}
                    description={
                      payTime && (
                        <div>
                          <div>{dayjs(payTime).format('YYYY-MM-DD')}</div>
                          <div>{dayjs(payTime).format('HH:mm:ss')}</div>
                        </div>
                      )
                    }
                  />
                  <Step
                    key={2}
                    icon={<SafetyCertificateOutlined />}
                    title={'采购完成'}
                    description={
                      payTime && (
                        <div>
                          <div>{dayjs(payTime).format('YYYY-MM-DD')}</div>
                          <div>{dayjs(payTime).format('HH:mm:ss')}</div>
                        </div>
                      )
                    }
                  />
                </Steps>
              </div>
            );
          },
        },
      ],
    },
    {
      title: '基本信息',
      columns: [
        {
          title: '采购订单号',
          dataIndex: ['orderTab', 'orderId'],
        },
        {
          title: '供应商名称',
          dataIndex: ['orderTab', 'sellerName'],
        },
        {
          title: '结算单号',
          dataIndex: ['orderTab', 'tradeNo'],
        },
        {
          title: '采购时间',
          dataIndex: ['orderTab', 'payTime'],
          renderText: (dom) => formatTime(dom),
        },
        {
          title: '支付时间',
          dataIndex: ['orderTab', 'payTime'],
          renderText: (dom) => formatTime(dom),
        },
        {
          title: '操作人',
          dataIndex: ['orderTab', 'username'],
        },
        {
          title: '支付方式',
          valueEnum: payTypeEnum,
          dataIndex: ['orderTab', 'payType'],
        },
        {
          title: '交易上链',
          valueEnum: chainStatusEnum,
          dataIndex: ['orderTab', 'isChain'],
        },
      ],
    },
    {
      title: '采购信息',
      columns: [
        {
          colSize: 3,
          dataIndex: 'buyProductList',
          title: '',
          render: (col: any, record) => (
            <Table
              {...tableConfig}
              size="middle"
              style={{ width: '100%' }}
              rowKey={'batchId'}
              // scroll={{ x: 2000 }}
              columns={columnsPurchase}
              expandable={{
                // defaultExpandAllRows: true,
                expandedRowRender: (record, index, indent, expanded) => {
                  return (
                    <Table
                      size="middle"
                      pagination={false}
                      columns={columnsSubPurchase}
                      dataSource={[record?.goodsInfoVO]}
                    />
                  );
                },
                // rowExpandable: (record) => record.proType !== 20,
              }}
              dataSource={col}
              footer={() => (
                <div style={{ textAlign: 'right' }}>
                  采购总数：{record.totalNum}
                  &nbsp;&nbsp;&nbsp;&nbsp; 采购总金额（元）：
                  {record.totalAmount}
                </div>
              )}
              pagination={false}
            />
          ),
        },
      ],
    },
    {
      title: '售票记录',
      columns: [
        {
          colSize: 3,
          dataIndex: 'buyProductList',
          title: '',
          className: 'no-padding',
          render: (col, record) => {
            return (
              <ProTable
                {...tableConfig}
                // className="order_InfoData"
                style={{ width: '100%' }}
                // bordered={true}
                // actionRef={actionRef}
                params={{
                  distributionType: 1,
                  orderId: currentRow?.orderId,
                  commerceCompanyId: coId,
                }}
                columns={saleTicketColumns}
                form={{
                  ignoreRules: false,
                }}
                request={orderGoodsDetailsReq}
                // pagination={
                //   orderType === 'JQTC'
                //     ? false
                //     : {
                //         defaultPageSize: 10,
                //       }
                // }
                rowKey={getUniqueId()}
                footer={(record) => {
                  // 计算总差价
                  let differencesPrice: number = 0;
                  record?.forEach((item) => {
                    differencesPrice += item.differencesPrice;
                  }, 0);
                  const showDiffPrice = differencesPrice.toFixed(2);
                  return <div style={{ textAlign: 'right' }}>总差价（元）：+{showDiffPrice}</div>;
                }}
                // formRef={exportState2.formRef}
                // columnsState={columnsState(exportState2)}
                // toolBarRender={() => [
                //   <ExportButton key={getUniqueId()} {...{ exportState: exportState2 }} />,
                // ]}
              />
            );
          },
        },
      ],
    },
  ];

  const defaultQueryParams = {
    buyerId: coId,
    orderType: 'EXFX',
    sellerName: initialName,
  };

  // 【退货】数据绑定
  const [payVisible, setPayVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [payPrice, setPayPrice] = useState(0);
  const exportState = useExport({
    columns,
    modulePath: 'E-commerce_PurchaseOrderExchange',
    params: defaultQueryParams,
  });

  return (
    <>
      <ProTable
        {...tableConfig}
        style={{
          ...modalState.tableStyle,
          ...subpageState.tableStyle,
          ...subpageState2.tableStyle,
        }}
        actionRef={actionRef}
        params={defaultQueryParams}
        request={async (params, sort, filter) => {
          const formValues = exportState.formRef.current?.getFieldsValue();
          const mergedParams = {
            ...params,
            sellerName: formValues.sellerName,
          };
          return apiListDistributeBuyOrder(mergedParams);
        }}
        // request={apiListDistributeBuyOrder}
        columns={columns}
        formRef={exportState.formRef}
        columnsState={columnsSet(exportState)}
        toolBarRender={() => [<Export key="export" {...exportState} />]}
      />
      <ProModal
        page
        {...modalState}
        title="采购订单"
        actionRef={actionRef}
        columns={modalColumns}
        params={{ orderId: orderGroupId }}
        infoRequest={async (params) => {
          const res: any = await apiDistributeBuyOrder(params);
          addOperationLogRequest({
            action: 'info',
            content: `查看【${currentRow?.orderId}】采购订单详情`,
          });
          setCounts(res.data.totalAmount);
          // 添加退货总数和金额
          res.data.totalAmount = res.data.buyProductList.reduce((prev: any, next: any) => {
            return prev + next.buyMoney * 1;
          }, 0);
          res.data.totalAmount = parseFloat(res.data.totalAmount).toFixed(2);
          res.data.totalNum = res.data.buyProductList.reduce((prev: any, next: any) => {
            return prev + next.purchaseNum * 1;
          }, 0);
          return res;
        }}
      />
      <Modal
        width={1200}
        title="采购订单详情"
        visible={detailsVisible}
        destroyOnClose
        footer={false}
        onCancel={() => {
          setDetailsVisible(false);
        }}
      >
        {/* 商品表格 */}
        <ProTable
          {...tableConfig}
          rowKey="id"
          actionRef={actionRef}
          params={{ orderId: orderGroupId }}
          request={async (e) => {
            const res: any = await apiDistributeBuyOrder(e);
            addOperationLogRequest({
              action: 'info',
              content: `查看【${currentRow?.orderId}】采购订单详情`,
            });
            setCounts(res.data.totalAmount);
            res.data = res.data.productList;
            res.data.map((item: any) => {
              item.id = getUniqueId();
            });
            return res;
          }}
          columns={columnsInitial}
          headerTitle={<div style={{}}>采购订单号：{currentRow?.orderId}&emsp;&emsp;&emsp;</div>}
          expandable={{
            expandedRowRender: (record: any, indexTable: number) => (
              <>
                <span style={{ padding: '8px', display: 'block' }}>入园日期及分时预约信息：</span>
                <List
                  style={{
                    padding: '4px 8px',
                    maxHeight: '300px',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                  }}
                  grid={{
                    gutter: 16,
                    xs: 1,
                    sm: 2,
                    md: 4,
                    lg: 4,
                    xl: 5,
                    xxl: 5,
                  }}
                  dataSource={record.dateList}
                  renderItem={(item: any, indexList: number) => (
                    <List.Item className="cardBox">
                      <Card
                        title={item.dayBegin}
                        hoverable
                        size="small"
                        extra={
                          <a
                            onClick={() => {
                              // 修改分时信息
                              setTimeDataSource(item.timeShareDetail);
                              // setTimeTitle(item.dayBegin)
                              setTimeVisible(true);
                            }}
                          >
                            详情
                          </a>
                        }
                      >
                        <p>数量：{item.totalNum}</p>
                        <p>金额：￥ {item.totalAmount}</p>
                      </Card>
                    </List.Item>
                  )}
                />
              </>
            ),
            rowExpandable: (record: any) => record.dateList,
          }}
        />
        <div
          style={{ marginRight: '24px', textAlign: 'right', fontWeight: '400', fontSize: '20px' }}
        >
          总计: {Number(counts).toFixed(2)} 元
          <br />
          <br />
          {showbtn ? (
            <Popconfirm
              placement="top"
              onConfirm={closeOrder}
              okText="是"
              cancelText="否"
              title={'确定取消订单吗？'}
            >
              <Button type="primary" danger>
                取消订单
              </Button>
            </Popconfirm>
          ) : (
            ''
          )}
        </div>
      </Modal>

      <Modal
        title="请确认！订单一经确认将直接扣除区块链额度"
        visible={payVisible}
        onOk={async () => {
          setConfirmLoading(true);
          try {
            // 区块链支付
            await apiBcPay({ orderId: payOrderId });
            // 支付成功
            setConfirmLoading(false);
            setPayVisible(false);
            message.success('支付成功');
            actionRef?.current?.reload();
          } catch (e) {
            setConfirmLoading(false);
          }
        }}
        confirmLoading={confirmLoading}
        onCancel={() => {
          setPayVisible(false);
        }}
      >
        <h2>金额总计：{payPrice}元</h2>
      </Modal>

      {/* 查看区块链交易记录 */}
      <Blockchain
        cochainVisible={cochainVisible}
        setCochainVisible={setCochainVisible}
        cochainColumns={cochainColumns}
        cochainDataDataSource={cochainDataDataSource}
      />
      {/* 分时预约详情弹窗 */}

      <Modal
        width={modelWidth.lg}
        title={'分时预约信息'}
        visible={timeVisible}
        destroyOnClose
        footer={null}
        onCancel={() => {
          setTimeVisible(false);
        }}
      >
        <ProTable
          {...tableConfig}
          rowKey="distributorTicketStockId"
          headerTitle={timeTitle}
          columns={timeTableColumns}
          dataSource={timeDataSource}
          search={false}
        />
      </Modal>

      {/* 非分时预约详情弹窗 */}
      <Modal
        width={modelWidth.lg}
        title={'入园日期信息'}
        visible={unTimeVisible}
        destroyOnClose
        footer={null}
        onCancel={() => {
          setUnTimeVisible(false);
        }}
      >
        <ProTable
          {...tableConfig}
          rowKey="distributorTicketStockId"
          // headerTitle={timeTitle}
          columns={unTimeTableColumns}
          dataSource={unTimeDataSource}
          search={false}
        />
      </Modal>

      {/* 退货 */}
      <Subpage {...subpageState2} title="我要退货">
        <Purchase
          currentRow={currentRow}
          orderId={orderGroupId}
          stockCertificate={stockCertificate}
          close={() => subpageState2.setOpen(false)}
        />
      </Subpage>

      {/* 进货 */}
      <Subpage {...subpageState} title="采购列表">
        <PurchaseAdd
          close={() => subpageState.setOpen(false)}
          buyed={() => {
            subpageState.setOpen(false);
            actionRef?.current?.reload();
          }}
        />
      </Subpage>
      {/* 采购订单售票详情 */}
      <TicketSaleDetail modalState={purchaseModalState} order={currentRow} type="purchase" />
      {/* 分时库存 */}
      <ProModal
        {...timeModalState}
        fullTitle="分时预约详情"
        columns={timeModalColumns}
        layout="horizontal"
        dataSource={timeModalData}
        onFinish={async () => true}
      />
    </>
  );
};

export default TableList;
