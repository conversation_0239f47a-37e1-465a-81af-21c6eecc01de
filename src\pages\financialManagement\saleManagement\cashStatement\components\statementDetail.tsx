/*
 * @FilePath: statementDetail.tsx
 * @Author: chentian<PERSON><PERSON>
 * @Date: 2022-10-17 13:43:35
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-07 11:46:46
 * Copyright: 2022 xxxTech CO.,LTD. All Rights Reserved.
 * @Descripttion:
 */

import ExportButton from '@/common/components/ExportButton';
import useExport from '@/common/components/ExportButton/useExport';
import Tag from '@/common/components/Tag';
import { columnsState, tableConfig } from '@/common/utils/config';
import { chainStatusEnum } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getCashSettlementDetail } from '@/services/api/cashSettlement';
import type { API } from '@/services/api/typings';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import { useModel } from '@umijs/max';
import styles from '../index.less';

interface IStatementDetailProps {
  visible: boolean;
  setVisible: (bool: boolean) => void;
  tableListItem: API.ICashSettlementListResponse | undefined;
}
interface IHeadProps {
  label: string;
  value: string;
}

const StatementDetail: React.FC<IStatementDetailProps> = ({
  visible,
  setVisible,
  tableListItem,
}) => {
  const actionRef = useRef<ActionType>();
  const { initialState } = useModel('@@initialState');
  const [amount, setAmount] = useState<number>(0); // 合计
  const [head, setHead] = useState<IHeadProps[]>([]); // 头部信息
  const columns: ProColumns<API.ICashDetailItem>[] = [
    {
      title: '订单号 / 退单号',
      dataIndex: 'orderId',
      key: 'orderId',
    },
    {
      title: '结算单号',
      dataIndex: 'tradeNo',
      key: 'tradeNo',
    },
    {
      title: '交易上链',
      dataIndex: 'isChain',
      valueEnum: chainStatusEnum,
      renderText: (dom) => <Tag type="chainStatus" value={dom} />,
    },
    {
      title: '支付 / 退款时间',
      dataIndex: 'paymentTime',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      search: false,
    },
    {
      title: '支付 / 退款时间',
      key: 'paymentTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            paymentBeginTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD HH:mm:ss'),
            paymentEndTime: dayjs(value[1], 'YYYY-MM-DD')
              .endOf('day')
              .format('YYYY-MM-DD HH:mm:ss'),
          };
        },
      },
    },
    {
      title: '实付金额',
      dataIndex: 'payAmount',
      key: 'payAmount',
      hideInSearch: true,
    },
  ];
  // 查询账单明细
  const cashSettlementDetailReq = async (params: API.ICashDetailParams) => {
    const realParams = {
      ...params,
      countBeginTime: tableListItem?.countBeginTime,
      countEndTime: tableListItem?.countEndTime,
      staffName: tableListItem?.staffName,
      staffAccount: tableListItem?.staffAccount,
      providerId: initialState?.currentCompany?.coId || '',
    };
    const { data } = await getCashSettlementDetail(realParams);

    addOperationLogRequest({
      action: 'info',
      content: `查看现金账单【${tableListItem?.id}】明细`,
    });

    if (data.data && data.data.length) {
      const headInfo: IHeadProps[] = [
        { label: '员工姓名：', value: data.data[0].staffName },
        { label: '员工账号：', value: data.data[0].staffAccount },
        { label: '个人商户号：', value: data.data[0].merchantNo },
      ];
      setAmount(data.countAmount);
      setHead(headInfo);
    } else {
      setAmount(0);
    }
    return {
      data: data.data,
      total: data.total,
    };
  };
  const exportState = useExport('/orderCash/orderCashDetailedPage', columns, {
    countBeginTime: tableListItem?.countBeginTime,
    countEndTime: tableListItem?.countEndTime,
    staffName: tableListItem?.staffName,
    staffAccount: tableListItem?.staffAccount,
    providerId: initialState?.currentCompany?.coId || '',
  });
  return (
    <Modal
      title="账单明细"
      visible={visible}
      width={modelWidth.xl}
      destroyOnClose={true}
      footer={null}
      onCancel={() => setVisible(false)}
    >
      <div>
        {head ? (
          <div style={{ padding: '24px' }}>
            {head.map((n: any, i: number) => {
              return (
                <div key={n.label} className={styles.head}>
                  {n.label}
                  <span>{n.value || '-'}</span>
                </div>
              );
            })}
          </div>
        ) : (
          ''
        )}

        <ProTable<API.ICashDetailItem, API.ICashDetailParams>
          {...tableConfig}
          search={{
            labelWidth: 'auto',
            collapseRender: false,
            collapsed: false,
            span: { xs: 24, sm: 12, md: 12, lg: 12, xl: 6, xxl: 6 },
          }}
          actionRef={actionRef}
          rowKey="orderId"
          request={cashSettlementDetailReq}
          columns={columns}
          formRef={exportState.formRef}
          columnsState={columnsState(exportState)}
          toolBarRender={() => [<ExportButton {...{ exportState }} />]}
        />
        <div className={styles.amount}>
          <span>合计：</span>
          {amount || 0}
        </div>
      </div>
    </Modal>
  );
};

export default StatementDetail;
