/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-06-05 11:20:50
 * @LastEditTime: 2023-06-06 16:08:43
 * @LastEditors: zhangfengfei
 */
import { getTimeShareAndStockDetail } from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { List, Spin } from 'antd';
import type { FC } from 'react';
import { useEffect } from 'react';
import { useModel, useRequest } from '@umijs/max';
import TimeShareCard from './TimeShareCard';

interface ExpandedContentProps {
  dataItem: API.TotalStockItem;
  expanded: boolean;
}

// 展开的分时预约信息
const ExpandedContent: FC<ExpandedContentProps> = ({
  dataItem: { productId, categoryType, productName },
  expanded,
}) => {
  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany || {};

  const { loading, data, run } = useRequest(getTimeShareAndStockDetail, {
    manual: true,
    formatResult(res) {
      return res.data.enterTimeGroupList;
    },
    initialData: [],
  });

  useEffect(() => {
    if (expanded) {
      run({
        productId,
        distributorId: coId,
        categoryType,
      });
    }
  }, [expanded]);

  return (
    <Spin spinning={loading}>
      <span style={{ padding: '8px', display: 'block' }}>入园日期及分时预约信息：</span>
      <List
        style={{
          padding: '4px 8px',
          maxHeight: '300px',
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
        grid={{
          gutter: 16,
          xs: 1,
          sm: 2,
          md: 4,
          lg: 4,
          xl: 5,
          xxl: 8,
        }}
        dataSource={data}
        renderItem={(item, index) => (
          <List.Item className="cardBox">
            <TimeShareCard dataItem={item} productName={productName} />
          </List.Item>
        )}
      />
    </Spin>
  );
};

export default ExpandedContent;
