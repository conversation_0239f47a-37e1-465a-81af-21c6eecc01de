/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-21 14:00:01
 * @LastEditTime: 2023-09-21 16:30:57
 * @LastEditors: zhangfengfei
 */
import OrderInfoContent from '@/common/components/OrderInfoContent';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { But<PERSON>, Card } from 'antd';
import qs from 'qs';
import type { FC } from 'react';
import { useLocation, useParams } from '@umijs/max';

interface OrderDetailProps {}

const OrderDetail: FC<OrderDetailProps> = () => {
  const { id } = useParams<{ id: string }>();
  const { search } = useLocation();

  const { orderType = 'JQMP' } = qs.parse(search, {
    ignoreQueryPrefix: true,
  });

  return (
    <Card>
      <Button
        type="link"
        icon={<ArrowLeftOutlined />}
        onClick={() => window.history.back()}
        style={{
          padding: 0,
        }}
      >
        返回
      </Button>

      <OrderInfoContent orderId={id} orderType={orderType} />
    </Card>
  );
};

export default OrderDetail;
