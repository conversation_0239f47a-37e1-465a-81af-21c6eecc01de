/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-11-20 10:37:08
 * @LastEditTime: 2024-01-05 10:20:14
 * @LastEditors: zhangfengfei
 */
import { InfoCircleTwoTone } from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-table/lib/typing';
import { Modal, message } from 'antd';
import type { MutableRefObject, ReactNode } from 'react';
import { Access } from '@umijs/max';

export default ({
  access,
  status,
  params,
  request,
  actionRef,
  content,
}: {
  /** 权限 */
  access: boolean;
  /** 启用状态 */
  status: boolean;
  /** 接口参数 */
  params: any;
  /** 接口函数 */
  request: Function;
  /** 表格对象 */
  actionRef: MutableRefObject<ActionType | undefined>;
  content?: ReactNode | string;
}) => (
  <Access accessible={access}>
    <a
      style={{ color: 'red' }}
      onClick={async () => {
        if (status) {
          Modal.warning({
            title: '不可删除',
            content: '请先禁用后删除',
            okText: '确定',
          });
          return;
        }
        Modal.confirm({
          title: '确认删除吗？',
          icon: <InfoCircleTwoTone />,
          content: content || '删除后不可恢复',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            request(params)
              .then(() => {
                message.success('删除成功');
                actionRef.current?.reloadAndRest();
              })
              .catch(() => {});
          },
        });
      }}
    >
      删除
    </a>
  </Access>
);
