import type { FC, ReactNode } from 'react';

interface PrefixTitleProps {
  children?: ReactNode;
}
/** 带蓝色前缀的标题组件 */
const PrefixTitle: FC<PrefixTitleProps> = ({ children }) => {
  return (
    <div
      style={{
        display: 'flex',
        width: '100%',
        alignItems: 'center',
      }}
    >
      <span
        style={{
          width: 3,
          height: 16,
          background: '#1890FF',
          marginRight: 6,
        }}
      />
      <div style={{ flex: 1 }}>{children}</div>
    </div>
  );
};

export default PrefixTitle;
