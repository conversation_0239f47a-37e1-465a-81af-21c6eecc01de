import { tableConfig } from '@/common/utils/config';
import { productTypeEnum, ticketTypeEnum } from '@/common/utils/enum';
import { getPriceStrategyByAgent } from '@/services/api/distribution';
import { apiAgentGoodsList } from '@/services/api/store';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Modal, Tag, message } from 'antd';
import React, { useEffect, useState } from 'react';
import GoodsDetail from './GoodsDetail';

let valAll: any = [];
export default ({
  visible,
  setVisible,
  storeId,
  actionRefUp,
  onfinish,
  editableKeys,
}: {
  visible: boolean;
  setVisible: any;
  storeId?: string;
  actionRefUp?: any;
  onfinish: any;
  editableKeys: any;
}) => {
  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany;
  const actionRef = React.useRef<ActionType>();
  const [groupType, setGroupType] = useState('1');
  // 商品详情
  const [detailData, setDetailData] = useState<any>({ id: '', isEnable: 0 });
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [detailLoading, setDetailLoading] = useState<boolean>(false);

  const columns: ProColumns[] = [
    {
      title: '平台',
      dataIndex: 'channel',
      valueEnum: { 1: '环球数科电商系统' },
      hideInTable: true,
    },
    {
      title: '导入类型',
      dataIndex: 'groupType',
      valueEnum: { 1: '代理授权凭证', 2: '公池导入', 3: '直销商品' },
      valueType: 'radio',
      hideInTable: true,
      initialValue: '1',
      fieldProps: {
        onChange: (e: any) => {
          setGroupType(e.target.value);
        },
      },
    },
    {
      title: '智能填充 AppID - AppSecret',
      dataIndex: 'appIDSecret',
      hideInTable: true,
      search: groupType !== '1' ? false : undefined,
      fieldProps: (form) => {
        return {
          placeholder: '粘贴数据自动填充',
          onChange: (e: any) => {
            const sum = e.target.value.replace(/AppID|:|;| /g, '').split('AppSecret');
            form.setFieldsValue({ appId: sum[0] });
            form.setFieldsValue({ appSecret: sum[1] });
            form.setFieldsValue({ appIDSecret: null });
          },
        };
      },
    },
    {
      title: 'AppID',
      dataIndex: 'appId',
      hideInTable: true,
      search: groupType !== '1' ? false : undefined,
    },
    {
      title: 'AppSecret',
      dataIndex: 'appSecret',
      hideInTable: true,
      search: groupType !== '1' ? false : undefined,
    },
    // {
    //   title: '商品主图',
    //   dataIndex: 'picUrl',
    //   hideInSearch: true,
    // },
    {
      width: 120,
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
      search: false,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      renderText: (dom: any, entity) => (
        <a
          onClick={async () => {
            // 商品详情
            setDetailLoading(true);
            setDetailVisible(true);
            try {
              // [普通票/旅游卡] 商品数据
              const { data } = await getPriceStrategyByAgent({
                unitId: entity?.unitId,
                distributorId: entity?.supplierId,
                priceId: entity?.priceId,
              });

              const prices = data?.price?.price || {};
              const _resData = {
                ...data,
                ...prices,
              };
              setDetailData(_resData);
              setDetailLoading(false);
            } catch (error) {
              console.log(error);
            }
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      width: 60,
      title: '票种',
      dataIndex: 'goodsType',
      valueEnum: ticketTypeEnum,
      hideInSearch: true,
      renderText: (dom: any, entity: any) => (entity.proType == 20 ? '-' : dom),
    },
    {
      width: 60,
      title: '类型',
      dataIndex: 'proType',
      valueEnum: productTypeEnum,
      hideInSearch: true,
    },
    {
      title: '分时时段',
      hideInSearch: true,
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '购买有效时间',
      search: false,
      render: (_, entity: any) =>
        entity.purchaseBeginTime && entity.purchaseEndTime
          ? entity.purchaseBeginTime + ' 至 ' + entity.purchaseEndTime
          : '-',
    },

    // {
    //   width: 80,
    //   title: '佣金类型',
    //   dataIndex: 'commissionType',
    //   valueEnum: { 0: '固定佣金', 1: '按比例' },
    //   hideInSearch: true,
    // },
    {
      title: '佣金比例（%）',
      dataIndex: 'commissionRate',
      hideInSearch: true,
      renderText: (dom) => (dom == 0 ? '-' : dom),
      // render: (dom: any, entity: any) => (entity.commissionType ? dom : '-'),
    },
    // {
    //   width: 80,
    //   title: '佣金数值',
    //   dataIndex: 'commissionAmount',
    //   hideInSearch: true,
    //   render: (dom: any, entity: any) => (!entity.commissionType ? dom : '-'),
    // },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      hideInSearch: true,
    },
    {
      title: '用户单独购买价格（元）',
      dataIndex: 'salePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      hideInSearch: true,
    },
    {
      title: '套餐中用户购买单品价格（元）',
      dataIndex: 'composePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      hideInSearch: true,
      // render: (dom: any, entity: any) => (entity.isCompose ? dom : '-'),
    },
  ];
  useEffect(() => {
    setGroupType('1');
    valAll = [];
  }, [visible]);
  return (
    <Modal
      width={1400}
      title={'所有商品'}
      visible={visible}
      destroyOnClose
      maskClosable={false}
      cancelText="取消"
      okText="确定"
      onCancel={() => {
        setVisible(false);
      }}
      onOk={async () => {
        if (valAll.length == 0) {
          message.info('无导入数据');
          return;
        }
        onfinish(valAll);
        setVisible(false);
        // try {
        //   const simpleGoods = valAll.map((item: any) => ({
        //     // "id": "",
        //     "name": item.proName,
        //     // "note": item,
        //     "priceId": item.priceId
        //   }))

        //   await apiStoreGoodsAdd({
        //     simpleGoods,
        //     storeId
        //   })
        //   message.success('导入成功');
        //   setVisible(false);
        //   actionRefUp?.current?.reload();
        // } catch (error) { }
      }}
    >
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="priceId"
        search={{
          labelWidth: 'auto',
          collapseRender: false,
          collapsed: false,
          span: { xs: 24, sm: 24, md: 12, lg: 12, xl: 8, xxl: 8 },
        }}
        manualRequest
        options={false}
        params={{ distributorId: coId, type: 2 }}
        request={async (params) => {
          if (groupType === '1' && (!params.appId || !params.appSecret)) {
            message.warning('请输入代理授权凭证');
            return;
          }
          return apiAgentGoodsList(params);
        }}
        columns={columns}
        rowSelection={{
          type: 'checkbox',
          onChange: (key, val) => {
            console.log(key, val);

            valAll = val;
            // console.log(key);
            // userList = String(key).split(',');
            // if (!userList[0]) userList = [];
          },
          getCheckboxProps: (record) => ({
            disabled: editableKeys.filter((item: any) => item == record.priceId).length > 0,
          }),
        }}
      />

      {/* 商品详情 */}
      <GoodsDetail
        visible={detailVisible}
        loading={detailLoading}
        setVisible={setDetailVisible}
        dataSource={detailData}
      />
    </Modal>
  );
};
