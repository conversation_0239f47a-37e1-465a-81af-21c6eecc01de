import { getMessage, updAllMessageCenter, updMessageCenter } from '@/services/api/message';
import { CloseOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import type { TabsProps } from 'antd';
import { Button, Drawer, message, Pagination, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import styles from './message.less';
import { getEnv } from '@/common/utils/getEnv';

const MessageModal = ({ show, close, setUnReadNum }) => {
  const currentCompanyId = localStorage.getItem('currentCompanyId');
  const { initialState } = useModel('@@initialState');
  const { userId } = initialState.userInfo;
  const [messageList, setMessageList] = useState([]);
  const [total, setTotal] = useState(0);
  const [currentTabStatus, setCurrentTabStatus] = useState(0); // 记录当前标签状态
  const [currentPage, setCurrentPage] = useState(1); // 记录当前页码
  const [delLoading, setDelLoading] = useState(false);
  const [activeKey, setActiveKey] = useState('1');
  const [unReadtatal, setUnReadtatal] = useState(0);
  const [Readtatal, setReadtatal] = useState(0);
  const { BACKEND_V2 } = getEnv();

  // 获取消息数据（含分页参数）
  const getmessageData = async (status, page) => {
    const res = await getMessage({
      current: page, // 传递当前页码
      pageSize: 5,
      status: status + '',
      enterpriseId: currentCompanyId,
      userId,
    });
    setUnReadNum(res.data.unredNum);
    setMessageList(res.data.records);
    setTotal(res.data.total);
    setUnReadtatal(res.data.unredNum);
    setReadtatal(res.data.readNum);
    setCurrentPage(page);
  };

  // 标签切换处理
  const tabOnChange = (e) => {
    setActiveKey(e);
    const status = e - 1;
    setMessageList([]);
    setTotal(0);
    setCurrentTabStatus(status);
    getmessageData(status, 1); // 切换标签时重置为第一页
  };

  const delAllMsg = async () => {
    setDelLoading(true);
    try {
      const res: any = await updAllMessageCenter(currentCompanyId);
      if (res.code === 20000) {
        await getmessageData(+activeKey - 1, 1); // 刷新未读消息列表
        // getmessageData(1, 1); // 刷新已读消息列表
        message.success('已标记所有为已读');
      } else {
        message.error(res.message || '操作失败');
      }
    } catch (error) {
      message.error('网络请求失败，请重试');
    } finally {
      setDelLoading(false);
    }
  };

  const returnBtnName = (type) => {
    if (type == 1 || type == 2) {
      return '前往采购';
    } else if (type == 3) {
      return '前往上架';
    } else if (type == 4) {
      return '商品管理';
    } else if (type == 5 || type == 8) {
      return '立即审核';
    } else if (type == 6) {
      return '查看代理供应商详情';
    } else if (type == 9) {
      return '查看经销供应商详情';
    } else if (type == 11) {
      return '立即补充库存';
    } else if (type == 12) {
      return '查看详情';
    } else {
      return null;
    }
  };

  const Item = ({ item }) => {
    return (
      <>
        <div
          key={item.createTime}
          className={styles.item}
          onClick={async () => {
            if (item.status == 0) {
              const res: any = await updMessageCenter(item.id);
              if (res.code === 20000) {
                getmessageData(item.status, 1);
              }
            }
          }}
        >
          <div>
            <div className={styles.time}>{item.createTime}</div>
            <div className={styles.content}>{item.messageInfo}</div>
            {item.type != 7 && item.type != 10 ? (
              <Button
                className={styles.btn}
                type="link"
                onClick={async (e) => {
                  e.stopPropagation();
                  const extraInfo = item?.extraInfo && JSON.parse(item?.extraInfo);
                  if (item.type == 1) {
                    history.push(
                      `/partner/supply-chain/purchase-order?name=${extraInfo?.supplierName}`,
                    );
                  } else if (item.type == 2) {
                    history.push('/partner/supply-chain/purchase-order');
                  } else if (item.type == 3) {
                    history.push('/business/my-shop');
                  } else if (item.type == 4) {
                    history.push('/business/my-shop');
                  } else if (item.type == 5) {
                    history.push('/partner/audit-manage');
                  } else if (item.type == 6) {
                    history.push('/partner/supply-chain/supplier?type=agency');
                  } else if (item.type == 8) {
                    history.push('/partner/audit-manage');
                  } else if (item.type == 9) {
                    history.push('/partner/supply-chain/supplier?type=sell');
                  } else if (item.type == 11) {
                    if (extraInfo?.merchantType == '1') {
                      window.open(
                        `${BACKEND_V2}${extraInfo?.uniqueCode}/ticket/stock?goodsName=${extraInfo?.goodsName}`,
                      );
                    } else if (extraInfo?.merchantType == '2') {
                      history.push(
                        `/partner/supply-chain/purchase-order?goodsName=${extraInfo?.goodsName}`,
                      );
                    }
                  } else if (item.type == 12) {
                    history.push(
                      `/statistic/stock/distributorProduct?goodsName=${extraInfo?.goodsName}`,
                    );
                  }
                  close();
                  if (item.status == 0) {
                    const res: any = await updMessageCenter(item.id);
                    if (res.code === 20000) {
                      getmessageData(item.status, 1);
                    }
                  }
                }}
              >
                {returnBtnName(item.type) + ' > '}
              </Button>
            ) : null}
          </div>
        </div>
      </>
    );
  };

  // 标签配置（渲染消息内容和时间）
  const items: TabsProps['items'] = [
    {
      key: '1',
      label: `未读(${unReadtatal})`,
      children: (
        <>
          {messageList.length > 0 ? (
            <div>
              {messageList.map((item, index) => (
                <Item item={item} key={index} />
              ))}
            </div>
          ) : (
            <div className={styles.noData}>暂无数据</div>
          )}
        </>
      ),
    },
    {
      key: '2',
      label: `已读(${Readtatal})`,
      children: (
        <>
          {messageList.length > 0 ? (
            <div>
              {messageList.map((item, index) => (
                <Item item={item} key={index} />
              ))}
            </div>
          ) : (
            <div className={styles.noData}>暂无数据</div>
          )}
        </>
      ),
    },
  ];

  // 首次打开时加载未读消息
  useEffect(() => {
    if (show) {
      getmessageData(+activeKey - 1, 1);
    }
  }, [show]);

  const operations = (
    <Button onClick={delAllMsg} type="link" loading={delLoading} disabled={delLoading}>
      全部已读
    </Button>
  );
  // 自定义抽屉头部内容（标题居左，关闭图标居右）
  const drawerHeader = (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
      }}
    >
      <span style={{ fontWeight: '600', fontSize: '16px' }}>消息中心</span> {/* 居左标题 */}
      <CloseOutlined onClick={close} style={{ cursor: 'pointer' }} /> {/* 居右关闭图标 */}
    </div>
  );
  return (
    <>
      <style>{`
        .ant-drawer .ant-drawer-header-title {
           display:none ;
         }
        .ant-drawer .ant-drawer-extra {
          width:100% ;
         }
      `}</style>
      <Drawer extra={drawerHeader} bodyStyle={{ padding: '0 12px' }} onClose={close} open={show}>
        <div style={{ padding: '0 10px' }}>
          <Tabs
            items={items}
            activeKey={activeKey}
            tabBarExtraContent={operations}
            onChange={tabOnChange}
          />
          {total > 0 && (
            <div className={styles.paginationBox}>
              <Pagination
                current={currentPage}
                showSizeChanger={false}
                total={total}
                pageSize={5}
                simple
                onChange={(page) => getmessageData(currentTabStatus, page)}
                style={{ marginTop: '20px' }}
              />
            </div>
          )}
        </div>
      </Drawer>
    </>
  );
};

export default MessageModal;
