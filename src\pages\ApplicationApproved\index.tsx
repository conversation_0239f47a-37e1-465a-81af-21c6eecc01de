import { Result, Space } from 'antd';
// import { history } from '@umijs/max';

const ApplicationApproved = ({ history, props }) => {
  const { name } = history.location.query;
  console.log('yyyyy', history.location.query.name);
  const text = <span style={{ fontWeight: 'bolder', color: 'black' }}>加入{name}</span>;
  return (
    <>
      <Result
        status="success"
        title="申请成功"
        // subTitle={`你已经完成${aaa}的申请。管理员审批通过后，你将收到短信通知，敬请留意`}
        subTitle={
          <Space>你已经完成{text}的申请。管理员审批通过后，你将收到短信通知，敬请留意。</Space>
        }
        // extra={[
        //   <Button type="primary" key="console">
        //     Go Console
        //   </Button>,
        //   <Button key="buy">Buy Again</Button>,
        // ]}
      />
    </>
  );
};

export default ApplicationApproved;
