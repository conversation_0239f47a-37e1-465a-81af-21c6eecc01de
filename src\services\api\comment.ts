/*
 * @Author: 李悍宇 <EMAIL>
 * @Date: 2025-06-20 10:50:39
 * @LastEditors: 李悍宇 <EMAIL>
 * @LastEditTime: 2025-06-30 10:30:05
 * @FilePath: \exchange\src\services\api\comment.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 评论模块
 */
import { request } from '@umijs/max';
import { scenicHost } from '.';
const url = (v: string) => scenicHost + '/comment' + v;
const scenicUrl = (v: string) => scenicHost + '/scenic' + v;

/** 新增标签 */
export function addLabel(data: any) {
  return request(url('/addLabel'), { data, method: 'POST' });
}

/** 修改标签 */
export function delLabel(id: string) {
  return request(url(`/delLabel/${id}`), { method: 'GET' });
}

/** 查询标签 */
export function findByLabelList(id: string) {
  return request(url(`/findByLabelList/${id}`), { method: 'GET' });
}

/** 查询评价 */
export function getCommentPage(data: any) {
  return request(url('/getCommentPage'), { data, method: 'POST' });
}

/** 回复评价 */
export function addMerchantComment(data: any) {
  return request(url('/addMerchantComment'), { data, method: 'POST' });
}

/** 回复评价 */
export function getScencList() {
  return request(scenicUrl(`/down/list?current=1&pageSize=999&systemType=paas`), { method: 'GET' });
}

/** 推荐 */
export function updRecommend(data: any) {
  return request(url('/updRecommend'), { data, method: 'POST' });
}

/** 拖拽 */
export function updSequence(data: any) {
  return request(url('/updSequence'), { data, method: 'POST' });
}
