import { logout, setCookie } from '@/services/api/cas';
import { history } from '@umijs/max';
import { parse, stringify } from 'querystring';
import { addOperationLogRequest } from './operationLog';
import { clearStorage } from './storage';
import { goToLogin } from './tool';
import { getEnv } from '@/common/utils/getEnv';

const isDev = process.env.NODE_ENV;

// 登录
export async function login() {
  const { hash } = location;

  const { tk, path, ...rest } = parse(hash.split('?')[1]);

  //  有 token
  if (tk) {
    // 本地开发 需要转回 localhost
    if (isDev && path) {
      window.location.href = `${path}/#/welcome?tk=${tk}&${stringify(rest)}`;
    }

    localStorage.removeItem('currentCompanyId');
    localStorage.removeItem('userInfo');
    clearStorage();
    try {
      await setCookie(tk as string);
      // 记录日志
      addOperationLogRequest({
        action: 'login',
        content: '登录易旅通系统',
      });

      // 需要跳转的路由
      if (rest.route) {
        history.replace(decodeURIComponent(rest.route) + '?' + stringify(rest));
      } else {
        history.replace('/welcome');
      }
    } catch (error) {
      console.log(error);
    }
  }
}

/**
 * 退出登录
 */
export async function loginOut() {
  try {
    await logout({
      appId: getEnv().APPID,
    });
    localStorage.removeItem('userInfo');
    localStorage.removeItem('currentCompanyId');
    clearStorage();
    goToLogin();
  } catch (error) {
    if (error.data?.code === 30001) {
      //没设置 cookie 跳转登陆页
      goToLogin();
    }
  }
}

// 检测浏览器版本
export const upgradeVersion = () => {
  const userAgent = window.navigator.userAgent;
  const browsers = [
    { name: 'Firefox', regex: /Firefox\/(\d+)/, minVersion: 90 },
    { name: 'Chrome', regex: /Chrome\/(\d+)/, minVersion: 92 },
    { name: 'Safari', regex: /Version\/(\d+).*Safari/, minVersion: 15.4 },
    { name: 'Opera', regex: /OPR\/(\d+)/, minVersion: 78 },
    { name: 'IE', regex: /MSIE (\d+)/, minVersion: 92 },
  ];

  const browser = browsers.find(({ regex }) => regex.test(userAgent));

  if (browser) {
    const match = userAgent.match(browser.regex);
    if (match && match[1]) {
      const version = parseInt(match[1], 10);
      // 低版本
      if (version < browser.minVersion) {
        history.replace('/upgrade');
      }
    } else {
      // 未知
      history.replace('/upgrade');
    }
  } else {
    // 未知浏览器
    history.replace('/upgrade');
  }
};
