/*
 * 进货弹窗
 * */
import ProModal from '@/common/components/ProModal';
import useProModal from '@/common/components/ProModal/useProModal';
import TimeStore from '@/common/components/TimeStore';
import { tableConfig } from '@/common/utils/config';
import { GuideStepStatus, productTypeEnum, ticketTypeEnum } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getUniqueId } from '@/common/utils/tool';
import { useGuide } from '@/hooks/useGuide';
import {
  apiBcPay,
  apiOrderInfo,
  apiSupplierTicketList,
  getDistTicketStockSelectList,
  getDistributionStatus,
} from '@/services/api/distribution';
import { EditOutlined, SettingOutlined } from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import type { ProFormInstance } from '@ant-design/pro-form';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useLocation } from '@umijs/max';
import type { TableProps } from 'antd';
import {
  Button,
  Card,
  DatePicker,
  InputNumber,
  List,
  Modal,
  Space,
  Table,
  Tabs,
  Tag,
  message,
} from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import React, { useEffect, useMemo, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';
import CommodityList from './Commodity';
import Detail from './CommodityDetails';
import TravelDetail from './TravelCommodityDetails';
import { getEnv } from '@/common/utils/getEnv';

let supplierList: any = [];
let sellerId: string = '';
let sellerName: string = '';
let isBC: any = 0;
let selectDateList: any = [];
const payOrderId = '';
let settlementId = '';
const Purchase = ({ close, buyed }: { close?: any; buyed?: any }) => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const initialName = searchParams.get('goodsName');
  const { updateGuideInfo } = useGuide();
  // 分时库存
  const modalState = useProModal();
  const [modalData, setModalData] = useState<any>({});
  const [lawsList, setLawsList] = useState<any>([]);
  const lawsColumns = [
    {
      title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join('-')}`,
      render: () => {},
    },
    {
      title: () => '采购数量/可用库存',
      render: ({ purchaseNum = 0, stockAmount = 0 }) => `${purchaseNum}/${stockAmount}`,
    },
    {
      title: () => '采购金额（元）',
      render: ({ purchaseAmount = 0 }) => purchaseAmount,
    },
  ];
  const modalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '入园有效时间',
          valueType: 'dateRange',
          dataIndex: 'dateRange',
          initialValue: [modalData.enterStartTime, modalData.enterEndTime],
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '',
          dataIndex: 'timeLaws',
          colProps: { span: 24 },
          renderFormItem: () => (
            <TimeStore
              lawsColumns={lawsColumns}
              lawsList={lawsList}
              set={(id: any, date: any) => {
                setModalSetData({
                  dateType: 'date',
                  timeShareId: [id],
                  dateList: [date],
                });
                setTimeTableData([
                  {
                    id,
                    goodsName: modalData.goodsName,
                    ticketGoodsType: modalData.ticketGoodsType,
                    salePrice: modalData.salePrice,
                    stockAmount: valueObj[date][id].stockAmount,
                    purchaseNum: valueObj[date][id].purchaseNum,
                    purchaseAmount: valueObj[date][id].purchaseAmount,
                  },
                ]);
                modalSetState.setType('edit');
              }}
            />
          ),
        },
        {
          title: '总采购数量',
          dataIndex: 'num',
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '总采购金额（元）',
          dataIndex: 'price',
          fieldProps: {
            disabled: true,
          },
        },
      ],
    },
  ];
  // 分时库存设置
  const modalSetState = useProModal();
  const [modalSetData, setModalSetData] = useState({});
  const timeEnum = useMemo(() => {
    const obj: Record<string, string> = {};
    lawsList?.forEach((item: any) => {
      obj[item.id] = [item.beginTime, item.endTime].join('-');
    });
    return obj;
  }, [lawsList]);
  const valueObj = useMemo(() => {
    const obj1: any = {};
    modalData.timeLaws?.forEach((item1: any) => {
      const obj2: any = {};
      item1.timeShareDateList.forEach((item2: any) => {
        obj2[item2.timeShareId] = item2;
      });
      obj1[item1.timeShareData] = obj2;
    });
    return obj1;
  }, [modalData]);
  const [timeTableData, setTimeTableData] = useState<any>([]);
  const timeColumns: TableProps['columns'] = [
    {
      title: '分时时段',
      dataIndex: 'id',
      fixed: 'left',
      render: (value) => timeEnum[value],
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      render: (value) => ticketTypeEnum[value],
    },
    {
      title: '可用库存',
      dataIndex: 'stockAmount',
      render: (value) => value ?? '-',
    },
    {
      title: '采购价（元）',
      dataIndex: 'salePrice',
    },
    {
      title: '采购数量',
      dataIndex: 'purchaseNum',
      fixed: 'right',
      render: (value, record, index) => (
        <InputNumber
          min={0}
          value={value}
          onChange={(v: any) => {
            const list = structuredClone(timeTableData);
            list[index].purchaseNum = v;
            list[index].purchaseAmount = parseFloat(String(v * modalData.salePrice)).toFixed(2);
            setTimeTableData(list);
          }}
        />
      ),
    },
    {
      title: '采购金额（元）',
      dataIndex: 'purchaseAmount',
      fixed: 'right',
    },
  ];
  const modalSetColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '时间选择',
          dataIndex: 'dateType',
          valueType: 'radio',
          valueEnum: {
            date: '按日期',
            week: '按星期',
          },
          initialValue: 'date',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          valueType: 'dependency',
          name: ['dateType'],
          columns: ({ dateType }) => [
            {
              title: '选择日期',
              dataIndex: 'dateList',
              valueType: 'date',
              hideInForm: dateType !== 'date',
              colProps: { xs: 24, sm: 12 },
              formItemProps: { rules: [{ required: true }] },
              fieldProps: (form) => {
                return {
                  multiple: true,
                  disabledDate: (current: any) => {
                    return (
                      current < dayjs(modalData.enterStartTime) ||
                      current > dayjs(modalData.enterEndTime)
                    );
                  },
                  onChange: (_: any, dateList: any) => {
                    const { dateType, timeShareId } = form.getFieldsValue();
                    setTimeTableData(
                      timeShareId?.map((id: any) => ({
                        id,
                        goodsName: modalData.goodsName,
                        ticketGoodsType: modalData.ticketGoodsType,
                        salePrice: modalData.salePrice,
                        ...(dateType == 'date' && dateList?.length == 1
                          ? {
                              stockAmount: valueObj[dateList[0]][id].stockAmount,
                              purchaseNum: valueObj[dateList[0]][id].purchaseNum,
                              purchaseAmount: valueObj[dateList[0]][id].purchaseAmount,
                            }
                          : {}),
                      })),
                    );
                  },
                };
              },
            },
            {
              title: '选择区间',
              dataIndex: 'dateRange',
              valueType: 'dateRange',
              hideInForm: dateType !== 'week',
              colProps: { xs: 24, sm: 12 },
              formItemProps: { rules: [{ required: true }] },
              initialValue: [modalData.enterStartTime, modalData.enterEndTime],
              fieldProps: {
                disabledDate: (current: any) => {
                  return (
                    current < dayjs(modalData.enterStartTime) ||
                    current > dayjs(modalData.enterEndTime)
                  );
                },
              },
            },
            {
              title: '设置星期',
              dataIndex: 'dateWeek',
              valueType: 'checkbox',
              valueEnum: {
                0: '每周日',
                1: '每周一',
                2: '每周二',
                3: '每周三',
                4: '每周四',
                5: '每周五',
                6: '每周六',
              },
              initialValue: ['0', '1', '2', '3', '4', '5', '6'],
              hideInForm: dateType !== 'week',
              formItemProps: { rules: [{ required: true }] },
            },
          ],
        },
      ],
    },
    {
      title: '',
      columns: [
        {
          title: '分时选择',
          dataIndex: 'timeShareId',
          valueEnum: timeEnum,
          fieldProps: (form) => {
            return {
              mode: 'multiple',
              onChange: (list: any) => {
                const { dateType, dateList } = form.getFieldsValue();
                setTimeTableData(
                  list?.map((id: any) => ({
                    id,
                    goodsName: modalData.goodsName,
                    ticketGoodsType: modalData.ticketGoodsType,
                    salePrice: modalData.salePrice,
                    ...(dateType == 'date' && dateList?.length == 1
                      ? {
                          stockAmount: valueObj[dateList[0]][id].stockAmount,
                          purchaseNum: valueObj[dateList[0]][id].purchaseNum,
                          purchaseAmount: valueObj[dateList[0]][id].purchaseAmount,
                        }
                      : {}),
                  })),
                );
              },
            };
          },
          colProps: { span: 24 },
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '',
          colProps: { span: 24 },
          renderFormItem: () => (
            <Table
              {...tableConfig}
              rowKey="id"
              columns={timeColumns}
              dataSource={timeTableData}
              pagination={false}
            />
          ),
        },
      ],
    },
  ];

  const [confirmLoading, setConfirmLoading] = useState(false);
  // 商品列表
  const [listVisible, setListVisible] = useState(false);

  const [product, setProduct] = useState<Record<string, any>>({});

  const [isClick, setIsClick] = useState(true);
  // 表单对象
  const actionRef = React.useRef<ProFormInstance>();
  // 数据绑定
  const { initialState } = useModel('@@initialState');
  const { coId, coName } = initialState?.currentCompany || {};
  const [dataSource, setDataSource] = useState<any>();
  const [dataIndex, setDataIndex] = useState<any>(0);
  const [payAmount, setPayAmount] = useState<any>(0); //总价格
  const [payCount, setPayCount] = useState<any>(0); //所选票的数量总数
  const [isChain, setIsChain] = useState<any>(0);
  const [isTravel, setIsTravel] = useState<boolean>(false);

  const getDistributionStatusReq = useRequest(getDistributionStatus, {
    manual: true,
  });
  useEffect(() => {
    if (initialName && actionRef.current) {
      actionRef.current.setFieldsValue({ goodsName: initialName });
      // actionRef.current.reload(); // 正确调用表格的 reload 方法
    }
  }, [initialName]);
  /**
   * 取 进货列表下拉框 信息
   */
  const getStockSelectListReq = useRequest(getDistTicketStockSelectList, {
    defaultParams: [coId],
    manual: false,
    formatResult: (res) => {
      supplierList = Object.entries(res.data.supplierMap).map((item: any[]) => ({
        label: item[1],
        value: item[0],
      }));
      return {
        supplierMap: supplierList,
        scenicMap: Object.entries(res.data.scenicMap).map((item: any[]) => ({
          label: item[1],
          value: item[1],
        })),
        proNameList: res.data.proNameList.map((item: any) => ({
          label: item,
          value: item,
        })),
        proTypeList: res.data.proTypeList.map((item: any) => ({
          label: productTypeEnum[item],
          value: item,
        })),
      };
    },
  });

  let supplierNameCopy = '';
  const [supplierName, setSupplierName] = useState<any>();
  // 总计
  const setTotal = (sum: any) => {
    let allPrice = 0;
    let count = 0;
    sum.map((item: any) => {
      if (item.price) {
        allPrice += item.price * 1;
        count += item.num;
      }
    });
    setPayCount(count);
    setPayAmount(allPrice.toFixed(2));
  };
  // 表格配置
  const tableColumns: ProColumns[] = [
    {
      title: '供应商',
      dataIndex: 'supplierId',
      hideInTable: true,
      valueType: 'select',
      formItemProps: { rules: [{ required: true }] },
      fieldProps: {
        options: getStockSelectListReq.data?.supplierMap,
        allowClear: false,
        onChange: (e: any) => {
          supplierList.map((item: any) => {
            if (item.value == e) {
              supplierNameCopy = item.label;
              settlementId = item.value;
              getDistributionStatusReq.run({
                distributorId: coId || '',
                type: 1,
                upDistributorId: item.value,
              });
            }
          });

          // setIsTravel(false)
          // setDataSource([]);
          // setPayAmount(0);
          // actionRef?.current?.submit();
        },
      },
    },
    {
      title: '库存批次号',
      dataIndex: 'batchId',
      fixed: 'left',
      render: (dom: any, entity: any) =>
        entity.enterDateList.length > 0 ? (
          '-'
        ) : (
          <span>
            {dom} {entity.isExchange === 1 && <Tag color="blue">交易所</Tag>}
          </span>
        ),
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      valueType: 'select',
      fieldProps: {
        options: getStockSelectListReq.data?.scenicMap,
        showSearch: true,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'name',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: getStockSelectListReq.data?.proNameList,
        showSearch: true,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
      search: false,
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      valueType: 'select',
      fieldProps: {
        options: getStockSelectListReq.data?.proTypeList,
        showSearch: true,
      },
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      valueEnum: ticketTypeEnum,
    },
    {
      title: '购买有效时间',
      dataIndex: 'purchaseTime',
      hideInSearch: true,
      render: (dom: any, entity) => {
        const startString = dayjs(entity.purchaseBeginTime).format('YYYY-MM-DD');
        const endString = dayjs(entity.purchaseEndTime).format('YYYY-MM-DD');
        return `${startString} 至 ${endString}`;
      },
    },
    {
      title: '购买有效时间',
      dataIndex: 'purchaseTime',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            purchaseBeginTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            purchaseEndTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '入园有效时间',
      dataIndex: 'day',
      hideInSearch: true,
      render: (dom: any, entity) => {
        const startString = dayjs(entity.enterStartTime).format('YYYY-MM-DD');
        const endString = dayjs(entity.enterEndTime).format('YYYY-MM-DD');
        return `${startString} 至 ${endString}`;
      },
    },
    {
      title: '入园有效时间',
      dataIndex: 'day',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            dayBegin: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            dayEnd: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '可用库存',
      dataIndex: 'totalNumber',
      search: false,
      fixed: 'right',
      valueType: 'digit',
    },
    {
      title: '市场标准价（元）',
      dataIndex: 'marketPrice',
      search: false,
      fixed: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '采购价（元）',
      dataIndex: 'salePrice',
      search: false,
      fixed: 'right',
      render: (dom: any, entity: any) => (entity.enterDateList.length > 0 ? '-' : dom.toFixed(2)),
    },

    {
      title: '采购数量', // 非分时可编辑
      dataIndex: 'num',
      hideInSearch: true,
      fixed: 'right',
      render: (dom: any, entity, index, _, schema: any) =>
        entity.timeLaws?.length ? (
          <Button
            icon={<EditOutlined />}
            iconPosition="end"
            block
            style={{ justifyContent: 'start', padding: '4px 11px' }}
            onClick={() => {
              const obj: any = {};
              // 遍历日期提取分时集
              entity?.timeLaws?.forEach(({ timeShareDateList }: any) => {
                timeShareDateList.forEach((item: any) => {
                  obj[item.timeShareId] = {
                    id: item.timeShareId,
                    beginTime: item.timeShareBeginTime,
                    endTime: item.timeShareEndTime,
                  };
                });
              });
              setDataIndex(index);
              setLawsList(Object.values(obj));
              setModalData(entity);
              modalState.setType('edit');
            }}
          >
            {dom ? dom : 0}
          </Button>
        ) : (
          <InputNumber
            min={0}
            precision={0}
            disabled={getDistributionStatusReq.data?.status == 2}
            defaultValue={dom ? dom : 0}
            onChange={(e: any) => {
              console.log(e, index, schema.dataIndex);
              const sum = JSON.parse(JSON.stringify(dataSource));
              sum[index].num = e;
              sum[index].price = (e * dataSource[index].salePrice).toFixed(2);
              setDataSource(sum);
              setTotal(sum);
            }}
          />
        ),
    },
    {
      title: '采购金额（元）',
      dataIndex: 'price',
      search: false,
      fixed: 'right',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      // render: (dom: any) => <Tag color={['red', 'blue'][dom]}>{['禁用', '启用'][dom]}</Tag>,
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_, entity) => (
        <a
          key={getUniqueId()}
          onClick={() => {
            if (isTravel) {
              TravelDetail.show(entity.goodsId, entity);
            } else {
              Detail.show(entity.goodsId);
            }
          }}
        >
          商品详情
        </a>
      ),
    },
  ];

  // 分时数据绑定
  const [timeVisible, setTimeVisible] = useState<any>();
  // 分时序列 [表格序，列表序]
  const [timeIndex, setTimeIndex] = useState<any>([0, 0]);
  const [timeDataSource, setTimeDataSource] = useState<any>();
  // 分时表格配置
  const timeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '分时预约',
      dataIndex: 'timeBeginTime',
      render: (_, entity) =>
        entity.timeBeginTime && entity.timeEndTime ? (
          <span>{entity.timeBeginTime + ' 至 ' + entity.timeEndTime}</span>
        ) : (
          '-'
        ),
    },

    {
      title: '进货数量',
      dataIndex: 'num',
      render: (dom: any, _, index, __, schema: any) => (
        <InputNumber
          min={0}
          precision={0}
          disabled={getDistributionStatusReq.data?.status == 2}
          defaultValue={dom ? dom : 0}
          onChange={(e) => {
            // onChange(e, index, schema.dataIndex);
            const sum = JSON.parse(JSON.stringify(timeDataSource));
            sum[index].num = e;
            sum[index].price = (e * sum[index].salePrice).toFixed(2);
            setTimeDataSource(sum);
          }}
        />
      ),
    },
    {
      title: '当前库存',
      dataIndex: 'number',
      align: 'right',
      valueType: 'digit',
    },
    {
      title: '单价（元）',
      dataIndex: 'salePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'price',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  // 创建订单数据
  const pushData = (dataSource: any) => {
    // 过滤空采购量数据
    const timeLaws: any = [];
    dataSource.timeLaws.forEach((e1: any) => {
      const list: any = [];
      e1.timeShareDateList.forEach((e2: any) => {
        if (e2.purchaseNum) list.push(e2);
      });
      if (list.length) timeLaws.push({ ...e1, timeShareDateList: list });
    });
    return {
      batchId: dataSource.batchId,
      groupId: dataSource.groupId, // 2024 年 8 月 9 日后端要求添加
      goodsId: dataSource?.goodsId, // 商品 id
      num: dataSource.num, // 进货数量
      timeLaws,
    };
  };

  // 一键复制弹窗
  const [useVisible, setUseVisible] = useState<any>();
  const [copyDateList, setCopyDateList] = useState<any>([]);
  function disabledDate(current: any) {
    return (
      copyDateList.filter((item: any) => dayjs(item).format('L') == current.format('L')).length == 0
    );
  }
  const onChangeDate = (_, e: any) => {
    selectDateList = [];
    const startDate = dayjs(e[0]);
    const endDate = dayjs(e[1]);
    while (endDate > startDate || startDate.format('D') === endDate.format('D')) {
      selectDateList.push(startDate.format('YYYY-MM-DD'));
      startDate.add(1, 'day');
    }
  };

  // 支付数据绑定
  const [payVisible, setPayVisible] = useState<boolean>(false);
  const [payDataSource, setPayDataSource] = useState<any>();
  const payTableColumns: ProColumns[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      valueEnum: productTypeEnum,
    },
    {
      title: '进货总量',
      dataIndex: 'num',
      align: 'right',
      valueType: 'digit',
    },
    {
      title: '进货总价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      render: (_, entity: any) => (entity.productPrice * entity.num).toFixed(2),
    },
  ];
  const submit = async () => {
    setIsClick(false);
    const productDistribute: any = [];
    dataSource.map((dataSource: any) => {
      if (dataSource.num) {
        productDistribute.push(pushData(dataSource));
      }
    });
    if (productDistribute.length == 0) {
      message.info('无提交数据！');
      setTimeout(() => {
        setIsClick(true);
      }, 500);
      return;
    }
    try {
      const { data, msg } = await apiOrderInfo({
        buyerCoId: coId,
        productDistribute,
        returnUrl: window.location.href,
        sellerCoId: sellerId,
      });
      if (!data && msg) {
        message.warning(msg);
        return;
      }
      // 更新引导
      updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_4 });
      addOperationLogRequest({
        action: 'add',
        content: `新增【${data.orderId}】采购订单`,
      });
      message.success('提交成功');
      if (+payAmount !== 0) {
        // 普通支付
        window.location.href = `${getEnv().PAY_HOST}?tradeNo=${data.tradeNo}`;
      }
      setIsClick(true);
      buyed();
    } catch (error) {
      console.log(error);
      setIsClick(true);
    }
  };

  return (
    <>
      <div>
        {isTravel ? (
          ''
        ) : (
          <Tabs
            tabBarExtraContent={{ left: <div style={{ width: 16 }} /> }}
            tabBarStyle={{ margin: '0', background: '#fff' }}
            defaultActiveKey="common"
            items={[
              {
                label: '普通门票',
                key: 'common',
              },
              {
                label: '区块链门票',
                key: 'chain',
              },
            ]}
            onChange={(e: any) => {
              console.log(e);

              setIsChain(e == 'chain' ? 1 : 0);
            }}
          />
        )}
        <br />
        {/* 进货表格 */}
        <ProTable
          {...tableConfig}
          formRef={actionRef}
          rowKey="id"
          columns={tableColumns}
          columnEmptyText={false}
          params={{ distributorId: coId, isChain, goodsName: initialName }}
          request={async (e: any) => {
            const formValues: any = actionRef.current?.getFieldsValue();
            setIsTravel(e.productType == 2);
            if (e.productType == 2 && isChain == 1) {
              setIsChain(0);
              return;
            }
            setDataSource([]);
            setPayCount(0);
            setPayAmount(0);
            isBC = isChain;
            if (e.supplierId) {
              const { data } = await apiSupplierTicketList({
                ...e,
                goodsName: formValues?.goodsName,
                productType: 1,
              });
              data.map((item: any) => {
                item.id = getUniqueId();
              });
              setSupplierName(supplierNameCopy);
              setDataSource(data);
            }
          }}
          pagination={false}
          dataSource={dataSource}
          form={{
            ignoreRules: false,
          }}
          onReset={() => {
            setIsTravel(false);
          }}
          onSubmit={(val: any) => {
            isBC = isChain;
            for (const item of supplierList) {
              if (item.value == val.supplierId) {
                sellerId = item.value;
                sellerName = item.label;
                break;
              }
            }
          }}
          expandable={{
            expandedRowRender: (record: any, indexTable: number) => (
              <>
                <span style={{ padding: '8px', display: 'block' }}>入园时段及分时预约信息：</span>
                <List
                  style={{
                    padding: '4px 8px',
                    maxHeight: '300px',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                  }}
                  grid={{
                    gutter: 16,
                    xs: 1,
                    sm: 2,
                    md: 4,
                    lg: 4,
                    xl: 5,
                    xxl: 8,
                  }}
                  dataSource={record.enterDateList}
                  renderItem={(item: any, indexList: number) => (
                    <List.Item className="cardBox">
                      <Card
                        title={item.enterStartTime}
                        hoverable
                        size="small"
                        actions={[
                          <span
                            key="span1"
                            onClick={() => {
                              // 设置序列
                              setTimeIndex([indexTable, indexList]);
                              // 修改分时信息
                              setTimeDataSource(item.detail);
                              setTimeVisible(true);
                            }}
                          >
                            <EditOutlined key="edit" /> 编辑
                          </span>,
                          <span
                            key="span2"
                            onClick={() => {
                              // 设置序列
                              setTimeIndex([indexTable, indexList]);
                              const sum: any = [];
                              console.log(indexList, record.enterDateList);
                              record.enterDateList.map((item: any, index: any) => {
                                if (index != indexList) sum.push(item.enterStartTime);
                              });
                              selectDateList = [];
                              setCopyDateList(sum);
                              setUseVisible(true);
                            }}
                          >
                            <SettingOutlined key="setting" /> 应用
                          </span>,
                        ]}
                      >
                        <p>数量：{item.num ? item.num : 0}</p>
                        <p>金额：￥ {item.price ? item.price : 0}</p>
                      </Card>
                    </List.Item>
                  )}
                />
              </>
            ),
            rowExpandable: (record: any) => record?.enterDateList?.length !== 0,
          }}
          summary={(pageData) => {
            let totalBorrow = 0;
            let totalRepayment = 0;

            pageData.forEach(({ borrow, repayment }) => {
              totalBorrow += borrow;
              totalRepayment += repayment;
            });

            return (
              <Table.Summary>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>总计</Table.Summary.Cell>
                  {new Array(9).fill(null).map((_, i) => {
                    //把空的位置全填上，不然 fixed left right 对不齐
                    return <Table.Summary.Cell index={i + 1} />;
                  })}
                  <Table.Summary.Cell index={10}>{payCount}</Table.Summary.Cell>
                  <Table.Summary.Cell index={11}>{payAmount}</Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            );
          }}
        />

        {/* 分时预约详情弹窗 */}
        <Modal
          width={1200}
          title={'分时预约信息'}
          open={timeVisible}
          destroyOnClose
          okText="确定"
          cancelText="取消"
          onCancel={() => {
            setTimeVisible(false);
          }}
          onOk={async () => {
            // 记录分时配置
            const ListObj = dataSource[timeIndex[0]].enterDateList[timeIndex[1]];
            ListObj.detail = timeDataSource;
            // 设置入园时段信息
            ListObj.num = 0;
            ListObj.price = 0;
            timeDataSource.map((item: any) => {
              if (item.price) {
                ListObj.num += item.num;
                ListObj.price += item.price * 1;
              }
            });
            // 设置产品信息
            dataSource[timeIndex[0]].num = 0;
            dataSource[timeIndex[0]].price = 0;
            dataSource[timeIndex[0]].enterDateList.map((item: any) => {
              if (item.num != undefined) {
                dataSource[timeIndex[0]].num += item.num;
                dataSource[timeIndex[0]].price += parseFloat((item.price * 1).toFixed(2));
              }
            });
            setDataSource(dataSource);
            // 总计
            let allPrice = 0;
            dataSource.map((item: any) => {
              if (item.price) allPrice += item.price * 1;
            });
            setPayAmount(allPrice.toFixed(2));
            setTimeVisible(false);
          }}
        >
          <ProTable
            rowKey="distributorTicketStockId"
            headerTitle={dataSource?.[timeIndex[0]]?.enterDateList[timeIndex[1]]?.enterStartTime}
            columns={timeTableColumns}
            columnEmptyText={false}
            dataSource={timeDataSource}
            search={false}
            options={false}
          />
        </Modal>

        {/* 应用 */}
        <Modal
          // width={1200}
          title={'应用于其他日期'}
          open={useVisible}
          destroyOnClose
          okText="确定"
          cancelText="取消"
          onCancel={() => {
            setUseVisible(false);
          }}
          onOk={async () => {
            if (selectDateList.length == 0) {
              message.info('请选择目标日期');
            } else {
              // 源日期
              const sourceDate = dataSource[timeIndex[0]].enterDateList[timeIndex[1]].detail;

              dataSource[timeIndex[0]].enterDateList.map((item: any) => {
                // 目标日期
                if (
                  selectDateList.filter((itemS: any) => itemS == item.enterStartTime).length > 0
                ) {
                  console.log(item.enterStartTime);
                  sourceDate.map((itemD1: any) => {
                    item.detail.map((itemD2: any) => {
                      // 对号入座
                      if (
                        itemD2.timeBeginTime == itemD1.timeBeginTime &&
                        itemD2.timeEndTime == itemD1.timeEndTime
                      ) {
                        itemD2.num = itemD1.num;
                        itemD2.price = itemD1.price;
                      }
                    });
                  });
                }
              });

              // 小计
              dataSource[timeIndex[0]].enterDateList.map((ListObj: any) => {
                // 设置入园时段信息
                ListObj.num = 0;
                ListObj.price = 0;
                ListObj.detail.map((item: any) => {
                  if (item.price) {
                    ListObj.num += item.num;
                    ListObj.price += item.price * 1;
                  }
                });
                ListObj.price = parseFloat((ListObj.price * 1).toFixed(2));
                // 设置产品信息
                dataSource[timeIndex[0]].num = 0;
                dataSource[timeIndex[0]].price = 0;
                dataSource[timeIndex[0]].enterDateList.map((item: any) => {
                  if (item.num != undefined) {
                    dataSource[timeIndex[0]].num += item.num;
                    dataSource[timeIndex[0]].price += parseFloat((item.price * 1).toFixed(2));
                  }
                });
                dataSource[timeIndex[0]].price = parseFloat(
                  (dataSource[timeIndex[0]].price * 1).toFixed(2),
                );
              });

              // 总计
              let allPrice = 0;
              dataSource.map((item: any) => {
                if (item.price) allPrice += item.price * 1;
              });
              setDataSource(dataSource);
              setPayAmount(allPrice.toFixed(2));
              setUseVisible(false);
            }
          }}
        >
          <DatePicker.RangePicker disabledDate={disabledDate} onChange={onChangeDate} />
        </Modal>

        {/* 支付确定弹窗 */}
        <Modal
          width={modelWidth.lg}
          title={'订单信息'}
          open={payVisible}
          destroyOnClose
          onCancel={() => {
            setPayVisible(false);
            setIsClick(true);
            buyed();
          }}
          okText="确认"
          cancelText="取消"
          onOk={async () => {
            setConfirmLoading(true);
            try {
              // 区块链支付
              await apiBcPay({ orderId: payOrderId });
              setConfirmLoading(false);
              // 支付成功
              message.success('支付成功');
              setPayVisible(false);
              setIsClick(true);
              setVisible(false);
              // 成功回调
              buyed();
              // actionRef?.current?.reload();
            } catch (e) {
              setConfirmLoading(false);
            }
          }}
          confirmLoading={confirmLoading}
        >
          <ProTable
            rowKey="distributorTicketStockId"
            headerTitle="请确认！订单一经确认将直接扣除区块链额度"
            columns={payTableColumns}
            dataSource={payDataSource}
            search={false}
            options={false}
          />
          <span style={{ display: 'flex', justifyContent: 'end', marginRight: '24px' }}>
            {/* 总计：{payAmount} 元 */}
            总计：{payAmount} 元
          </span>
        </Modal>
      </div>
      {/* 商品列表 */}
      <CommodityList
        listVisible={listVisible}
        setListVisible={setListVisible}
        product={product}
        isChain={isChain}
        isTravel={isTravel}
      />
      <div
        className="flex w-100 justify-content-center align-items-center"
        style={{
          position: 'sticky',
          height: 72,
          backgroundColor: 'white',
          bottom: 0,
          zIndex: 2,
          boxShadow: '0px -2px 9px -1px rgba(208,208,208,0.5)',
        }}
      >
        <Space>
          <Button onClick={close} key="1">
            取消
          </Button>
          <Button type="primary" onClick={submit} key="2">
            提交采购
          </Button>
        </Space>
      </div>
      {/* 分时库存 */}
      <ProModal
        {...modalState}
        fullTitle="分时预约采购数量"
        columns={modalColumns}
        layout="horizontal"
        dataSource={modalData}
        onFinish={async (v) => {
          setDataSource((list: any) => {
            list[dataIndex] = { ...list[dataIndex], ...v };
            setTotal(list);
            return list;
          });
          return true;
        }}
      />
      {/* 设置库存 */}
      <ProModal
        {...modalSetState}
        fullTitle="编辑采购数量"
        columns={modalSetColumns}
        layout="horizontal"
        dataSource={modalSetData}
        onFinish={async (v) => {
          const obj = structuredClone(valueObj);
          let dateList = [];
          if (v.dateType == 'date') {
            dateList = v.dateList;
          } else {
            let nowDate = dayjs(v.dateRange[0]);
            do {
              if (v.dateWeek.includes(String(nowDate.day()))) {
                dateList.push(nowDate.format('YYYY-MM-DD'));
              }
              nowDate = nowDate.add(1, 'day');
            } while (!nowDate.isAfter(dayjs(v.dateRange[1])));
          }
          dateList.forEach((d: string) => {
            v.timeShareId.forEach((t: string, i: number) => {
              if (obj[d]?.[t]) {
                obj[d][t].purchaseNum = timeTableData[i].purchaseNum;
                obj[d][t].purchaseAmount = timeTableData[i].purchaseAmount;
              }
            });
          });
          let [num, price] = [0, 0];
          const timeLaws = Object.keys(obj).map((item1) => ({
            timeShareData: item1,
            timeShareDateList: Object.values(obj[item1]),
          }));
          console.log(timeLaws);

          timeLaws.forEach(({ timeShareDateList }: any) => {
            timeShareDateList.forEach(({ purchaseNum, purchaseAmount }: any) => {
              if (purchaseNum) num += purchaseNum;
              if (purchaseAmount) price += +purchaseAmount;
            });
          });
          setModalData((v: any) => ({
            ...v,
            timeLaws,
            num,
            price,
          }));
          return true;
        }}
      />
    </>
  );
};

export default Purchase;
