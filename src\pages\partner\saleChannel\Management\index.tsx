import { Tabs } from 'antd';
import { createContext, useState } from 'react';
import { useAccess } from '@umijs/max';
import Agent from './components/Agent';
import Distributor from './components/Distributor';

export const saleMangeTabItems = [
  {
    label: '全部代理商',
    key: 'all-agency',
  },
  {
    label: '全部经销商',
    key: 'all-sell',
  },
];

export const SaleMangeTabKeyContext = createContext<string>('all-agency');

export default () => {
  const access = useAccess();
  const [tabKey, setTabKey] = useState('all-agency');

  const menuItem = [
    ...(access.canAgentGroupManagement_agentSelect
      ? [
          {
            label: '全部代理商',
            key: 'all-agency',
            children: <Agent />,
          },
        ]
      : []),
    ...(access.canAgentGroupManagement_dealerSelect
      ? [
          {
            label: '全部经销商',
            key: 'all-sell',
            children: <Distributor />,
          },
        ]
      : []),
  ];
  return (
    <SaleMangeTabKeyContext.Provider value={tabKey}>
      <Tabs
        tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
        items={menuItem}
        onChange={(active) => {
          setTabKey(active);
        }}
      />
    </SaleMangeTabKeyContext.Provider>
  );
};
