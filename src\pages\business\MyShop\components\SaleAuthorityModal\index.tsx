import { tableConfig } from '@/common/utils/config';
import { GuideStepStatus } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getHashParams, removeStateFromUrl } from '@/common/utils/tool';
import { useGuide } from '@/hooks/useGuide';
import type { ModalState } from '@/hooks/useModal';
import { getCoAllUsers } from '@/services/api/cas';
import { getShopPermissionList, updateStoreAuthorize } from '@/services/api/store';
import type { API } from '@/services/api/typings';
import { ProFormSelect } from '@ant-design/pro-form';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useAccess, useModel, useRequest } from '@umijs/max';
import { Modal, Switch, message } from 'antd';
import { isEmpty } from 'lodash';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';

interface FCNameProps {
  modalState: ModalState;
}

const FCName: FC<FCNameProps> = ({ modalState: { visible, setVisible } }) => {
  const access = useAccess();
  const { initialState } = useModel('@@initialState');
  const { coId }: any = initialState?.currentCompany || {};
  const queryParams = getHashParams();
  const [userId, setUserId] = useState('');

  const actionRef = useRef<ActionType>();
  const { updateGuideInfo } = useGuide();

  const getAllUsersReq = useRequest(getCoAllUsers, {
    manual: true,
    initialData: [],
    onSuccess(data, params) {
      if (data.length > 0) {
        setUserId(data[0].userId);
      }
    },
  });

  const [currentRow, setCurrentRow] = useState<API.ShopPermissionListItem>();

  const updateStoreAuthorizeReq = useRequest(updateStoreAuthorize, {
    manual: true,
    onSuccess(data, params) {
      message.success('修改成功');
      const { authorizeList = [], revokeList = [] } = params[0];

      const username = getAllUsersReq.data?.find((i) => i.userId === userId)?.username;

      const permissionType =
        authorizeList.length > 0 ? authorizeList[0].permissionType : revokeList[0].permissionType;

      const logList = [
        {
          title: `店铺【${currentRow?.name}】${
            permissionType === 1 ? '窗口售票权限' : '自助售票权限'
          }`,
          dataIndex: 'isEnable',
        },
      ];
      addOperationLogRequest({
        action: 'edit',
        changeConfig: {
          beforeData: {
            isEnable: isEmpty(authorizeList) ? '开' : '关',
          },
          afterData: {
            isEnable: isEmpty(authorizeList) ? '关' : '开',
          },
          list: logList,
        },
        content: `编辑员工【${username}】销售权限`,
      });

      // 更新引导
      updateGuideInfo({
        tabIndex: 0,
        status: permissionType === 1 ? GuideStepStatus.step0_5 : GuideStepStatus.step0_6,
      });

      actionRef.current?.reload();
    },
  });

  const tableReq = async (params) => {
    const { data } = await getShopPermissionList(params);

    return data;
  };

  const columns: ProColumnType<API.ShopPermissionListItem>[] = [
    {
      title: '序号',
      valueType: 'index',
    },
    {
      title: '店铺名称',
      dataIndex: 'name',
    },
    {
      title: '窗口售票权限',
      dataIndex: '1',
      renderText: (_, record) => {
        const { permissionType = [], id } = record;
        return (
          <Switch
            disabled={!access.canMyShop_editAuthority}
            checked={permissionType?.includes(1)}
            checkedChildren="开"
            unCheckedChildren="关"
            onChange={(val) => {
              setCurrentRow(record);
              updateStoreAuthorizeReq.run({
                [val ? 'authorizeList' : 'revokeList']: [
                  {
                    permissionType: 1,
                    storeId: id,
                  },
                ],
                userId,
              });
            }}
          />
        );
      },
    },
    {
      title: '自助售票权限',
      dataIndex: '2',
      renderText: (_, record) => {
        const { permissionType = [], id } = record;

        return (
          <Switch
            disabled={!access.canMyShop_editAuthority}
            checked={permissionType?.includes(2)}
            checkedChildren="开"
            unCheckedChildren="关"
            onChange={(val) => {
              setCurrentRow(record);
              updateStoreAuthorizeReq.run({
                [val ? 'authorizeList' : 'revokeList']: [
                  {
                    permissionType: 2,
                    storeId: id,
                  },
                ],
                userId,
              });
            }}
          />
        );
      },
    },
  ];

  useEffect(() => {
    getAllUsersReq.run({ companyId: coId });
  }, []);

  return (
    <Modal
      title="员工销售权限"
      open={visible}
      footer={false}
      width={modelWidth.md}
      onCancel={() => {
        if (queryParams?.operate) {
          history.pushState(null, null, removeStateFromUrl('operate'));
        }
        setVisible(false);
      }}
      closable
      destroyOnClose
    >
      <ProFormSelect
        showSearch
        allowClear={false}
        label="员工账号"
        formItemProps={{
          style: {
            width: 328,
          },
        }}
        fieldProps={{
          value: userId,
          onChange: (val) => {
            setUserId(val);
          },
          options: (getAllUsersReq.data || []).map((item: any) => ({
            label: item.username,
            value: item.userId,
          })),
        }}
      />
      <ProTable
        {...tableConfig}
        rowKey={'id'}
        actionRef={actionRef}
        columns={columns}
        params={{
          distributorId: coId,
          userId,
        }}
        options={false}
        search={false}
        request={tableReq}
      />
    </Modal>
  );
};

export default FCName;
