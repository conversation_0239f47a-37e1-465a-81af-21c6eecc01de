/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-24 16:22:58
 * @LastEditTime: 2023-05-30 11:27:14
 * @LastEditors: zhangfengfei
 */
import ChainModal from '@/common/components/ChainModal';
import Tags from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import { orderTypeEnum, payTypeEnum } from '@/common/utils/enum';
import useModal from '@/hooks/useModal';
import type { API } from '@/services/api/typings';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Tag } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useState } from 'react';
import styles from './index.less';

interface RefundOrderTableProps {
  dataItem: API.OrderAndOrderRefundListItem;
}

const RefundOrderTable: FC<RefundOrderTableProps> = ({ dataItem: { orderRefund, buyerName } }) => {
  const refundDetailModal = useModal();

  const [currentRow, setCurrentRow] = useState<API.OrderRefundItem>();

  const columns: ProColumns<API.OrderRefundItem>[] = [
    {
      dataIndex: 'refundId',
      width: 250,
      fixed: 'left',
    },
    {
      dataIndex: 'buyerName',
      renderText: () => buyerName || '-',
      width: 150,
    },
    {
      title: '交易类型',
      renderText: () => '退货',
      width: 150,
    },
    {
      title: '结算单号',
      width: 220,
      dataIndex: 'tradeNo',
    },
    {
      dataIndex: 'createTime',
      width: 200,
      renderText: (dom: any) => dayjs(dom).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      width: 200,
      valueEnum: payTypeEnum,
      hideInSearch: true,
    },
    {
      dataIndex: 'purchaseQuantity',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '总金额（元）',
      dataIndex: 'refundAmount',
      width: 120,
      renderText: (text) => (text ? `-${parseFloat(text).toFixed(2)}` : text),
    },

    {
      dataIndex: 'username',
      width: 150,
    },
    {
      dataIndex: 'refundStatus',
      width: 100,
      valueEnum: orderTypeEnum,
      render: (dom: any) => {
        return <Tag color="blue">{dom}</Tag>;
      },
    },
    {
      title: '交易上链',
      dataIndex: 'isChainOrderRefund',
      fixed: 'right',
      width: 100,
      renderText: (dom) => <Tags type="chainStatus" value={dom} />,
    },
    {
      title: '操作',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (dom: any, record) => {
        return record.txId ? (
          <ChainModal
            chainData={{
              txId: record.txId,
            }}
          />
        ) : (
          '-'
        );
      },
    },
  ];

  return (
    <ProTable<API.OrderRefundItem>
      {...tableConfig}
      tableLayout="fixed"
      rowKey="refundId"
      className={styles.table}
      columns={columns}
      dataSource={orderRefund}
      showHeader={false}
      options={false}
      search={false}
      pagination={false}
    />
  );
};

export default RefundOrderTable;
