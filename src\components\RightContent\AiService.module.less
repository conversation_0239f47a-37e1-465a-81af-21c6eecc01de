.drawer {
  :global {
    .ant-drawer-body {
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: 0;
    }
  }
}

.chatContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
}

.messageList {
  flex: 1;
  margin: 0;
  padding: 0;
  overflow-y: auto;

  :global {
    .ant-list-item {
      padding: 8px 0;
      border: none;
    }
  }
}

.messageItem {
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.userMessage {
  justify-content: flex-end;
}

.aiMessage {
  justify-content: flex-start;
}

.messageContent {
  position: relative;
  max-width: 100%;
  padding: 8px 12px;
  border-radius: 12px;
  animation: fadeIn 0.3s ease;
}

.userMessage .messageContent {
  margin-left: auto;
  color: white;
  background: #1890ff;
  border-radius: 12px 12px 0 12px;
}

.aiMessage .messageContent {
  margin-right: auto;
  color: rgba(0, 0, 0, 0.85);
  background: #f5f5f5;
  border-radius: 12px 12px 12px 0;
}

.messageText {
  // white-space: pre-wrap;
  word-break: break-word;
}

.messageTime {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
}

.userMessage .messageTime {
  color: rgba(255, 255, 255, 0.7);
}

.aiMessage .messageTime {
  color: rgba(0, 0, 0, 0.45);
}

.inputContainer {
  margin-top: 10px;

  .newMsg {
    margin-bottom: 10px;
  }

  .botIpt {
    display: flex;
    gap: 8px;
    align-items: flex-end;
  }
}

.input {
  flex: 1;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  resize: none;

  &:focus {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.sendButton {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 16px;
}

@keyframes fadeIn {
  from {
    transform: translateY(10px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.aiServiceIcon {
  width: 24px;
  margin-right: 12px;
  cursor: pointer;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16px;
  // border-bottom: 1px solid #f0f0f0;

  .title {
    font-weight: 500;
    font-size: 16px;
  }

  .close {
    color: rgba(0, 0, 0, 0.45);
    font-size: 16px;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: rgba(0, 0, 0, 0.75);
    }
  }
}

.iconContainer {
  display: flex;
  align-items: center;
  height: 100%;
}

.thinkingAnimation {
  display: flex;
  gap: 4px;
  align-items: center;
  padding: 4px 0;
}

.dot {
  width: 8px;
  height: 8px;
  background-color: rgba(0, 0, 0, 0.45);
  border-radius: 50%;
  animation: thinking 1.4s infinite ease-in-out;

  &:nth-child(1) {
    animation-delay: 0s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

@keyframes thinking {

  0%,
  80%,
  100% {
    transform: scale(0.6);
    opacity: 0.6;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}