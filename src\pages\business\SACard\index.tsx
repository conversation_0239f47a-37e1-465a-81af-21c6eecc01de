import ExportButton from '@/common/components/ExportButton';
import useExport from '@/common/components/ExportButton/useExport';
import useModal from '@/common/components/ProModal/useProModal';
import { columnsState, tableConfig } from '@/common/utils/config';
import { SACardApproveStateEnum, SACardBatchStateEnum } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getUniqueId } from '@/common/utils/tool';
import {
  batchCardApproveAuthorize,
  batchCardPage,
  batchCardUpdateApproveState,
  batchCardinfo,
} from '@/services/api/saCard';
import { apiShopList } from '@/services/api/store';
import { PlusOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-form';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useRequest } from 'ahooks';
import { Button, Modal, message } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { Access, history, useModel, useRequest as useUmiRequest } from '@umijs/max';

interface SACardBatchEntity {
  accountName: string;
  approveState: number;
  createTime: string;
  id: string;
  nickName: string;
  sendCardBatchId: string;
  sendCardNumber: number;
  storeId: string;
  storeName: string;
  travelCardGoodsId: string;
  travelCardGoodsName: string;
}

interface DetailEntity {
  buyerCompanyName: string;
  buyerIdentity: string;
  buyerName: string;
  buyerPhone: string;
  checkResult: string;
  groupOrderId: string;
  id: string;
  sendCardBatchId: string;
  status: number;
  travelCardGoodsId: string;
  travelCardGoodsName: string;
}

/*
 * @Author: bankewei
 * @Date: 2023 年 12 月 21 日
 */
export default () => {
  const { initialState } = useModel('@@initialState');
  const { coId }: any = initialState?.currentCompany || {};

  const modalState = useModal();
  const actionRef = useRef<ActionType>();
  const actionRef2 = useRef<ActionType>();
  const [detailVisible, setDetailVisible] = useState(false); //详情显示
  const [searchBatchId, setSearchBatchId] = useState(''); //查看详细的批次 ID

  const tableColumns: ProColumnType[] = [
    {
      title: '序号',
      valueType: 'index',
    },
    {
      title: '发卡批次号',
      dataIndex: 'sendCardBatchId',
    },
    {
      title: '账号名称',
      dataIndex: 'accountName',
    },
    {
      title: '昵称',
      dataIndex: 'nickName',
    },
    {
      title: '店铺名称',
      dataIndex: 'storeName',
      hideInSearch: true,
    },
    {
      title: '店铺名称',
      dataIndex: 'storeId',
      hideInTable: true,
      renderFormItem: () => (
        <ProFormSelect
          options={getStoreInfoRequest.data}
          fieldProps={{
            labelInValue: true,
            loading: getStoreInfoRequest.loading,
          }}
        />
      ),
      search: {
        transform: (value) => {
          return { storeId: value.key };
        },
      },
    },
    {
      title: '权益卡名称',
      dataIndex: 'travelCardGoodsName',
    },
    {
      title: '发卡数量',
      dataIndex: 'sendCardNumber',
      hideInSearch: true,
    },
    {
      title: '提交时间',
      valueType: 'dateTime',
      dataIndex: 'createTime',
      hideInSearch: true,
    },
    {
      title: '提交时间',
      key: 'time',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startCreateTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            endCreateTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '审核状态',
      dataIndex: 'approveState',
      valueEnum: SACardApproveStateEnum,
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_: any, entity: any) => [
        <a
          onClick={() => {
            setDetailVisible(true);
            setSearchBatchId(entity.sendCardBatchId);
          }}
          key="k1"
        >
          查看
        </a>,
        <Access
          key={getUniqueId()}
          accessible={batchAuthorizeRequest.data && entity.approveState == 1}
        >
          <a
            onClick={() => {
              setSearchBatchId(entity.sendCardBatchId);
              updateApproveStateRequest.run({
                approveState: 2,
                id: entity.id,
              });
            }}
            key="k2"
          >
            通过
          </a>
        </Access>,
        <Access
          key={getUniqueId()}
          accessible={batchAuthorizeRequest.data && entity.approveState == 1}
        >
          <a
            style={{ color: 'red' }}
            onClick={() => {
              setSearchBatchId(entity.sendCardBatchId);
              updateApproveStateRequest.run({
                approveState: 3,
                id: entity.id,
              });
            }}
            key="k3"
          >
            驳回
          </a>
        </Access>,
      ],
    },
  ];

  const detailColumns: ProColumnType[] = [
    {
      title: '序号',
      valueType: 'index',
    },
    {
      title: '发卡批次号',
      dataIndex: 'sendCardBatchId',
      hideInSearch: true,
    },
    {
      title: '订单号',
      dataIndex: 'groupOrderId',
    },
    {
      title: '子订单号',
      dataIndex: 'orderId',
    },
    // {
    //   title: '权益卡号',
    //   dataIndex: 'travelCardGoodsId',
    // },
    {
      title: '权益卡名称',
      dataIndex: 'travelCardGoodsName',
      hideInSearch: true,
    },
    {
      title: '姓名',
      dataIndex: 'buyerName',
    },
    {
      title: '身份证号',
      dataIndex: 'buyerIdentity',
    },
    {
      title: '手机号',
      dataIndex: 'buyerPhone',
    },
    {
      title: '所属企业',
      dataIndex: 'buyerCompanyName',
      hideInSearch: true,
    },
    // {
    //   title: '校验结果',
    //   dataIndex: 'checkResult',
    //   hideInSearch: true,
    // },
    {
      title: '订单状态',
      dataIndex: 'status',
      hideInSearch: true,
      valueEnum: SACardBatchStateEnum,
    },
  ];

  /** 是否有权限审批 */
  const batchAuthorizeRequest = useRequest(batchCardApproveAuthorize, {
    defaultParams: [{ coId: coId }],
    throttleWait: 1000, //节流
  });

  /** 获取店铺信息 */
  const getStoreInfoRequest = useUmiRequest(apiShopList, {
    defaultParams: [{ id: coId, pageSize: 999 }],
    formatResult({ data }: any) {
      if (data.length) {
        return data.map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
      } else {
        return [];
      }
    },
  });

  /** 修改审核状态 */
  const updateApproveStateRequest = useRequest(batchCardUpdateApproveState, {
    manual: true,
    throttleWait: 2000, //节流
    onSuccess(result: any, params) {
      if (result) {
        addOperationLogRequest({
          action: 'audit',
          content: `审核${
            params[0].approveState == 2 ? '通过' : '驳回'
          }发卡批次【${searchBatchId}】`,
        });

        message.success('操作成功');
        actionRef.current?.reload();
      }
    },
    onError() {},
  });
  const exportState = useExport('/batch/card/info', detailColumns);

  return (
    <>
      <ProTable
        {...tableConfig}
        rowKey="id"
        style={modalState.tableStyle}
        actionRef={actionRef}
        columns={tableColumns}
        toolBarRender={() => [
          <Button
            key="k1"
            type="primary"
            onClick={() => {
              history.push('/business/saCardManageAdd');
            }}
          >
            <PlusOutlined /> 批量导入
          </Button>,
        ]}
        request={(params) =>
          batchCardPage({
            companyIds: coId,
            ...params,
          })
        }
      />
      <Modal
        title="详情"
        width={modelWidth.xl}
        destroyOnClose={true}
        open={detailVisible}
        onCancel={() => {
          setDetailVisible(false);
        }}
        footer={null}
      >
        <ProTable
          {...tableConfig}
          style={modalState.tableStyle}
          actionRef={actionRef2}
          columns={detailColumns}
          request={async (params) => {
            const result = await batchCardinfo(params);
            addOperationLogRequest({
              action: 'info',
              content: `查看发卡批次【${searchBatchId}】详情`,
            });
            return result;
          }}
          formRef={exportState.formRef}
          columnsState={columnsState(exportState)}
          toolBarRender={() => [<ExportButton {...{ exportState }} />]}
          pagination={{
            showQuickJumper: true,
            pageSize: 10,
          }}
          params={{ sendCardBatchId: searchBatchId }}
        />
      </Modal>
    </>
  );
};
