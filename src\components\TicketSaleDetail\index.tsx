/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-21 17:05:19
 * @LastEditTime: 2023-07-14 18:15:12
 * @LastEditors: zhangfengfei
 */
import ExportButton from '@/common/components/ExportButton';
import useExport from '@/common/components/ExportButton/useExport';
import { columnsState, tableConfig } from '@/common/utils/config';
import { productTypeEnum, ticketTypeEnum, whetherEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import type { ModalState } from '@/hooks/useModal';
import { getOrderGoodsDetails, getOrderProDetails } from '@/services/api/ticket';
import type { API } from '@/services/api/typings';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useState } from 'react';
const { Paragraph, Text } = Typography;

interface TicketSaleDetailProps {
  modalState: ModalState;
  order?: Record<string, any>;
  /** 采购订单 | 销售订单 */
  type: 'purchase' | 'sale';
}

const TicketSaleDetail: FC<TicketSaleDetailProps> = ({
  modalState: { visible, setVisible },
  type,
  order,
}) => {
  const { orderId } = order || {};

  const [sumDiffPrice, setSumDiffPrice] = useState<string>();

  const productTableReq = async (params: any) => {
    const { data } = await getOrderProDetails(params);
    addOperationLogRequest({
      action: 'info',
      content: `查看【${orderId}】售票详情`,
    });
    return data;
  };
  const goodsTableReq = async (params: any) => {
    const { data } = await getOrderGoodsDetails(params);
    setSumDiffPrice(data.sumDifferencesPrice);
    return data;
  };

  const onCancel = () => {
    setVisible(false);
  };

  const productColumns: ProColumns<API.OrderProDetailItem>[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      valueEnum: productTypeEnum,
    },

    {
      title: type === 'purchase' ? '面向用户售票数' : '下级经销商售票数',
      dataIndex: 'sellNumber',
      align: 'right',
      valueType: 'digit',
    },
    {
      title: '进货总量',
      dataIndex: 'num',
      align: 'right',
      valueType: 'digit',
    },
    {
      title: '进货单价（元）',
      dataIndex: 'purchasePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '进货金额（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  const goodsColumns: ProColumns<API.OrderGoodsDetailItem>[] = [
    {
      title: '商品名称',
      dataIndex: 'productSkuName',
    },
    {
      title: '票种',
      dataIndex: 'ticketType',
      valueEnum: ticketTypeEnum,
    },
    {
      title: '数字资产',
      dataIndex: 'isChainTicket',
      valueEnum: whetherEnum,
    },
    {
      title: '分时时段',
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList?.length
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '售票时间',
      dataIndex: 'createTime',
      renderText: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '是否退票',
      dataIndex: 'ticketStatus',
      valueEnum: {
        0: '-',
        1: '否',
        2: '是',
      },
    },
    {
      title: '补差价（元）',
      dataIndex: 'differencesPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '定价（元）',
      dataIndex: 'definePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];
  const exportState = useExport('/order/orderPurchaseGoodsPageList', goodsColumns, {
    orderId,
    distributionType: type === 'purchase' ? '1' : '2',
    moduleFlag: type === 'purchase' ? '1' : '2',
  });

  return (
    <Modal
      title="售票详情"
      width={1000}
      visible={visible}
      onCancel={onCancel}
      footer={false}
      destroyOnClose
    >
      <ProTable<API.OrderProDetailItem, any>
        {...tableConfig}
        headerTitle={type === 'purchase' ? '进货信息' : '出货信息'}
        columns={productColumns}
        request={productTableReq}
        params={{ orderId, distributionType: type === 'purchase' ? '1' : '2' }}
        pagination={{
          defaultPageSize: 10,
        }}
        search={false}
      />
      <ProTable<API.OrderGoodsDetailItem, any>
        {...tableConfig}
        headerTitle={'售票记录'}
        columns={goodsColumns}
        request={goodsTableReq}
        params={{ orderId, distributionType: type === 'purchase' ? '1' : '2' }}
        pagination={{
          defaultPageSize: 10,
        }}
        search={false}
        style={{ marginTop: 24 }}
        formRef={exportState.formRef}
        columnsState={columnsState(exportState)}
        toolBarRender={() => [<ExportButton {...{ exportState }} />]}
      />
      <Paragraph style={{ textAlign: 'right' }}>
        <Text strong style={{ display: 'inline-block', paddingRight: 24 }}>
          补差总额：{sumDiffPrice || '-'} 元
        </Text>
      </Paragraph>
    </Modal>
  );
};

export default TicketSaleDetail;
