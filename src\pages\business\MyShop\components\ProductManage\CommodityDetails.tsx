import DetailsPop from '@/common/components/DetailsPop';
import CheckRuleDetails from '@/common/components/RuleDetails/CheckRuleDetails';
import IssueRuleDetails from '@/common/components/RuleDetails/IssueRuleDetails';
import RetreatRuleDetails from '@/common/components/RuleDetails/RetreatRuleDetails';
import { productTypeEnum, ticketTypeEnum, whetherEnum } from '@/common/utils/enum';
import { getPriceStrategyByAgent } from '@/services/api/distribution';
import { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';

const Detail = ({ id, supplierId, priceId }: any) => {
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });

  const columnsInitial = [
    {
      title: '基础信息',
      columns: [
        {
          title: '景区名称',
          dataIndex: 'scenicName',
        },
        {
          title: '供应商名称',
          dataIndex: 'supplierName',
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
        },
        {
          title: '商品名称',
          dataIndex: 'goodsName',
        },
        {
          title: '票种',
          dataIndex: 'goodsType',
          valueEnum: ticketTypeEnum,
        },
        {
          title: '类型',
          dataIndex: 'proType',
          valueEnum: productTypeEnum,
        },
        {
          title: '分时预约',
          dataIndex: 'timeShareBeginTime',
          renderText: (dom: any, entity: any) =>
            dom ? entity.timeShareBeginTime + ' - ' + entity.timeShareEndTime : '-',
        },
        {
          title: '购买有效时间',
          dataIndex: 'purchaseBeginTime',
          renderText: (dom: any, entity: any) =>
            dom ? entity.purchaseBeginTime + ' 至 ' + entity.purchaseEndTime : '-',
        },
        {
          title: '数字资产',
          dataIndex: 'isDigit',
          valueEnum: whetherEnum,
        },
      ],
    },
    {
      title: '价格信息',
      columns: [
        {
          title: '用户单独购买价格',
          dataIndex: ['price', 'price', 'salePrice'],
          renderText: (dom: any) => <span>￥ {dom}</span>,
        },
        {
          title: '套餐中用户购买单品价格',
          dataIndex: ['price', 'price', 'composePrice'],
          renderText: (dom: any) => <span>￥ {dom}</span>,
        },
        {
          title: '佣金比例',
          dataIndex: ['price', 'price', 'commissionRate'],
          renderText: (dom: any) => <span>{dom} %</span>,
        },
      ],
    },
    {
      title: '规则信息',
      columns: [
        {
          title: '出票规则',
          dataIndex: 'issueName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                IssueRuleDetails.show(entity.issueId);
              }}
            >
              {dom}
            </a>
          ),
        },
        {
          hideInDescriptions: dataSource.proType == 20,
          title: '检票规则',
          dataIndex: 'checkName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                CheckRuleDetails.show(entity.checkId);
              }}
            >
              {dom}
            </a>
          ),
        },
        {
          hideInDescriptions: dataSource.proType == 20,
          title: '退票规则',
          dataIndex: 'retreatName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                RetreatRuleDetails.show(entity.retreatId);
              }}
            >
              {dom}
            </a>
          ),
        },
      ],
    },
    {
      title: '说明信息',
      columns: [
        {
          title: '备注',
          dataIndex: 'remark',
        },
      ],
    },
  ];
  const init = async (e: string, sId: string, pId: string) => {
    try {
      setLoading(true);
      setDetailsVisible(true);
      const { data } = await getPriceStrategyByAgent({
        unitId: e,
        distributorId: sId,
        priceId: pId,
      });
      setDataSource(data);
      setLoading(false);
    } catch (error) {}
  };
  useEffect(() => {
    init(id, supplierId, priceId);
  }, [id, supplierId, priceId]);

  return (
    <DetailsPop
      title="商品详情"
      visible={detailsVisible}
      isLoading={isLoading}
      setVisible={setDetailsVisible}
      columnsInitial={columnsInitial}
      dataSource={dataSource}
    />
  );
};

Detail.show = (id: string, supplierId: string, priceId: string) => {
  const detailBox = document.createElement('div');

  const root = createRoot(detailBox);
  root.render(<Detail id={id} supplierId={supplierId} priceId={priceId} />);
};

export default Detail;
