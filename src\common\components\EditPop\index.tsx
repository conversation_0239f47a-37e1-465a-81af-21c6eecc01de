/**
 * 新增/编辑 弹窗组件
 **/
import PrefixTitle from '@/common/components/PrefixTitle';
import { ModelWidth } from '@/common/utils/enum';
import type { ProFormInstance } from '@ant-design/pro-components';
import { BetaSchemaForm, ModalForm } from '@ant-design/pro-components';
import { Spin } from 'antd';
import React, { useEffect } from 'react';
import style from './index.module.less';

const EditPop = ({
  title,
  visible,
  setVisible,
  columns,
  dataSource,
  onFinish,
  onValuesChange,
  width,
  disabled = false,
  loading = false,
}: {
  title: string; // 标题
  visible: boolean; // 弹窗状态
  setVisible: any; // 设置弹窗状态
  columns: any[]; // 详情配置数据
  dataSource: any; // 默认数据
  onFinish?: any; // 确定回调
  onValuesChange?: any; // 数据变化回调
  width?: any; // 弹窗宽
  disabled?: boolean;
  loading?: boolean; // 加载状态
}) => {
  // 自动优化默认属性
  const newColumns = columns.map((item: any) => {
    if (item.valueType !== 'dependency') {
      const childColumns = (item.columns ?? []).map((i) => ({
        width: '100%',
        colProps: { xs: 24, sm: 12 },
        ...i,
      }));

      return {
        rowProps: { gutter: 24 },
        valueType: 'group',
        ...item,
        title: item.title && <PrefixTitle>{item.title} </PrefixTitle>,
        columns: childColumns,
      };
    }
    return item;
  });

  const formRef = React.useRef<ProFormInstance<any>>();
  // 初始化
  useEffect(() => {
    if (visible && dataSource?.id) {
      console.log(dataSource, 'dataSource');
      setTimeout(() => {
        console.log(1211212);
        formRef?.current?.setFieldsValue(dataSource);
      }, 50);
    }
  }, [visible, dataSource]);

  return (
    <ModalForm
      width={width || ModelWidth.md}
      formRef={formRef}
      open={visible}
      onOpenChange={setVisible}
      title={(dataSource?.id ? '编辑' : '新增') + title}
      modalProps={{
        maskClosable: false,
        destroyOnClose: true,
      }}
      preserve={false}
      onFinish={async (values) => {
        return await onFinish(values, formRef);
      }}
      onFinishFailed={() => {
        setImmediate(() => {
          document
            .querySelector('.ant-form-item-has-error')
            ?.scrollIntoView({ behavior: 'smooth', block: 'center' });
        });
      }}
      onValuesChange={(e) => {
        if (onValuesChange) onValuesChange(formRef, e);
      }}
      rowProps={{
        gutter: 24,
      }}
      grid
      className={style.formWrap}
      disabled={disabled}
    >
      <Spin spinning={loading}>
        <BetaSchemaForm layoutType="Embed" columns={newColumns} />
      </Spin>
    </ModalForm>
  );
};

export default EditPop;
