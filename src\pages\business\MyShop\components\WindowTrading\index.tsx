import ChainModal from '@/common/components/ChainModal';
import Export, { columnsSet } from '@/common/components/Export';
import useExport from '@/common/components/Export/useExport';
import Tag from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import {
  chainStatusEnum,
  payTypeEnum,
  productTypeEnum,
  ticketTypeEnum,
  tradeTypeEnum,
  whetherEnum,
} from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getCoAllUsers } from '@/services/api/cas';
import {
  apiSupplierList,
  apiWindowPaymentList,
  apiWindowTradeList,
  apiWindowsTradeDetail,
} from '@/services/api/store';
import { ProTable, type ActionType, type ProColumns } from '@ant-design/pro-components';
import type { ProFormInstance } from '@ant-design/pro-form';
import { useModel, useRequest } from '@umijs/max';
import { Col, Row, Tabs } from 'antd';
import Decimal from 'decimal.js';
import { uniqBy } from 'lodash';
import dayjs from 'dayjs';
import { useContext, useEffect, useRef, useState } from 'react';
import { TabKeyContext } from '../..';

const { TabPane } = Tabs;

export default ({ store: { value: storeId, label: name } }: any) => {
  const { initialState } = useModel('@@initialState');
  const { coId = '', coName } = initialState?.currentCompany || {};
  const [firstTabKey, setFirstTabKey] = useState('1');
  const [secondTabKey, setSecondTabKey] = useState('ticketing');

  const tabKey = useContext(TabKeyContext);

  const getAllUsersReq = useRequest(getCoAllUsers, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return res.data.map(({ nickname, username, userId }) => {
        return {
          value: userId,
          label: nickname || username || userId,
        };
      });
    },
  });

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<Record<string, any>>();

  const formRef = useRef<ProFormInstance>();

  //总价格
  const [count, setCount] = useState({
    priceTotal: 0,
    totalPeople: 0,
  });

  // 交易总览
  const overviewColumns: ProColumns<any, string>[] = [
    {
      title: '售票员昵称',
      dataIndex: 'userId',
      valueType: 'select',
      fieldProps: {
        options: getAllUsersReq.data,
        showSearch: true,
      },
      render: (_, { userId }) => {
        const current = getAllUsersReq.data?.find((i) => i.value === userId);
        return current?.label || '-';
      },
    },
    {
      title: '统计时间',
      dataIndex: 'date',
      valueType: 'dateTimeRange',
      initialValue: [dayjs().startOf('days'), dayjs().endOf('days')],
      search: {
        transform: (date) => {
          return {
            startTime: date[0],
            endTime: date[1],
          };
        },
      },
      hideInTable: true,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      hideInSearch: true,
      render: (_, record) => {
        return (
          <a
            onClick={() => {
              setCurrentRow({
                ...record,
                timeRange: formRef.current?.getFieldValue('date'),
              });
              setFirstTabKey('2');
              setSecondTabKey('ticketing');
            }}
          >
            <>查看明细</>
          </a>
        );
      },
    },
  ];

  //个人交易统计 - 按出票
  const ticketingColumns: ProColumns<any>[] = [
    {
      title: '店铺名称',
      dataIndex: 'name',
      hideInTable: true,
      fieldProps: {
        disabled: true,
        value: name,
      },
    },
    {
      title: '统计时间',
      dataIndex: 'times',
      valueType: 'dateTimeRange',
      hideInTable: true,
      initialValue: currentRow?.timeRange || [dayjs().startOf('days'), dayjs().endOf('days')],
      search: {
        transform: (value: string[]) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '售票员昵称',
      dataIndex: 'userId',
      initialValue: currentRow?.userId,
      valueType: 'select',
      fieldProps: {
        options: getAllUsersReq.data,
        showSearch: true,
      },
      render: (_, { userId }) => {
        const current = getAllUsersReq.data?.find((i) => i.value === userId);
        return current?.label || '-';
      },
    },
    {
      title: '票号',
      dataIndex: 'ticketNumber',
      ellipsis: true,
    },
    {
      title: '订单号',
      dataIndex: 'orderId',
      ellipsis: true,
    },

    {
      title: '退订号',
      dataIndex: 'refundId',
      ellipsis: true,
    },
    {
      title: '结算 / 退款',
      dataIndex: 'tradeNo',
      ellipsis: true,
      fieldProps: {
        placeholder: '请输入结算单号 / 退款单号',
      },
    },
    {
      title: '交易上链',
      dataIndex: 'isChainOfOrder',
      valueEnum: chainStatusEnum,
      renderText: (dom) => <Tag type="chainStatus" value={dom} />,
    },

    {
      title: '支付方式',
      dataIndex: 'payType',
      valueType: 'select',
      valueEnum: payTypeEnum,
    },
    {
      title: '供应商',
      dataIndex: 'credit',
      valueType: 'select',
      params: { storeId },
      request: async (params) => {
        const { data } = await apiSupplierList(params.storeId);
        const options = data.map((item: any) => {
          return {
            label: item.coName,
            value: item.coId,
          };
        });

        return uniqBy(
          [
            ...options,
            {
              label: coName,
              value: coId,
            },
          ],
          'value',
        );
      },
      renderText(text, record, index, action) {
        return record.sellerName || '-';
      },
    },
    {
      title: '交易类型',
      dataIndex: 'tradeType',
      valueType: 'select',
      valueEnum: {
        '0': '售票',
        '1': '退票',
      },
    },

    {
      title: '单票 / 组合票',
      dataIndex: 'isCompose',
      ellipsis: true,
      search: false,
      renderText: (text, record) => {
        if (text == 1) {
          return <span style={{ color: 'red' }}>组合票</span>;
        }
        return <span>单票</span>;
      },
    },
    {
      title: '组合名',
      dataIndex: 'composeName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      ellipsis: true,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '类别',
      dataIndex: 'productType',
      search: false,
      valueType: 'select',
      valueEnum: productTypeEnum,
    },
    {
      title: '票种',
      dataIndex: 'ticketType',
      valueType: 'select',
      valueEnum: ticketTypeEnum,
      search: false,
    },

    {
      title: '数字资产',
      dataIndex: 'isChain',
      valueEnum: whetherEnum,
    },
    {
      title: '人数',
      dataIndex: 'numberOfPeople',
      search: false,
    },
    {
      title: '金额（元）',
      dataIndex: 'price',
      align: 'right',
      renderText: (dom) =>
        parseFloat(dom) ? Number(parseFloat(dom).toFixed(2)).toLocaleString() : dom,
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      render: (text, record) => {
        return <>{record.isChain == 1 && <ChainModal ticketNumber={record.ticketNumber} />}</>;
      },
    },
  ];

  // 个人交易统计（按支付方式）
  const payTypeColumns: ProColumns<any>[] = [
    {
      title: '统计日期',
      dataIndex: 'date',
      valueType: 'dateTimeRange',
      hideInTable: true,
      initialValue: currentRow?.timeRange || [dayjs().startOf('days'), dayjs().endOf('days')],
      search: {
        transform: (value: string[]) => {
          return {
            createDate: value[0],
            endDate: value[1],
          };
        },
      },
    },
    {
      title: '售票员昵称',
      dataIndex: 'userId',
      initialValue: currentRow?.userId,
      valueType: 'select',
      fieldProps: {
        options: getAllUsersReq.data,
        showSearch: true,
      },
      hideInTable: true,
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      valueType: 'select',
      valueEnum: payTypeEnum,
      align: 'center',
      renderText: (dom, record: any) => {
        return (
          <span style={{ fontWeight: record.isEnd ? 700 : 500 }}>
            {payTypeEnum[record?.payType] || record?.payType}
          </span>
        );
      },
      onCell: (record) => {
        if (record.isTotal) {
          return { colSpan: 5 };
        }
        return record.payTypeCell;
      },
    },
    {
      title: '交易类型',
      dataIndex: 'tradeType',
      align: 'center',
      valueType: 'select',
      valueEnum: {
        '0': '售票',
        '1': '退票',
      },
      onCell: (record) => {
        if (record.isTotal) {
          return { colSpan: 0 };
        }
        return record.tradeTypeCell;
      },
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      search: false,
      onCell: (record) => {
        if (record.isTotal) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: '票种类型',
      dataIndex: 'ticketType',
      valueEnum: ticketTypeEnum,
      search: false,
      onCell: (record) => {
        if (record.isTotal) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: '价格',
      dataIndex: 'price',
      search: false,
      onCell: (record) => {
        if (record.isTotal) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: '票数',
      dataIndex: 'totalTicket',
      search: false,
      renderText: (dom: HTMLElement, record: any) => (
        <span style={{ fontWeight: record.isEnd ? 700 : 500 }}>{dom}</span>
      ),
    },
    {
      title: '人数',
      dataIndex: 'totalPeople',
      search: false,
      renderText: (dom: HTMLElement, record: any) => (
        <span style={{ fontWeight: record.isEnd ? 700 : 500 }}>{dom}</span>
      ),
    },
    {
      title: '金额（元）',
      dataIndex: 'totalAmount',
      search: false,
      align: 'right',
      renderText: (dom, record: any) => (
        <span style={{ fontWeight: record.isEnd ? 700 : 500 }}>
          {parseFloat(dom) ? Number(parseFloat(dom).toFixed(2)).toLocaleString() : dom}
        </span>
      ),
    },
    // {
    //   title: '金额（元）',
    //   dataIndex: 'payAmount',
    //   search: false,
    //   renderText: (dom, record: any) => {
    //     if (record?.payType === 'total') {
    //       return <span style={{ fontWeight: 600 }}>{record?.payAmount}</span>;
    //     }
    //     return <>{record?.payAmount}</>;
    //   },
    // },
  ];

  const tabKeyValue = useContext(TabKeyContext);

  const windowOverviewRequest = async (params: any) => {
    const { data } = await apiWindowTradeList(params);
    addOperationLogRequest({
      action: 'info',
      module: tabKey,
      content: `查看窗口交易总览`,
    });

    return {
      total: data?.length || 0,
      data,
    };
  };

  //窗口交易明细（按出票）
  const windowsTradeByTicketingRequest = async (params: any) => {
    //查看明细
    const {
      data: { priceTotal, totalPeople, records, total },
    } = await apiWindowsTradeDetail(params);

    addOperationLogRequest({
      action: 'info',
      module: tabKeyValue,
      content: '按出票方式查看个人交易记录',
    });

    setCount({
      priceTotal,
      totalPeople,
    });

    return {
      data: records || [],
      total,
    };
  };

  //窗口交易明细（按支付方式）
  const getWindowPaymentList = async (params: any) => {
    const { data = [] } = await apiWindowPaymentList(params);
    // const { data = [] } = await apiWindowPaymentList({
    //   ...params,
    //   createDate: '2023-08-01 00:00:00',
    //   endDate: '2024-08-01 23:59:59',
    // });

    const tableList = [];
    data.forEach((payTypeItem) => {
      // 支付方式层级
      let payTypeRowSpan = 0; // 支付方式合并单元格

      payTypeItem.data.forEach((t) => {
        payTypeRowSpan += t.data.length;
      });
      payTypeItem.data.forEach((tradeTypeItem, tradeTypeIndex) => {
        let tradeTypeRowSpan = tradeTypeItem.data.length;
        // 交易类型层级
        tradeTypeItem.data.forEach((item, index) => {
          // 交易数据层级
          tableList.push({
            payType: payTypeItem.payType, // 支付方式
            tradeType: tradeTypeEnum[tradeTypeItem.tradeType], // 交易类型
            payTypeCell: {
              rowSpan: payTypeRowSpan,
            },
            tradeTypeCell: {
              rowSpan: tradeTypeRowSpan,
            },
            ...item,
          });
          payTypeRowSpan = 0;
          tradeTypeRowSpan = 0;
        });
      });

      // 合计
      tableList.push({
        payType: '小计',
        isTotal: true,
        totalTicket: payTypeItem.totalTicket,
        totalPeople: payTypeItem.totalPeople,
        totalAmount: payTypeItem.totalAmount,
      });
    });

    // 总计
    const totalCount = {
      totalTicket: 0,
      totalPeople: 0,
      totalAmount: 0,
    };
    tableList.forEach((item) => {
      if (item.isTotal) {
        totalCount.totalTicket += item.totalTicket;
        totalCount.totalPeople += item.totalPeople;
        totalCount.totalAmount = Decimal.add(totalCount.totalAmount, item.totalAmount).toNumber();
      }
    });

    if (tableList.length > 0) {
      tableList.push({
        isTotal: true,
        isEnd: true,
        payType: '总计',
        ...totalCount,
      });
    }

    addOperationLogRequest({
      action: 'info',
      module: tabKeyValue,
      content: '按支付方式查看个人交易记录',
    });

    return {
      total: data.length,
      data: tableList,
    };
  };

  useEffect(() => {
    if (secondTabKey === 'ticketing' && currentRow?.username && actionRef.current?.reload) {
      actionRef.current?.reload();
    }
  }, [secondTabKey, currentRow?.username]);
  const exportState = useExport({
    columns: ticketingColumns,
    modulePath: 'E-commerce_MyShopDailyReport',
    params: { storeId },
  });

  useEffect(() => {
    if (coId) {
      getAllUsersReq.run({ companyId: coId });
    }
  }, [coId]);

  return (
    <Tabs
      tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
      defaultActiveKey="1"
      activeKey={firstTabKey}
      onChange={(key) => {
        setFirstTabKey(key);
        // 重置二级 key
        setSecondTabKey('ticketing');
        if (key === '1') {
          setCurrentRow(undefined);
        }
      }}
      // destroyInactiveTabPane
    >
      <TabPane tab="窗口交易总览" key="1">
        <ProTable
          {...tableConfig}
          bordered
          formRef={formRef}
          params={{ storeId }}
          request={windowOverviewRequest}
          columns={overviewColumns}
        />
      </TabPane>
      <TabPane tab="个人交易统计" key={'2'}>
        <Tabs
          tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
          defaultActiveKey="ticketing"
          activeKey={secondTabKey}
          onChange={(key) => {
            setSecondTabKey(key);
          }}
        >
          <TabPane tab="按出票方式" key="ticketing">
            <ProTable
              {...tableConfig}
              key={currentRow?.username}
              actionRef={actionRef}
              bordered
              params={{ storeId }}
              request={windowsTradeByTicketingRequest}
              formRef={exportState.formRef}
              columnsState={columnsSet(exportState)}
              toolBarRender={() => [<Export key="export" {...exportState} />]}
              columns={ticketingColumns}
            />
            <Row gutter={[20, 0]} justify="end">
              <Col pull={1}>
                <h2>总人数：{Number(count.totalPeople || 0)}</h2>
              </Col>
              <Col pull={1}>
                <h2>
                  总金额：￥{Number(Number(count.priceTotal || 0).toFixed(2)).toLocaleString()}
                </h2>
              </Col>
            </Row>
          </TabPane>
          <TabPane tab="按支付方式" key="paymennt">
            <ProTable
              {...tableConfig}
              bordered
              key={currentRow?.username}
              pagination={false}
              params={{ storeId }}
              request={getWindowPaymentList}
              columns={payTypeColumns}
            />
          </TabPane>
        </Tabs>
      </TabPane>
    </Tabs>
  );
};
