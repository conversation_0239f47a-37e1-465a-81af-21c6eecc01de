import { Tag } from 'antd';

const tagObj: any = {
  // 禁启用状态
  disabledStatus: {
    0: { label: '已禁用', color: '' },
    1: { label: '已启用', color: 'blue' },
  },
  // 认证状态
  approveStatus: {
    0: { label: '未认证', color: 'orange' },
    1: { label: '已认证', color: 'blue' },
    2: { label: '认证不通过', color: 'cyan' },
    3: { label: '认证中', color: 'green' },
    4: { label: '认证失败', color: 'red' },
  },
  // 注册状态
  registrationStatus: {
    0: { label: '未创建', color: 'orange' },
    1: { label: '已创建', color: 'blue' },
  },
  // 服务状态
  serviceStatus: {
    0: { label: '已过期', color: '' },
    1: { label: '试用中', color: 'green' },
    2: { label: '正式期', color: 'blue' },
    3: { label: '已过期', color: '' },
  },
  // 订单状态
  orderStatus: {
    10: { label: '创建订单', color: 'green' },
    11: { label: '取消订单', color: '' },
    12: { label: '订单超时失效', color: '' },
    13: { label: '用户取消订单', color: '' },
    14: { label: '系统取消订单', color: '' },
    20: { label: '待付款', color: 'orange' },
    21: { label: '已支付', color: 'blue' },
    22: { label: '支付失败', color: 'red' },
    30: { label: '已完成', color: 'blue' },
    31: { label: '出库成功', color: 'green' },
    32: { label: '出库失败', color: 'red' },
    33: { label: '出票失败', color: 'red' },
    34: { label: '出票中', color: 'orange' },
    50: { label: '退款中', color: 'orange' },
    51: { label: '已退款', color: 'blue' },
    52: { label: '退款失败', color: 'red' },
    53: { label: '退款取消', color: '' },
    54: { label: '部分退款', color: 'green' },
  },
  // 订单状态 - 搜索
  searchOrderStatus: {
    11: { label: '已取消', color: '' },
    20: { label: '待付款', color: 'orange' },
    22: { label: '支付失败', color: 'red' },
    32: { label: '出库失败', color: 'red' },
    33: { label: '出票失败', color: 'red' },
    30: { label: '已完成', color: 'blue' },
  },
  // 审核状态
  auditStatus: {
    1: { label: '待审核', color: 'orange' },
    2: { label: '审核通过', color: 'blue' },
    3: { label: '已拒绝', color: 'red' },
    4: { label: '审核失败', color: 'red' },
  },
  // 审核状态 - 权益卡
  equityCardAuditStatus: {
    0: { label: '待审核', color: 'orange' },
    1: { label: '审核失败', color: 'red' },
    2: { label: '审核通过', color: 'blue' },
  },
  // 审核状态 - 权益票
  equityTicketAuditStatus: {
    1: { label: '待审核', color: 'orange' },
    2: { label: '审核通过', color: 'blue' },
    3: { label: '审核失败', color: 'red' },
  },
  // 票状态
  ticketStatus: {
    0: { label: '未核销', color: 'orange' },
    1: { label: '部分核销', color: 'green' },
    2: { label: '已过期', color: '' },
    3: { label: '已完成', color: 'blue' },
    4: { label: '已退票', color: 'blue' },
  },
  // 交易上链
  chainStatus: {
    0: { label: '未上链', color: 'orange' },
    1: { label: '已上链', color: 'blue' },
  },
};

export default ({ type, value }: { type: string; value: any }) => {
  const obj = tagObj[type][value];
  return obj ? <Tag color={obj.color}>{obj.label}</Tag> : '-';
};
