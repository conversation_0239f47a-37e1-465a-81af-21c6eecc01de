/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-25 10:12:54
 * @LastEditTime: 2023-09-21 14:51:20
 * @LastEditors: zhangfengfei
 */
import { tableConfig } from '@/common/utils/config';
import { productTypeEnum, ticketStatusEnum, ticketTypeEnum } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { DataMaskTypeEnum } from '@/common/utils/tool';
import DataMask from '@/components/DataMask';
import { useMask } from '@/hooks/useMask';
import useModal from '@/hooks/useModal';
import OrderManagement from '@/pages/business/orderManagement/Sale/OrderManagement';
import {
  confirmSellerBill,
  getAgentBillList,
  getAgentBillPreList,
} from '@/services/api/billManage';
import { ticketDetails } from '@/services/api/datareport';
import { apiAgentInfo } from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Descriptions, Modal, Popconfirm, Space, Tabs, message } from 'antd';
import dayjs from 'dayjs';
import QRCode from 'qrcode.react';
import type { FC } from 'react';
import { useRef, useState } from 'react';
import { Access, history, useAccess, useModel, useRequest } from '@umijs/max';
import SettlementDetailModal from './SettlementDetailModal';

type CommissionBillProps = Record<string, never>;

/**
 * @description 账单管理->佣金账单->后结算
 * */
const CommissionBill: FC<CommissionBillProps> = () => {
  const { initialState } = useModel('@@initialState');
  const { coId = '' } = initialState?.currentCompany || {};

  const actionRef = useRef<ActionType>();

  const detailModalState = useModal();

  const [tableListItem, setTableListItem] = useState<API.SellerBillItem>();
  const [activeKey, setActiveKey] = useState<string>('1');
  // 门票详细
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  // 门票详情数据
  const [rowInfo, setRowInfo] = useState<Record<string, any>>({});

  // 添加数据脱敏相关 hook
  const [handleDetailsMaskChange, maskDetailsDataFn] = useMask();

  const billConfirmReq = useRequest(confirmSellerBill, {
    manual: true,
    onSuccess: () => {
      message.success('确认成功');
      actionRef.current?.reload();
    },
  });
  const access = useAccess();

  const tableListReq = async (params: API.AgentBillListParams) => {
    let request: any = getAgentBillPreList;
    if (activeKey === '2') {
      request = getAgentBillList;
    }
    const { data } = await request(params);
    return {
      data: data.records,
      total: data.total,
    };
  };

  const detailModal = useModal();

  const onConfirm = () => {
    if (tableListItem)
      billConfirmReq.run({
        id: tableListItem.billId,
      });
  };

  // 获取门票详情数据
  const details = async (item: any) => {
    //门票详情
    try {
      const { code, data } = await ticketDetails(item.ticketNumber);
      if (code == 20000) {
        setRowInfo(data);
      }
      setDetailsVisible(true);
    } catch (err) {
      console.log(err);
    }
  };

  const columns: ProColumns<API.SellerBillItem>[] = [
    {
      title: '单号',
      dataIndex: 'billId',
      fieldProps: (form) => {
        return {
          onChange: (e: any) => {
            form.setFieldsValue({
              billId: String(e.target.value.match(/\d+/g) || '').slice(0, 19),
            });
          },
        };
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
      render: (dom: any) => {
        return <span>{dayjs(dom).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startDate: value[0],
            endDate: value[1],
          };
        },
      },
    },

    {
      title: '代理供应商',
      dataIndex: 'name',
      valueType: 'select',
      request: async () => {
        const { data } = await apiAgentInfo({
          distributorId: coId,
          current: 1,
          pageSize: 999, // 延用之前分页接口，分页参数传个很大的数兜底
          type: 2,
        });
        return data.map((item: any) => ({
          label: item.coName,
          value: item.coId,
        }));
      },
      search: {
        transform: (value) => ({
          sellerId: value,
        }),
      },
    },

    // {
    //   title: '结算开始时间',
    //   dataIndex: 'startDay',
    //   search: false,
    // },
    {
      title: '结算结束时间',
      dataIndex: 'endDay',
      search: false,
    },

    {
      title: '结算状态',
      dataIndex: 'settlementStatus',
      valueType: 'select',
      valueEnum: {
        0: '待结算',
        1: '已确认',
      },
    },
    {
      title: '应结算金额（元）',
      dataIndex: 'amount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      renderText: (_, record) => (
        <Space
          onClick={() => {
            setTableListItem(record);
          }}
        >
          <Access accessible={access.canComissionBill_detail}>
            <a
              onClick={() => {
                detailModalState.setVisible(true);
              }}
            >
              结算明细
            </a>
          </Access>
          {
            record.settlementStatus === 0 && (
              <Popconfirm
                placement="top"
                title={'是否确认已收到全部金额？'}
                onConfirm={onConfirm}
                okButtonProps={{
                  loading: billConfirmReq.loading,
                }}
              >
                <a>确认</a>
              </Popconfirm>
            )
            // :
            // (
            // <Popconfirm
            //   placement="top"
            //   title={'已确认完成，请勿重复操作。'}
            //   okButtonProps={{ style: { display: 'none' } }}
            // >
            //   <a>确认</a>
            // </Popconfirm>
            // )
          }
        </Space>
      ),
    },
  ];

  const columns1: ProColumns[] = [
    {
      title: '票号',
      dataIndex: 'ticketNumber',
      render: (dom, entity) => (
        <a
          onClick={() => {
            details(entity);
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '子订单号',
      dataIndex: 'orderId',
    },
    {
      title: '来源店铺',
      dataIndex: 'storeName',
      hideInSearch: true,
    },
    {
      title: '代理供应商',
      dataIndex: 'sellerName',
      hideInSearch: true,
    },
    {
      title: '代理供应商',
      dataIndex: 'name',
      valueType: 'select',
      hideInTable: true,
      request: async () => {
        const { data } = await apiAgentInfo({
          distributorId: coId,
          current: 1,
          pageSize: 999, // 延用之前分页接口，分页参数传个很大的数兜底
          type: 2,
        });
        return data.map((item: any) => ({
          label: item.coName,
          value: item.coId,
        }));
      },
      search: {
        transform: (value) => ({
          sellerId: value,
        }),
      },
    },

    {
      title: '支付时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            createTime: `${value[0]} 00:00:00`,
            createEndTime: `${value[1]} 23:59:59`,
          };
        },
      },
    },
    {
      title: '支付时间',
      dataIndex: 'payTime',
      search: false,
      render: (dom: any) => {
        return <span>{dayjs(dom).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },
    {
      title: '佣金金额（元）',
      dataIndex: 'actualComAmount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '操作',
      valueType: 'option',
      render: (dom, { orderId, orderType }) => (
        <a
          onClick={() => {
            // setCurrentRow(entity);
            // detailModal.setVisible(true);
            history.push(
              `/financial-management/commission-management/order-detail/${orderId}?orderType=${orderType}`,
            );
          }}
        >
          查看
        </a>
      ),
    },
  ];

  return (
    <>
      <Tabs
        style={{ background: '#fff' }}
        tabBarStyle={{ padding: '0 24px', margin: '0' }}
        onChange={(activeKey) => {
          setActiveKey(activeKey);
        }}
        items={[
          {
            label: '前结算',
            key: '1',
          },
          {
            label: '后结算',
            key: '2',
          },
        ]}
      />
      <ProTable<API.AgentBillItem, API.AgentBillListParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="billId"
        columns={activeKey === '1' ? columns1 : columns}
        params={{
          agentId: coId,
          activeKey: activeKey,
        }}
        request={tableListReq}
        pagination={{
          defaultPageSize: 10,
        }}
        toolbar={{
          style: {
            margin: 0,
          },
        }}
      />
      {/* 结算明细 modal */}
      <SettlementDetailModal currentItem={tableListItem} modalState={detailModalState} />
      <Modal
        title={
          <>
            <span>门票详情</span>
            <DataMask
              onDataMaskChange={handleDetailsMaskChange}
              logContent="查看【门票详情】用户隐私信息"
            />
          </>
        }
        visible={detailsVisible}
        width={modelWidth.md}
        destroyOnClose={true}
        footer={
          <>
            <Button onClick={() => setDetailsVisible(false)}>取消</Button>
          </>
        }
        onCancel={async () => {
          setDetailsVisible(false);
        }}
      >
        {/* 门票详情 */}
        <Descriptions title="基础信息" column={2}>
          <Descriptions.Item label="票号">{rowInfo.id}</Descriptions.Item>
          <Descriptions.Item label="服务商名称">{rowInfo.providerName}</Descriptions.Item>
          <Descriptions.Item label="产品名称">{rowInfo.proName}</Descriptions.Item>
          <Descriptions.Item label="产品类型">{productTypeEnum[rowInfo.proType]}</Descriptions.Item>
          <Descriptions.Item label="商品名称">{rowInfo.goodsName}</Descriptions.Item>
          <Descriptions.Item label="票种">{ticketTypeEnum[rowInfo.goodsType]}</Descriptions.Item>
          <Descriptions.Item label="购票人姓名">
            {maskDetailsDataFn(rowInfo.pilotName, DataMaskTypeEnum.NAME)}
          </Descriptions.Item>
          <Descriptions.Item label="购票人手机号">
            {maskDetailsDataFn(rowInfo.pilotPhone, DataMaskTypeEnum.PHONE)}
          </Descriptions.Item>
          <Descriptions.Item label="入园时间">{rowInfo.enterDate}</Descriptions.Item>
          <Descriptions.Item label="状态">{ticketStatusEnum[rowInfo.status]}</Descriptions.Item>
          <Descriptions.Item label="出票时间">{rowInfo.createTime}</Descriptions.Item>
          <Descriptions.Item label="订单号">{rowInfo.orderId}</Descriptions.Item>
          <Descriptions.Item label="二维码">
            <QRCode
              style={{ margin: '0px 0px', position: 'relative' }}
              value={rowInfo.printStr} //value 参数为生成二维码的链接 我这里是由后端返回
              size={120} //二维码的宽高尺寸
              fgColor="#000000" //二维码的颜色
            />
          </Descriptions.Item>
          <Descriptions.Item label="景区名称">{rowInfo.scenicName}</Descriptions.Item>
        </Descriptions>
      </Modal>

      {/* 订单详情 */}
      <OrderManagement modalState={detailModal} currentRow={tableListItem} />
    </>
  );
};

export default CommissionBill;
