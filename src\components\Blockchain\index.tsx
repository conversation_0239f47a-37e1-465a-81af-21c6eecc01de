import { getUniqueId } from '@/common/utils/tool';
import ProDescriptions from '@ant-design/pro-descriptions';
import { Button, Divider, Modal } from 'antd';
import React, { useEffect } from 'react';
import styles from './index.less';
import { getEnv } from '@/common/utils/getEnv';
const { NFT_HOST = '' } = getEnv();

export interface IChart {
  cochainVisible: boolean;
  setCochainVisible: any;
  cochainColumns: any;
  cochainDataDataSource: any;
}

const Blockchain: React.FC<IChart> = ({
  cochainVisible,
  setCochainVisible,
  cochainColumns,
  cochainDataDataSource,
}) => {
  // 监听窗口大小改变
  useEffect(() => {}, []);
  return (
    <Modal
      title="区块链链上信息"
      className={styles.modal}
      visible={cochainVisible}
      destroyOnClose={true}
      width={'730px'}
      footer={null}
      onCancel={() => {
        setCochainVisible(false);
      }}
    >
      <div style={{ minHeight: '337px' }}>
        {cochainColumns.map((item: any, index: any) => [
          <ProDescriptions
            title={item.title}
            loading={false}
            dataSource={cochainDataDataSource}
            columns={item.columns}
            column={2}
            key={getUniqueId()}
            labelStyle={{ fontWeight: 'bold' }}
            contentStyle={{ display: 'block' }}
            // bordered={true}
          />,
          cochainColumns[index + 1] ? <Divider key={getUniqueId()} /> : '',
        ])}
        <div style={{ display: 'flex', marginTop: '40px', justifyContent: 'center' }}>
          <Button type="primary" onClick={() => window.open(NFT_HOST)}>
            查看上链信息
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default Blockchain;
