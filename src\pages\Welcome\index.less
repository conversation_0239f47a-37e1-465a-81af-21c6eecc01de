.main {
  :global {
    .card {
      position: relative;
      height: 160px;
      padding: 24px;
      border-radius: 2px;

      .title {
        font-weight: bold;
        font-size: 24px;
        line-height: 1;
      }

      .content {
        width: calc(100% - 100px) !important;
        margin-top: 14px;
        font-size: 14px;
        line-height: 1;
      }

      .button {
        position: absolute;
        bottom: 24px;
        left: 24px;
        width: 58px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 2px;
        cursor: pointer;
      }

      .icon {
        position: absolute;
        top: 50%;
        right: 14px;
        width: 135px;
        height: 135px;
        transform: translateY(-50%);
      }
    }

    .card_order {
      background: linear-gradient(316deg, #d5feff 0%, #fff 68%, #f8fffe 100%);
      border: 1px solid #caf5ff;

      .title {
        color: #1890ff;
      }

      .content {
        color: #3060ea;
        font-size: 32px;
      }

      .button {
        color: #fff;
        background: rgba(24, 144, 255, 0.9);
      }

      .icon {
        background: url('../../assets/home_card_order.png') no-repeat center/contain;
      }
    }

    .card_scenic {
      background: linear-gradient(316deg, #ead5ff 0%, #fff 66%, #fbf8ff 100%);
      border: 1px solid #f7e4ff;

      .title {
        color: #5a18ba;
      }

      .content {
        color: #6700b2;
      }

      .button {
        color: #aa2ed3;
        background: rgba(104, 33, 207, 0.18);
      }

      .icon {
        background: url('../../assets/home_card_scenic.png') no-repeat center/contain;
      }
    }

    .card_pay {
      background: linear-gradient(316deg, #ffead5 0%, #fff 66%, #fff9f8 100%);
      border: 1px solid #ffece4;

      .title {
        color: #ff721b;
      }

      .content {
        color: #b25b00;
      }

      .button {
        color: #de7d22;
        background: rgba(255, 114, 27, 0.17);
      }

      .icon {
        background: url('../../assets/home_card_pay.png') no-repeat center/contain;
      }
    }

    .card_exchange {
      background: linear-gradient(316deg, #d5e6ff 0%, #fff 66%, #f8fbff 100%);
      border: 1px solid #dae2fa;

      .title {
        color: #0050c9;
      }

      .content {
        color: #0047b2;
      }

      .button {
        color: #1890ff;
        background: rgba(24, 144, 255, 0.11);
      }

      .icon {
        background: url('../../assets/home_card_exchange.png') no-repeat center/contain;
      }
    }

    .card_financial {
      background: linear-gradient(316deg, #cbf0ff 0%, #fff 66%, #f8fcff 100%);
      border: 1px solid #d0f2ff;

      .title {
        color: #0077a5;
      }

      .content {
        color: #1e7ba0;
      }

      .button {
        color: #117198;
        background: rgba(96, 186, 222, 0.17);
      }

      .icon {
        background: url('../../assets/home_card_financial.png') no-repeat center/contain;
      }
    }

    // ant 组件样式
    .ant-tabs-nav {
      margin: 0 10px;

      &::before {
        border-bottom: none;
      }
    }
  }
}
