import ProTable from '@/common/components/ProTable';
import { DownloadStatus } from '@/common/utils/enum';
import { apiCustomDownloadPage } from '@/services/api/config';
import type { API } from '@/services/typings';
import type { ProColumns } from '@ant-design/pro-components';
import { Tag } from 'antd';

export default () => {
  const tableColumns: ProColumns<API.NoticeListItem>[] = [
    {
      title: '报表名称',
      dataIndex: 'fileName',
    },
    {
      title: '状态',
      dataIndex: 'downloadStatus',
      // valueEnum: DownloadStatus,
      render: (text) => <Tag color={DownloadStatus[text].color}>{DownloadStatus[text].text}</Tag>,
    },
    {
      title: '报表创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, record) =>
        record.downloadStatus == 1 && (
          <a href={record.downloadUrl + '?response-content-type=application/octet-stream'}>下载</a>
        ),
    },
  ];
  return (
    <ProTable
      search={false}
      columns={tableColumns}
      request={apiCustomDownloadPage}
      headerTitle={
        <div style={{ color: '#bfbfbf', lineHeight: '22px' }}>
          为保护数据安全，只能查看当前登录账号下载的报表明细；报表数据仅保留 72
          小时，超时后将自动清除
        </div>
      }
    />
  );
};
