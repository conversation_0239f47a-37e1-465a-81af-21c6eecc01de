/**
 * 电商数据报表接口
 */
import { getData } from '@/common/utils/tool';
import { request } from '@umijs/max';
import { scenicHost } from '.';
import { getEnv } from '@/common/utils/getEnv';

let part: string = 'scenic_order_test';
const { DATA_HOST } = getEnv();

if (DATA_HOST == 'https://prod.shukeyun.com/data/api/model/exec') part = 'ck_order_scenic';

// 获取供应商 (上游) 列表  type 1 经销 2 代理
export async function getSupplierList(params: {
  current: number;
  pageSize: number;
  distributorId: number;
  type: number;
}) {
  return request(`${scenicHost}/distribution/distribution/dealerInfo`, {
    method: 'GET',
    params: { ...params },
    // skipErrorHandler: true, //不走错误处理
  });
}
// 获取分销商 (下游) 列表  经销
export async function getDistributorList(params: any) {
  return request(`${scenicHost}/distribution/distribution/downDistributorList`, {
    method: 'GET',
    params: { ...params },
    // skipErrorHandler: true, //不走错误处理
  });
}
// 获取分销商 (下游) 列表  代理
export async function getAgentsList(params: any) {
  return request(`${scenicHost}/ticketAgent/agentsList`, {
    method: 'GET',
    params: { ...params },
    // skipErrorHandler: true, //不走错误处理
  });
}

/** 面向渠道 (经销) 商订单 */
export async function distributor(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_distributor/scenic_ads_dw/`, {
      method: 'POST',
      data: params,
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 面向渠道商进货订单详情  receive  retreat
export async function distributorReceiveInfo(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_distributor_my_purchase_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 面向渠道商退货订单详情
export async function distributorRetreatInfo(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_distributor_my_return_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 面向下游渠道商进货订单详情
export async function belowDistributorReceiveInfo(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_distributor_downstream_purchase_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 面向下游渠道商退货订单详情
export async function belowDistributorRetreatInfo(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_distributor_downstream_return_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

/** 面向用户 */
export async function user(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_user_by_store/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 面向用户下单订单详情
export async function userReceiveOrder(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_user_by_store_order_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 面向用户下单票详情
export async function userReceiveTicket(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_user_by_store_ticket_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 面向用户退单订单详情
export async function userRetreatOrder(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_user_by_store_un_order_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 面向用户退单票详情
export async function userRetreatTicket(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_user_by_store_un_ticket_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 门票详情
export async function ticketDetails(ticketId: string) {
  return request(`${scenicHost}/orderTicketCheck/info/${ticketId}`, {
    method: 'GET',
  });
}

/** 面向代理商 */
export async function agent(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_agent_by_agent/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 下单订单详情
export async function agentReceiveOrder(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_agent_by_agent_order_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 下单票详情
export async function agentReceiveTicket(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_agent_by_agent_ticket_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

// 退单订单详情
export async function agentRetreatOrder(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_agent_by_agent_re_order_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}
// 退单票详情
export async function agentRetreatTicket(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_agent_by_agent_re_ticket_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}
