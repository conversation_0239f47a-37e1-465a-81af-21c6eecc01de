/*
 * @FilePath: Toagent.tsx
 * @Author: chentian<PERSON><PERSON>
 * @Date: 2022-09-29 10:08:57
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-09-30 18:02:02
 * Copyright: 2022 xxxTech CO.,LTD. All Rights Reserved.
 * @Descripttion:
 */

import { Tabs } from 'antd';
import React, { useState } from 'react';
import Byagent from './Byagent';
import Bygoods from './Bygoods';
const Toagent: React.FC<Record<string, any>> = (props) => {
  // TAP list
  const tapList: Record<string, string>[] = [
    {
      title: '按代理商',
    },
    {
      title: '按商品',
    },
  ];

  const [TabIndex, setTabIndex] = useState<number | string>(0);

  // tab 切换事件
  const onChange = async (key: string) => {
    setTabIndex(key);
  };

  return (
    <>
      <Tabs
        tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
        defaultActiveKey="0"
        onChange={onChange}
      >
        <Tabs.TabPane tab={tapList[0].title} key={0}>
          {TabIndex == 0 ? (
            <Byagent columnsDetail={props.columnsDetail} distributorList={props.distributorList} />
          ) : (
            ''
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab={tapList[1].title} key={1}>
          {TabIndex == 1 ? <Bygoods /> : ''}
        </Tabs.TabPane>
      </Tabs>
    </>
  );
};

export default Toagent;
