/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-12 09:34:54
 * @LastEditTime: 2023-09-15 10:46:56
 * @LastEditors: zhangfengfei
 */
import Delete from '@/common/components/Delete';
import Disabled from '@/common/components/Disabled';
import EditPop from '@/common/components/EditPop';
import useProModal from '@/common/components/ProModal/useProModal';
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getHashParams } from '@/common/utils/tool';
import {
  apiGroupAdd,
  apiGroupDel,
  apiGroupEdit,
  apiGroupList,
  getDealerApplicationList,
  updateDistributionGroupStatus,
} from '@/services/api/distribution';
import type { API } from '@/services/typings';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProFormColumnsType} from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Access, useModel, useRequest } from '@umijs/max';
import { Button, Space, Tabs, message } from 'antd';
import { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useAccess } from '@umijs/max';
import AgentGroup from './components/AgentGroup';
import Distributor from './components/DistributorGroup/Distributor';
import ProductConfig from './components/DistributorGroup/ProductConfig';

export const saleGroupTabItems = [
  {
    label: '代理商分组',
    key: 'agency-group',
  },
  {
    label: '经销商分组',
    key: 'sell-group',
  },
];

export const SaleGroupTabKeyContext = createContext<string>('agency-group');

export default () => {
  const modalState = useProModal();
  const { initialState } = useModel('@@initialState');
  const { coId, settlementId } = initialState?.currentCompany || {};
  const queryParams = getHashParams();

  const saleGroupTabKey = useContext(SaleGroupTabKeyContext);
  const access = useAccess();
  const [tabKey, setTabKey] = useState(queryParams?.tabKey || 'agency-group');

  // 【收款场景】下拉列表
  const downListReq = useRequest(getDealerApplicationList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      return (res.data || []).map((item: any) => ({
        value: item.code,
        label: item.name,
      }));
    },
  });

  const collectionEnum = useMemo(() => {
    return downListReq.data?.reduce((prev: any, cur: any) => {
      prev[cur.value] = cur.label;
      return prev;
    }, {});
  }, [downListReq.data]);

  const logList = [
    {
      dataIndex: 'name',
      title: '分组名称',
    },
    {
      dataIndex: 'collectionCode',
      title: '收款场景',
      valueEnum: collectionEnum,
    },
  ];

  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      title: '分组名称',
      dataIndex: 'name',
    },
    {
      title: '分销商数量',
      dataIndex: 'distributorAmount',
      hideInSearch: true,
      render: (dom, entity) => (
        <a
          onClick={() => {
            setGroupId(entity.id);
            setGroupName(entity.name);
            setDistribVisible(true);
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '商品配置数量',
      dataIndex: 'distributorPriceAmount',
      hideInSearch: true,
    },

    {
      title: '收款场景',
      dataIndex: 'collectionCode',
      hideInSearch: true,
      valueType: 'select',
      fieldProps: {
        loading: downListReq.loading,
        options: downListReq.data,
      },
    },
    {
      title: '授权状态',
      dataIndex: 'status',
      hideInSearch: true,
      render: (_, entity: any) => (
        <Disabled
          access={access.canSupplierGroup_dealerGroupOpenClose}
          status={entity.status == 1}
          params={{ id: entity.id, status: entity.status == 1 ? 2 : 1 }}
          request={async (params: any) => {
            const data = await updateDistributionGroupStatus(params);
            console.log(1212112121);

            addOperationLogRequest({
              action: 'disable',
              module: saleGroupTabKey,
              content: `${entity.status == 1 ? '禁用' : '启用'}【${entity.name}】`,
            });

            return data;
          }}
          actionRef={actionRef}
        />
      ),
    },
    {
      width: 200,
      title: '操作',
      valueType: 'option',
      render: (_, entity) => (
        <Space size="large">
          <Access accessible={access.canSupplierGroup_dealerGroupEdit}>
            <a
              onClick={async () => {
                entity.isEnable = entity.status == 1 ? 1 : 0;

                setGroupId(entity.id);
                setDataSource(entity);
                // 编辑
                setEditVisible(true);
              }}
            >
              编辑
            </a>
          </Access>
          <Access key="configAccess" accessible={access.canSupplierGroup_insertProduct}>
            <a
              onClick={() => {
                setGroupId(entity.id);
                setGroupName(entity.name);
                modalState.setType('info');
              }}
            >
              销售授权
            </a>
          </Access>
          <Delete
            access={access.canSupplierGroup_dealerGroupDelete}
            status={entity.status == 1}
            params={entity.id}
            request={async (params: any) => {
              const data = await apiGroupDel(params);
              addOperationLogRequest({
                action: 'del',
                module: saleGroupTabKey,
                content: `删除【${entity.name}】`,
              });
              return data;
            }}
            actionRef={actionRef}
          />
        </Space>
      ),
    },
  ];

  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const editColumns: ProFormColumnsType<any>[] = [
    {
      columns: [
        {
          title: '分组名称',
          dataIndex: 'name',
          formItemProps: {
            rules: [{ required: true, max: 30 }],
          },
        },
        {
          title: '收款场景',
          dataIndex: 'collectionCode',
          valueType: 'select',
          fieldProps: {
            loading: downListReq.loading,
            options: downListReq.data,
          },

          formItemProps: {
            rules: [{ required: true }],
          },
        },
      ],
    },
  ];

  const [groupId, setGroupId] = useState('');
  const [groupName, setGroupName] = useState('');

  // 【列表】数据绑定
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });

  // 【分销商列表】数据绑定
  const [distribVisible, setDistribVisible] = useState<boolean>(false);

  useEffect(() => {
    downListReq.run({ merchantId: settlementId });
  }, []);

  /**
   * 把一整个组件全抽到上一层来，为的是让下一个组件能和最根的 tabs 页成为同一级，然后才能使用二级页面轮子
   */
  const getDistributorgroup = (): React.ReactNode => (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        style={modalState.tableStyle}
        {...tableConfig}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key="Access" accessible={access.canSupplierGroup_dealerGroupInsert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                // 初始化表单
                setDataSource({ id: '', isEnable: 0 });
                setEditVisible(true);
              }}
            >
              <PlusOutlined /> 新建
            </Button>
          </Access>,
        ]}
        params={{ distributorId: coId }}
        request={apiGroupList}
        columns={columns}
      />

      {/* 添加/修改分组 */}
      <EditPop
        title="经销商分组"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/修改
        onFinish={async (val: any) => {
          if (dataSource.id) val.id = dataSource.id;
          const msgType = val.id ? '修改' : '添加';
          const hide = message.loading('正在' + msgType);
          val.distributorId = coId;
          try {
            if (dataSource.id) {
              await apiGroupEdit({ ...val });
              addOperationLogRequest({
                action: 'edit',
                module: saleGroupTabKey,
                changeConfig: {
                  list: logList,
                  beforeData: dataSource,
                  afterData: val,
                },
                content: `编辑【${val.name}】`,
              });
            } else {
              await apiGroupAdd({ ...val });
              addOperationLogRequest({
                action: 'add',
                module: saleGroupTabKey,
                content: `新增【${val.name}】`,
              });
            }
            message.success(msgType + '成功');
            // 关闭弹窗并刷新列表
            setEditVisible(false);
            actionRef?.current?.reload();
          } catch (error) {}
          hide();
        }}
      />

      {/* 分销商列表 */}
      <Distributor
        visible={distribVisible}
        setVisible={setDistribVisible}
        groupId={groupId}
        groupName={groupName}
        upActionRef={actionRef}
      />
    </>
  );

  const menuItem = [
    ...(access.canSupplierGroup_agentGroupSelect
      ? [
          {
            label: '代理商分组',
            key: 'agency-group',
            children: <AgentGroup />,
          },
        ]
      : []),
    ...(access.canSupplierGroup_dealerGroupSelect
      ? [
          {
            label: '经销商分组',
            key: 'sell-group',
            children: getDistributorgroup(),
          },
        ]
      : []),
  ];

  return (
    <>
      <div style={modalState.tableStyle}>
        <SaleGroupTabKeyContext.Provider value={tabKey}>
          <Tabs
            tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
            items={menuItem}
            activeKey={tabKey}
            onChange={(active) => {
              setTabKey(active);
            }}
          />
        </SaleGroupTabKeyContext.Provider>
      </div>
      {/* 经销商分组的配置产品 */}
      {modalState.type == 'info' ? (
        <ProductConfig
          groupId={groupId}
          groupName={groupName}
          upActionRef={actionRef}
          modalState={modalState}
        />
      ) : (
        ''
      )}
    </>
  );
};
