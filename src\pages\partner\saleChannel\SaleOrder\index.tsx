import { Tabs } from 'antd';
import React, { useState } from 'react';
import ExchangePage from './ExchangePage';
import SelfChannelPage from './SelfChannelPage';

const { TabPane } = Tabs;

const TableList: React.FC = () => {
  const [firstTabKey, setFirstTabKey] = useState('1');

  return (
    <>
      <Tabs
        tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
        defaultActiveKey="1"
        activeKey={firstTabKey}
        destroyInactiveTabPane
        onChange={(key) => {
          setFirstTabKey(key);
        }}
      >
        <TabPane tab="自有渠道" key="1">
          <SelfChannelPage />
        </TabPane>
        <TabPane tab="交易所" key="2">
          <ExchangePage />
        </TabPane>
      </Tabs>
    </>
  );
};

export default TableList;
