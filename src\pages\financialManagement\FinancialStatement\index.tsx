/*
 * @Author: xuanshuncong
 * @Date: 2022-09-29 13:40
 * @LastEditTime: 2023-09-15 10:53:54
 * @LastEditors: zhangfengfei
 */

import { Tabs } from 'antd';
import type { FC } from 'react';
import IncomeDetail from './component/IncomeDetail';
import IncomeOverview from './component/IncomeOverview';
import './index.less';

type FinancialCProp = {};

const FinancialStatement: FC<FinancialCProp> = () => {
  const tapList = [
    {
      label: '收支总览',
      key: '0',
      children: <IncomeOverview />,
    },
    {
      label: '收支明细',
      key: '1',
      children: <IncomeDetail />,
    },
  ];

  return (
    <Tabs tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }} items={tapList} />
  );
};

export default FinancialStatement;
