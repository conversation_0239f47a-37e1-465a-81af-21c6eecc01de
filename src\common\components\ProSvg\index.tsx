import { useEffect } from 'react';

export default ({ src, color, width, height }: any) => {
  // const host =
  //   process.env.NODE_ENV == 'development' ? '' : getEnv().ENV == 'prod' ? 'exchange/' : 'scenic/exchange/';
  useEffect(() => {
    const getEle = (id: any) => document.getElementById(id);
    const setSvg = () => {
      document.body.insertAdjacentHTML(
        'beforeend',
        `<svg
          id="svg"
          aria-hidden="true"
          style="position: absolute; width: 0px; height: 0px; overflow: hidden"
        ></svg>`,
      );
      return getEle('svg');
    };
    const svg: any = getEle('svg') || setSvg();
    if (!getEle(src)) {
      // 使用绝对路径确保在任何路由下都能正确访问
      fetch(`/tab_icon/${src}.svg`)
        .then((res) => {
          if (!res.ok) {
            console.warn(`Failed to load SVG: /tab_icon/${src}.svg`);
            return;
          }
          res.text().then((data) => {
            // // 单色图标
            // data = data.replace(/fill="#\w+"/g, 'fill="currentColor"');
            // // 多色图标
            // data = data.replace(/fill="#\w+"/g, (match) => {
            //   // 获取匹配到的颜色值
            //   const colorValue = match.match(/#(\w+)/)[1]
            //   const opacityValue = parseInt(colorValue.slice(-2), 16) / 255
            //   return `fill="currentColor" opacity="${opacityValue}"`
            // })
            // 自动注入
            svg.insertAdjacentHTML(
              'beforeend',
              `<symbol id="${src}" ${data.slice(
                data.indexOf('<svg') + 4,
                data.indexOf('</svg>'),
              )} </symbol>`,
            );
          });
        })
        .catch((error) => {
          console.error(`Error loading SVG: /tab_icon/${src}.svg`, error);
        });
    }
  }, [src]);
  return (
    <svg aria-hidden="true" style={{ width, height, color }}>
      <use xlinkHref={'#' + src} />
    </svg>
  );
};
