/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-15 15:31:52
 * @LastEditTime: 2023-06-30 17:33:22
 * @LastEditors: zhang<PERSON><PERSON>i
 */
// 出退票统计
export const filterBarData1 = (result: any) => {
  const dimensions = ['type', '已支付', '已退款'];

  const source: any[] = [];
  for (const item of result) {
    const current = source.find((i) => i.type === item.name);
    if (current) {
      Object.assign(current, {
        [item.legend]: item.value,
      });
      // console.log(current);
    } else {
      source.push({
        type: item.name,
        [item.legend]: item.value,
      });
    }
  }
  return {
    dimensions,
    source,
  };
};

// 佣金统计
export const filterBarData2 = (result: any[]) => {
  const dimensions = ['type', '前结算', '后结算'];

  const source = result.map((item) => ({
    type: item.date,
    ['前结算']: item.pre_settlement,
    ['后结算']: item.post_settlement,
    coName: item.seller_name,
  }));

  return {
    dimensions,
    source,
  };
};

// 当前企业交易统计
export const filterLineData1 = (result: any) => {
  const dimensions = ['type', '交易金额'];

  const source: any[] = [];
  for (const item of result) {
    const current = source.find((i) => i.type === item.name);
    if (current) {
      Object.assign(current, {
        [item.legend]: item.value,
      });
      // console.log(current);
    } else {
      source.push({
        type: item.name,
        [item.legend]: item.value,
      });
    }
  }
  return {
    dimensions,
    source,
  };
};

// 下级代理交易统计
export const filterLineData2 = (result: any, sortValue: any) => {
  const dimensions = ['type', '交易金额'];

  const source: any[] = [];
  for (const item of result) {
    const current = source.find((i) => i.type === item.name);
    if (current) {
      Object.assign(current, {
        [item.legend]: item.value,
      });
      // console.log(current);
    } else {
      source.push({
        type: item.name,
        [item.legend]: item.value,
      });
    }
  }
  return {
    dimensions,
    source,
  };
};

export function getBarOptions(
  {
    dimensions,
    source,
  }: {
    dimensions: any[];
    source: any[];
  },
  unit: string = '',
) {
  return {
    // grid: {
    //   left: '36px',
    //   bottom: '60px',
    //   top: '48px',
    //   right: '24px',
    // },
    legend: {
      bottom: 0,
      icon: 'circle',
    },
    tooltip: {
      // valueFormatter: (value: number) => `${value}${unit}`,
      formatter: ({ seriesName, marker, name, value }) => {
        return `${name}<br /> ${marker} ${seriesName} ${value.coName || ''} &nbsp;&nbsp;&nbsp;${
          value[seriesName]
        }${unit} `;
      },
    },
    color: ['#1890FF', '#9787F2'],
    dataset: {
      source,
      dimensions,
    },
    xAxis: { type: 'category' },
    yAxis: {
      splitLine: {
        lineStyle: {
          type: [5, 5],
        },
      },
      minInterval: unit == '单' ? 1 : 0.01,
    },
    series: dimensions.slice(1, dimensions.length).map((i) => ({ type: 'bar', barMaxWidth: 40 })),
  };
}

export function getLineOptions(
  {
    dimensions,
    source,
  }: {
    dimensions: any[];
    source: any[];
  },
  unit: string = '',
) {
  return {
    // grid: {
    //   left: '36px',
    //   bottom: '60px',
    //   top: '48px',
    //   right: '24px',
    // },
    legend: {
      bottom: 0,
      icon: 'circle',
    },
    xAxis: {
      type: 'category',
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: [5, 5],
        },
      },
      minInterval: 0.01,
    },
    tooltip: {
      trigger: 'axis',
      valueFormatter: (value: number) => `${value}${unit}`,
      borderWidth: 1,
      borderColor: '#1890FF',
    },
    color: ['#1890FF', '#A699F4'],
    dataset: {
      source,
      dimensions,
    },
    series: dimensions.slice(1, dimensions.length).map((i, k) => ({
      type: 'line',
      symbol: 'none',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: k ? '#A699F488' : '#1890FF88',
            },
            {
              offset: 1,
              color: k ? '#A699F400' : '#1890FF00',
            },
          ],
          global: false, // 缺省为 false
        },
      },
    })),
  };
}
