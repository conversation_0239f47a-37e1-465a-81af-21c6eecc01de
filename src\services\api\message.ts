/**
 * 消息通知
 */
import { request } from '@umijs/max';
import { scenicHost } from '.';
const url = (v: string) => scenicHost + '/messageHandler' + v;

/** 获取消息 */
export function getMessage(data: any) {
  return request(url('/findByMessageCenter'), { data, method: 'POST' });
}

/** 删除消息单条 */
export function updMessageCenter(id: string) {
  return request(url(`/updMessageCenter/${id}`), { method: 'GET' });
}

/** 删除消息全部 */
export function updAllMessageCenter(id: string) {
  return request(url(`/updAllMessageCenter/${id}`), { method: 'GET' });
}

/** 添加消息 */
export function addMessage(data: any) {
  return request(url(`/addMessage`), { data, method: 'POST' });
}

/** 获取消息详情 */
export function getMessageDetail(id: string) {
  return request(url(`/findByMessage/${id}`), { method: 'GET' });
}

/** 编辑消息详情 */
export function updMessage(data: any) {
  return request(url(`/updMessage`), { data, method: 'POST' });
}

/** 获取全部商品 */
export function getAllGoods(params: any) {
  return request(url(`/findByGoodsList`), {
    method: 'GET',
    params,
  });
}
