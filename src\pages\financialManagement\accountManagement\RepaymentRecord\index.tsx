/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-23 10:32:10
 * @LastEditTime: 2023-09-15 18:19:01
 * @LastEditors: zhangfeng<PERSON>i
 */
import { tableConfig } from '@/common/utils/config';
import { SettlementStatusEnum } from '@/common/utils/enum';
import useModal from '@/hooks/useModal';
import { getConsumerSettlementList } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Space } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import SettlementDetailModal from './SettlementDetailModal';

type CreditSettlementProps = Record<string, never>;

/**
 * @description 财务管理->账户管理->还款记录
 * */
const CreditSettlement: FC<CreditSettlementProps> = () => {
  const { initialState } = useModel('@@initialState');
  const { settlementId = '' } = initialState?.currentCompany || {};
  const access = useAccess();
  const actionRef = useRef<ActionType>();

  const detailModalState = useModal();

  const [tableListItem, setTableListItem] = useState<API.ConsumerSettlementItem>();

  const tableListReq = async (params: API.ConsumerSettlementListParams) => {
    const { data } = await getConsumerSettlementList(params);

    return {
      data: data.page,
      total: data.totalNumberOfResults,
    };
  };

  const columns: ProColumns<API.ConsumerSettlementItem>[] = [
    {
      title: '单号',
      dataIndex: 'id',
    },
    {
      title: '创建时间',
      dataIndex: 'creationDate',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      search: false,
    },
    {
      title: '创建时间',
      key: 'time',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            creationFromDate: value[0],
            creationToDate: value[1],
          };
        },
      },
    },
    {
      title: '授信方',
      dataIndex: 'provideLegalName',
      search: {
        transform: (value) => {
          return {
            providerName: value,
          };
        },
      },
    },

    {
      title: '结算状态',
      dataIndex: 'settlementStatus',
      valueType: 'select',
      valueEnum: SettlementStatusEnum,
    },
    // {
    //   title: '付款方式',
    //   dataIndex: 'payType',
    //   valueType: 'select',
    //   valueEnum: {
    //     0: '线下还款',
    //   },
    //   search: false,
    // },
    {
      title: '付款时间',
      dataIndex: 'payedDate',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      search: false,
    },
    {
      title: '付款时间',
      dataIndex: 'date_hide',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            payedFromDate: value[0],
            payedToDate: value[1],
          };
        },
      },
    },
    {
      title: '应结算金额（元）',
      dataIndex: 'amount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '已付款金额（元）',
      dataIndex: 'payedAmount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      renderText: (_, record) => (
        <Space
          onClick={() => {
            setTableListItem(record);
          }}
        >
          <Access accessible={access.canReturnRecord_detail}>
            <a
              onClick={() => {
                detailModalState.setVisible(true);
              }}
            >
              查看结算明细
            </a>
          </Access>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.ConsumerSettlementItem, API.ConsumerSettlementListParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        params={{
          merchantId: settlementId,
        }}
        request={tableListReq}
        pagination={{
          defaultPageSize: 10,
        }}
      />
      {/* 结算明细modal */}
      <SettlementDetailModal currentItem={tableListItem} modalState={detailModalState} />
    </>
  );
};

export default CreditSettlement;
