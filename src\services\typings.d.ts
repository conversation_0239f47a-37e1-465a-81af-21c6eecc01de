export interface ResponseData<T = any> {
  code: number;
  data: T;
  msg: string;
}

export interface ResponseListData<T = Record<string, any>[]> {
  code: number;
  data: {
    current: number;
    pageSize: number;
    data: T;
    total: number;
  };
  msg: string;
}

declare namespace API {
  interface CoListItem {
    name: string;
    coId: string;
    coName: string;
    contactName: string;
    coPhone: string;
    coUrl: string;
    contactPhone: string;
    coCode: string;
    isEnable: string;
    modifyTime: string;
    settlementStatus: string;
    settlementName: string;
    registerStatus: number;
    coTypeList: string[];
  }

  type CurrentUser = {
    name?: string;
    avatar?: string;
    userid?: string;
    email?: string;
    signature?: string;
    title?: string;
    group?: string;
    tags?: { key?: string; label?: string }[];
    notifyCount?: number;
    unreadCount?: number;
    country?: string;
    access?: string;
    geographic?: {
      province?: { label?: string; key?: string };
      city?: { label?: string; key?: string };
    };
    address?: string;
    phone?: string;
  };

  type LoginResult = {
    status?: string;
    type?: string;
    currentAuthority?: string;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  type RuleListItem = {
    scenicId: any;
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    credential?: string;
    secret?: string;
    autoRegister?: boolean;
    loginTypeCode?: string;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };

  type Permission = {
    group?: string;
    code: string;
    action: string;
    user?: string;
  };

  type ScenicList = {
    isEnable?: number;
    provinceCode?: string;
    cityCode?: string;
    areaCode?: string;
  } & PageParams;

  //tip: 2022 年 1 月 17 日：慧旅云角色管理的权限参数 pr01，用户管理 p01
  type GetPermissionListParam = {
    type: 'p01' | 'pr01';
  };

  interface PermissionItem {
    id: number;
    groupCode: string;
    groupName: string;
    code: string;
    codeName: string;
    actionCode: string;
    action: string;
  }

  interface UserPermissionParams {
    clientIp?: string;
    nickname: string;
    password: string;
    permissionCode: UserPermissionItem[];
    roleIds: string[];
    username: string;
  }
  export interface UpdateUserParams {
    addRoleIds: string[];
    delRoleIds: string[];
    deletePermission: UserPermissionItem[];
    updatePermission: UserPermissionItem[];
    userId: string;
  }

  interface ComDeptListItem {
    companyId: string;
    deptIds: string[];
  }

  interface UserPermissionItem {
    action: string;
    code: string;
    group: string;
  }

  interface ChargeApprovalListItem {
    id: string;
    scenicName: string;
    productName: string;
    goodsName: string;
    goodsId: string;
    serviceChargeRate: number;
    approvalType: number;
    approvalState: number;
    dataSourcesType: number;
    marketPrice: number;
  }

  interface TravelCardGoodsListItem {
    overallDiscount: number;
    beginDiscount: number;
    endDiscount: number;
    marketPrice: number;
    dailyPrice: number;
    goodsName: string;
    holidayPrice: number;
    id: string;
    effectiveTime: string;
    isEnable: number;
    isFirst: number;
    issueId: string;
    notices: string;
    rightsId: string;
    rightsName: string;
    scenicId: string;
    serviceChargeRate: number;
    travelCardId: string;
    travelCardName: string;
  }
  interface UserPageListParams {
    nickname?: string;
    phone?: number;
    username?: string;
  }

  interface UserPageListItem {
    nickname: string;
    phone: string;
    status: number;
    userId: string;
    username: string;
  }
  interface DisableUserParams {
    appId: string;
    coId?: string;
    scenicId?: string;
    system: number;
    userId: string;
  }

  interface RoleListItem {
    code: string;
    name: string;
    roleId: string;
    status: number;
  }

  interface RoleListParams extends PageParams {
    relationId?: string;
    type: '01' | '02' | '03';
  }

  interface RoleListData {
    page: API.PageParams;
    roles: API.RoleListItem[];
  }

  interface RoleInfoItem {
    roleId: string;
    roleName: string;
  }

  interface ChargeApprovalDetail {
    createTime: string;
    createUserName: string;
    goodsId: string;
    goodsName: string;
    id: string;
    productName: string;
    scenicName: string;
    updateContent: string;
  }

  interface OperationLogItem {
    id: string;
    app: string;
    project: string;
    module: string | null;
    function: string;
    content: string;
    createTime: string;
    creator: string;
    nickname: string;
    deviceIp: string;
    contentDetails: string;
  }

  interface CompanyItem {
    coId: string;
    coName: string;
    contactName: string;
    coPhone: string;
    coUrl: string;
    coCode: string;
    isEnable: string;
    modifyTime: string;
    settlementStatus: string;
    settlementName: string;
    registerStatus: number;
    coTypeList: string[];
    contactPhone: string;
  }

  interface CoSettlementInfo {
    bdsAccount: string;
    bdsOrg: string;
    creationDate: string;
    freeze: boolean;
    id: string;
    idCardOutDate: number;
    idCardType: string;
    init: boolean;
    isGroupAccount: number;
    legalName: string;
    marketPlaceList: string;
    openAuditStatus: string;
    registered: boolean;
    registrationName: string;
    riskLevel: number;
    status: string;
    tunnelMerchant: boolean;
    updateDate: string;
    version: number;
  }

  interface CompanyInfo {
    coId: string;
    coName: string;
    scenic: {
      scenicId: string;
      scenicName: string;
      uniqueIdentity: string;
    }[];
    contactName: string;
    contactPhone: string;
    coPhone: string;
    coCode: string;
    coFax: string;
    coEmail: string;
    coProvinceCode: number;
    coCityCode: number;
    coAreaCode: number;
    coProvinceName: string;
    coCityName: string;
    coAreaName: string;
    coAddressInfo: string;
    isEnable: string;
    coRemark: string;
    coHostAddress: string;
    coStoreLogo: string;
    coSite: string;
    coBackstageLogo: string;
    coBackstageLoginLogo: string;
    coBackstageAbrLogo: string;
    coHostTitle: string;
    settlementId: string;
    settlementStatus: string;
    settlementName: string;
    coTypes: CoTypeItem[];
    isOTA: string;
    bdsAccount: string;
    bdsOrg: string;
    collectionCode: null;
    legalName: string;
  }

  interface CoTypeItem {
    coId: string;
    type: string;
  }

  interface NoticeListItem {
    noticeId: string;
    noticeUrl: string;
    noticeDisplayBeginTime: string;
    noticeDisplayEndTime: string;
    noticeType: number;
    noticePosition: number;
    receiveList: string[];
    receiveNameList: ReceiveNameListItem[];
    noticeContent: string;
    isEnable: number;
    isClose: number;
  }

  interface ReceiveNameListItem {
    id: string;
    name: string;
  }

  interface ScenicListItem {
    scenicId: string;
    scenicName: string;
    companyName: string;
    contracts: string;
    contractsPhone: string;
    isEnable: string;
    settlementStatus: null;
    region: string;
    bookEndTime: string;
    advanceBookDay: string;
    sortPriority: string;
    uniqueIdentity: string;
    serviceStatus: string;
    startDate: string;
    endDate: string;
    screenId: string;
    isMustService: null;
  }
  interface GuideInfo {
    stepList: any;
    guidanceContent: string;
    guideUpdateFlag: any;
  }
}

declare namespace User {
  interface UserInfo {
    userId: string;
    nickname?: string;
    username?: string;
    avatar?: string;
    usable?: boolean;
    imgUrl?: string;
    phone?: string;
  }
}
