import { tableConfig } from '@/common/utils/config';
import { payTypeEnum } from '@/common/utils/enum';
import { apiIncomeOverviewList } from '@/services/api/financialStatement';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { message } from 'antd';
import dayjs from 'dayjs';
import { useRef } from 'react';
import { useModel } from '@umijs/max';
type DataItem = {
  pay_type: string;
  income: number;
  expand: number;
  amount_total: number;
};

interface IIncomeOverviewParams {
  startTime: string;
  endTime: string;
}

const columns: ProColumns<DataItem>[] = [
  {
    title: '统计日期',
    dataIndex: 'date',
    valueType: 'dateRange',
    hideInTable: true,
    initialValue: [dayjs().startOf('months').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    search: {
      transform: (value) => {
        return {
          startTime: value[0],
          endTime: value[1],
        };
      },
    },
  },
  {
    title: '支付方式',
    dataIndex: 'paytype',
    search: false,
    render: (_, record) => {
      if (record.pay_type === '总计') {
        return <span style={{ fontWeight: 600 }}>总计</span>;
      }

      return <div>{payTypeEnum[record?.pay_type] || '其他'}</div>;
    },
  },
  {
    title: '收入（元）',
    dataIndex: 'income',
    search: false,
    render: (_, record) => {
      if (record.pay_type === '总计') {
        return <span style={{ fontWeight: 600 }}>{record?.income}</span>;
      }
      return record?.income;
    },
    align: 'right',
    renderText: (dom) => parseFloat(dom).toFixed(2),
  },
  {
    title: '支出（元）',
    dataIndex: 'expand',
    search: false,
    render: (_, record) => {
      if (record.pay_type === '总计') {
        return <span style={{ fontWeight: 600 }}>{record?.expand}</span>;
      }
      return record?.expand;
    },
    align: 'right',
    renderText: (dom) => parseFloat(dom).toFixed(2),
  },
  {
    title: '合计（元）',
    dataIndex: 'amount_total',
    search: false,
    render: (_, record) => {
      if (record.pay_type === '总计') {
        return <span style={{ fontWeight: 600 }}>{record?.amount_total}</span>;
      }
      return record?.amount_total;
    },
    align: 'right',
    renderText: (dom) => parseFloat(dom).toFixed(2),
  },
];

export default () => {
  const actionRef = useRef<ActionType>();
  //获取企业ID
  const { initialState }: any = useModel('@@initialState');
  const companyID = initialState?.currentCompany?.coId;
  const getIncomeOverviewList = async (params: Record<string, IIncomeOverviewParams>) => {
    const { startTime, endTime } = params;
    try {
      const query = {
        start_day: startTime || '',
        end_day: endTime || '',
        co_id: companyID,
      };

      const data = await apiIncomeOverviewList(query);
      // const tableData = JSON.parse(data.result) || [];
      const tableData = data.result || [];
      let income = 0;
      let expand = 0;
      let amount_total = 0;
      tableData.forEach((item: any) => {
        income += Number(item.income);
        expand += Number(item.expand);
        amount_total += Number(item.amount_total);
      });
      return {
        success: true,
        data: tableData?.length
          ? tableData.concat({
              pay_type: '总计',
              income: Number(income).toFixed(2),
              expand: Number(expand).toFixed(2),
              amount_total: Number(amount_total).toFixed(2),
            })
          : [],
      };
    } catch (e: any) {
      console.error('错误111', e);
      message.error(e.msg || '请求错误！');
      return {
        success: false,
        data: [],
      };
    }
  };

  return (
    <ProTable<DataItem>
      {...tableConfig}
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={getIncomeOverviewList}
      columnsState={{
        persistenceKey: 'pro-table-singe-demos',
        persistenceType: 'localStorage',
        onChange(value) {},
      }}
      pagination={false}
      dateFormatter="string"
    />
  );
};
