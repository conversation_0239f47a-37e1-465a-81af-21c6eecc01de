import { getUnifyPullDownStock } from '@/services/api/distribution';
import { addMessage, getAllGoods, getMessageDetail, updMessage } from '@/services/api/message';
import { CloseOutlined, PlusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import {
  FooterToolbar,
  ProForm,
  ProFormDependency,
  ProFormItem,
  ProFormList,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useLocation, useModel, useRequest } from '@umijs/max';
import type { CheckboxProps} from 'antd';
import { Button, Checkbox, Collapse, message, Select, Space, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import EmployeeModal from './components/addEmployee'; // 确保路径正确
import styles from './index.less';

const { Option } = Select;

const generateGroup = () => ({
  id: Date.now().toString(36) + Math.random().toString(36).substr(2), // 生成唯一ID
  condition: [],
});
const currentCompanyId = localStorage.getItem('currentCompanyId');
const InventoryAlert: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { coId = '' } = initialState?.currentCompany || {};
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const [conditionGroups, setConditionGroups] = useState([generateGroup()]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [checked, setChecked] = useState(true);
  const [messageData, setMessageData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const proFormRef = useRef<any>();
  const [collapseShow, setCollapseShow] = useState<string[]>([
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
  ]);
  // const [goodsPagination, setGoodsPagination] = useState({
  //   current: 1,
  //   pageSize: 20,
  //   total: 0,
  // });
  // const [goodsLoading, setGoodsLoading] = useState(false);
  // const [goodsOptions, setGoodsOptions] = useState<any[]>([]);
  // 新增：管理接收人数据
  const [receivers, setReceivers] = useState<{ userId: string; nickname: string; phone: string }[]>(
    [],
  );

  const addConditionGroup = () => {
    if (conditionGroups.length >= 10) {
      message.info('条件组不能超过10个');
      return;
    }
    const newGroup = generateGroup();
    setConditionGroups((prev) => [...prev, newGroup]);
    setCollapseShow((prev) => [...prev, String(conditionGroups.length)]);
  };

  const removeConditionGroup = (i: number) => {
    if (conditionGroups.length <= 1) return;
    const prevData = proFormRef.current?.getFieldsFormatValue?.()?.conditionGroups;
    const arr = prevData.filter((group, index) => index !== i);
    setConditionGroups(arr);
    proFormRef.current?.setFieldsValue({
      conditionGroups: arr,
    });
    // setConditionGroups((prev) => prevData.filter((group) => group.id !== id));
  };

  // 新增：处理删除行
  const handleDelete = (userId: string) => {
    setReceivers(receivers.filter((item) => item.userId !== userId));
  };

  const onChange: CheckboxProps['onChange'] = (e) => {
    setChecked(e.target.checked);
  };

  //产品名称下拉框数据
  const getProductListReq = useRequest(getUnifyPullDownStock, {
    defaultParams: [{ getUnifyPullDown: coId, type: '3' }],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: res.data[key],
          label: res.data[key],
        };
      });
    },
  });
  // 获取商品列表
  const getGoods = useRequest(getAllGoods, {
    defaultParams: [
      {
        current: '1',
        pageSize: '9999',
        commerceCompanyId: currentCompanyId,
        distributorId: currentCompanyId,
      },
    ],
    formatResult: (res: any) => {
      return res.data.data?.map((item, index) => {
        return {
          value: item.goodsName,
          label: item.goodsName,
        };
      });
    },
  });
  // 下拉加载的写法
  // const loadGoods = async (current: number, pageSize: number) => {
  //   setGoodsLoading(true);
  //   try {
  //     const res = await getStockProductList({
  //       current: current.toString(),
  //       pageSize: pageSize.toString(),
  //       commerceCompanyId: currentCompanyId,
  //       distributorId: currentCompanyId,
  //     });

  //     if (res.code === 20000) {
  //       const newOptions = res.data.data.map((item: any) => ({
  //         value: item.goodsId,
  //         label: item.goodsName,
  //       }));

  //       setGoodsOptions((prev) => (current === 1 ? newOptions : [...prev, ...newOptions]));

  //       setGoodsPagination({
  //         current,
  //         pageSize,
  //         total: res.data.total,
  //       });
  //     }
  //   } finally {
  //     setGoodsLoading(false);
  //   }
  // };

  // // 商品选择框滚动处理
  // const handleGoodsScroll = async (e: React.UIEvent<HTMLDivElement>) => {
  //   const { currentTarget } = e;
  //   const { scrollTop, scrollHeight, clientHeight } = currentTarget;

  //   // 触底判断（距离底部50px时加载）
  //   if (
  //     scrollHeight - scrollTop - clientHeight < 50 &&
  //     !goodsLoading &&
  //     goodsOptions.length < goodsPagination.total
  //   ) {
  //     await loadGoods(goodsPagination.current + 1, goodsPagination.pageSize);
  //   }
  // };
  // useEffect(() => {
  //   console.log(goodsOptions);
  // }, []);
  // 回显
  const getMessage = async () => {
    if (searchParams.get('data')) {
      const parsedData = JSON.parse(searchParams.get('data'));
      console.log(parsedData);
      // console.log(JSON.parse(`"${searchParams.get('data')}"`));
      // 解析为对象
      // const parsedData = JSON.parse(unescapedString);
      const { data, notificationChannels, replenish, user } = parsedData;
      setMessageData(parsedData);
      setConditionGroups(data);
      setReceivers(user);
      setChecked(replenish == 1);
      proFormRef.current?.setFieldsValue({
        conditionGroups: data || [],
        notificationChannels,
        replenish,
      });
      setLoading(false);
      return;
    }
    setLoading(true);
    try {
      const res = await getMessageDetail(currentCompanyId);
      setLoading(false);
      if (JSON.stringify(res.data) !== '{}' && res.data !== null) {
        setMessageData(res.data);
        // 转换条件组数据，添加唯一 id
        const transformedGroups = res.data.data?.map((group: any, index: number) => ({
          id: group.id || generateGroup().id,
          condition: group.condition.map((cond: any) => ({
            ...cond,
            // 确保字段名匹配，如 conditionValue 转换为对应字段
            conditionValue: cond.conditionValue || cond.value,
          })),
        })) || [generateGroup()];
        setConditionGroups(transformedGroups || []);
        // 设置接收人
        if (res.data.user) {
          setReceivers(res.data.user);
        }
        // 设置复选框
        setChecked(res.data.replenish === 1);
        // 更新表单初始值
        proFormRef.current?.setFieldsValue({
          conditionGroups: transformedGroups || [],
          notificationChannels: res.data.notificationChannels,
          replenish: res.data.replenish,
        });
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // loadGoods(1, goodsPagination.pageSize);
    getMessage();
  }, []);
  // useEffect(() => {
  //   console.log(proFormRef.current?.getFieldsFormatValue?.());
  // }, [conditionGroups]);
  // 表格列定义
  // const columns = [
  //   {
  //     title: '昵称',
  //     dataIndex: 'nickname',
  //     key: 'userId',
  //     width: 300,
  //   },
  //   {
  //     title: '手机号',
  //     dataIndex: 'phone',
  //     key: 'userId',
  //     width: 300,
  //   },
  //   {
  //     title: '操作',
  //     dataIndex: 'operation',
  //     key: 'operation',
  //     width: 200,
  //     render: (_, record) => (
  //       <Button type="link" danger onClick={() => handleDelete(record.userId)}>
  //         删除
  //       </Button>
  //     ),
  //   },
  // ];

  const TableCom = () => {
    return (
      <div style={{ width: '90%' }}>
        <div style={{ display: 'flex', alignItems: 'baseline' }}>
          <span style={{ width: '75px' }}>通知渠道：</span>
          <ProFormItem name="notificationChannels" initialValue={messageData?.notificationChannels}>
            <Select mode="multiple" placeholder="选择通知渠道" style={{ width: '300px' }}>
              <Option value={0}>站内信</Option>
              <Option value={1}>短信</Option>
            </Select>
          </ProFormItem>
        </div>
        <div style={{ marginBottom: 20, display: 'flex', alignItems: 'center' }}>
          <span style={{ width: '60px' }}>接收人：</span>
          <div className={styles.receiversBox}>
            {/* <Table columns={columns} dataSource={receivers} rowKey={(record) => record.userId} /> */}
            {receivers?.map((item, index) => {
              return (
                <div className={styles.mytag}>
                  <span className={styles.text}>{item.nickname}</span>
                  <span className={styles.text}>{item.phone}</span>
                  <CloseOutlined
                    className={styles.outLind}
                    onClick={() => handleDelete(item.userId)}
                  />
                </div>
              );
            })}
            <Button type="link" onClick={() => setIsModalOpen(true)}>
              <PlusCircleOutlined style={{ fontSize: '20px' }} />
            </Button>
            <EmployeeModal
              visible={isModalOpen}
              existingReceivers={receivers.map((r) => r.userId)} // 新增传递已存在的keys
              onCancel={() => setIsModalOpen(false)}
              onConfirm={(selectedKeys, data) => {
                const selectedReceivers = data.filter(
                  (item) =>
                    selectedKeys.includes(item.userId) &&
                    !receivers.some((r) => r.userId === item.userId),
                );
                setReceivers([...receivers, ...selectedReceivers]);
              }}
            />
          </div>
        </div>
        <ProFormItem name="replenish">
          <Checkbox checked={checked} onChange={onChange}>
            回补完成时通知所有预警接收人
          </Checkbox>
          <Tooltip title="适用高频缺货商品">
            <QuestionCircleOutlined />
          </Tooltip>
        </ProFormItem>
      </div>
    );
  };
  const renderConditionGroup = (item: any, index: number) => ({
    key: String(index),
    label: (
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <span>条件组 {index + 1}</span>
        {conditionGroups.length > 1 && conditionGroups.length - 1 === index && (
          <Button
            type="text"
            key={`remove-btn-${index}`}
            danger
            icon={<CloseOutlined />}
            onClick={() => removeConditionGroup(index)}
            style={{
              position: 'absolute',
              right: '6px',
              top: '7px',
              zIndex: 1,
              color: '#000',
            }}
          />
        )}
      </div>
    ),
    children: (
      <ProFormList
        key={`form-list-${index}`}
        // label={`条件组${index + 1}`}
        name={['conditionGroups', index, 'condition']}
        // initialValue={item.condition || []}
        alwaysShowItemLabel
        creatorButtonProps={{
          creatorButtonText: '添加条件',
        }}
        max={10}
        style={{
          background: '#fff',
          width: '100%',
          padding: '8px',
          marginBottom: 16,
        }}
      >
        {/* 原有的条件组内容保持不变 */}
        <ProForm.Group key={`group-${index}`}>
          <ProFormSelect
            options={[
              { value: 1, label: '商品库存' },
              { value: 2, label: '产品范围' },
              { value: 3, label: '商品范围' },
            ]}
            name="condition"
            rules={[
              {
                required: true,
              },
            ]}
            width={400}
          />
          <ProFormDependency name={['condition']}>
            {({ condition }) =>
              condition == 1 ? (
                <ProFormSelect
                  options={[
                    { value: '1', label: '小于' },
                    { value: '2', label: '等于' },
                    { value: '3', label: '小于等于' },
                  ]}
                  width={430}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  name="symbol"
                />
              ) : (
                <ProFormSelect
                  options={[
                    { value: '4', label: '包含' },
                    { value: '5', label: '不包含' },
                  ]}
                  width={430}
                  name="symbol"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                />
              )
            }
          </ProFormDependency>
          <ProFormDependency name={['condition']}>
            {({ condition }) =>
              condition == 1 ? (
                <ProFormText
                  name="number"
                  width={480}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                />
              ) : condition == 2 ? (
                <ProFormSelect
                  name="conditionValue"
                  width={480}
                  fieldProps={{
                    mode: 'multiple',
                    options: getProductListReq.data,
                    loading: getProductListReq.loading,
                  }}
                  placeholder="请选择产品"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                />
              ) : (
                <ProFormSelect
                  name="conditionValue"
                  width={480}
                  fieldProps={{
                    mode: 'multiple',
                    options: getGoods.data,
                    loading: getGoods.loading,
                    // options: goodsOptions,
                    // loading: goodsLoading,
                    // onPopupScroll: handleGoodsScroll, // 添加滚动事件
                    // // 显示加载更多提示
                    // dropdownRender: (menu) => (
                    //   <>
                    //     {menu}
                    //     {goodsLoading && (
                    //       <div style={{ padding: 8, textAlign: 'center' }}>加载中...</div>
                    //     )}
                    //     {!goodsLoading && goodsOptions.length < goodsPagination.total && (
                    //       <div style={{ padding: 8, textAlign: 'center' }}>
                    //         <Button
                    //           type="link"
                    //           onClick={() =>
                    //             loadGoods(
                    //               goodsPagination.current + 1,
                    //               goodsPagination.pageSize,
                    //             )
                    //           }
                    //         >
                    //           加载更多
                    //         </Button>
                    //       </div>
                    //     )}
                    //   </>
                    // ),
                  }}
                  placeholder="请选择商品"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                />
              )
            }
          </ProFormDependency>
        </ProForm.Group>
      </ProFormList>
    ),
  });

  return (
    <>
      <style>{`
         .ant-pro-form-list-item-show-label{
         justify-content: space-between;
        }
         .ant-pro-footer-bar-left {
         flex: none !important;
         }
      `}</style>
      <div style={{ background: '#fff', padding: '24px', minHeight: '720px' }}>
        <div style={{ color: '#999' }}>
          为了减轻经营中重复且必要的工作，库存预警会帮助你实时监控商品库存状况，发送提醒从而提升人效。预计每周为你节省1人/日工作量
        </div>
        <div className={styles.flexB}>
          <div className={styles.flexB} style={{ margin: '30px 0 16px' }}>
            <div className={styles.line} />
            <span className={styles.title}>执行条件</span>
          </div>
          {conditionGroups.length < 10 && (
            <Button type="primary" onClick={addConditionGroup}>
              + 添加条件组
            </Button>
          )}
        </div>
        <ProForm
          formRef={proFormRef}
          loading={loading}
          submitter={{
            // render: (_, dom) => <div className={styles.botBtn}>{dom}</div>,
            render: (_, dom) => <FooterToolbar className={styles.botBtn}>{dom}</FooterToolbar>,
          }}
          onFinish={async (values) => {
            if (
              !(conditionGroups?.length > 0 && values.conditionGroups[0]?.condition?.length > 0)
            ) {
              message.warning('请至少填写一项条件组');
              return;
            }
            if (values?.notificationChannels?.length === 0) {
              message.warning('请至少选择一条通知渠道');
              return;
            }
            if (receivers?.length === 0) {
              message.warning('请至少选择一位接收人');
              return;
            }
            setConditionGroups(values?.conditionGroups || []);
            const submitData = {
              data: values.conditionGroups,
              enterpriseId: currentCompanyId,
              replenish: checked ? 1 : 0,
              notificationChannels: values.notificationChannels,
              user: receivers,
            };
            if (!messageData) {
              const res = await addMessage(submitData);
              if (res.code === 20000) {
                message.success('提交成功');
              }
              return true;
            }
            const ress = await updMessage(submitData);
            if (ress.code === 20000) {
              message.success('提交成功');
            }
            return true;
          }}
          // onValuesChange={(_changedValues: any, values: any) => {
          //   //  console.log(_changedValues,values,8999);
          //   setConditionGroups(values);
          // }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Collapse
              activeKey={collapseShow}
              // defaultActiveKey={[0, 1, 2, 3, 4, 5]}
              onChange={(key: string[]) => {
                setCollapseShow(key);
              }}
              items={conditionGroups.map((item, index) => renderConditionGroup(item, index))}
             />
          </Space>
          <div className={styles.flexB}>
            <div className={styles.flexB} style={{ margin: '30px 0 16px' }}>
              <div className={styles.line} />
              <span className={styles.title}>发送消息通知</span>
            </div>
          </div>
          <TableCom />
        </ProForm>
      </div>
    </>
  );
};

export default InventoryAlert;
