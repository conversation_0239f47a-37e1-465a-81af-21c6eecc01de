/*
 * @FilePath: cashSettlement.ts
 * @Author: chent<PERSON><PERSON><PERSON>
 * @Date: 2022-10-19 09:25:06
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-10-19 17:38:05
 * Copyright: 2022 xxxTech CO.,LTD. All Rights Reserved.
 * @Descripttion:
 */

import { request } from '@umijs/max';
import { scenicHost } from '.';
import type { API } from './typings';

// 现金账单后端 list 接口格式
interface ResponseListData<T = Record<string, any>> {
  code: number;
  data: {
    data: T;
    current: number;
    pageSize: number;
    total: number;
  };
  msg: string;
}

interface UserItem {
  companyId: string;
  companyIds: any[];
  status: number;
  userId: string;
  nickname: string;
  username: string;
}

type ResponseData<T> = {
  code: number;
  data: T;
  msg: string;
};

/** 获取现金账单 list */
export async function getCashSettlementList(
  params: API.ICashLIstParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.ICashSettlementListResponse[]>>(
    `${scenicHost}/orderCash/orderCashPayCountPage`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/** 查询企业用户列表 */
export function getUserList(params: {
  relationId?: string;
  companyId: string;
  status?: number;
  appId: string;
  system: number; // 禁用系统类型：1 景区系统，2 电商系统，3 慧旅云
}) {
  return request<ResponseData<UserItem[]>>(`${scenicHost}/orgStructure/userCompanyInfoList`, {
    method: 'POST',
    data: params,
  });
}

/** 新增现金账单统计 */
export function addCashSettlementStatistics(params: { staffName?: string; staffAccount?: string }) {
  return request(`${scenicHost}/orderCash/orderCashPayCount`, {
    method: 'POST',
    data: params,
  });
}

/** 查看现金账单明细 */
export function getCashSettlementDetail(
  params: API.ICashDetailParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.ICashDetailResponse>>(
    `${scenicHost}/orderCash/orderCashDetailedPage`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/** 修改现金账单结算状态 */
export function updetaCashSettlementState(
  params: API.IUpdateCashSettlementStateParams,
  options: Record<string, any> = {},
) {
  return request(`${scenicHost}/orderCash/state`, {
    method: 'PUT',
    data: params,
    ...options,
  });
}
