import type { AxiosResponse, RequestConfig, RequestOptions } from '@@/plugin-request/request';
import { history } from '@umijs/max';
import { message } from 'antd';
import { CodeType, SERVICE_CODE_ERROR } from './common/utils/code';
import { goToLogin } from './common/utils/tool';
import type { ResponseData } from './services/typings';

// 与后端约定的响应数据格式
/**
 * @name 错误处理
 * pro 自带的错误处理，可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
const codeMap = new Map();

export const errorConfig: RequestConfig<ResponseData> = {
  errorConfig: {
    //  此函数不执行  https://github.com/umijs/umi/issues/8381
    errorThrower: () => {
      // const { code, msg } = res;
      // if (code !== CodeType.SUCCUSS) {
      //   const error: any = new Error(msg);
      //   error.name = 'BizError';
      //   error.info = res;
      //   throw error; // 抛出自制的错误
      // }
    },
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      // 我们的 errorThrower 抛出的错误。
      if (error.name === SERVICE_CODE_ERROR) {
        const errorInfo: ResponseData | undefined = error.data;
        if (errorInfo) {
          const { code, msg } = errorInfo || {};

          // 未登录
          switch (code) {
            case CodeType.UNAUTHORIZED:
              if (!codeMap.get(CodeType.UNAUTHORIZED)) {
                codeMap.set(code, true);
                message.warning(msg, 1, () => {
                  codeMap.set(code, false);
                  goToLogin();
                });
              }
              break;
            // 无权限
            case CodeType.NO_PERMISSION:
              message.warning('无访问权限', 1, () => {
                if (history.location.pathname !== '/welcome') {
                  history.push('/welcome');
                  location.reload();
                }
              });

              break;

            default:
              message.warning(msg);
          }
        }
      } else if (error.response) {
        // Axios 的错误
        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        message.error(`Response status:${error.response.status}`);
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在 node.js 中是 http.ClientRequest 的实例
        message.error('None response! Please retry.');
      } else {
        // 发送请求时出了点问题
        message.error('Request error, please retry.');
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (config: RequestOptions) => {
      const { headers, params, method, data, requestType } = config;
      const coId = localStorage.getItem('currentCompanyId');
      const common = {
        systemType: 'e-commerce',
        commerceCompanyId: coId,
      };
      if (method === 'get' || method === 'delete') {
        config.params = { ...params, ...common };
      } else if (requestType === 'form') {
        config.data = data;
      } else {
        config.data = { ...data, ...common };
      }

      return {
        ...config,
        withCredentials: false,
        headers: {
          ...headers,
          'System-Type': 'e-commerce',
          // 'X-extra-info': sessionStorage.getItem('x-extra-info') || '',
          'Content-Type': 'application/json; charset=utf-8',
          'X-Trace-Id': Math.random().toString(36).substring(2, 6) + '-' + new Date().getTime(), // 本次请求唯一标识
        },
      };
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // 拦截响应数据，进行个性化处理
      const { status, data, config } = response as AxiosResponse<ResponseData>;

      if (status >= 200 && status < 300) {
        if (
          data.code !== CodeType.SUCCUSS &&
          data.code !== CodeType.DATA_SUCCUSS &&
          config?.requestType !== 'form'
        ) {
          return Promise.reject({
            name: SERVICE_CODE_ERROR,
            ...response,
          });
        }

        return response;
      } else {
        // do something
        return Promise.reject(response);
      }
    },
  ],
};
