import { getInstanceByDom, init } from 'echarts';
import React, { useEffect, useRef } from 'react';

export interface IChart {
  options: any;
}

const Chart: React.FC<IChart> = ({ options }) => {
  const chartRef = useRef(null);
  let chartInstance: any = null;
  const renderChart = () => {
    try {
      const renderedInstance = getInstanceByDom(chartRef.current!);
      if (renderedInstance) {
        chartInstance = renderedInstance;
      } else {
        chartInstance = init(chartRef.current!);
      }

      chartInstance.setOption(options);
    } catch (err) {
      console.error(err);
      if (chartInstance) {
        chartInstance.dispose();
      }
    }
  };

  // 页面初始化时，开始渲染图表
  useEffect(() => {
    renderChart();
  }, [options]);

  // 监听窗口大小改变
  useEffect(() => {
    const dom: any = document.querySelector('main');
    const observer = new ResizeObserver(() => chartInstance.resize());
    observer.observe(dom);
    return () => {
      observer.unobserve(dom);
      if (chartInstance) chartInstance.dispose();
    };
  }, []);

  return <div ref={chartRef} style={{ width: '100%', height: 320 }} />;
};

export default Chart;
