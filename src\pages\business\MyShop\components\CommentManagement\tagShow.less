.tagBox {
  padding: 29px 24px 100px;
  background-color: #fff;

  .sideBox {
    background: #FAFAFA;
    width: 894px;
    min-height: 290px;
    margin-bottom: 16px;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    flex-direction: row;
    align-content: flex-start;
    padding: 16px;

    .tagItem {
      padding: 6px 8px;
      color: #1890FF;
      border-radius: 4px;
      border: 1px solid #1890FF;
      height: 32px;
      display: flex;
      align-items: center;
      gap: 10px;
      cursor: grab;
      transition: all 0.2s ease;
      
      // 拖拽时的鼠标样式
      &:active {
        cursor: grabbing;
      }
    }
    
    // 拖拽中的标签样式
    .dragging {
      opacity: 0.7;
      transform: scale(1.05);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      background-color: rgba(24, 144, 255, 0.05);
      border-color: #096dd9;
      color: #096dd9;
      z-index: 10;
    }
    
    // 拖拽目标位置的样式
    .dragOver {
      background-color: rgba(24, 144, 255, 0.1);
      transform: scale(1.02);
    }
  }
}  