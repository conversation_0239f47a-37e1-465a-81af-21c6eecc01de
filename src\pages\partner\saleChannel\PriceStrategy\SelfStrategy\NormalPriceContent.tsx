import React, { useEffect, useState } from 'react';
import { Card, Form, Input, Button, message, InputNumber, Table, Switch, Row, Col, Typography, Space, Tooltip, Spin } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { EditOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { ceil } from 'lodash';
import { ticketTypeEnum } from '@/common/utils/enum';
import { useModel } from '@umijs/max';
import { addPriceStrategy } from '@/services/api/distribution';
import { GuideStepStatus } from '@/common/utils/enum';
import { useGuide } from '@/hooks/useGuide';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import type { API } from '@/services/api/typings';
import GoodsTable from './components/GoodsTable';

const { Text } = Typography;

// 定义数据类型
interface DataType {
  key: string;
  productName: string;
  ticketType: string;
  marketPrice: number | string;
  priceRatio: string;
  priceRange: string;
  purchasePrice: number | string;
}

// 商品信息类型
interface GoodsItemType {
  name?: string;
  marketPrice?: number;
  overallDiscount?: number;
  beginDiscount?: number;
  endDiscount?: number;
  purchasePrice?: string[];
  goodsId?: string;
  unitId?: string;
  unitType?: number;
  ruleType?: number;
  goodsType?: string;
  isCompose?: boolean;
  discount?: number;
  composeDiscount?: number;
  salePrice?: number;
  composePrice?: number;
  commissionRate?: number;
  group?: any[];
}

// 表单字段类型
interface PriceFormValues {
  singlePrice: number;
  singlePricePercent: number;
  combinedSaleEnabled: boolean;
  combinedPrice: number;
  combinedPricePercent: number;
}

// 组件 Props 类型
interface NormalPriceContentProps {
  priceData?: API.SelfPriceStrategyItem;
  onSubmit?: (data: API.SelfPriceStrategyItem) => void;
}

const NormalPriceContent: React.FC<NormalPriceContentProps> = ({ priceData, onSubmit }) => {
  // 从缓存中获取商品信息
  const [goodsItem, setGoodsItem] = useState<GoodsItemType>({});
  const { initialState } = useModel('@@initialState');
  const { coId = '', coName = '' } = initialState?.currentCompany || {};
  const { updateGuideInfo } = useGuide();
  const [form] = Form.useForm<PriceFormValues>();
  
  // 保存编辑前的表单值，用于取消时恢复
  const [initialFormValues, setInitialFormValues] = useState<PriceFormValues | null>(null);
  
  // 获取缓存的商品信息
  useEffect(() => {
    const cachedItem = localStorage.getItem('priceStrategyItem');
    if (cachedItem) {
      const parsedItem = JSON.parse(cachedItem);
      setGoodsItem(parsedItem);
    }
  }, []);

  // 市场标准价
  const marketPrice = goodsItem.marketPrice || 0;
  
  // 编辑状态
  const [isEditing, setIsEditing] = useState(false);
  
  // 组合销售开关状态
  const [combinedSaleEnabled, setCombinedSaleEnabled] = useState(goodsItem?.isCompose || false);
  
  // 计算价格区间
  const [beginPrice, endPrice] = [
    ceil((goodsItem?.beginDiscount * marketPrice) / 100, 2),
    ceil((goodsItem?.endDiscount * marketPrice) / 100, 2),
  ];
  
  // 添加 loading 状态
  const [loading, setLoading] = useState(true);
  const [price, setPrice] = useState(0);

  // 使用父组件传递的 priceData 进行数据回显
  useEffect(() => {
    if (goodsItem.unitId && priceData) {
      const currentPrice = (goodsItem?.overallDiscount * goodsItem?.marketPrice) / 100;
      setPrice(currentPrice);
      console.log('goodsItem', goodsItem);
      
      const { price } = priceData;
      const { isCompose, salePrice, composePrice } = price || {};
      
      // 计算折扣百分比
      const sDiscount = salePrice !== undefined ? Math.round((salePrice / currentPrice) * 100) : 100;
      const cDiscount = composePrice !== undefined ? Math.round((composePrice / currentPrice) * 100) : 100;
      console.log('当前价 S', currentPrice);
      console.log('折扣率', sDiscount);
      const singlePrice = salePrice.toFixed(2);
      const singlePricePercent = Math.round((salePrice / marketPrice) * 100);
      const combinedPrice = composePrice.toFixed(2);
      const combinedPricePercent = Math.round((composePrice / marketPrice) * 100);
      const formValues = {
        singlePrice: singlePrice,
        singlePricePercent: singlePricePercent,
        combinedSaleEnabled: isCompose || false,
        combinedPrice: combinedPrice,
        combinedPricePercent: combinedPricePercent,
      };
      console.log(formValues, salePrice, currentPrice);
      console.log(formValues)
      console.log(formValues)
      
      setCombinedSaleEnabled(formValues.combinedSaleEnabled);
      form.setFieldsValue(formValues);
      setInitialFormValues(formValues);
      
      // 数据加载完成，设置 loading 为 false
      setLoading(false);
    } else {
      // 如果没有 unitId 或 priceData，也需要关闭 loading
      const defaultValues = {
        singlePrice: marketPrice,
        singlePricePercent: 100,
        combinedSaleEnabled: false,
        combinedPrice: marketPrice,
        combinedPricePercent: 100,
      };
      form.setFieldsValue(defaultValues);
      setInitialFormValues(defaultValues);
      setLoading(false);
    }
  }, [goodsItem, priceData, form, marketPrice]);

  // 处理单买价格变化
  const handleSinglePriceChange = (value: number) => {
    if (value !== null) {
      console.log('修改单买价格，输入值：', value, '市场标准价：', marketPrice);
      form.setFieldValue('singlePricePercent', Math.round((value / marketPrice) * 100));
    }
  };

  // 处理单买价格百分比变化
  const handleSinglePercentChange = (value: number) => {
    if (value !== null) {
      console.log('修改单买价格百分比，输入值：', value, '市场标准价：', marketPrice);
      form.setFieldValue('singlePrice', parseFloat(((marketPrice * value) / 100).toFixed(2)));
    }
  };

  // 处理组合价格变化
  const handleCombinedPriceChange = (value: number) => {
    if (value !== null) {
      form.setFieldValue('combinedPricePercent', Math.round((value / marketPrice) * 100));
    }
  };

  // 处理组合价格百分比变化
  const handleCombinedPercentChange = (value: number) => {
    if (value !== null) {
      form.setFieldValue('combinedPrice', parseFloat(((marketPrice * value) / 100).toFixed(2)));
    }
  };

  // 处理组合销售开关变化
  const handleCombinedSaleChange = (checked: boolean) => {
    setCombinedSaleEnabled(checked);
    form.setFieldValue('combinedSaleEnabled', checked);
  };

  // 处理编辑按钮点击
  const handleEdit = () => {
    // 保存当前表单值，用于取消时恢复
    setInitialFormValues(form.getFieldsValue());
    setIsEditing(true);
  };

  // 处理表单提交
  const handleFinish = async (values: PriceFormValues) => {
    console.log('保存时，表单中的值：', values);
    console.log('保存时，商品信息：', goodsItem);
    console.log('保存时，市场标准价：', marketPrice);
    setIsEditing(false);
    
    const priceData: any = {
      salePrice: values.singlePrice,
      discount: ceil(values.singlePricePercent / 100, 4),
      isCompose: values.combinedSaleEnabled,
    };
    
    // 只有在启用组合销售时才添加组合价格相关字段
    if (values.combinedSaleEnabled) {
      priceData.composePrice = values.combinedPrice;
      priceData.composeDiscount = ceil(values.combinedPricePercent / 100, 4);
      priceData.commissionRate = goodsItem.commissionRate || 0
    }
    
    // 调用父组件传递的 onSubmit 回调
    if (onSubmit) {
      onSubmit({
        priceId: '',
        price: priceData
      });
    }
  };

  // 处理取消按钮点击
  const handleCancel = () => {
    // 恢复到编辑前的状态
    if (initialFormValues) {
      form.setFieldsValue(initialFormValues);
      setCombinedSaleEnabled(initialFormValues.combinedSaleEnabled);
    }
    setIsEditing(false);
  };

  return (
    <div style={{ maxWidth: '1000px', margin: '0' }}>
      <GoodsTable 
        goodsItem={goodsItem} 
        beginPrice={beginPrice}
        endPrice={endPrice}
      />

      {/* 单价设置部分 - 改为表单形式 */}
      <Spin spinning={loading}>
        <Card 
          style={{ marginTop: 24, background: '#f9f9f9', border: 'none' }}
          bodyStyle={{ padding: '16px 24px' }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Text strong>单价设置：</Text>
            {isEditing ? (
              <Space>
                <Button onClick={handleCancel}>取消</Button>
                <Button type="primary" onClick={form.submit}>保存</Button>
              </Space>
            ) : (
              <Button type="link" onClick={handleEdit}>
                编辑
              </Button>
            )}
          </div>

          <Form
            form={form}
            layout="vertical"
            onFinish={handleFinish}
            disabled={!isEditing}
          >
            <Form.Item
              label={
                <Text>
                  <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
                  单买价格：
                </Text>
              }
              style={{ marginBottom: 16 }}
            >
              <Space align="center" size={8}>
                <Form.Item
                  name="singlePrice"
                  noStyle
                  rules={[
                    { required: true, message: '请输入单买价格' },
                    {
                      validator: (_, value) => {
                        if (value < beginPrice || value > endPrice) {
                          return Promise.reject(`价格必须在 ${beginPrice} 元至 ${endPrice} 元之间`);
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                >
                  <InputNumber
                    style={{ width: 160 }}
                    onChange={handleSinglePriceChange}
                    min={beginPrice || 0}
                    max={endPrice || undefined}
                    precision={2}
                    addonAfter="元"
                  />
                </Form.Item>
                <Text>即市场标准价</Text>
                <Form.Item
                  name="singlePricePercent"
                  noStyle
                  rules={[
                    { required: true, message: '请输入折扣百分比' },
                    {
                      validator: (_, value) => {
                        if (value < (goodsItem?.beginDiscount || 0) || value > (goodsItem?.endDiscount || 100)) {
                          return Promise.reject(`折扣必须在 ${goodsItem?.beginDiscount || 0}% 至 ${goodsItem?.endDiscount || 100}% 之间`);
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                >
                  <InputNumber
                    style={{ width: 160 }}
                    onChange={handleSinglePercentChange}
                    min={goodsItem?.beginDiscount || 0}
                    max={goodsItem?.endDiscount || 100}
                    precision={0}
                    addonAfter="%"
                  />
                </Form.Item>
              </Space>
            </Form.Item>

            {/* 组合销售部分 */}
            <Form.Item
              label={
                <Text strong>
                  <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
                  组合销售：
                </Text>
              }
              name="combinedSaleEnabled"
              valuePropName="checked"
            >
              <Switch 
                onChange={handleCombinedSaleChange}
                className={combinedSaleEnabled ? 'ant-switch-checked' : ''}
                checkedChildren="启用"
                unCheckedChildren="禁用"
              />
            </Form.Item>

            <Form.Item
              label={
                <Text>
                  <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
                  组合价格：
                </Text>
              }
              style={{ marginTop: 16 }}
            >
              <Space align="center" size={8}>
                <Form.Item
                  name="combinedPrice"
                  noStyle
                  rules={[
                    { 
                      required: combinedSaleEnabled, 
                      message: '请输入组合价格' 
                    },
                    {
                      validator: (_, value) => {
                        if (!combinedSaleEnabled) return Promise.resolve();
                        if (value < beginPrice || value > endPrice) {
                          return Promise.reject(`价格必须在 ${beginPrice} 元至 ${endPrice} 元之间`);
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                >
                  <InputNumber
                    style={{ width: 160 }}
                    onChange={handleCombinedPriceChange}
                    disabled={!isEditing || !combinedSaleEnabled}
                    min={beginPrice || 0}
                    max={endPrice || undefined}
                    precision={2}
                    addonAfter="元"
                  />
                </Form.Item>
                <Text>即市场标准价</Text>
                <Form.Item
                  name="combinedPricePercent"
                  noStyle
                  rules={[
                    { 
                      required: combinedSaleEnabled, 
                      message: '请输入折扣百分比' 
                    },
                    {
                      validator: (_, value) => {
                        if (!combinedSaleEnabled) return Promise.resolve();
                        if (value < (goodsItem?.beginDiscount || 0) || value > (goodsItem?.endDiscount || 100)) {
                          return Promise.reject(`折扣必须在 ${goodsItem?.beginDiscount || 0}% 至 ${goodsItem?.endDiscount || 100}% 之间`);
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                >
                  <InputNumber
                    style={{ width: 160 }}
                    onChange={handleCombinedPercentChange}
                    disabled={!isEditing || !combinedSaleEnabled}
                    min={goodsItem?.beginDiscount || 0}
                    max={goodsItem?.endDiscount || 100}
                    precision={0}
                    addonAfter="%"
                  />
                </Form.Item>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      </Spin>
    </div>
  );
};

export default NormalPriceContent; 