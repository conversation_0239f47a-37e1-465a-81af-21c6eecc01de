/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-23 14:25:33
 * @LastEditTime: 2023-09-15 15:40:56
 * @LastEditors: zhangfeng<PERSON>i
 */
import { addOperationLogRequest } from '@/common/utils/operationLog';
import {
  transformDateArrToString,
  transformDateStringToArr,
  transformStringToInfo,
} from '@/common/utils/tool';
import SettlementCycle from '@/components/SettlementCycle';
import type { ModalState } from '@/hooks/useModal';
import { addCreditAccount, getAgentInfoList, updateCreditAccount } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import type { ProFormInstance} from '@ant-design/pro-components';
import { ProForm, ProFormDigit, ProFormSelect } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-table';
import { Modal, message } from 'antd';
import type { FC } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAccess, useModel, useRequest } from '@umijs/max';

const SettlementTypeEnum = {
  WEEK: '每周结算',
  MONTH: '每月结算',
  YEAR: '每年结算',
};
interface CreditAccountModalProps {
  currentItem?: API.CreditAccountItem;
  modalState: ModalState;
  actionRef: React.MutableRefObject<ActionType | undefined>;
}

/**操作 - 新增、详情、修改 */
const CreditAccountModal: FC<CreditAccountModalProps> = ({
  actionRef,
  currentItem,
  modalState: { visible, setVisible, type, setType },
}) => {
  const access = useAccess();

  const { settlementType, settlementCycle, consumer } = currentItem || {};

  const { initialState } = useModel('@@initialState');
  const { settlementId = '', coId = '' } = initialState?.currentCompany || {};

  const [option, setOption] = useState<Record<string, any>>();

  const formRef = useRef<
    ProFormInstance<{
      balance?: number;
      legalName?: string;
      warningAmount: number;
      settlementType: 'WEEK' | 'MONTH' | 'YEAR';
      settlementCycle: any[];
    }>
  >();

  const modalTitle = useMemo(() => {
    switch (type) {
      case 'add':
        return '新增授信客户';
      case 'update':
        return '修改授信客户';
      default:
        return '授信客户详情';
    }
  }, [type]);

  const addCreditAccountReq = useRequest(addCreditAccount, {
    manual: true,
    onSuccess: (data) => {
      message.success('添加成功');
      addOperationLogRequest({
        action: 'add',
        content: `新增授信账户【${option?.settlementName}】`,
      });
      setVisible(false);
      actionRef?.current?.reload();
    },
  });

  const updateCreditAccountReq = useRequest(updateCreditAccount, {
    manual: true,
    onSuccess: () => {
      addOperationLogRequest({
        action: 'edit',
        content: `编辑授信账户【${currentItem?.consumer?.registrationName}】详情`,
      });
      message.success('修改成功');
      setVisible(false);
      actionRef?.current?.reload();
    },
  });

  const onAdd = async () => {
    try {
      const addValues = await formRef.current?.validateFields();
      const { legalName: id = '', settlementType, settlementCycle, ...rest } = addValues!;
      addCreditAccountReq.run({
        ...rest,
        merchantId: settlementId,
        consumer: {
          id,
          type: 'merchant',
        },
        settlementType,
        settlementCycle: transformDateArrToString({
          type: settlementType,
          date: settlementCycle,
        }),
      });
    } catch (error) {}
  };

  const onUpdate = async () => {
    try {
      const updateValues = await formRef.current?.validateFields();
      const { settlementCycle, settlementType, ...rest } = updateValues!;
      updateCreditAccountReq.run({
        ...rest!,
        id: currentItem!.id,
        merchantId: settlementId,
        settlementType,
        settlementCycle: transformDateArrToString({
          type: settlementType,
          date: settlementCycle,
        }),
      });
    } catch (error) {}
  };

  const onOk = useCallback(() => {
    switch (type) {
      case 'add':
        onAdd();
        break;
      case 'update':
        onUpdate();
        break;
      default:
        break;
    }
  }, [type]);

  const onValuesChange = ({ settlementType }: any, values: any) => {
    // 周期单选改变需重置周期值
    if (settlementType) {
      formRef.current?.setFieldsValue({
        settlementCycle: [],
      });
    }
  };

  // 详情列表 用 ProForm.Item 不会打乱样式
  const infoList = [
    {
      label: '编号',
      content: currentItem?.id,
    },
    {
      label: '授信客户账号',
      content: currentItem?.consumer?.registrationName,
    },

    {
      label: '授信客户名称',
      content: currentItem?.consumer?.legalName,
    },
    {
      label: '授信金额',
      content: currentItem?.balance,
    },
    {
      label: '预警值',
      content: currentItem?.warningAmount,
    },
    {
      label: '结算时间',
      content: SettlementTypeEnum[currentItem?.settlementType || ''],
    },
    {
      label: ' ',
      content: transformStringToInfo({
        type: currentItem?.settlementType || 'WEEK',
        date: currentItem?.settlementCycle || '',
      }),
    },
  ];
  const initSettlementCycle = useMemo(() => {
    return type === 'update' && settlementType === 'YEAR'
      ? transformDateStringToArr({
          type: settlementType || 'WEEK',
          date: settlementCycle || '',
        })
      : [];
  }, [settlementCycle, settlementType, type]);
  // 添加重置、修改赋值表单
  useEffect(() => {
    if (visible) {
      if (type === 'add') {
        formRef.current?.resetFields();
      }
      if (type === 'update' && currentItem) {
        formRef.current?.setFieldsValue({
          ...currentItem,
          legalName: consumer?.legalName,
          settlementCycle: transformDateStringToArr({
            type: settlementType || 'WEEK',
            date: settlementCycle || '',
          }),
        });
      }
    }
  }, [type, visible]);

  return (
    <Modal
      width={900}
      title={modalTitle}
      visible={visible}
      maskClosable
      // destroyOnClose
      confirmLoading={type === 'add' ? addCreditAccountReq.loading : updateCreditAccountReq.loading}
      onCancel={() => setVisible(false)}
      onOk={onOk}
      okButtonProps={{
        style: {
          display: type === 'info' ? 'none' : undefined,
        },
      }}
    >
      <ProForm
        formRef={formRef}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 8 }}
        layout="horizontal"
        autoComplete="off"
        preserve={false}
        initialValues={{
          settlementType: 'WEEK',
          settlementCycle: initSettlementCycle,
        }}
        onValuesChange={onValuesChange}
        submitter={{
          render: false,
        }}
      >
        {type === 'info' &&
          infoList.map((item, index) => (
            <ProForm.Item key={item.label} label={item.label} colon={item.label !== ' '}>
              {item.content ?? '-'}
            </ProForm.Item>
          ))}

        {type !== 'info' && (
          <>
            {type === 'add' && (
              <ProFormSelect
                name="legalName"
                label="授信客户名称"
                rules={[
                  {
                    required: true,
                  },
                ]}
                request={async () => {
                  const { data } = await getAgentInfoList({ id: coId });
                  return data.map((item) => ({
                    label: item.coName,
                    value: item.settlementId,
                    settlementName: item.settlementName,
                  }));
                }}
                fieldProps={{
                  onChange(value, option) {
                    setOption(option);
                  },
                }}
              />
            )}

            {type === 'update' && (
              <>
                {infoList.slice(0, 3).map((item, index) => (
                  <ProForm.Item key={item.label} label={item.label} colon={item.label !== ' '}>
                    {item.content ?? '-'}
                  </ProForm.Item>
                ))}
                <ProFormDigit name="balance" disabled label="授信金额（元）" />
              </>
            )}
            <ProFormDigit
              min={0}
              name="warningAmount"
              label="预警值（元）"
              rules={[
                {
                  required: true,
                },
              ]}
              placeholder="请输入预警值"
            />
            <SettlementCycle
              wrapperCol={{ span: 20 }}
              radioLabel="结算时间"
              radioName="settlementType"
              dateName="settlementCycle"
              formRef={formRef}
            />
          </>
        )}
      </ProForm>
    </Modal>
  );
};

export default CreditAccountModal;
