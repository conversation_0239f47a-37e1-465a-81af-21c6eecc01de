import { loginOut } from '@/common/utils/app';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { DownOutlined, LogoutOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons';
import { history, useAccess, useModel } from '@umijs/max';
import { Avatar, Menu, Space, Spin } from 'antd';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, { useCallback } from 'react';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
import { getEnv } from '@/common/utils/getEnv';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu }) => {
  const access = useAccess();
  const { initialState, setInitialState } = useModel('@@initialState');
  const { CAS_HOST, IMG_HOST } = getEnv();

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'logout') {
        setInitialState((s) => ({ ...s, currentUser: undefined }));
        addOperationLogRequest({
          action: 'login',
          content: '退出登录慧景云系统',
        });
        loginOut();
        return;
      }
      if (key === 'center') {
        addOperationLogRequest({
          action: 'link',
          content: '跳转个人中心',
        });
        // location.href = `${CAS_HOST}/#/`; //当前页开打，2022 年 6 月 2 日改为新窗口打开
        open(`${CAS_HOST}/#/accountsettings`);
        return;
      }
      history.push(`/account/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );
  if (!initialState) {
    return loading;
  }

  const menuHeaderDropdown = (
    <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
      {menu && (
        <Menu.Item key="center">
          <UserOutlined />
          个人中心
        </Menu.Item>
      )}
      {menu && (
        <Menu.Item key="settings">
          <SettingOutlined />
          个人设置
        </Menu.Item>
      )}
      {menu && <Menu.Divider />}
      <Menu.Item key="center">
        <UserOutlined />
        个人中心
      </Menu.Item>
      <Menu.Item key="logout">
        <LogoutOutlined />
        退出登录
      </Menu.Item>
    </Menu>
  );
  return (
    <div className="pointer">
      <HeaderDropdown overlay={menuHeaderDropdown}>
        <Space align="center">
          {initialState.userInfo?.avatar ? (
            <Avatar size="small" src={`${IMG_HOST}${initialState.userInfo.avatar}`} alt="avatar" />
          ) : (
            ''
          )}
          <span>
            {initialState?.userInfo?.nickname ||
              initialState?.userInfo?.username ||
              initialState?.userInfo?.phone}
          </span>
          <DownOutlined />
        </Space>
      </HeaderDropdown>
    </div>
  );
};

export default AvatarDropdown;
