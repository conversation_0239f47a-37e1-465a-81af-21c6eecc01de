/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-06 16:45:25
 * @LastEditTime: 2023-03-29 14:52:14
 * @LastEditors: zhangfengfei
 */
import { ticketTypeEnum, whetherEnum } from '@/common/utils/enum';
import { getUniqueId } from '@/common/utils/tool';
import { apiSimpleGoods, getProductConfigDetail } from '@/services/api/distribution';
import { getTravelCardGoodsGoodsList } from '@/services/api/ticket';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';

import { tableConfig } from '@/common/utils/config';
import { isNil } from 'lodash';
import Detail from './CommodityDetails';
import TravelDetail from './TravelCommodityDetails';

const CommodityList = ({
  listVisible,
  setListVisible,
  product: { ticketId, scenicId, distributorTicketStockConfigId },
  isTravel,
  isChain,
}: any) => {
  const columns: ProColumns[] = [
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '产品名称',
      dataIndex: isTravel ? 'goodsName' : 'name',
      search: false,
    },
    {
      title: '票种',
      dataIndex: 'type',
      valueEnum: ticketTypeEnum,
      renderText: (text) => {
        if (isTravel) {
          return '权益卡';
        }
        return text;
      },
    },
    {
      title: '数字资产',
      dataIndex: 'isDigit',
      valueEnum: whetherEnum,
    },
    {
      title: '分时预约',
      dataIndex: 'beginTime',
      search: false,
      render: (dom: any, entity: any) => {
        if (isTravel) {
          return '-';
        }
        return dom ? entity.beginTime + ' - ' + entity.endTime : '-';
      },
    },
    {
      search: false,
      title: '特殊折扣率',
      dataIndex: 'overallDiscount',
    },
    {
      search: false,
      title: '分销折扣区间',
      key: 'discount',
      render: (_, entity: any) =>
        !isNil(entity.beginDiscount ?? entity.endDiscount)
          ? entity.beginDiscount + ' ~ ' + entity.endDiscount
          : '-',
    },
    // {
    //   title: '商品有效期（天）',
    //   dataIndex: isTravel ? 'effectiveTime' : 'validityDay',
    //   hideInSearch: true,
    // },
    // {
    //   title: '是否有效',
    //   dataIndex: 'isEnable',
    //   search: false,
    //   render: (dom: any) => <Tag color={['red', 'blue'][dom]}>{['禁用', '启用'][dom]}</Tag>,
    // },
    {
      hideInTable: isTravel,
      title: '定价（元）',
      dataIndex: 'price',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, entity) => (
        <a
          key={getUniqueId()}
          onClick={() => {
            if (isTravel) {
              TravelDetail.show(entity.id, entity);
            } else {
              Detail.show(entity.id);
            }
          }}
        >
          查看
        </a>
      ),
    },
  ];
  const tableListReq = async (params: any) => {
    const { data } = await (isTravel
      ? getTravelCardGoodsGoodsList(params)
      : apiSimpleGoods({
          ...params,
          isChain: isChain ? 1 : 0,
        }));
    if (isTravel) {
      return data;
    }
    // 普通门票需要多调一个查定价的接口
    const { data: goodsDetails } = await getProductConfigDetail({
      productConfigId: distributorTicketStockConfigId,
    });
    const newData = data.data.map((item) => {
      const currentItem = goodsDetails.configGoodsDetail.find((i) => i.goodsId === item.id);
      return {
        ...item,
        price: currentItem?.price,
      };
    });

    return { ...data, data: newData };
  };

  return (
    <Modal
      width={1200}
      title={'商品列表'}
      visible={listVisible}
      footer={false}
      onCancel={() => {
        setListVisible(false);
      }}
      destroyOnClose
    >
      <ProTable
        {...tableConfig}
        rowKey={getUniqueId()}
        options={{ setting: false, density: false }}
        search={false}
        params={{ scenicId, [isTravel ? 'travelCardId' : 'ticketId']: ticketId }}
        request={tableListReq}
        // request={apiSimpleGoods}
        columns={columns}
      />
    </Modal>
  );
};

export default CommodityList;
