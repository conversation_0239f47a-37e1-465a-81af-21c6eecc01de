/**
 * 搜索树，带搜索栏的树组件
 * 传参@see this.SearchTreeProps
 */
import type { TreeProps } from 'antd';
import { Input, message, Tag, Tooltip, Tree, Typography } from 'antd';
import type RcTree from 'rc-tree';
import React, { useEffect, useRef, useState } from 'react';

import routesAccess from '@/../config/routes';
import { modelWidth } from '@/common/utils/gConfig';
import type { DataNode, Key } from 'rc-tree/lib/interface';

const { Search } = Input;
const { Text } = Typography;

const gData: DataNode[] = [];
const generateData = (data: DataNode[] | undefined) => {
  if (data) {
    gData.push(...data);
  }
};

const dataList: DataNode[] = [];
const generateList = (data: DataNode[]) => {
  for (let i = 0; i < data.length; i++) {
    const node = data[i];
    const { key, title } = node;
    dataList.push({ key, title: title });
    if (node.children) {
      generateList(node.children);
    }
  }
};
function clearData() {
  gData.length = 0;
  dataList.length = 0;
}

const getParentKey = (key: Key, tree: DataNode[]): Key | undefined => {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey;
};

//过滤权限选择，只保留第三层以上的的最终选择(角色管理带ＩＤ，所以有三个 | )
function filterPermissions(permissions: string): boolean {
  const symbol = '|';
  let index = permissions.indexOf(symbol);
  let num = 0;
  while (index !== -1) {
    num++;
    index = permissions.indexOf(symbol, index + 1);
  }
  return num >= 2;
}

interface SearchTreeProps extends TreeProps {
  /** 搜索栏的文字占位符 */
  searchPlaceholder: string;
  /** 树形选择之后的回调 */
  setCheck?: React.Dispatch<React.SetStateAction<React.Key[] | undefined>>;
  /** 是否在最后一项显示权限字符串 */
  showTooltip?: boolean;
}

let seachCount = 0;
let show = false;
let jumpKey: Key[] = [];

const SearchTree = ({
  searchPlaceholder,
  onCheck,
  defaultCheckedKeys,
  treeData,
  showTooltip = true, //默认显示Tooltip
}: SearchTreeProps) => {
  const treeRef = useRef<RcTree>(null);
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const [init, setInit] = useState<boolean>(false);

  const onExpand = (expandedKeys: Key[]) => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };

  const onSearch = async (value: string) => {
    jumpKey = [];
    show = true;
    //过滤无需查询
    let input = value.trim();
    if (input.length == 0 || input === '/') {
      input = '';
    }

    const expandedKeys = dataList
      .map((item: DataNode) => {
        if (input.length != 0 && String(item.title).indexOf(input) > -1) {
          return getParentKey(item.key!, gData);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i); //过滤掉null

    setSearchValue(input);
    setAutoExpandParent(true);
    setExpandedKeys(expandedKeys as Key[]);
    setTimeout(() => {
      if (jumpKey.length > 0 && treeRef) {
        treeRef.current?.scrollTo({ key: jumpKey[0], align: 'top' });
      }
    }, 50);
  };

  const loop = (data: DataNode[]): DataNode[] => {
    const d = data.map((item: DataNode) => {
      const t = String(item.title!);
      const index = t.indexOf(searchValue);
      const beforeStr = t.slice(0, index);
      const afterStr = t.slice(index + searchValue.length);
      //输入查询到的高亮
      let title;
      if (index > -1 && searchValue.length > 0) {
        title = (
          <span>
            {beforeStr}
            <Tag color="blue">{searchValue}</Tag>
            {afterStr}
          </span>
        );
        seachCount++;
        jumpKey.push(item.key);
      } else {
        title = <span>{t}</span>;
      }
      //只要最后一层
      if (showTooltip && filterPermissions(item.key as string)) {
        title = (
          <Tooltip placement="right" title={item.key}>
            {title}
          </Tooltip>
        );
      }
      if (item.children) {
        return { title, key: item.key, children: loop(item.children) };
      }
      return {
        title,
        key: item.key,
      };
    });
    return d;
  };

  const getData = (): DataNode[] => {
    seachCount = 0;
    const d = loop(gData);
    if (show) {
      message.success(`已搜索 ${seachCount} 条数据`);
      show = false;
    }
    const s = JSON.stringify(routesAccess);
    const i = (v: any) => {
      const r = s.indexOf('can' + v.key.split('|').at(-1));
      return r == -1 ? Infinity : r;
    };
    d[0]?.children?.sort((a, b) => i(a) - i(b));
    return d;
  };

  useEffect(() => {
    clearData();
    generateData(treeData);
    generateList(gData);
    setInit(true);
  }, []); // 如果指定的是[], 回调函数只会在第一次render()后执行

  return (
    <div>
      <Search style={{ marginBottom: 8 }} placeholder={searchPlaceholder} onSearch={onSearch} />
      {
        //2022年5月25日 修改渲染逻辑使其在初始化结束后再渲染。
        //猜测先前的直接渲染可能是因为其组件渲染时复用机制导致defaultCheckedKeys第一次无法生效，毕竟treeData是后计算
        init ? (
          <Tree
            height={modelWidth.sm}
            checkable={true}
            ref={treeRef}
            multiple={false}
            selectable={false}
            onExpand={onExpand}
            defaultCheckedKeys={defaultCheckedKeys}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            treeData={getData()}
            onCheck={onCheck}
          />
        ) : (
          '加载中'
        )
      }
    </div>
  );
};

/**
 * 2022年2月7日弃用原先的class写法，因为不好hook
 * 
 * @see https://reactjs.org/warnings/invalid-hook-call-warning.html
 * 
 * To avoid confusion, it’s not supported to call Hooks in other cases:

🔴 Do not call Hooks in class components.
🔴 Do not call in event handlers.
🔴 Do not call Hooks inside functions passed to useMemo, useReducer, or useEffect.
 */

export default SearchTree;
export { filterPermissions };
