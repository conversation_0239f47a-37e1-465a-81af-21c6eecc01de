// scripts/post-build-enhanced.js
const fs = require('fs');
const path = require('path');

const distPath = path.join(__dirname, '../dist');
const indexPath = path.join(distPath, 'index.html');

// 读取 index.html
let html = fs.readFileSync(indexPath, 'utf8');

// 提取所有资源文件路径
const cssFiles = [];
const asyncScripts = [];
const normalScripts = [];
let mainJsFile = '';

// 解析 HTML 中的资源
function parseResources(html) {
  // 提取 CSS 文件
  const cssRegex = /<link[^>]*rel="stylesheet"[^>]*href="([^"]*)"[^>]*>/g;
  let match;
  while ((match = cssRegex.exec(html)) !== null) {
    cssFiles.push(match[1]);
  }

  // 提取异步脚本
  const asyncScriptRegex = /<script[^>]*async[^>]*src="([^"]*)"[^>]*>/g;
  while ((match = asyncScriptRegex.exec(html)) !== null) {
    asyncScripts.push(match[1]);
  }

  // 提取普通脚本
  const scriptRegex = /<script(?![^>]*async)[^>]*src="([^"]*)"[^>]*>/g;
  while ((match = scriptRegex.exec(html)) !== null) {
    const src = match[1];

    // 跳过外部脚本（如百度地图、腾讯地图等）
    if (src.startsWith('//') || src.startsWith('http')) {
      continue;
    }

    if (src.includes('preload_helper') || src.includes('scripts/')) {
      asyncScripts.push(src);
    } else if (src.includes('umi.') && src.endsWith('.js')) {
      mainJsFile = src;
    } else {
      normalScripts.push(src);
    }
  }
}

parseResources(html);

console.log('提取到的文件：');
console.log('CSS 文件:', cssFiles);
console.log('异步脚本:', asyncScripts);
console.log('普通脚本:', normalScripts);
console.log('主 JS 文件:', mainJsFile);

// 创建动态资源加载的脚本
const dynamicLoadScript = `
<script>
(function() {
  const getPublicPath = function() {
    const hostname = window.location.hostname;
    if (['dev.shukeyun.com', 'test.shukeyun.com', 'canary.shukeyun.com'].includes(hostname)) {
      return "/scenic/exchange/";
    } else {
      return "/";
    }
  }

  const publicPath = getPublicPath();
  window.publicPath = publicPath;

  // 设置 webpack 运行时 publicPath
  if (typeof __webpack_public_path__ !== 'undefined') {
    __webpack_public_path__ = publicPath;
  }

  // 动态加载 CSS
  const cssFiles = ${JSON.stringify(cssFiles)};
  cssFiles.forEach(function(href) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href.startsWith('/') ? publicPath + href.substring(1) : publicPath + href;
    document.head.appendChild(link);
  });

  // 动态加载异步脚本
  const asyncScripts = ${JSON.stringify(asyncScripts)};
  asyncScripts.forEach(function(src) {
    const script = document.createElement('script');
    script.src = src.startsWith('/') ? publicPath + src.substring(1) : publicPath + src;
    if (src.includes('loading.js')) {
      script.async = true;
    }
    document.head.appendChild(script);
  });

  // 动态加载普通脚本
  const normalScripts = ${JSON.stringify(normalScripts)};
  normalScripts.forEach(function(src) {
    const script = document.createElement('script');
    script.src = src.startsWith('/') ? publicPath + src.substring(1) : publicPath + src;
    document.head.appendChild(script);
  });
})();
</script>
`;

// 移除原有的资源引用（保留外部脚本）
html = html.replace(/<link[^>]*rel="stylesheet"[^>]*href="[^"]*"[^>]*>/g, '');
html = html.replace(/<script[^>]*async[^>]*src="\/[^"]*"[^>]*><\/script>/g, '');
html = html.replace(/<script[^>]*src="\/[^"]*"[^>]*><\/script>/g, (match) => {
  // 保留外部脚本（如地图API）
  if (match.includes('//') || match.includes('http')) {
    return match;
  }
  // 保留内联脚本设置
  if (match.includes('window.TMap') || match.includes('window.BMap')) {
    return match;
  }
  // 保留主JS文件
  if (mainJsFile && match.includes(mainJsFile)) {
    return match;
  }
  return '';
});

// 在 head 开始处插入动态加载脚本
html = html.replace('<head>', '<head>' + dynamicLoadScript);

// 修改主 JS 文件的加载方式
if (mainJsFile) {
  const mainJsRegex = new RegExp(
    `<script[^>]*src="${mainJsFile.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"[^>]*><\/script>`,
  );
  html = html.replace(
    mainJsRegex,
    `<script>
    (function() {
      const script = document.createElement('script');
      script.src = window.publicPath + '${mainJsFile.substring(1)}';
      document.body.appendChild(script);
    })();
  </script>`,
  );
}

// 写回文件
fs.writeFileSync(indexPath, html);

console.log('✅ index.html has been processed for dynamic publicPath');
console.log('✅ 处理完成的文件保存在:', indexPath);
