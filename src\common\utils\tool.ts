import { history } from '@@/core/history';
import { message } from 'antd';
import copy from 'copy-to-clipboard';
import CryptoJS from 'crypto-js';
import { cloneDeep, omit, omitBy } from 'lodash';
import MarkdownIt from 'markdown-it';
import dayjs from 'dayjs';
import { StoreGoodsTypeEnum, TicketTypeEnum, UnitTypeEnum } from './enum';
import { clearStorage } from './storage';
import { getEnv } from '@/common/utils/getEnv';
/**
 * 获取随机 id，可用于唯一标识
 * @return 数字与字母混合的字符串
 **/
export const getUniqueId = () => {
  const s = [];
  const hexDigits = '0123456789abcdef';
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  // bits 12-15 of the time_hi_and_version field to 0010
  s[14] = '4';
  // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
  s[8] = s[13] = s[18] = s[23] = '-';

  return s.join('');
};

/**
 * 获取景区唯一标识符
 * @return 字符串
 * */
export const getScenicIdentifier = () => {
  const path = /#\/\w+\/?/.exec(window.location.hash)?.[0]; //匹配 #/path/
  if (path) {
    return path.replace('#', '').replace(/\//g, '');
  } else {
    return 'null';
  }
};

/**
 * 景区系统跳转页面
 * 跳转页面 jumpPage.push()
 * 替换页面 jumpPage.push()
 * */
export const jumpPage = {
  push: (path: string) => {
    history.push(`/${getScenicIdentifier()}${path}`);
  },
  replace: (path: string) => {
    history.replace(`/${getScenicIdentifier()}${path}`);
  },
};

function encrypt(algorithm: string, content: string): string {
  if (algorithm === 'md5') {
    return CryptoJS.MD5(content).toString();
  } else if (algorithm === 'sha1') {
    return CryptoJS.SHA1(content).toString();
  }
  return '';
}

/**
 * MD5 加密
 * @param s 原明文
 * @returns 加密后
 */
export const MD5 = (s: string | undefined): string => {
  if (s) {
    return encrypt('md5', s);
  }
  return '';
};
/**
 * SHA1 加密
 * @param s 原明文
 * @returns 加密后
 */
export const SHA1 = (s: string | undefined): string => {
  if (s) {
    return encrypt('sha1', s);
  }
  return '';
};
/**
 * 随机生成字符串，默认 8 位
 * @param l
 * @returns 随机字符串
 */
export const randomString = (l: number = 8): string => {
  let result = '';
  for (let i = 0; i < l; i++) {
    let r = Math.floor(Math.random() * 36); //10个数字加26小写字母
    if (r < 10) {
      //0011 0000      060      48      0x30      0      字符0
      r += 0x30;
    } else {
      //0110 0001      0141      97      0x61      a      小写字母a
      r += 0x61 - 10;
    }
    result += String.fromCodePoint(r);
  }
  return result;
  // String.fromCharCode(
  // return hash.update(s).digest('base64');
};

/**
 * 复制文本
 * @param text
 */
export const copyText = (text: any) => {
  if (copy(text)) {
    message.success('复制成功');
  } else {
    message.error('复制失败');
  }
};

export const formatTime = (time: any) => {
  if (time) {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  } else {
    return time;
  }
};

/**
 * 格式化金额
 * @number 要格式化的数字
 * @decimals 保留几位小数
 * @decPoint 小数点符号
 * @thousandsSep 千分位符号
 */
export const numberFormat = (
  number: number,
  decimals?: number = 2,
  decPoint?: string = '.',
  thousandsSep?: string = ',',
) => {
  const newNum = (number + '').replace(/[^0-9+-Ee.]/g, '');
  const n = !isFinite(+newNum) ? 0 : +newNum,
    prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
    sep = typeof thousandsSep === 'undefined' ? ',' : thousandsSep,
    dec = typeof decPoint === 'undefined' ? '.' : decPoint,
    toFixedFix = function (n, prec) {
      const k = Math.pow(10, prec);
      return '' + Math.ceil(n * k) / k;
    };
  let s = [];

  s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
  const re = /(-?\d+)(\d{3})/;
  while (re.test(s[0])) {
    s[0] = s[0].replace(re, '$1' + sep + '$2');
  }

  if ((s[1] || '').length < prec) {
    s[1] = s[1] || '';
    s[1] += new Array(prec - s[1].length + 1).join('0');
  }
  return s.join(dec);
};

//对象转url参数
export const objToUrlPath = (data) => {
  const _result = [];
  for (const key in data) {
    const value = data[key];
    if (value.constructor == Array) {
      value.forEach(function (_value) {
        _result.push(key + '=' + _value);
      });
    } else {
      _result.push(key + '=' + value);
    }
  }
  return _result.join('&');
};

/**
 * 返回登录页
 */
export const goToLogin = () => {
  if (
    process.env.NODE_ENV === 'development' ||
    location.host === 'test.shukeyun.com' ||
    location.host === 'dev.shukeyun.com'
  ) {
    const con = confirm('未登录，是否跳转登录页？（用于调试，正式环境不会有此弹窗）');
    if (con == false) return;
  }
  localStorage.removeItem('currentCompany'); //清除电商默认企业
  localStorage.removeItem('userInfo');
  clearStorage();

  const { hash } = window.location;
  const { LOGIN_HOST, APPID } = getEnv();
  let url = `${LOGIN_HOST}/#/login?appId=${APPID}&${hash.split('?')[1] || ''}`;
  if (process.env.NODE_ENV === 'development') {
    //本地开发环境
    url += `&path=${location.origin}`;
  }
  window.location.href = url;
};

// 结算模块 Arr 为前端表单数据结构 String 为后端接口数据结构 Info 为前端显示数据结构

export function transformDateStringToArr(params: {
  type: 'WEEK' | 'MONTH' | 'YEAR';
  date: string;
}) {
  const { type, date } = params;
  if (type === 'YEAR') {
    return date
      .split(',')
      .map((i) => i.split('/'))
      .map((item) => ({
        month: item[0],
        day: item[1],
      }));
  }
  return date.split(',');
}

export function transformDateArrToString(
  params:
    | { type: 'WEEK' | 'MONTH'; date: number[] }
    | { type: 'YEAR'; date: { month: number; day: number }[] },
) {
  const { type, date } = params;
  if (type === 'YEAR') {
    return date.map(({ month, day }) => `${month}/${day}`).join(',');
  }
  return date.join(',');
}

export function transformStringToInfo(params: { type: 'WEEK' | 'MONTH' | 'YEAR'; date: string }) {
  const { type, date } = params;
  if (type === 'WEEK') {
    const weekEnum = {
      1: '周一',
      2: '周二',
      3: '周三',
      4: '周四',
      5: '周五',
      6: '周六',
      7: '周日',
    };
    return date
      .split(',')
      .map((item) => weekEnum[item])
      .join('，');
  } else if (type === 'MONTH') {
    return date
      .split(',')
      .map((item) => (item === '31' ? '月末最后一天' : `${item}号`))
      .join('，');
  }

  return date
    .split(',')
    .map((i) => i.split('/'))
    .map((item) => `${item[0]}月${item[1]}日`)
    .join('，');
}

/**
 * 下载流文件
 * @param {Blob} result
 * @param {String} fileName
 * @param {String} type
 */
export function downloadBlobFile(result: any, fileName: string, type: string) {
  const blob = new Blob([result], { type: `application/${type};charset=utf-8` });
  const downloadElement = document.createElement('a');
  const href = window.URL.createObjectURL(blob); // 创建下载的链接
  downloadElement.href = href;
  downloadElement.download = fileName; // 下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click();
  document.body.removeChild(downloadElement); // 下载完成移除元素
  window.URL.revokeObjectURL(href); // 释放掉 blob 对象
}

export const toUploadFileList = (data?: string) => {
  const { IMG_HOST } = getEnv();
  const res = data
    ? data.split(',').map((item, index) => ({
        uid: (index + 1) * -1,
        name: 'image.png',
        status: 'done',
        response: [
          {
            path: item,
          },
        ],
        url: IMG_HOST + item,
      }))
    : [];
  return res as any[];
};

type ValueType = string | number;
interface OmitConfig<T> {
  /** 按 key 删除 */
  omitKeys?: (keyof T)[];
  /** 删除返回为真的 enum 值 */
  omitValueBy?: (value: ValueType, key: keyof T) => boolean;
}

/**
 * @description: TS 的 enum 转 antd-pro 的 valueEnum
 */
export function toValueEnum<T extends Record<string, ValueType>>(
  enumObj: T,
  config: OmitConfig<T> = {},
) {
  const { omitKeys, omitValueBy } = config;
  let newEnumObj = cloneDeep(enumObj);
  // 根据配置删除
  if (omitKeys) {
    newEnumObj = omit(newEnumObj, omitKeys) as any;
  } else if (omitValueBy) {
    newEnumObj = omitBy(newEnumObj, omitValueBy) as any;
  }
  // 过滤掉 enum 值为 number 时的反向映射
  const arr = Object.entries(newEnumObj).filter((item) => isNaN(Number(item[0])));
  // key value 对调 对应上 antd pro 的 valueEnum 格式
  const newArr: [ValueType, string][] = arr.map((item) => [item[1], item[0]]);
  return new Map(newArr);
}

/**
 * @description: 获取票的票种 (三个值去判断，就离谱)
 */
export function getStoreGoodsTypeText(
  unitType: UnitTypeEnum,
  storeGoodsType: StoreGoodsTypeEnum,
  goodsType: TicketTypeEnum,
) {
  if (storeGoodsType === StoreGoodsTypeEnum.组合票) {
    return '-';
  } else {
    if (unitType === UnitTypeEnum.权益卡) {
      return '权益卡';
    }
    return TicketTypeEnum[goodsType] || '-';
  }
}

export function isMobile() {
  return /Mobi|Android|iPhone/i.test(navigator.userAgent);
}

interface IData {
  code: number;
  data: any;
  msg: string;
  // 兼容一下旧格式返回的 JSON 字符串格式
  result: string;
}

// 兼容大数据接口格式
export const getData = (data: IData, successCode = 20000 | 20001) => {
  if (data.code >= 20000 && data.code < 30000) {
    return data.data;
  }
  // 兼容一下旧格式返回的 JSON 字符串格式
  if (data && data.result && typeof data.result === 'string') {
    return Object.assign(data, {
      result: JSON.parse(data.result),
    });
  }
  return Promise.reject(data);
};

/**
 *
 * @param arr 数组源
 * @param symbolic 拼接的符号
 * @returns
 */
export const transformArrToString = (arr: any[], symbolic = ',') => {
  return Array.isArray(arr) ? arr.join(symbolic) : '';
};

export const transformStringToArr = (str: string, symbolic = ',') => {
  return str?.length ? str.split(symbolic) : [];
};

/**
 *
 * @param url "http://example.com/#/page?foo=bar&baz=qux"
 * @returns {foo: "bar", baz: "qux"}
 */
export const getHashParams = (url: string = window.location.href) => {
  const params: any = {};

  const list = url.split('?')?.[1]?.toString()?.split('&') || [];
  list.forEach((pair: string) => {
    const [key, value] = pair?.split('=');
    params[decodeURIComponent(key)] = decodeURIComponent(value);
  });
  return params;
};

/**
 * 删除 location.href 中不需要的 state，返回新的 url
 * @param field string | string[] 要删掉的字段
 * @returns "http://example.com/#/page?foo=bar&baz=qux"
 */
export const removeStateFromUrl = (field: string | string[]) => {
  const queryParams = getHashParams();
  const fieldList = Array.isArray(field) ? field : [field];
  fieldList.map((f) => {
    if (queryParams?.[f]) {
      delete queryParams?.[f];
    }
  });
  const url = window.location.href?.split('?')[0];
  const queryParamsString = objToUrlPath(queryParams) ? `?${objToUrlPath(queryParams)}` : '';
  return `${url}${queryParamsString}`;
};

// 创建 markdown-it 实例
const md = new MarkdownIt({
  html: true, // 启用 HTML 标签
  breaks: true, // 转换 '\n' 为 <br>
  linkify: true, // 自动转换 URL 为链接
  typographer: true, // 启用一些语言中性的替换 + 引号美化
});

/**
 * 将 Markdown 文本转换为 HTML
 * @param text Markdown 文本
 * @returns HTML 字符串
 */
export const markdownToHtml = (text: string): string => {
  if (!text) return '';
  return md.render(text);
};

/**
 * 将 Markdown 文本转换为纯文本（去除 HTML 标签）
 * @param text Markdown 文本
 * @returns 纯文本
 */
export const markdownToText = (text: string): string => {
  if (!text) return '';
  return md.render(text).replace(/<[^>]+>/g, '');
};

/**
 * 数据脱敏类型
 */
export enum DataMaskTypeEnum {
  /** 姓名 */
  NAME = 'NAME',
  /** 身份证 */
  ID_CARD = 'ID_CARD',
  /** 手机号 */
  PHONE = 'PHONE',
}

/**
 * 数据脱敏
 * @param text 要脱敏的文本
 * @param type 脱敏类型，默认自动识别，可选值：NAME、ID_CARD、PHONE
 * @returns 脱敏后的文本
 */
export const dataMask = (text: string, type: DataMaskTypeEnum = DataMaskTypeEnum.NAME) => {
  if (!text) return '';

  switch (type) {
    case DataMaskTypeEnum.NAME:
      return text.length > 1 ? text.slice(0, 1) + '*'.repeat(Math.max(0, text.length - 1)) : text;
    case DataMaskTypeEnum.ID_CARD:
      if (text.length <= 8) return text;
      return text.slice(0, 6) + '*'.repeat(Math.max(0, text.length - 8)) + text.slice(-2);
    case DataMaskTypeEnum.PHONE:
      if (text.length <= 7) return text;
      return text.slice(0, 3) + '*'.repeat(Math.max(0, text.length - 7)) + text.slice(-4);
    default:
      return text;
  }
};
