/**
 * 2024年8月27日
 * 把这页全改为与慧景云同步,这页不再使用.
 * 看代码里有什么余额和充值之类的代码
 * 与产品沟通后亦不知为何物,故弃之而新立
 */

import DetailsPop from '@/common/components/DetailsPop';
import EditPop from '@/common/components/EditPop';
import Tag from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getUniqueId } from '@/common/utils/tool';
import { postPermissionAuthorize, servicePackage } from '@/services/api/cas';
import {
  apiCoEdit,
  apiCoInfoList,
  apiPerInfoList,
  apiPermission,
  apiPersonalRegister,
  apiTlementInfo,
} from '@/services/api/distribution';
import { apiAgency, apiCharge } from '@/services/api/erp';
import type { API } from '@/services/api/typings';
import { PlusOutlined } from '@ant-design/icons';
import ProDescriptions from '@ant-design/pro-descriptions';
import type { ProFormColumnsType } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormDigit, ProFormMoney, ProFormText } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Input, Space, Spin, Tabs, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import InviteModal from './components/InviteModal';
import { MyIcon_1, MyIcon_2 } from './components/MyIcon';
import { getEnv } from '@/common/utils/getEnv';

//结算 ID
let tlementId: any;
//余额
let number: any;
//获取当前的地址
let url: string;
//点击 '下一步按钮' 的加载状态
let clickStatus: boolean;
// let loadingStatus: boolean = false;
const TableList: React.FC = (props) => {
  const access = useAccess();
  const formRef = useRef();
  const formRef2 = useRef();
  const [formObj] = ProForm.useForm();
  const { initialState, setInitialState } = useModel('@@initialState');
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [currentRow, setCurrentRow] = useState<Record<string, any>>();

  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const [columnsKey, setColumnsKey] = useState<any>('enterprise');
  const [rechargeAmount, setRechargeAmount] = useState(undefined);
  //点击 '下一步按钮' 的加载状态
  const [loadingStatus, setLoadingStatus] = useState(false);
  //点击下一步关闭按钮
  const [OffStatus, setOffStatus] = useState(false);
  //提现状态
  const [withdrawStatus, setWithdrawStatus] = useState(false);

  const [payeeMessage, setPayeeMessage] = useState({});
  const { MERCHANT_HOST, APPID } = getEnv();

  useEffect(() => {
    url = window.location.href;

    // console.log(props);
    // console.log('当前地址', url);
  }, []);
  const moneyData = [1000, 2000, 4000, 10000, 20000, 50000];

  let companyId: any;
  let flag = false;
  let inputVal: any;
  const tlementIdMeothd = (val: any) => {
    console.log(val);
    tlementId = val;
  };

  //忘记密码回调
  const forgetPassword = () => {
    window.open(getEnv().MERCHANT_HOST);
  };
  //选择金额事件
  const buttonSelect = (e: any) => {
    formRef?.current?.setFieldsValue({ agencyPayAmount: e.target.value.slice(1) });
    // setRechargeAmount(e.target.value);
  };
  //调用充值接口
  const recharge = async (params: any) => {
    console.log(params, payeeMessage);
    const pars = {
      data: {
        agencyPays: [
          {
            accountType: 'COMPANY',
            ...params,
            memo: '',
            receiver: '',
            receiverBankAccountName: `888888|${payeeMessage.bdsAccount}|${payeeMessage.bdsOrg}`,
            receiverBankName: '区块链分销系统',
          },
        ],
        returnUrl: url,
      },
      merchantId: tlementId,
    };
    // console.log(pars);

    try {
      if (payeeMessage.bdsAccount == '' || payeeMessage.bdsOrg == '') {
        message.info('该企业未开通商户，无法进行充值');
      } else {
        // const tirmId = setTimeout(() => {
        //   message.info('网络延迟，请稍后再试~')
        // }, 500)
        const result = await apiAgency(pars);

        const { tradeNo, outTradeNo }: any = result.data;
        window.open(`${getEnv().PAY_HOST}?merchantId=36&outTradeNo=${outTradeNo}`, '_self');
        // history.push('/pay')
        // setDetailsVisible(true);
        // setOffStatus(true)
        // props.replace.push('www.baidu.com')
        // console.log(result);
      }
      // setAccount(data)
    } catch (e) {
      console.error(e);
    }
  };

  //关闭提现
  const onWithdrawStatus = () => {
    console.log('*************');
    setWithdrawStatus(true);
  };
  const showConfirm = () => {
    // confirm({
    //   title: '您确认要提现吗？',
    //   content: '',
    //   okText: '确认',
    //   cancelText: '取消',
    //   onOk() {
    //   },
    //   onCancel() {
    //     console.log('Cancel');
    //   },
    // });
  };
  const [status, setStatus] = useState(true);
  const inputMeothed = (e: any) => {
    console.log(e.target.value);
    inputVal = e.target.value;
    formObj.setFieldsValue({ payOperate: e.target.value });
  };
  //调用提现接口
  const onWithdraw = async (params: any) => {
    // console.log(params);
    const { totalPrice, payOperate }: any = params;
    const pars = {
      data: {
        businessType: 'CHARGE',
        totalPrice,
        payOperate,
      },
      merchantId: tlementId,
    };
    console.log('提现', pars);
    try {
      if (totalPrice > number || totalPrice == 0) {
        // const tirmId = setTimeout(() => {
        console.log(inputVal);
        // }, timeout);
        // clearInterval(tirmId)

        message.info('提现金额剩余不足，请及时充值');

        // window.location.reload()
        // history.push('/dnterprise')
        // setWithdrawStatus(true)
      } else {
        const result = await apiCharge(pars);
        const tirmId = setTimeout(() => {
          window.location.reload();
        }, 500);
        message.success('提现成功');
        // const { tradeNo, outTradeNo }: any = result.data
        // window.open(getEnv().MERCHANT_HOST + `#/tradeNo=${tradeNo}&outTradeNo=${outTradeNo}`)
        // console.log(result);
      }
    } catch (e) {
      console.error(e);
    }
  };

  //调用可用金额接口
  // const getAvailData = async (params: any) => {
  //   console.log(params);
  //   const { bdsAccount, bdsOrg } = params;
  //   try {
  //     // setPayeeMessage(params)
  //     console.log(payeeMessage);
  //     const { data }: any = await apiBalanceCoins(bdsAccount, bdsOrg);
  //     // setBalanceData(result.data)
  //     console.log('剩余充值金额', data);
  //     setAccount(data);
  //   } catch (e) {
  //     console.error(e);
  //   }
  // };
  // 企业配置
  const enterprise: ProColumns[] = [
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
    },
    {
      title: '企业名称',
      dataIndex: 'coName',
      hideInSearch: true,
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
      hideInSearch: true,
    },
    {
      title: '手机号',
      dataIndex: 'coPhone',
      hideInSearch: true,
    },
    {
      title: '认证状态',
      dataIndex: 'settlementStatus',
      hideInSearch: true,
      render: (dom: any) => <Tag type="approveStatus" value={dom} />,
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_, entity: any) => (
        <Space>
          <a
            onClick={async () => {
              try {
                setLoading(true);
                // const data = await apiCoInfo(entity.coId);
                const data = entity;
                data.id = data.coId;
                const bdsAccount = data.bdsAccount;
                const bdsOrg = data.bdsOrg;
                companyId = entity.coId;
                setPayeeMessage({ bdsAccount, bdsOrg });
                // //获取区块链稳定余额
                // if (bdsAccount !== '' && bdsOrg !== '') {
                //   const { data: val }: any = await apiBalanceCoins(bdsAccount, bdsOrg);
                //   number = val;
                //   data.number = val;
                //   data.done = true;
                //   clickStatus = true;
                // } else {
                //   clickStatus = false;
                // }
                tlementIdMeothd(data.settlementId);
                setDataSource(data);
                setDetailsVisible(true);
                setLoading(false);

                addOperationLogRequest({
                  action: 'info',
                  content: `查看企业【${entity.coName}】详情`,
                });
              } catch (error) {}
            }}
          >
            查看
          </a>
          <Access accessible={access.canEnterpriceManage_edit}>
            <a
              onClick={() => {
                setDataSource({ ...entity, id: entity.coId });
                setEditVisible(true);
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={access.canEnterpriceManage_certification}>
            <a
              style={{ cursor: 'pointer' }}
              onClick={async () => {
                setCurrentRow(entity);
                // if (entity.settlementStatus == 1) {
                //   Modal.info({
                //     title: '结算开户信息',
                //     content: (
                //       <>
                //         <p style={{ marginTop: '20px' }}>
                //           商户名称：<code>{entity.legalName}</code>
                //         </p>
                //         <p>
                //           商户账号：<code>{entity.settlementName}</code>
                //         </p>
                //       </>
                //     ),
                //   });
                // } else {
                const hide = message.loading('正在查询开户信息');
                try {
                  const res = await apiTlementInfo({ id: entity.coCode });
                  hide();
                  if (res.openAuditStatus == 'PASSED') {
                    message.success('已认证');
                    actionRef?.current?.reload();
                    // Modal.info({
                    //   title: '结算开户信息',
                    //   content: (
                    //     <>
                    //       <p style={{ marginTop: '20px' }}>
                    //         商户账号：<code>{res.legalName}</code>
                    //       </p>
                    //       <p>
                    //         商户编号：<code>{res.registrationName}</code>
                    //       </p>
                    //     </>
                    //   ),
                    // });
                  } else if (res.openAuditStatus == 'WAITING') {
                    message.info('审核中');
                  } else {
                    addOperationLogRequest({
                      action: 'link',
                      content: `前往【${entity.coName}】企业认证`,
                    });

                    window.open(getEnv().MERCHANT_HOST);
                  }
                } catch (error) {
                  hide();
                }
                // }
              }}
            >
              {entity.settlementStatus == 3 ? (
                '审核状态'
              ) : entity.settlementStatus != 1 ? (
                <span style={{ color: 'orange' }}>去认证</span>
              ) : (
                ''
              )}
            </a>
          </Access>
        </Space>
      ),
    },
  ];
  // 个人配置
  const personal: ProColumns[] = [
    {
      title: '姓名',
      dataIndex: 'realName',
      hideInSearch: true,
    },
    {
      title: '结算用户名',
      dataIndex: 'settlementName',
      hideInSearch: true,
    },
    {
      title: '身份证号',
      dataIndex: 'identity',
      hideInSearch: true,
    },
    // {
    //   title: '电子邮箱',
    //   dataIndex: 'address',
    //   hideInSearch: true,
    // },
    {
      title: '手机号',
      dataIndex: 'phone',
      // hideInSearch: true,
    },
    // {
    //   title: '认证状态',
    //   dataIndex: 'status',
    //   hideInSearch: true,
    //   render: (dom: any, entity: any) => <>
    //     <Tag color={entity ? 'blue' : 'red'}>{entity ? '已认证' : '未认证'}</Tag>
    //     {entity ? <a>去认证</a> : ''}
    //   </>
    // },
    {
      title: '操作',
      valueType: 'option',
      render: (dom: any, entity: any) =>
        entity.identity ? (
          '已认证'
        ) : (
          <a
            onClick={() => {
              setCertifVisible(true);
            }}
          >
            去认证
          </a>
        ),
    },
  ];
  const tabColumns = { enterprise, personal };

  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const editColumns = [
    {
      title: '基本信息',
      columns: [
        {
          title: '统一社会信用代码',
          dataIndex: 'coCode',
          formItemProps: {
            rules: [
              { required: true },
              {
                pattern: /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/g,
                message: '请输入正确的 18 位统一社会信用代码',
              },
            ],
          },
          fieldProps: { disabled: dataSource.id },
        },
        {
          title: '企业名称',
          dataIndex: 'coName',
          formItemProps: { rules: [{ required: true, max: 30 }] },
        },
        {
          title: '联系人',
          dataIndex: 'contactName',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '手机号',
          dataIndex: 'coPhone',
          formItemProps: {
            rules: [
              { required: true },
              {
                pattern: /^1(3\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\d|9[0-35-9])\d{8}$/,
                message: '请输入格式不正确',
              },
            ],
          },
        },
        {
          title: '邮箱',
          dataIndex: 'coEmail',
        },
        {
          title: '地址',
          dataIndex: 'coAddressInfo',
          formItemProps: { rules: [{ max: 200 }] },
        },
      ],
    },
  ];

  // 【认证】数据绑定
  const [certifVisible, setCertifVisible] = useState<boolean>(false);
  const certifColumns = [
    {
      title: '',
      columns: [
        {
          title: '姓名',
          dataIndex: 'realName',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '身份证号',
          dataIndex: 'identity',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '结算用户名',
          dataIndex: 'settlementName',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '结算密码',
          dataIndex: 'settlementPassword',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '手机号',
          dataIndex: 'phone',
          formItemProps: {
            rules: [
              { required: true },
              {
                pattern: /^1(3\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\d|9[0-35-9])\d{8}$/,
                message: '请输入格式不正确',
              },
            ],
          },
        },
      ],
    },
  ];

  // 【列表】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);

  const columnsInitial: ProFormColumnsType[] = [
    {
      title: '基础信息',
      columns: [
        {
          title: '统一社会信用代码',
          dataIndex: 'coCode',
        },
        {
          title: '企业名称',
          dataIndex: 'coName',
        },
        {
          title: '联系人',
          dataIndex: 'contactName',
        },
        {
          title: '手机号',
          dataIndex: 'coPhone',
        },
        {
          title: '邮箱',
          dataIndex: 'coEmail',
        },
        {
          title: '地址',
          dataIndex: 'coAddressInfo',
        },
        {
          title: '商户账号',
          dataIndex: 'settlementName',
        },
      ],
    },
    {
      title: '结算开户信息',
      columns: [
        {
          title: '商户名称',
          dataIndex: 'legalName',
        },
        {
          title: '商户账号',
          dataIndex: 'settlementName',
        },
      ],
    },
    {
      hideInDescriptions: !clickStatus,
      title: '余额信息',
      columns: [
        {
          hideInDescriptions: !clickStatus,
          // title: '可用余额',
          dataIndex: 'number',
          render: (dom: any, record: any) => {
            return (
              <div>
                {!clickStatus ? (
                  ''
                ) : (
                  <>
                    <ProDescriptions
                      title="账号管理"
                      column={1}
                      // dataSource={data}
                      columns={[]}
                    >
                      <ProDescriptions.Item label="可用余额">
                        <Space align="start">
                          <MyIcon_1 />
                          <span>
                            {record.number +
                              '=' +
                              parseFloat((record.number / 100).toString()).toLocaleString() +
                              '元'}
                          </span>
                        </Space>

                        {/* <div ><MyIcon />&nbsp;1,774.91 元</div> */}
                      </ProDescriptions.Item>
                      <ProDescriptions.Item>
                        <Space>
                          <ModalForm
                            title="充值"
                            formRef={formRef}
                            // visible={clickStatus}
                            trigger={
                              <Button
                                type="primary"
                                onClick={() => {
                                  console.log(clickStatus);
                                }}
                              >
                                充值
                              </Button>
                            }
                            onFinish={async (value) => {
                              flag = true;
                              recharge(value);
                            }}
                            submitter={{
                              searchConfig: {
                                submitText: '下一步',
                              },
                              onSubmit: (e) => {
                                flag = true;
                              },
                            }}
                          >
                            <Spin spinning={flag} tip="Loading...">
                              <ProForm.Group>
                                <ProFormMoney
                                  width="md"
                                  name="agencyPayAmount"
                                  label="充值金额"
                                  placeholder="请输入金额"
                                  min={0}
                                  initialValue={0}
                                />
                              </ProForm.Group>
                              <ProDescriptions>
                                {moneyData.map((item, index) => {
                                  return (
                                    <ProDescriptions.Item key={index}>
                                      <Input
                                        type="button"
                                        style={{ margin: '0 10px' }}
                                        onClick={(e) => {
                                          buttonSelect(e);
                                        }}
                                        value={`￥${item}`}
                                      />{' '}
                                    </ProDescriptions.Item>
                                  );
                                })}
                              </ProDescriptions>
                            </Spin>
                          </ModalForm>
                          <ModalForm
                            autoComplete="new-password"
                            title="提现"
                            formRef={formRef2}
                            form={formObj}
                            trigger={<Button>提现</Button>}
                            // visible={withdrawStatus}
                            // onValuesChange={setWithdrawStatus}
                            onFinish={async (value) => {
                              onWithdraw(value);
                            }}
                            submitter={{
                              searchConfig: {
                                submitText: '提现',
                              },
                              // onSubmit: (e) => {
                              //   console.log(123132, e)
                              // }
                            }}
                          >
                            <ProForm.Item label="可提现金额（元）" name="withdrawalAmount">
                              <Space>
                                <MyIcon_2 />
                                <div style={{ color: 'red', fontSize: '18px' }}>
                                  <span>
                                    {parseFloat((record.number / 100).toString()).toLocaleString()}
                                  </span>
                                </div>
                              </Space>
                            </ProForm.Item>

                            <ProForm.Item>
                              <ProFormDigit
                                // label="InputNumber"
                                // initialValue={1}
                                name="totalPrice"
                                placeholder="请输入提现金额"
                                width={200}
                                label="本次提现金额（元）"
                                rules={[{ required: true, message: '请输入提现金额' }]}
                              />
                            </ProForm.Item>

                            {/* <ProFormMoney
                            name="totalPrice"

                            // min={0}
                            placeholder="请输入提现金额"
                            // initialValue={1}
                            width={200}
                          /> */}
                            {/* <a>查看提现限额</a> */}
                            <ProForm.Item style={{ display: 'none' }}>
                              <Input.Password autoComplete="new-password" />
                            </ProForm.Item>
                            <ProForm.Item>
                              <ProForm.Group>
                                <ProFormText.Password
                                  placeholder="请输入密码"
                                  name="payOperate"
                                  label="支付密码"
                                  width={200}
                                  rules={[
                                    { required: true, message: '请输入密码' },
                                    { max: 6, message: '密码不能大于6位' },
                                  ]}
                                />
                                <a style={{ lineHeight: '90px' }} onClick={forgetPassword}>
                                  忘记密码？
                                </a>
                              </ProForm.Group>
                              <Space>
                                ( <span>注：此次提现余额将返回到钱包账户</span>
                                <a href={MERCHANT_HOST} target="_blank" rel="noreferrer">
                                  请前往查看
                                </a>
                                )
                              </Space>
                            </ProForm.Item>
                          </ModalForm>
                        </Space>
                      </ProDescriptions.Item>
                    </ProDescriptions>
                  </>
                )}
              </div>
            );
          },
        },
      ],
    },
  ];
  const [inviteVisible, setInviteVisible] = useState(false);

  // 从别的标签回来刷新页面
  async function handleVisibilityChange() {
    if (document.visibilityState === 'visible' && currentRow) {
      await apiTlementInfo({ id: currentRow?.coCode });
      actionRef.current?.reload();
    }
  }

  useEffect(() => {
    // 避免注册多个
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    // 添加事件监听器
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      // 组件卸载移除
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [currentRow]);

  return (
    <>
      <Tabs
        style={{ background: '#fff' }}
        tabBarStyle={{ padding: '0 24px', margin: '0' }}
        onChange={(activeKey) => {
          setColumnsKey(activeKey);
          actionRef?.current?.reload();
        }}
        items={[
          {
            label: '企业',
            key: 'enterprise',
          },
          {
            label: '个人',
            key: 'personal',
          },
        ]}
      />
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        actionRef={actionRef}
        toolBarRender={() =>
          columnsKey == 'enterprise' ? (
            <Space key={getUniqueId()}>
              {/* <Access key={getUniqueId()} accessible={access.canEnterpriceManage_invite}>
                <Button
                  key="invite"
                  type="primary"
                  onClick={() => {
                    setInviteVisible(true);
                  }}
                >
                  <UserAddOutlined /> 邀请
                </Button>
              </Access> */}
              <Access key={getUniqueId()} accessible={access.canEnterpriceManage_insert}>
                <Button
                  type="primary"
                  key="primary"
                  onClick={() => {
                    // 初始化表单
                    setDataSource({ id: '', coId: '', isEnable: 0 });
                    setEditVisible(true);
                  }}
                >
                  <PlusOutlined /> 新建
                </Button>
              </Access>
            </Space>
          ) : (
            []
          )
        }
        params={{ userId: initialState?.userInfo?.userId }}
        request={columnsKey == 'enterprise' ? apiCoInfoList : apiPerInfoList}
        columns={tabColumns[columnsKey]}
      />

      {/* 新增修改 */}
      <EditPop
        title="企业"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/修改
        onFinish={async (val: any) => {
          if (dataSource.id) val.id = dataSource.id;
          const msgType = val.id ? '修改' : '添加';
          const hide = message.loading('正在' + msgType);
          val.isOTA = 1;
          val.coId = val.id;
          delete val.id;
          try {
            const data = await apiCoEdit({
              isSale: true,
              co: val,
              coAddress: { coAddressInfo: val.coAddressInfo },
              relationCoAndType: [
                {
                  coId: val.coId,
                  type: 1, // 0：景区运营；1：分销商
                },
              ],
            });

            if (dataSource.id) {
              addOperationLogRequest({
                action: 'edit',
                content: `编辑企业【${val.coName}】`,
                changeConfig: {
                  beforeData: dataSource,
                  afterData: val,
                  list: editColumns[0].columns,
                },
              });
            } else {
              addOperationLogRequest({
                action: 'add',
                content: `新增企业【${val.coName}】`,
              });
            }

            if (!dataSource.id) {
              // // 创建企业时关联到用户
              // await apiAssociateCompany({
              //   companyId: data,
              //   userId: initialState?.userInfo?.userId,
              // });
              // 给当前用户配置企业权限
              await apiPermission({
                companyId: data,
                type: '03',
                appId: APPID,
              });
              // 启用企业
              const params = {
                permissionCode: [
                  {
                    group: 'e-commerce/user_status',
                    code: data,
                    action: '',
                  },
                ],
                users: [initialState?.userInfo?.userId],
                appId: APPID,
              };
              await postPermissionAuthorize(params);
              // 开通微服务
              await servicePackage({
                companyId: data,
                isAll: true,
              });
            }
            message.success(msgType + '成功');
            setEditVisible(false);
            location.reload();
          } catch (error) {
            console.log(error);
          }
          hide();
        }}
      />

      {/* 认证弹窗 */}
      <EditPop
        title="认证信息"
        visible={certifVisible}
        setVisible={setCertifVisible}
        columns={certifColumns}
        dataSource={{ id: '', isEnable: 0 }}
        // 新增/修改
        onFinish={async (val: any) => {
          const hide = message.loading('正在提交');
          try {
            const data = await apiPersonalRegister(val);
            message.success('提交成功');
            // 给当前用户配置个人企业权限
            await apiPermission({
              companyId: data,
              type: '032',
              appId: APPID,
            });
            // 启用个人企业
            const params = {
              permissionCode: [
                {
                  group: 'e-commerce/user_status',
                  code: data,
                  action: '',
                },
              ],
              users: [initialState?.userInfo?.userId],
              appId: APPID,
            };
            await postPermissionAuthorize(params);
            location.reload();
            // // 关闭弹窗并刷新列表
            // setCertifVisible(false);
            // // 更新企业下拉列表
            // const { data: companyList } = await apiMerchantList();
            // const companyDownList = companyList.map((item: any) => ({
            //   value: item.id,
            //   label: item.name,
            // }));
            // setInitialState((s: any) => ({ ...s, companyDownList }));
            // // location.reload();
            // // 更新企业表格列表
            // actionRef?.current?.reload();
          } catch (error) {}
          hide();
        }}
      />

      {/* 详情 */}
      <DetailsPop
        limit={{
          edit: 'canEnterpriceManage_edit',
        }}
        title="企业详情"
        visible={detailsVisible}
        isLoading={isLoading}
        setVisible={setDetailsVisible}
        columnsInitial={columnsInitial}
        dataSource={dataSource}
        // // 禁启用
        // onEnable={async (succe: any, fail: any) => {
        //   let params = {
        //     coId: dataSource.coId,
        //     isEnable: 1 - dataSource.isEnable,
        //   };
        //   await apiCoStatus(params);
        //   succe();
        //   actionRef.current?.reload();
        // }}
        // 删除
        // onDelete2={async (succe: any, fail: any) => {
        //   try {
        //     await apiCoDel(dataSource.coId);
        //     succe();
        //     actionRef.current?.reload();
        //   } catch (error) {}
        // }}
        // // 修改
        // onUpdate={() => {
        //   setEditVisible(true);
        // }}
      />
      <InviteModal visible={inviteVisible} setVisible={setInviteVisible} />
    </>
  );
};

export default TableList;
