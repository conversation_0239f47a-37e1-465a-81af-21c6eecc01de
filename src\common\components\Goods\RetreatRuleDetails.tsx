import { Tag } from 'antd';
import { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';
import { getUniqueId } from '../../../common/utils/tool';
import { ticketRetreatXq } from '../../../services/api/ticket';
import DetailsPop from '../DetailsPop';

const RetreatRuleDetails = ({ id }: any) => {
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [rulesData, setRulesData] = useState(false);

  const columnsInitial = [
    {
      title: '基础信息',
      columns: [
        {
          title: '景区名称',
          dataIndex: 'scenicName',
          // render: () => scenicName,
        },
        {
          title: '退票规则名称',
          dataIndex: 'name',
        },
      ],
    },
    {
      title: '退票信息',
      columns: [
        {
          span: 2,
          title: '是否可退票',
          dataIndex: 'isRetreat',
          render: (dom: any) => ['否', '是'][dom],
        },
        {
          span: 2,
          title: '',
          dataIndex: 'data',
          render: (dom: any) =>
            dom ? (
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                {dom.map((item: any, index: any) => (
                  <div key={getUniqueId()} style={{ marginBottom: '8px' }}>
                    距生效时间
                    <Tag style={{ marginLeft: '8px' }}>{['前', '后'][dom[index].flag]}</Tag>
                    <Tag style={{ width: 150 }}>
                      {dom[index].dayBeginTime.padStart(2, '0')} 天{' '}
                      {dom[index].dayBeginHour.padStart(2, '0')} 时 ~{' '}
                      {dom[index].dayEndTime.padStart(2, '0')} 天{' '}
                      {dom[index].dayEndHour.padStart(2, '0')} 时
                    </Tag>
                    退票费率：
                    <Tag style={{ width: 38 }}>
                      {dom[index].rate.padEnd(3, '.0').padEnd(4, '0')}
                    </Tag>
                  </div>
                ))}
              </div>
            ) : (
              '-'
            ),
        },
        {
          span: 2,
          title: '',
          dataIndex: 'bookData',
          render: (dom: any) =>
            dom ? (
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                {dom.map((item: any, index: any) => (
                  <div key={getUniqueId()} style={{ marginBottom: '8px' }}>
                    预订成功后
                    <Tag style={{ marginLeft: '8px', width: 150 }}>
                      {dom[index].dayBeginTime.padStart(2, '0')} 天{' '}
                      {dom[index].dayBeginHour.padStart(2, '0')} 时 ~{' '}
                      {dom[index].dayEndTime.padStart(2, '0')} 天{' '}
                      {dom[index].dayEndHour.padStart(2, '0')} 时
                    </Tag>
                    退票费率：
                    <Tag style={{ width: 38 }}>
                      {dom[index].rate.padEnd(3, '.0').padEnd(4, '0')}
                    </Tag>
                  </div>
                ))}
              </div>
            ) : (
              '-'
            ),
        },
        {
          title: '其他退票费率',
          dataIndex: 'isRetreat',
          render: (dom: any, entity: any) =>
            dom ? entity.defaultRate.padEnd(3, '.0').padEnd(4, '0') : '-',
        },
      ],
    },
    {
      title: '说明信息',
      columns: [
        {
          title: '备注',
          dataIndex: 'remark',
        },
      ],
    },
  ];
  const init = async (e: string) => {
    setLoading(true);
    setDetailsVisible(true);
    try {
      const data = await ticketRetreatXq({ id: e });
      data.data.rules = data.data.data;
      setDataSource(data.data);
      setRulesData(!rulesData);
      setLoading(false);
    } catch (error) {}
  };

  useEffect(() => {
    init(id);
  }, [id]);

  return (
    <DetailsPop
      title="退票规则详情"
      visible={detailsVisible}
      isLoading={isLoading}
      setVisible={setDetailsVisible}
      columnsInitial={columnsInitial}
      dataSource={dataSource}
    />
  );
};

RetreatRuleDetails.show = (id: string) => {
  const detailBox = document.createElement('div');
  document.body.appendChild(detailBox);
  const root = createRoot(detailBox);
  root.render(<RetreatRuleDetails id={id} />);
};

export default RetreatRuleDetails;
