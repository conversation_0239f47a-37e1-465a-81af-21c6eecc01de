/*
 * @Author: 李悍宇 <EMAIL>
 * @Date: 2025-05-21 10:26:16
 * @LastEditors: 李悍宇 <EMAIL>
 * @LastEditTime: 2025-06-25 10:22:13
 * @FilePath: \exchange\src\components\RightContent\components\AiMessage.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Chart from '@/components/Chart/index';
import { RightOutlined, SyncOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import styles from './ApiMessage.less';
interface Props {
  onMessageClick: (msg: string) => void; // 父组件传递的回调函数
  nowShow: number;
}

// 随机获取指定数量的消息
const getRandomItems = (array: string[], count: number = 3) => {
  if (array.length <= count) return [...array];

  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled.slice(0, count);
};

export const GetInitialMessage = ({ onMessageClick, nowShow }: Props) => {
  // 存储各类型消息的随机列表
  const [randomMessagesMap, setRandomMessagesMap] = useState<Record<number, string[]>>({});

  // 刷新指定类型的消息列表
  const refreshMessages = (tab: number) => {
    const messages = getRandomItems(msgList[tab] || []);
    setRandomMessagesMap((prev) => ({ ...prev, [tab]: messages }));
  };

  // 初始化或刷新当前显示的消息
  useEffect(() => {
    if (!randomMessagesMap[nowShow]) {
      refreshMessages(nowShow);
    }
  }, [nowShow]);

  const handleItemClick = (msg: string) => {
    onMessageClick(msg);
  };

  const msgList = {
    0: [
      '如何进行企业认证？',
      '如何创建门票？',
      '如何推广店铺？',
      '带我去采购页面',
      '如何找到订单管理',
      '在哪里可以装修店铺',
      '帮我统计最近30天的门票订单销售额',
      '昨天退单数量是多少',
      '本周下级代理商的交易金额是多少',
      '帮我上架一个门票',
      '当商品库存不足10时给我发送站内信提醒',
      '当产品库存小于100时给我发送短信',
      '请帮我设置门票的价格策略',
    ],
    1: ['如何进行企业认证？', '如何创建门票？', '如何推广店铺？'],
    2: ['带我去采购门票', '如何找到订单管理？', '在哪里可以装修店铺？'],
    3: [
      '帮我统计最近30天的门票订单销售额？',
      '昨天退单数量是多少？',
      '本周下级代理的交易金额是多少',
    ],
    4: ['当商品库存不足10时给我发送站内信提醒', '当产品库存小于100时给我发送短信'],
    5: [],
  };
  const titleMsg = {
    0: '你好，我是智能客服助手，我可以为你介绍功能、查找功能入口、搜索订单/门票销售数据、协助管理票务、创建库存预警、设置动态价格策略。',
    1: '我可以帮助您快捷了解产品功能说明和相关业务概念。',
    2: '我可以帮助您快捷找到管理后台的特定功能页面。',
    3: '我可以帮助您快捷查询订单、门票销售数据:',
    4: '我可以帮助您快捷设置库存阈值消息提醒。',
    5: '我可以帮助您快捷设置动态价格策略。<br/>通过对话描述你想要创建或者编辑的门票价格策略，如“帮我设置[商品名称]的单买价格为20元，并根据天气情况动态浮动”',
  };
  const currentMessages = randomMessagesMap[nowShow] || [];
  return (
    <div>
      <div dangerouslySetInnerHTML={{ __html: titleMsg[nowShow] }} />
      <div className={styles.wen}>
        {nowShow !== 5 && <span>你可以试着问我：</span>}
        {nowShow === 0 && (
          <Button
            type="link"
            onClick={() => {
              refreshMessages(nowShow);
            }}
          >
            <SyncOutlined />
            换一换
          </Button>
        )}
      </div>
      {currentMessages.map((item, index) => {
        return (
          <span key={index} className={styles.btnItem} onClick={() => handleItemClick(item)}>
            <span>{item}</span>
            <RightOutlined />
          </span>
        );
      })}
    </div>
  );
};

//带我去采购门票
export const BuyTickMessage = ({ confrimText, buyList }) => {
  // const buyList = ['采购订单列表', '采购订单详情'];
  return (
    <div>
      <div>{confrimText}</div>
      <div className={styles.butBtn}>
        {buyList?.map((item, index) => {
          return (
            <span
              key={index}
              className={styles.item}
              onClick={() => {
                if (item.url.includes('my-shop?')) {
                  history.push(`${item.url}&t=${Date.now()}`);
                } else {
                  history.push(item.url);
                }
              }}
            >
              {item.text}
            </span>
          );
        })}
      </div>
    </div>
  );
};

//数据检索
export const GetEchartsData = ({ value, detail, beginTime, endTime, description }) => {
  const xData = [];
  const yData = [];
  detail.forEach((item, index) => {
    xData.push(item.day);
    yData.push(item.value);
  });
  const option = {
    grid: {
      left: '15%', // 关键修改：设置左侧边距为容器宽度的15%，使图表整体右移
      right: '5%', // 保留右侧边距避免内容贴边
      top: '10%',
      bottom: '10%',
    },
    xAxis: {
      type: 'category',
      data: xData,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: yData,
        type: 'line',
      },
    ],
  };
  return (
    <div className={styles.echartBox}>
      <div>{description}</div>
      <div className={styles.money}>￥{value}</div>
      <div className={styles.echartsShow}>
        <div>
          统计时间：{beginTime}至{endTime}
        </div>
        {detail?.length > 0 ? (
          <Chart options={option} />
        ) : (
          <div style={{ color: '#999',textAlign:'center' }}>图表无数据</div>
        )}
      </div>
      {/* <div>
        最近30天（2025年4月15日至2025年5月14日），你的店铺订单销售额为0元。这可能意味着在此期间没有订单支付成功。建议你检查以下方面：
        <br /> 商品状态：确认商品是否正常上架，是否有库存。
        <br /> 营销活动：检查是否有进行有效的促销活动，吸引顾客下单。
        <br /> 流量来源：分析店铺的访客来源，是否需要加强推广或优化流量渠道。
        <br /> 支付问题：确认支付流程是否正常，是否存在支付失败的情况。
        <br /> 如果需要进一步分析或帮助，请随时告诉我！
      </div> */}
    </div>
  );
};

//票务管理
export const TicketMessage = ({ list, url, text }) => {
  // const list = [
  //   '产品类型:门票',
  //   '产品名称:欢乐谷成人票',
  //   'C端显示名称:欢乐谷成人票',
  //   '市场标准价(元):40',
  //   '有效时长天数(天):1天',
  //   '可入园天数:1天',
  // ];
  return (
    <div className={styles.ticket}>
      <div>{text}</div>
      <ul className={styles.ticketCard}>
        {list?.map((item, index) => {
          return <li key={index}>{item}</li>;
        })}
      </ul>
      <Button
        type={'primary'}
        className={styles.btn}
        onClick={() => {
          history.push(url);
        }}
      >
        预览
      </Button>
    </div>
  );
};
