import ChainModal from '@/common/components/ChainModal';
import ProModal from '@/common/components/ProModal';
import useProModal from '@/common/components/ProModal/useProModal';
import CheckRuleDetails from '@/common/components/RuleDetails/CheckRuleDetails';
import IssueRuleDetails from '@/common/components/RuleDetails/IssueRuleDetails';
import RetreatRuleDetails from '@/common/components/RuleDetails/RetreatRuleDetails';
import Tags from '@/common/components/Tag';
import TimeStore from '@/common/components/TimeStore';
import { tableConfig } from '@/common/utils/config';
import {
  chainStatusEnum,
  orderTypeEnum,
  orderTypeSearch,
  payTypeEnum,
  productTypeEnum,
  ticketStatusEnum,
  ticketTypeEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { formatTime, getUniqueId } from '@/common/utils/tool';
import { numberValidator } from '@/common/utils/validate';
import Blockchain from '@/components/Blockchain';
import TicketSaleDetail from '@/components/TicketSaleDetail';
import useModal from '@/hooks/useModal';
import {
  apiDistributeBuyOrder,
  apiDistributeRefundInfo,
  getOrderAndOrderRefundList,
  getUnifyPullDown,
} from '@/services/api/distribution';
import { getOrderGoodsDetails } from '@/services/api/ticket';
import type { API } from '@/services/api/typings';
import {
  DownOutlined,
  SafetyCertificateOutlined,
  SolutionOutlined,
  UpOutlined,
} from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useLocation, useModel, useRequest } from '@umijs/max';
import { Card, List, Modal, Space, Steps, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import qs from 'qs';
import React, { useEffect, useRef, useState } from 'react';
import SalesReturn from './SalesReturn';

const TableList: React.FC = () => {
  // 分时库存
  const timeModalState1 = useProModal();
  const timeModalState2 = useProModal();
  const [timeModalData, setTimeModalData] = useState<any>({});
  const [lawsList, setLawsList] = useState<any>([]);
  const lawsColumns1 = [
    {
      title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join('-')}`,
      render: () => {},
    },
    {
      title: () => '采购数量',
      render: ({ purchaseNum = 0 }) => purchaseNum,
    },
    {
      title: () => '采购金额（元）',
      render: ({ purchaseAmount = 0 }) => purchaseAmount,
    },
  ];
  const lawsColumns2 = [
    {
      title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join('-')}`,
      render: () => {},
    },
    {
      title: () => '退货数量/采购数量',
      render: ({ purchaseNum = 0, stockAmount = 0 }) => `${purchaseNum}/${stockAmount}`,
    },
    {
      title: () => '退货金额（元）',
      render: ({ purchaseAmount = 0 }) => purchaseAmount,
    },
  ];
  const timeModalColumns1: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '入园有效时间',
          valueType: 'dateRange',
          dataIndex: 'dateRange',
          initialValue: [timeModalData.enterStartTime, timeModalData.enterEndTime],
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '',
          dataIndex: 'timeLaws',
          colProps: { span: 24 },
          renderFormItem: () => <TimeStore lawsColumns={lawsColumns1} lawsList={lawsList} />,
        },
        {
          title: '总采购数量',
          dataIndex: 'num',
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '总采购金额（元）',
          dataIndex: 'price',
          fieldProps: {
            disabled: true,
          },
        },
      ],
    },
  ];
  const timeModalColumns2: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '入园有效时间',
          valueType: 'dateRange',
          dataIndex: 'dateRange',
          initialValue: [timeModalData.enterStartTime, timeModalData.enterEndTime],
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '',
          dataIndex: 'timeLaws',
          colProps: { span: 24 },
          renderFormItem: () => <TimeStore lawsColumns={lawsColumns2} lawsList={lawsList} />,
        },
        {
          title: '总退货数量',
          dataIndex: 'num',
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '总退货金额（元）',
          dataIndex: 'price',
          fieldProps: {
            disabled: true,
          },
        },
      ],
    },
  ];

  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany || {};
  // 【景区】信息
  const [orderId, setOrderId] = useState<string>();
  const [currentRow, setCurrentRow] = useState<API.OrderAndOrderRefundListItem>();
  const [counts, setCounts] = useState(0);
  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);

  const saleModalState = useModal();
  const modalState = useProModal();
  const { Step } = Steps;
  // 首页跳转的参数 只做一次查询
  const { search } = useLocation();
  const [searchParams, setSearchParams] = useState<Record<string, any> | undefined>(
    qs.parse(search, {
      ignoreQueryPrefix: true,
    }),
  );

  const [refundStatus, setRefundStatus] = useState<number>(0);
  const [refundIsChain, setRefundIsChain] = useState<number>(0);

  const getAllDealersReq = useRequest(getUnifyPullDown, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return res.data.map((name: any) => {
        return {
          value: name,
          label: name,
        };
      });
    },
  });
  const getUsernameListReq = useRequest(getUnifyPullDown, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return res.data.map((name: any) => {
        return {
          value: name,
          label: name,
        };
      });
    },
  });
  useEffect(() => {
    if (coId) {
      getAllDealersReq.run({ getUnifyPullDown: coId, type: '1' });
      getUsernameListReq.run({ getUnifyPullDown: coId, type: '4' });
    }
  }, [coId]);
  const orderGoodsDetailsReq = async (params: any) => {
    const { data } = await getOrderGoodsDetails(params);
    return data;
  };

  const columns: ProColumns<API.OrderAndOrderRefundListItem>[] = [
    {
      title: '销售订单号 / 退单号',
      dataIndex: 'orderId',
      width: 250,
      fixed: 'left',
      initialValue: searchParams?.orderId,
    },
    {
      title: '经销商名称',
      width: 150,
      dataIndex: 'buyerName',
      valueType: 'select',
      fieldProps: {
        options: getAllDealersReq.data,
        showSearch: true,
      },
    },
    {
      title: '日期',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },

    {
      title: '交易类型',
      dataIndex: 'transactionType',
      hideInSearch: true,
      renderText: (dom, record) => (record?.isOrderRefund === 'Y' ? '退货' : '进货'),
      width: 150,
    },

    {
      title: '结算单号',
      dataIndex: 'tradeNo',
      width: 220,
      search: false,
    },

    {
      title: '时间',
      dataIndex: 'createTime',
      width: 200,
      hideInSearch: true,
      renderText: (dom) => dayjs(dom).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      width: 200,
      valueEnum: payTypeEnum,
      hideInSearch: true,
    },
    {
      title: '总数',
      dataIndex: 'purchaseQuantity',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '总金额（元）',
      width: 120,
      dataIndex: 'totalAmount',
      hideInSearch: true,
      renderText: (text, record) => {
        const money = parseFloat(text).toFixed(2);
        return record?.isOrderRefund === 'Y'
          ? record?.refundAmount
            ? `-${parseFloat(record?.refundAmount).toFixed(2)}`
            : record?.refundAmount
          : parseFloat(money) > 0
          ? `+${money}`
          : money;
      },
    },
    {
      title: '操作人',
      dataIndex: 'username',
      valueType: 'select',
      width: 200,
      fieldProps: {
        options: getUsernameListReq.data,
        showSearch: true,
      },
    },
    {
      title: '状态',
      dataIndex: 'orderStatus',
      hideInSearch: true,
      width: 150,
      valueEnum: (row) => (row?.isOrderRefund === 'Y' ? orderTypeEnum : orderTypeEnum),
      fixed: 'right',
      render: (dom: any) => {
        return <Tag color="blue">{dom}</Tag>;
      },
    },

    {
      title: '状态',
      dataIndex: 'orderStatus',
      hideInTable: true,
      valueEnum: orderTypeSearch,
    },
    {
      title: '交易上链',
      dataIndex: 'isChain',
      valueEnum: chainStatusEnum,
      width: 100,
      fixed: 'right',
      renderText: (dom) => <Tags type="chainStatus" value={dom} />,
      search: false,
    },
    {
      title: '操作',
      key: 'option',
      hideInSearch: true,
      width: 100,
      fixed: 'right',
      render: (dom: any, record: any) => {
        return (
          <Space
            onClick={() => {
              setCurrentRow(record);
            }}
          >
            <a
              onClick={async () => {
                // setDetailsVisible(true);
                setOrderId(record.orderGroupId);
                modalState.setType('info');
                addOperationLogRequest({
                  action: 'info',
                  content: `查看【${record.orderId}】销售订单`,
                });
              }}
            >
              查看
            </a>

            {record.txId && (
              <ChainModal
                chainData={{
                  txId: record.txId,
                }}
              />
            )}
          </Space>
        );
      },
    },
  ];
  //查看区块链交易数据
  const [txid, setTxId] = useState('');
  const [txIds, setTxIds] = useState<any>([]);
  const [tokenIds, setTokenIds] = useState<any>([]);
  const [cochainVisible, setCochainVisible] = useState(false);
  const [cochainDataDataSource, setCochainDataDataSource] = useState<any>({});
  const cochainColumns = [
    {
      title: '区块链账号',
      columns: [
        {
          title: '发行方',
          dataIndex: 'fromAccount',
          span: 5,
          render: (dom: any) => dom,
        },
        {
          title: '接收方',
          dataIndex: 'toAccount',
          span: 5,
          render: (dom: any) => dom,
        },
      ],
    },
    {
      title: '存证信息',
      columns: [
        {
          title: '存证时间',
          dataIndex: 'transactionAt',
          span: 5,
        },
        {
          title: 'TokenID',
          dataIndex: 'tokenIds',
          span: 5,
          render: (dom: any) => {
            return dom && dom.data.length > 0
              ? dom?.data.map((item: any, index: number) => (
                  <>
                    <span>{item}</span>
                    <br />
                    {index + 1 == dom.data.length && dom.data.length > 1 ? (
                      <span
                        style={{ color: '#1890ff', cursor: 'pointer' }}
                        onClick={() => {
                          const obj = { ...cochainDataDataSource };
                          if (dom.unfold) {
                            obj.tokenIds = {
                              unfold: false,
                              data: cochainDataDataSource.tokenIds.data.slice(0, 5),
                            };
                            setCochainDataDataSource(obj);
                          } else {
                            obj.tokenIds = { unfold: true, data: tokenIds };
                            setCochainDataDataSource(obj);
                          }
                        }}
                      >
                        {dom.unfold ? '收起 ' : '展开 '}
                        {dom.unfold ? <UpOutlined /> : <DownOutlined />}
                      </span>
                    ) : (
                      ''
                    )}
                  </>
                ))
              : '-';
          },
        },
        {
          title: '交易哈希',
          dataIndex: 'txId',
          span: 5,
          render: (dom: any) => {
            return dom && dom.data.length > 0
              ? dom?.data.map((item: any, index: number) => (
                  <>
                    <span>{item}</span>
                    <br />
                    {index + 1 == dom.data.length && dom.data.length > 1 ? (
                      <span
                        style={{ color: '#1890ff', cursor: 'pointer' }}
                        onClick={() => {
                          const obj = { ...cochainDataDataSource };
                          if (dom.unfold) {
                            obj.txId = {
                              unfold: false,
                              data: cochainDataDataSource.txId.data.slice(0, 5),
                            };
                            setCochainDataDataSource(obj);
                          } else {
                            obj.txId = { unfold: true, data: txIds };
                            setCochainDataDataSource(obj);
                          }
                        }}
                      >
                        {dom.unfold ? '收起 ' : '展开 '}
                        {dom.unfold ? <UpOutlined /> : <DownOutlined />}
                      </span>
                    ) : (
                      ''
                    )}
                  </>
                ))
              : '-';
          },
        },
      ],
    },
  ];

  // 【列表】数据绑定
  const columnsInitial = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'ticketType',
      search: false,
      valueEnum: productTypeEnum,
    },
    {
      title: '入园日期',
      dataIndex: 'dayBegin',
      search: false,
      render: (dom: any, entity: any) =>
        entity.noDateList ? (
          <a
            onClick={() => {
              setUnTimeDataSource(entity.noDateList);
              setUnTimeVisible(true);
            }}
          >
            详情
          </a>
        ) : (
          '-'
        ),
    },
    {
      title: '进货总量',
      dataIndex: 'totalNum',
      search: false,
      align: 'right',
      valueType: 'digit',
    },
    {
      title: '进货金额（元）',
      dataIndex: 'totalAmount',
      search: false,
      align: 'right',
      renderText: (dom: any) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  // 分时数据绑定
  const [timeVisible, setTimeVisible] = useState<any>();
  // 分时序列 [表格序，列表序]
  const [timeTitle, setTimeTitle] = useState<any>();
  const [timeDataSource, setTimeDataSource] = useState<any>();
  // 分时表格配置
  const timeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '分时预约',
      dataIndex: 'timeShare',
      // render: (_, entity) =>
      //   entity.timeBeginTime && entity.timeEndTime ? (
      //     <span>{entity.timeBeginTime + ' 至 ' + entity.timeEndTime}</span>
      //   ) : (
      //     '-'
      //   ),
    },
    // {
    //   title: '当前库存',
    //   dataIndex: 'number',
    // },
    {
      title: '进货数量',
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  // 非分时数据绑定
  const [unTimeVisible, setUnTimeVisible] = useState<boolean>(false);
  const [unTimeDataSource, setUnTimeDataSource] = useState<any>();
  const unTimeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
      render: (dom: any, record, index, __, schema: any) => (
        <span>
          {dom} {record.isExchange === 1 && <Tag color="blue">交易所</Tag>}
        </span>
      ),
    },

    {
      title: '入园日期',
      dataIndex: 'dayBegin',
      hideInSearch: true,
      render: (dom: any, entity: any) => entity.dayBegin + ' 至 ' + entity.dayEnd,
    },
    {
      title: '进货数量',
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  const columnsPurchase: ProColumns[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      render: (dom: keyof typeof productTypeEnum) => <span>{productTypeEnum[dom]}</span>,
    },
    {
      title: '库存批次号',
      width: 180,
      dataIndex: 'batchId',
      render: (dom, record) => (
        <span>
          {dom}
          {record.isExchange === 1 && <Tag color="blue">交易所</Tag>}
        </span>
      ),
    },
    {
      title: '购买有效时间',
      width: 200,
      dataIndex: 'buyStartTime',
      render: (dom, record) => (
        <span>
          {dayjs(record.buyStartTime).format('YYYY-MM-DD')}至
          {dayjs(record.buyEndTime).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: '入园有效时间',
      width: 200,
      dataIndex: 'dayBegin',
      render: (dom, record) => (
        <span>
          {dayjs(record.dayBegin).format('YYYY-MM-DD')}至{dayjs(record.dayEnd).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: '分时时段',
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList?.length
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '市场标准价（元）',
      width: 150,
      dataIndex: 'marketPrice',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '有效时长天数（天）',
      width: 150,
      dataIndex: 'validDay',
    },
    {
      title: '可入园天数',
      dataIndex: 'enterDay',
    },
    {
      title: '使用次数',
      width: 80,
      dataIndex: 'useTimes',
      render: (dom) => <span>每天{dom}次</span>,
    },
    {
      title: '采购数量',
      dataIndex: 'purchaseNum',
    },
    // {
    //   title: '退货数量',
    //   dataIndex: 'refundNum',
    // },
    {
      title: '单价（元）',
      dataIndex: 'unitPrice',
    },
    {
      title: '金额（元）',
      dataIndex: 'buyMoney',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, entity: any) =>
        entity.timeLaws && (
          <a
            key={getUniqueId()}
            onClick={() => {
              setLawsList(entity.timeShareVoList);
              setTimeModalData({
                enterStartTime: entity.dayBegin,
                enterEndTime: entity.dayEnd,
                timeLaws: JSON.parse(entity.timeLaws),
                num: entity.purchaseNum,
                price: entity.buyMoney,
              });
              timeModalState1.setType('edit');
            }}
          >
            分时详情
          </a>
        ),
    },
  ];
  const columnsSubPurchase: ColumnsType = [
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      dataIndex: 'type',
      render: (dom: keyof typeof ticketTypeEnum) => <span>{ticketTypeEnum[dom]}</span>,
    },
    {
      title: '特殊折扣率',
      dataIndex: 'overallDiscount',
      render: (dom) => <span>{dom}%</span>,
    },
    {
      title: '分销折扣区间',
      width: 150,
      dataIndex: 'overallDiscount',
      render: (dom, record) => (
        <span>
          {record?.beginDiscount}%-{record?.endDiscount}%
        </span>
      ),
    },
    {
      title: '权益票',
      dataIndex: 'isRights',
      render: (dom) => <span>{dom == 1 ? '权益票' : '非权益票'}</span>,
    },
    {
      title: '数字资产',
      width: 80,
      dataIndex: 'isDigit',
      render: (dom) => <span>{dom == 1 ? '是' : '否'}</span>,
    },
    {
      title: '定价（元）',
      dataIndex: 'marketPrice',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '出票规则',
      dataIndex: 'issueName',
      render: (dom: any, entity: any) => (
        <a
          onClick={() => {
            IssueRuleDetails.show(entity.issueId);
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '检票规则',
      dataIndex: 'checkName',
      render: (dom: any, entity: any) => (
        <a
          onClick={() => {
            CheckRuleDetails.show(entity.checkId);
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '退票规则',
      dataIndex: 'retreatName',
      render: (dom: any, entity: any) => (
        <a
          onClick={() => {
            RetreatRuleDetails.show(entity.retreatId);
          }}
        >
          {dom}
        </a>
      ),
    },
  ];

  const fetchListData = async (params: any) => {
    const { data } = await getOrderAndOrderRefundList(params);
    // 首页跳转 特殊处理

    if (searchParams?.orderId) {
      const item = data.data[0];
      setCurrentRow(item);
      setDetailsVisible(true);
      addOperationLogRequest({
        action: 'info',
        content: `查看【${searchParams?.orderId}】销售订单`,
      });
      setOrderId(item.orderGroupId);
      setSearchParams(undefined);
    }

    // 处理展开的 children 数据
    const _newData: any = [];
    data.data?.forEach((record) => {
      const _newRecord: any = { ...record };
      if (!isEmpty(record.orderRefund)) {
        _newRecord.children = record.orderRefund?.map((e) => ({
          ...e,
          buyerName: record?.buyerName,
          isOrderRefund: 'Y',
          orderId: e?.refundId,
          orderStatus: e?.refundStatus,
          isChain: e?.isChainOrderRefund,
        }));
      }
      _newData.push(_newRecord);
    });
    return {
      ...data,
      data: _newData,
    };
  };

  const columnsRefundInitial: ColumnsType = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      render: (dom: keyof typeof productTypeEnum) => <span>{productTypeEnum[dom]}</span>,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      render: (dom: keyof typeof ticketTypeEnum) => <span>{ticketTypeEnum[dom]}</span>,
    },
    {
      title: '库存批次号',
      width: 180,
      dataIndex: 'batchId',
      render: (dom, record) => (
        <span>
          {dom}
          {record.isExchange === 1 && <Tag color="blue">交易所</Tag>}
        </span>
      ),
    },
    {
      title: '购买有效时间',
      width: 200,
      dataIndex: 'buyStartTime',
      render: (dom, record) => (
        <span>
          {dayjs(record.buyStartTime).format('YYYY-MM-DD')}至
          {dayjs(record.buyEndTime).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: '入园有效时间',
      width: 200,
      dataIndex: 'dayBegin',
      render: (dom, record) => (
        <span>
          {dayjs(record.dayBegin).format('YYYY-MM-DD')}至{dayjs(record.dayEnd).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: '分时时段',
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList?.length
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '市场标准价（元）',
      width: 150,
      dataIndex: 'marketPrice',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '有效时长天数（天）',
      width: 150,
      dataIndex: 'validDay',
    },
    {
      title: '可入园天数',
      dataIndex: 'enterDay',
    },
    {
      title: '使用次数',
      width: 80,
      dataIndex: 'useTimes',
      render: (dom) => <span>每天{dom}次</span>,
    },
    {
      title: '采购数量',
      dataIndex: 'purchaseNum',
    },
    {
      title: '退货数量',
      dataIndex: 'refundNum',
    },
    {
      title: '单价（元）',
      dataIndex: 'unitPrice',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '退货金额（元）',
      dataIndex: 'refundMoney',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '退单状态',
      dataIndex: 'refundStatus',
      render: (dom) => <Tags type="orderStatus" value={dom} />,
    },
    {
      title: '交易上链',
      dataIndex: 'isChain',
      render: (dom) => <Tags type="chainStatus" value={dom} />,
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, entity: any) =>
        entity.timeLaws && (
          <a
            key={getUniqueId()}
            onClick={() => {
              setLawsList(entity.timeShareVoList);
              setTimeModalData({
                enterStartTime: entity.dayBegin,
                enterEndTime: entity.dayEnd,
                timeLaws: JSON.parse(entity.timeLaws),
                num: entity.refundNum,
                price: entity.refundMoney,
              });
              timeModalState2.setType('edit');
            }}
          >
            分时详情
          </a>
        ),
    },
  ];

  const saleTicketColumns: ProColumns[] = [
    {
      title: '票号',
      dataIndex: 'ticketNumber',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '商品名称',
      dataIndex: 'productSkuName',
    },
    {
      title: '票种',
      dataIndex: 'ticketType',
      valueEnum: ticketTypeEnum,
    },
    {
      title: '数字资产',
      dataIndex: 'isDigit',
      search: false,
      render: (dom) => <span>{dom == 1 ? '是' : '否'}</span>,
    },
    {
      title: '库存批次号',
      dataIndex: 'batchId',
      render: (dom: any, record, index, __, schema: any) => (
        <span>
          {dom} {record.isExchange === 1 && <Tag color="blue">交易所</Tag>}
        </span>
      ),
      formItemProps: {
        rules: [numberValidator()],
      },
    },
    {
      title: '出票时间',
      dataIndex: 'cTime',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            ticketStartCreateTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            ticketEndCreateTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '出票时间',
      dataIndex: 'createTime',
      search: false,
    },
    {
      title: '预计入园时间',
      dataIndex: 'eTime',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            playStartCreateTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            playEndCreateTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '预计入园时间',
      dataIndex: 'enterTime',
      search: false,
    },
    {
      title: '分时预约时间',
      search: false,
      dataIndex: 'timeShare',
    },
    {
      title: '有效时长天数（天）',
      search: false,
      dataIndex: 'validDay',
    },
    {
      title: '可入园天数',
      search: false,
      dataIndex: 'enterDay',
    },
    {
      title: '使用次数',
      dataIndex: 'useTimes',
      search: false,
      render: (dom) => `每天${dom}次`,
    },
    {
      title: '人数',
      dataIndex: 'num',
      search: false,
    },
    {
      title: '采购单价（元）',
      dataIndex: 'purchasePrice',
      search: false,
      render: (dom: any) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额（元）',
      dataIndex: 'totalMoney',
      search: false,
      render: (dom: any) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '门票状态',
      dataIndex: 'ticketStatus',
      valueEnum: ticketStatusEnum,
    },
  ];

  // const exportState2 = useExport('/order/orderPurchaseGoodsPageList', saleTicketColumns, {
  //   distributionType: 1,
  //   orderId: currentRow?.orderId,
  //   commerceCompanyId: coId,
  // });

  const modalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '',
          colSize: 3,
          dataIndex: 'orderStatusFlow',
          render: (orderStatusFlow, record) => {
            const orderStatus: keyof typeof orderTypeEnum = record?.orderTab?.orderStatus;
            const orderTime = record?.orderStatusFlow?.orderTime;
            const payTime = record?.orderStatusFlow?.payTime;
            let current;
            if (orderTime) current = 0;
            if (payTime) current = 2;
            return (
              <div style={{ width: '100%' }}>
                <h3>当前订单状态：{orderTypeEnum[orderStatus]}</h3>
                <Steps style={{ margin: '20px auto' }} size="small" current={current}>
                  <Step
                    key={0}
                    icon={<SolutionOutlined />}
                    title={'创建订单'}
                    description={
                      <div>
                        <div>{dayjs(orderTime).format('YYYY-MM-DD')}</div>
                        <div>{dayjs(orderTime).format('HH:mm:ss')}</div>
                      </div>
                    }
                  />
                  <Step
                    key={1}
                    icon={<SafetyCertificateOutlined />}
                    title={'支付成功'}
                    description={
                      payTime && (
                        <div>
                          <div>{dayjs(payTime).format('YYYY-MM-DD')}</div>
                          <div>{dayjs(payTime).format('HH:mm:ss')}</div>
                        </div>
                      )
                    }
                  />
                  <Step
                    key={2}
                    icon={<SafetyCertificateOutlined />}
                    title={'采购完成'}
                    description={
                      payTime && (
                        <div>
                          <div>{dayjs(payTime).format('YYYY-MM-DD')}</div>
                          <div>{dayjs(payTime).format('HH:mm:ss')}</div>
                        </div>
                      )
                    }
                  />
                </Steps>
              </div>
            );
          },
        },
      ],
    },
    {
      title: '基本信息',
      columns: [
        {
          title: '销售订单号',
          dataIndex: ['orderTab', 'orderId'],
        },
        {
          title: '经销商名称',
          dataIndex: ['orderTab', 'sellerName'],
        },
        {
          title: '结算单号',
          dataIndex: ['orderTab', 'tradeNo'],
        },
        {
          title: '采购时间',
          dataIndex: ['orderTab', 'payTime'],
          renderText: (dom) => formatTime(dom),
        },
        {
          title: '支付时间',
          dataIndex: ['orderTab', 'payTime'],
          renderText: (dom) => formatTime(dom),
        },
        {
          title: '操作人',
          dataIndex: ['orderTab', 'username'],
        },
        {
          title: '支付方式',
          valueEnum: payTypeEnum,
          dataIndex: ['orderTab', 'payType'],
        },
        {
          title: '交易上链',
          valueEnum: chainStatusEnum,
          dataIndex: ['orderTab', 'isChain'],
        },
      ],
    },
    {
      title: '销售信息',
      columns: [
        {
          colSize: 3,
          dataIndex: 'buyProductList',
          title: '',
          render: (col: any, record) => (
            <Table
              {...tableConfig}
              size="middle"
              rowKey={(record) => record.batchId}
              columns={columnsPurchase}
              expandable={{
                defaultExpandAllRows: true,
                expandedRowRender: (record, index, indent, expanded) => {
                  return (
                    <Table
                      size="middle"
                      pagination={false}
                      columns={columnsSubPurchase}
                      dataSource={[record?.goodsInfoVO]}
                    />
                  );
                },
                // rowExpandable: (record) => record.proType !== 20,
              }}
              dataSource={col}
              footer={() => (
                <div style={{ textAlign: 'right' }}>
                  销售总数：{record.totalNum}
                  &nbsp;&nbsp;&nbsp;&nbsp; 销售总金额（元）：
                  {record.totalAmount}
                </div>
              )}
              pagination={false}
            />
          ),
        },
      ],
    },
    {
      title: '退单信息',
      columns: [
        {
          colSize: 3,
          dataIndex: 'productList',
          title: '',
          render: (col: any, record) => (
            <Table
              {...tableConfig}
              bordered
              size="middle"
              // scroll={{ x: 2000 }}
              columns={columnsRefundInitial}
              dataSource={col}
              footer={() =>
                record.refundNum ? (
                  <div style={{ textAlign: 'right' }}>
                    退货总数：{record.refundNum}
                    &nbsp;&nbsp;&nbsp;&nbsp; 退货总金额（元）：
                    {record.refundAmount}
                  </div>
                ) : null
              }
              pagination={false}
            />
          ),
        },
      ],
    },
    {
      title: '售票记录',
      columns: [
        {
          colSize: 3,
          dataIndex: 'buyProductList',
          title: '',
          className: 'no-padding',
          render: (col, record) => {
            return (
              <ProTable
                {...tableConfig}
                // className="order_InfoData"
                style={{ width: '100%' }}
                // bordered={true}
                actionRef={actionRef}
                params={{
                  distributionType: 1,
                  orderId: currentRow?.orderId,
                  commerceCompanyId: coId,
                }}
                columns={saleTicketColumns}
                form={{
                  ignoreRules: false,
                }}
                request={orderGoodsDetailsReq}
                // pagination={
                //   orderType === 'JQTC'
                //     ? false
                //     : {
                //         defaultPageSize: 10,
                //       }
                // }
                rowKey="key"
                footer={(record) => {
                  // 计算总差价
                  let differencesPrice: any = 0;
                  record?.forEach((item) => {
                    differencesPrice += item.differencesPrice;
                  }, 0);
                  return (
                    <div style={{ textAlign: 'right' }}>
                      总差价（元）：+{(differencesPrice || 0)?.toFixed(2)}
                    </div>
                  );
                }}
                // formRef={exportState2.formRef}
                // columnsState={columnsState(exportState2)}
                // toolBarRender={() => [
                //   <ExportButton key={getUniqueId()} {...{ exportState: exportState2 }} />,
                // ]}
              />
            );
          },
        },
      ],
    },
  ];

  return (
    <>
      <ProTable<API.OrderAndOrderRefundListItem>
        {...tableConfig}
        style={modalState.tableStyle}
        // tableLayout="fixed"
        actionRef={actionRef}
        rowKey="orderId"
        params={{ sellerId: coId, orderType: 'JQFX', ...searchParams }}
        request={fetchListData}
        columns={columns}
        scroll={{ x: 1580 }}
        // expandable={{
        //   expandedRowRender: (record, index, indent, expanded) => {
        //     return <RefundOrderTable dataItem={record} />;
        //   },
        //   rowExpandable: (record) => !isEmpty(record.orderRefund),
        //   expandedRowClassName: (record) => styles.expanded,
        // }}
      />

      <Modal
        width={1200}
        title={'销售订单详情'}
        open={detailsVisible}
        destroyOnClose
        footer={false}
        onCancel={() => {
          setDetailsVisible(false);
        }}
      >
        {/* 商品表格 */}
        <ProTable<API.RuleListItem, API.PageParams>
          {...tableConfig}
          rowKey="id"
          actionRef={actionRef}
          params={{ orderId }}
          request={async (e) => {
            const res: any = await apiDistributeBuyOrder(e);
            setCounts(res.data.totalAmount);
            setTxId(res.data.txId);
            res.data = res.data.productList;
            res.data.map((item: any) => {
              item.id = getUniqueId();
            });
            return res;
          }}
          columns={columnsInitial}
          headerTitle={<div style={{}}>销售订单号：{currentRow?.orderId}&emsp;&emsp;&emsp;</div>}
          expandable={{
            expandedRowRender: (record: any, indexTable: number) => (
              <>
                <span style={{ padding: '8px', display: 'block' }}>入园日期及分时预约信息：</span>
                <List
                  style={{
                    padding: '4px 8px',
                    maxHeight: '300px',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                  }}
                  grid={{
                    gutter: 16,
                    xs: 1,
                    sm: 2,
                    md: 4,
                    lg: 4,
                    xl: 5,
                    xxl: 5,
                  }}
                  dataSource={record.dateList}
                  renderItem={(item: any, indexList: number) => (
                    <List.Item className="cardBox">
                      <Card
                        title={item.dayBegin}
                        hoverable
                        size="small"
                        extra={
                          <a
                            onClick={() => {
                              // 修改分时信息
                              setTimeDataSource(item.timeShareDetail);
                              // setTimeTitle(item.dayBegin)
                              setTimeVisible(true);
                            }}
                          >
                            详情
                          </a>
                        }
                      >
                        <p>数量：{item.totalNum}</p>
                        <p>金额：￥ {item.totalAmount}</p>
                      </Card>
                    </List.Item>
                  )}
                />
              </>
            ),
            rowExpandable: (record: any) => record.dateList,
          }}
        />

        <div
          style={{ marginRight: '40px', textAlign: 'right', fontWeight: '400', fontSize: '20px' }}
        >
          总计：{counts} 元
        </div>

        <SalesReturn refundIds={currentRow?.orderRefund?.map((i) => i.refundId) || []} />
      </Modal>

      <ProModal
        page
        {...modalState}
        title="销售订单"
        actionRef={actionRef}
        columns={modalColumns}
        params={{ orderId }}
        infoRequest={async (params) => {
          const res: any = await apiDistributeBuyOrder(params);
          setCounts(res.data.totalAmount);
          setTxId(res.data.txId);
          // 添加退货总数和金额
          res.data.totalAmount = res.data.buyProductList.reduce((prev: any, next: any) => {
            return prev + next.buyMoney * 1;
          }, 0);
          res.data.totalAmount = parseFloat(res.data.totalAmount).toFixed(2);
          res.data.totalNum = res.data.buyProductList.reduce((prev: any, next: any) => {
            return prev + next.purchaseNum * 1;
          }, 0);

          // 退单信息
          const refundIds = currentRow?.orderRefund?.map((i) => i.refundId) || [];
          if (refundIds.length > 0) {
            const resRefund: any = await apiDistributeRefundInfo({
              orderId: refundIds,
            });
            const refundProductList: any = [];
            resRefund.data.distributeRefund.forEach((item: any) => {
              item.productList.forEach((i: any) => {
                i.refundStatus = item.refundStatus;
                i.isChain = item.isChain;
              });
              refundProductList.push(...item.productList);
            });

            // 添加退货总数和金额
            res.data.refundAmount = refundProductList.reduce((prev: any, next: any) => {
              return prev + next.refundMoney * 1;
            }, 0);
            res.data.refundAmount = parseFloat(res.data.refundAmount).toFixed(2);
            res.data.refundNum = refundProductList.reduce((prev: any, next: any) => {
              return prev + next.refundNum * 1;
            }, 0);
            res.data.productList = refundProductList;
          }

          addOperationLogRequest({
            action: 'info',
            content: `查看【${orderId}】采购退单详情`,
          });
          return res;
        }}
        // infoRequest={async (params) => {
        //   const res: any = await apiDistributeRefundInfo(params);
        //   addOperationLogRequest({
        //     action: 'info',
        //     content: `查看【${orderId}】采购退单详情`,
        //   });
        //   // 添加退货总数和金额
        //   res.data.refundAmount = res.data.productList.reduce((prev: any, next: any) => {
        //     return prev + next.refundMoney * 1;
        //   }, 0);
        //   res.data.refundAmount = parseFloat(res.data.refundAmount).toFixed(2);
        //   res.data.refundNum = res.data.productList.reduce((prev: any, next: any) => {
        //     return prev + next.refundNum * 1;
        //   }, 0);
        //   return res;
        // }}
      />

      {/* 销售订单售票详情 */}
      <TicketSaleDetail modalState={saleModalState} order={currentRow} type="sale" />

      {/* 查看区块链交易记录 */}
      <Blockchain
        cochainVisible={cochainVisible}
        setCochainVisible={setCochainVisible}
        cochainColumns={cochainColumns}
        cochainDataDataSource={cochainDataDataSource}
      />

      {/* 分时预约详情弹窗 */}
      <Modal
        width={modelWidth.lg}
        title={'分时预约信息'}
        visible={timeVisible}
        destroyOnClose
        footer={null}
        onCancel={() => {
          setTimeVisible(false);
        }}
      >
        <ProTable
          {...tableConfig}
          headerTitle={timeTitle}
          columns={timeTableColumns}
          dataSource={timeDataSource}
          search={false}
        />
      </Modal>

      {/* 非分时预约详情弹窗 */}
      <Modal
        width={modelWidth.lg}
        title={'入园日期信息'}
        visible={unTimeVisible}
        destroyOnClose
        footer={null}
        onCancel={() => {
          setUnTimeVisible(false);
        }}
      >
        <ProTable
          {...tableConfig}
          columns={unTimeTableColumns}
          dataSource={unTimeDataSource}
          search={false}
        />
      </Modal>
      {/* 分时库存 */}
      <ProModal
        {...timeModalState1}
        fullTitle="分时详情"
        columns={timeModalColumns1}
        layout="horizontal"
        dataSource={timeModalData}
        onFinish={async () => true}
      />
      <ProModal
        {...timeModalState2}
        fullTitle="分时详情"
        columns={timeModalColumns2}
        layout="horizontal"
        dataSource={timeModalData}
        onFinish={async () => true}
      />
    </>
  );
};

export default TableList;
