.item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // height: 160px;
  /* 显示的最大行数（可根据实际行高调整） */
  padding: 10px 0;
  border-bottom: 1px solid #eee;

  .content {
    flex: 1;  
    /* 占据剩余空间 */
    display: -webkit-box;
    /* 关键属性 */
    -webkit-box-orient: vertical;
    /* 垂直排列 */
    -webkit-line-clamp: 8;
    /* 显示的最大行数（可根据实际行高调整） */
    overflow: hidden;
    text-overflow: ellipsis;
    /* 省略号 */
    line-height: 1.5;
    /* 建议设置行高，方便计算行数 */
  }

  .time {
    color: #999;
    font-size: 12px;
    margin-bottom: 8px;
  }

  .btn {
    padding: 0;
  }
}

.paginationBox {
  // float: right;
  // margin-top: 30px;
  position: absolute;
  bottom: 2%;
  right: 10px;
}

.noData {
  text-align: center;
  color: #ccc;
  margin-top: 30%;
}