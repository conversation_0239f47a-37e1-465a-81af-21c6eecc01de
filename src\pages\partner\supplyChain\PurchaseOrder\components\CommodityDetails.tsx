import DetailsPop from '@/common/components/DetailsPop';
import CheckRuleDetails from '@/common/components/RuleDetails/CheckRuleDetails';
import IssueRuleDetails from '@/common/components/RuleDetails/IssueRuleDetails';
import RetreatRuleDetails from '@/common/components/RuleDetails/RetreatRuleDetails';
import { discountRate, ticketTypeEnum, useTypeEnum } from '@/common/utils/enum';
import { apiSimpleGoodsDetail } from '@/services/api/distribution';
import { Tag } from 'antd';
import { isEmpty, isNil } from 'lodash';
import { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';

const Detail = ({ id }: any) => {
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });

  const columnsInitial = [
    {
      title: '基础信息',
      columns: [
        {
          title: '所属产品名称',
          dataIndex: 'ticketName',
        },
        {
          title: '商品名称',
          dataIndex: 'goodsName',
        },
        {
          title: '票种',
          dataIndex: 'type',
          valueEnum: ticketTypeEnum,
        },
        {
          title: '使用方式',
          dataIndex: 'useType',
          valueEnum: useTypeEnum,
        },
      ],
    },
    {
      title: '分时信息',
      columns: [
        {
          title: '分时预约',
          dataIndex: 'timeShareVoList',
          render: (_, { timeShareVoList }: any) => (isEmpty(timeShareVoList) ? '关' : '开'),
        },
        {
          title: '分时时段',
          dataIndex: 'timeShareVoList',
          render: (_, { timeShareVoList }: any) => {
            return timeShareVoList
              ? timeShareVoList.map((item: any) => (
                  <Tag key={item.id} style={{ marginBottom: 12 }}>
                    {[item.beginTime, item.endTime].join('-')}
                  </Tag>
                ))
              : '-';
          },
        },
      ],
    },
    {
      title: '价格信息',
      columns: [
        {
          title: discountRate[dataSource.type],
          render: (_: any, entity: any) => {
            return (
              <div>
                <span>{entity.overallDiscount} %</span>
                <br />
                <span style={{ marginLeft: '-83px' }}>
                  {`${entity.marketPrice} * ${entity.overallDiscount}%`} ={' '}
                  {(entity.marketPrice * (entity.overallDiscount / 100)).toFixed(2)}{' '}
                </span>
              </div>
            );
          },
        },
        {
          title: '分销折扣区间（%）',
          render: (_: any, entity: any) => {
            return (
              <>
                <span>
                  {!isNil(entity.beginDiscount ?? entity.endDiscount)
                    ? entity.beginDiscount + ' ' + '%' + ' ~ ' + entity.endDiscount + ' ' + '%'
                    : '-'}
                </span>
                <br />
                <span style={{ marginLeft: '-110px' }}>
                  {(
                    ((entity.beginDiscount * 1) / 100) *
                    (entity.marketPrice * (entity.overallDiscount / 100))
                  ).toFixed(2)}{' '}
                  ~{' '}
                  {(
                    ((entity.endDiscount * 1) / 100) *
                    (entity.marketPrice * (entity.overallDiscount / 100))
                  ).toFixed(2)}
                </span>
              </>
            );
          },
        },
      ],
    },
    {
      title: '其他信息',
      columns: [
        {
          title: '是否允许购买数量控制',
          dataIndex: 'isPeopleNumber',
          render: (dom: any) => ['否', '是'][dom],
        },
        {
          title: '最小起订量',
          dataIndex: 'minPeople',
          render: (dom: any, entity: any) => (entity.isPeopleNumber ? dom : '-'),
        },
        {
          title: '单次最大预订量',
          dataIndex: 'maxPeople',
          render: (dom: any, entity: any) => (entity.isPeopleNumber ? dom : '-'),
        },
        {
          title: '使用有效期',
          dataIndex: 'validityDay',
          render: (dom: any) => <span>游玩日期起 {dom} 天内有效</span>,
        },
      ],
    },
    {
      title: '关联规则',
      columns: [
        {
          title: '出票规则',
          dataIndex: 'issueName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                IssueRuleDetails.show(entity.issueId);
              }}
            >
              {dom}
            </a>
          ),
        },
        {
          title: '检票规则',
          dataIndex: 'checkName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                CheckRuleDetails.show(entity.checkId);
              }}
            >
              {dom}
            </a>
          ),
        },
        {
          title: '退票规则',
          dataIndex: 'retreatName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                RetreatRuleDetails.show(entity.retreatId);
              }}
            >
              {dom}
            </a>
          ),
        },
      ],
    },
    {
      title: '说明信息',
      columns: [
        {
          title: '备注',
          dataIndex: 'remark',
        },
      ],
    },
  ];
  const init = async (e: string) => {
    setLoading(true);
    setDetailsVisible(true);
    const { data } = await apiSimpleGoodsDetail(e);
    data.useType += '';
    setDataSource(data);
    setLoading(false);
  };
  useEffect(() => {
    init(id);
  }, [id]);

  return (
    <DetailsPop
      limit={{
        openClose: 'canTicketGoodSet_openClose',
        delete: 'canTicketGoodSet_delete',
        edit: 'canTicketGoodSet_edit',
      }}
      title="商品详情"
      visible={detailsVisible}
      isLoading={isLoading}
      setVisible={setDetailsVisible}
      columnsInitial={columnsInitial}
      dataSource={dataSource}
    />
  );
};

Detail.show = (id: string) => {
  const detailBox = document.createElement('div');
  document.body.appendChild(detailBox);
  const root = createRoot(detailBox);
  root.render(<Detail id={id} />);
};

export default Detail;
