import DetailsPop from '@/common/components/DetailsPop';
import { TicketCheckXq } from '@/services/api/ticket';
import { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';

const CheckRuleDetails = ({ id }: any) => {
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);

  const columnsInitial = [
    {
      title: '基础信息',
      columns: [
        {
          title: '所属景区',
          dataIndex: 'scenicName',
          // render: () => scenicName,
        },
        {
          title: '身份识别类型',
          dataIndex: 'identityTypeList',
          valueType: 'checkbox',
          options: ['人脸', '票', '身份证'],
        },
        {
          title: '检票规则名称',
          dataIndex: 'name',
          span: 2,
        },
        {
          title: '检票点名称',
          dataIndex: 'pointList',
          valueType: 'select',
          render: (dom: any, entity: any) =>
            entity.checkInPointList.map((item: any) => item.checkName + ' '),
        },
      ],
    },
    {
      title: '检票信息',
      columns: [
        {
          title: '检票控制方式',
          dataIndex: 'controlType',
          render: (dom: any) => '平台检票',
        },
        {
          title: '检票间隔时间 (秒)',
          dataIndex: 'intervalTime',
        },
        {
          title: '检票通行方式',
          dataIndex: 'adoptType',
          render: (dom: any) => ['一检一人', '一检多人'][dom],
        },
      ],
    },
    {
      title: '其他信息',
      columns: [
        {
          title: '分时预约检票设置',
          dataIndex: 'timeShareBook',
          render: (dom: any) =>
            [
              '检票时间不可提前不可延后',
              '检票时间可提前但不可延后',
              '检票时间不可提前但可延后',
              '检票时间可提前可延后',
            ][dom],
        },
      ],
    },
  ];
  const init = async (e: string) => {
    setLoading(true);
    setDetailsVisible(true);
    const { data } = await TicketCheckXq({ id: e });
    data.adoptType *= 1;
    data.controlType *= 1;
    data.timeShareBook *= 1;
    data.proType += '';
    setDataSource(data);
    setLoading(false);
  };

  useEffect(() => {
    init(id);
  }, [id]);

  return (
    <DetailsPop
      title="检票规则详情"
      visible={detailsVisible}
      isLoading={isLoading}
      setVisible={setDetailsVisible}
      columnsInitial={columnsInitial}
      dataSource={dataSource}
      zIndex={9999}
    />
  );
};

CheckRuleDetails.show = (id: string) => {
  const detailBox = document.createElement('div');
  document.body.appendChild(detailBox);
  const root = createRoot(detailBox);
  root.render(<CheckRuleDetails id={id} />);
};

export default CheckRuleDetails;
