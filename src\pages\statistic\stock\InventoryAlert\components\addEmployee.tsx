import { getUserPageList } from '@/services/api/cas';
import { history, useModel } from '@umijs/max';
import { Button, Form, Input, message, Modal, Pagination, Table } from 'antd';
import { useEffect, useState } from 'react';
const { Item } = Form;

const EmployeeModal = ({ visible, existingReceivers = [], onCancel, onConfirm }) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [companyId, setCompanyId] = useState<string>('');
  const [dataSource, setDataSource] = useState<
    {
      userId: string; // 新增唯一标识字段
      nickname: string;
      phone: string;
    }[]
  >([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1); // 当前页码
  const [pageSize, setPageSize] = useState(6); // 每页数量
  const [form] = Form.useForm(); // 管理表单状态
  const { initialState } = useModel('@@initialState');

  // 初始化公司ID
  const loadInitialData = () => {
    if (!initialState?.currentCompany?.coId) return;
    setCompanyId(initialState.currentCompany.coId);
  };

  useEffect(() => {
    if (!visible) {
      setSelectedRowKeys([]);
      form.resetFields();
    } else {
      loadInitialData();
    }
  }, [visible]);

  // 监听公司ID变化，触发数据查询
  useEffect(() => {
    if (companyId) handUserList();
  }, [companyId]);

  // 查询用户列表（支持分页和筛选）
  const handUserList = async (params: Record<string, any> = {}) => {
    const hide = message.loading('加载中~');
    try {
      // 获取表单输入值（昵称、手机号）
      const formValues = form.getFieldsValue();
      const res: any = await getUserPageList({
        companyId,
        current: params.current || current, // 优先使用参数中的current，否则用状态
        pageSize: params.pageSize || pageSize, // 优先使用参数中的pageSize，否则用状态
        status: 1,
        ...formValues, // 传递昵称、手机号筛选条件
        ...params, // 其他额外参数（如分页触发时的current）
      });
      hide();
      setDataSource(res.data?.data || []); // 假设接口返回的列表字段是list
      setTotal(res.data?.total || 0); // 假设接口返回的总数是total
      return res;
    } catch (e) {
      console.log(e);
      hide();
      return {};
    }
  };

  // 表格列定义（添加复选框列）
  const columns = [
    { title: '昵称', dataIndex: 'nickname', key: 'nickname' },
    { title: '手机号', dataIndex: 'phone', key: 'phone' },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        if (status === 1) return '启用';
        if (status === 2) return '禁用';
        return `未知状态(${status})`; // 处理其他可能的状态值
      },
    },
  ];

  return (
    <Modal
      title="选择员工"
      visible={visible}
      onCancel={() => {
        setSelectedRowKeys([]);
        onCancel();
      }}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="ok"
          type="primary"
          onClick={() => {
            onConfirm(
              selectedRowKeys,
              dataSource.filter((item) => selectedRowKeys.includes(item.userId)),
            ); // 传递选中数据
            onCancel(); // 关闭模态框
          }}
        >
          确定
        </Button>,
      ]}
    >
      <Form form={form} style={{ margin: '20px 0 0', display: 'flex' }} autoComplete="off">
        <Item name="nickname" label="昵称">
          <Input placeholder="请输入员工昵称" style={{ width: '220px' }} />
        </Item>
        <Item name="phone" label="手机号" style={{ marginLeft: '20px' }}>
          <Input placeholder="请输入员工手机号" style={{ width: '220px' }} />
        </Item>
        <Item style={{ marginLeft: 20 }}>
          <Button
            type="primary"
            onClick={async () => {
              setCurrent(1); // 查询时重置为第1页
              await handUserList({ current: 1 }); // 传递current=1
            }}
          >
            查询
          </Button>
          <Button
            style={{ marginLeft: 8 }}
            onClick={async () => {
              form.resetFields(); // 清空表单
              setCurrent(1); // 刷新时重置为第1页
              await handUserList({ current: 1 }); // 传递current=1
            }}
          >
            刷新
          </Button>
        </Item>
      </Form>
      <Button
        style={{ marginBottom: '12px' }}
        onClick={async () => {
          history.push('/user/manage');
        }}
      >
        用户管理
      </Button>

      <Table
        rowKey={(record) => record.userId}
        columns={columns}
        dataSource={dataSource}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys: string[]) => setSelectedRowKeys(keys),
          getCheckboxProps: (record: any) => ({
            disabled: existingReceivers.includes(record.userId) || record.status == 2,
          }),
        }}
        pagination={false}
      />

      <div style={{ textAlign: 'center', margin: '12px 0' }}>
        已选择 {selectedRowKeys.length} 个员工
      </div>

      <Pagination
        current={current}
        total={total}
        pageSize={pageSize}
        showSizeChanger={false}
        showQuickJumper
        style={{ textAlign: 'center' }}
        onChange={(page) => {
          setCurrent(page); // 更新当前页状态
          handUserList({ current: page }); // 分页时传递current参数
        }}
      />
    </Modal>
  );
};

export default EmployeeModal;
