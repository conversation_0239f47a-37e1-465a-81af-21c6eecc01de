/**
 * 权限配置
 *
 * 命名规范：can[Code]_[action]
 * action = select 时为菜单权限，请省略 _[action]
 * action = open/close 等含有特殊字符时，请省略 / 并采用驼峰命名
 *
 * 备注规范：Ⅰ一级菜单 Ⅱ二级菜单 Ⅲ三级菜单
 * insert：新建
 * delete：删除
 * edit：修改
 * select： 查看
 * open/close：启用/禁用
 * 其它权限需单独注释
 */
export default function access(initialState: {
  permissionList?: [
    {
      group?: string;
      code: string;
      action: string;
    },
  ];
}) {
  const { permissionList } = initialState || {};
  function has(code: string, action: string) {
    let boolean = false;
    for (const item of permissionList || []) {
      if (code == item.code && action == item.action) {
        boolean = true;
        break;
      }
    }
    return boolean;
  }
  return {
    // Ⅰ导航栏
    // Ⅱ帮助中心
    canHelp: has('Help', 'select'),

    // Ⅰ首页
    canHomePage: has('HomePage', 'select'),

    // Ⅰ系统设置
    // Ⅱ风控管理
    canRiskManagement: has('RiskManagement', 'select'),
    canRiskManagement_accountant: has('RiskManagement', 'accountant'), // 复核员信息
    canRiskManagement_disable: has('RiskManagement', 'disable'), // 禁用/启用
    // Ⅱ操作日志
    canOperationLog: has('OperationLog', 'select'),

    // Ⅰ用户中心
    // Ⅱ用户管理
    canOrganizationManage: has('OrganizationManage', 'select'),
    canOrganizationManage_insert: has('OrganizationManage', 'insert'),
    canOrganizationManage_insertDepartment: has('OrganizationManage', 'insertDepartment'), // 新建部门
    canOrganizationManage_revisePassword: has('OrganizationManage', 'revisePassword'), // 重置密码
    canOrganizationManage_deleteDepartment: has('OrganizationManage', 'deleteDepartment'), // 删除部门
    canOrganizationManage_edit: has('OrganizationManage', 'edit'),
    canOrganizationManage_openClose: has('OrganizationManage', 'open/close'),
    canOrganizationManage_invite: has('OrganizationManage', 'invite'), // 邀请
    canOrganizationManage_authorityCopy: has('OrganizationManage', 'authorityCopy'), // 权限拷贝
    // Ⅱ权限管理
    canRoleManage: has('RoleManage', 'select'),
    canRoleManage_insert: has('RoleManage', 'insert'),
    canRoleManage_delete: has('RoleManage', 'delete'),
    canRoleManage_edit: has('RoleManage', 'edit'),
    canRoleManage_openClose: has('RoleManage', 'open/close'),
    // Ⅱ用户审批
    canUserApprove: has('UserApprove', 'select'),
    canUserApprove_refuseAgree: has('UserApprove', 'refuse/agree'), // 审批

    // Ⅰ企业管理
    canEnterpriceManage: has('EnterpriceManage', 'select'),
    canEnterpriceManage_insert: has('EnterpriceManage', 'insert'),
    canEnterpriceManage_edit: has('EnterpriceManage', 'edit'),
    canEnterpriceManage_certification: has('EnterpriceManage', 'certification'), // 认证
    canEnterpriceManage_invite: has('EnterpriceManage', 'invite'), // 邀请

    // Ⅰ合作伙伴管理
    // Ⅱ上游供应商
    // Ⅲ供应商管理
    canSupplierManagement: has('SupplierManagement', 'select'),
    canSupplierManagement_agentSelect: has('SupplierManagement', 'agentselect'), // 查看我的代理供应商
    canSupplierManagement_agentSelectAppID: has('SupplierManagement', 'agentSelectAppID'), // 查看凭证
    canSupplierManagement_agentDeleteSupplier: has('SupplierManagement', 'agentDeleteSupplier'), // 移除代理供应商
    canSupplierManagement_dealerSelect: has('SupplierManagement', 'dealerselect'), // 查看我的经销供应商
    canSupplierManagement_dealerDeleteSuppliers: has('SupplierManagement', 'dealerDeleteSuppliers'), // 移除经销供应商
    // Ⅲ采购订单
    canPurchaseOrder: has('PurchaseOrder', 'select'),
    canPurchaseOrder_insert: has('PurchaseOrder', 'purchase'), // 进货
    canPurchaseOrder_delete: has('PurchaseOrder', 'refund'), // 退货
    // Ⅲ采购退单
    canPurchaseRefundOrder: has('PurchaseRefundOrder', 'select'),
    // Ⅱ下游分销商
    // Ⅲ分销商分组
    canSupplierGroup: has('SupplierGroup', 'select'),
    canSupplierGroup_agentGroupSelect: has('SupplierGroup', 'agentgroupselect'), // 查看代理商分组
    canSupplierGroup_agentGroupInsert: has('SupplierGroup', 'agentgroupinsert'), // 新建代理商分组
    canSupplierGroup_agentGroupGpenClose: has('SupplierGroup', 'agentgroupopen/close'), // 启用禁用代理商分组
    canSupplierGroup_agentGroupDelete: has('SupplierGroup', 'agentgroupdelete'), // 删除代理商分组
    canSupplierGroup_agentGroupEdit: has('SupplierGroup', 'agentgroupedit'), // 修改代理商分组
    canSupplierGroup_deleteAgent: has('SupplierGroup', 'DeleteAgent'), // 移除代理商
    canSupplierGroup_insertAgent: has('SupplierGroup', 'InsertAgent'), // 添加代理商
    canSupplierGroup_dealerGroupSelect: has('SupplierGroup', 'dealergroupselect'), // 查看经销商分组
    canSupplierGroup_dealerGroupInsert: has('SupplierGroup', 'dealergroupinsert'), // 新建经销商分组
    canSupplierGroup_dealerGroupDelete: has('SupplierGroup', 'dealergroupdelete'), // 删除经销商分组
    canSupplierGroup_dealerGroupEdit: has('SupplierGroup', 'dealergroupedit'), // 修改经销商分组
    canSupplierGroup_dealerGroupOpenClose: has('SupplierGroup', 'dealergroupopen/close'), // 启用禁用经销商分组
    canSupplierGroup_insertDealer: has('SupplierGroup', 'insertdealer'), // 添加经销商
    canSupplierGroup_deleteDealer: has('SupplierGroup', 'deletedealer'), // 移除经销商
    canSupplierGroup_insertProduct: has('SupplierGroup', 'insertProduct'), // 配置产品
    // Ⅲ分销商管理
    canAgentGroupManagement: has('AgentGroupManagement', 'select'),
    canAgentGroupManagement_agentSelect: has('AgentGroupManagement', 'agentselect'), // 查看代理商
    canAgentGroupManagement_insertAppID: has('AgentGroupManagement', 'InsertAppID'), // 生成授权码
    canAgentGroupManagement_setCommission: has('AgentGroupManagement', 'setcommission'), // 配置佣金结算模式
    canAgentGroupManagement_dealerSelect: has('AgentGroupManagement', 'dealerselect'), // 查看经销商
    canAgentGroupManagement_invite: has('AgentGroupManagement', 'invite'), // 邀请
    // Ⅲ我发放的佣金账单
    canComissionBill: has('ComissionBill', 'select'),
    canComissionBill_detail: has('ComissionBill', 'detail'), // 查看结算明细
    // Ⅲ销售订单
    canSalesOrder: has('SalesOrder', 'select'),
    // Ⅲ销售退单
    canSalesRefundOrder: has('SalesRefundOrder', 'select'),
    // Ⅲ价格策略
    canPriceStrategy: has('PriceStrategy', 'select'),
    canPriceStrategy_setPrice: has('PriceStrategy', 'setprice'), // 设置价格策略
    // Ⅱ合作邀请审核
    canCheckManagement: has('CheckManagement', 'select'),
    canCheckManagement_detail: has('CheckManagement', 'detail'), // 查看详情
    canCheckManagement_passReject: has('CheckManagement', 'pass/reject'), // 通过/驳回

    // ⅠC端业务管理
    // Ⅱ店铺管理
    canMyShop: has('MyShop', 'select'),
    canMyShop_insertShop: has('MyShop', 'InsertShop'), // 创建店铺
    canMyShop_ediitShop: has('MyShop', 'EdiitShop'), // 修改店铺
    canMyShop_shopManage: has('MyShop', 'ShopManage'), // 管理店铺
    canMyShop_previewShop: has('MyShop', 'PreviewShop'), // 预览店铺
    canMyShop_decorateShop: has('MyShop', 'decorateshop'), // 装修店铺
    canMyShop_dailyReport: has('MyShop', 'DailyReport'), // 窗口交易报表
    canMyShop_editAuthority: has('MyShop', 'editauthority'), // 编辑员工销售权限
    canMyShop_selectAuthority: has('MyShop', 'selectauthority'), // 查看员工可售店铺
    canMyShop_windowsRefund: has('MyShop', 'WindowsRefund'), // 退票 TODO
    canMyShop_orderSelect: has('MyShop', 'orderselect'), // 查看店铺订单
    canMyShop_commentSelect: has('MyShop', 'commentselect'), // 查看店铺订单
    canMyShop_refundSelect: has('MyShop', 'refundselect'), // 查看店铺退单
    canMyShop_commissionSelect: has('MyShop', 'commissionselect'), // 查看订单佣金
    // ⅡC端订单管理
    // Ⅲ订单管理
    canOrderManage: has('OrderManage', 'select'),
    canOrderManage_delete: has('OrderManage', 'delete'), // 批量删除
    // Ⅲ退单管理
    canRefundOrderManage: has('RefundOrderManage', 'select'),

    // Ⅰ财务管理
    // Ⅱ授信管理
    // Ⅲ授信账号
    canCreditCustomerManage: has('CreditCustomerManage', 'select'),
    canCreditCustomerManage_insert: has('CreditCustomerManage', 'insert'), // 新增客户
    canCreditCustomerManage_credit: has('CreditCustomerManage', 'credit'), // 授信
    canCreditCustomerManage_edit: has('CreditCustomerManage', 'edit'), // 修改
    canCreditCustomerManage_detail: has('CreditCustomerManage', 'detail'), // 收支明细 TODO
    // Ⅲ操作记录
    canCreditRecord: has('CreditRecord', 'select'),
    // Ⅲ授信结算单
    canCreditBill: has('CreditBill', 'select'),
    canCreditBill_detail: has('CreditBill', 'detail'), // 查看结算明细
    canCreditBill_confirm: has('CreditBill', 'confirm'), // 确认
    // Ⅲ授信给我的账户
    canCreditAccount: has('CreditAccount', 'select'),
    canCreditAccount_detail: has('CreditAccount', 'detail'), // 查看收支明细
    // Ⅲ我的还款记录
    canReturnRecord: has('ReturnRecord', 'select'),
    canReturnRecord_detail: has('ReturnRecord', 'detail'), // 查看结算明细
    // Ⅱ佣金管理
    // Ⅲ我获得的佣金账单
    canBalanceAssignment: has('BalanceAssignment', 'select'),
    canBalanceAssignment_detail: has('BalanceAssignment', 'detail'), // 查看结算明细

    // Ⅰ数据统计
    // Ⅱ我的库存查询
    // Ⅲ票务库存
    canDistributeProcduct: has('DistributeProcduct', 'select'),
    // Ⅲ订单汇总/明细
    canOrderSummaryDetails: has('OrderSummaryDetails', 'select'),
    canFinancialStatement: has('FinancialAccount', 'select'),

    // Ⅱ 财务管理 - 直销管理
    canFinancialAccount: has('FinancialAccount', 'select'),
    // Ⅲ 财务管理 - 直销管理 - 现金账单
    canBillAccount: has('BillAccount', 'select'),
    canBillAccount_insert: has('BillAccount', 'insert'), // 生成现金账单
    canBillAccount_confirm: has('BillAccount', 'confirm'), // 确认账单
  };
}
