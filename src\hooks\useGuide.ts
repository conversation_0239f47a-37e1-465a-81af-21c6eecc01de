import { GuideStepStatus } from '@/common/utils/enum';
import { getGuideInfo, saveGuideInfo } from '@/common/utils/storage';
import { getUniqueId } from '@/common/utils/tool';
import { getGuideListReq, updateGuideListReq } from '@/services/api/erp';
import { useModel } from '@umijs/max';

export const useGuide = () => {
  const { setInitialState } = useModel('@@initialState');
  let guideInfo = {
    stepList: [],
    guidanceContent: '',
  };

  const dealGuideInfo = (list) => {
    const guideSteps = getGuideInfo();
    guideInfo = {
      ...guideInfo,
      ...guideSteps,
      stepList: list || [],
    };

    saveGuideInfo(guideInfo);
    setInitialState((s) => ({ ...s, guideUpdateFlag: getUniqueId() }));
    return guideInfo;
  };

  const fetchGuideInfo = async () => {
    try {
      let stepList = [];
      const { data: guideRes0 } = await getGuideListReq({ tabIndex: 0 });
      const { data: guideRes1 } = await getGuideListReq({ tabIndex: 1 });
      stepList = stepList.concat(guideRes0, guideRes1);
      return dealGuideInfo(stepList);
    } catch (error) {
      return false;
    }
  };

  const updateGuideInfo = async (params) => {
    // 如果guideInfo是空数组或者guidanceContent的isUsed为true就直接跳过调用
    const guideSteps = getGuideInfo();
    const _isFinish = (guideSteps?.stepList || [])?.findIndex(
      (e) => e?.guidanceContent == params?.status && e?.isUsed,
    );
    // 完成企业认证可能认证的是其他企业，这里不做更新优化
    const isSkipFinish = [GuideStepStatus.step0_1, '官网商品管理'].includes(params.status);
    if (!isSkipFinish && (!guideSteps?.stepList?.length || _isFinish !== -1)) {
      return null;
    }
    try {
      await updateGuideListReq(params);
      await fetchGuideInfo();
    } catch (error) {
      return false;
    }
  };

  return { fetchGuideInfo, updateGuideInfo };
};
