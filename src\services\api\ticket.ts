// @ts-ignore
/* eslint-disable */
// import { request } from './api';
import { message } from 'antd';
import { request } from '@umijs/max';
import { scenicHost } from '.';
import { API, ResponseData } from './typings';

type ResponseListData<T, S extends Record<string, any> = {}> = {
  code: number;
  data: { current: number; pageSize: number; data: T; total: number } & S;
  msg: string;
};

/**
 * lin start
 */

/* 【票务模块】 */
// 产品列表
export async function apiSimpleTicket(params: any) {
  const { data } = await request(`${scenicHost}/simpleTicket/pageList`, {
    method: 'GET',
    params,
  });
  return data;
}
// 产品新增/修改
export function apiSimpleTicketAdd(params: any) {
  return request(`${scenicHost}/simpleTicket/info`, {
    method: 'POST',
    data: params,
  });
}
// 产品详情
export async function apiSimpleTicketInfo(id: any) {
  const { data } = await request(`${scenicHost}/simpleTicket/info/${id}`);
  return data;
}
// 产品删除
export function apiSimpleTicketDel(id: string) {
  return request(`${scenicHost}/simpleTicket/info/${id}`, {
    method: 'DELETE',
  });
}
// 产品禁/启用
export function apiSimpleTicketStatus(params: any) {
  return request(`${scenicHost}/simpleTicket/status`, {
    method: 'POST',
    data: params,
  });
}

/* 【员工销售权限】 */
// 企业下拉列表
export async function apiGetCoInfo(id: any) {
  const { data } = await request(`${scenicHost}/scenic/getCoInfo/${id}`);
  return data;
}
// 员工列表
export async function getTicketUserPermissionList(params: any) {
  const { data } = await request(`${scenicHost}/ticketUser/ticketUserPermissionPageList`, {
    method: 'GET',
    params,
  });
  return data;
}
// 所以权限（票，商品）
export async function apiTicketPermissionList(params: any) {
  const { data } = await request(`${scenicHost}/ticketUser/ticketPermissionList`, {
    method: 'GET',
    params,
  });
  return data;
}
// 设置权限
export function apiRevokeAndAuthorize(params: any) {
  return request(`${scenicHost}/permission/revokeAndAuthorize`, {
    method: 'POST',
    data: params,
  });
}

/**
 * lin end
 */

/** 新建规则 PUT /api/rule */
export async function updateRule(options?: { [key: string]: any }) {
  return request<API.RuleListItem>(`${scenicHost}/rule`, {
    method: 'PUT',
    ...(options || {}),
  });
}

// 检票点
/** 检票点分页列表查询 GET */
export async function getCheckPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.pageNum = params.current;
  const { code, data } = await request(`${scenicHost}/checkInPoint/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  // 处理启用状态的样式
  data.data.map((e: any) => {
    e.isEnable = {
      color: e.isEnable == '1' ? 'blue' : 'red',
      text: e.isEnable == '1' ? '启用' : '禁用',
    };
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 检票点根据 id 删除 */
export async function delCheckaddr(id: string) {
  return request(`${scenicHost}/checkInPoint/del/${id}`, {
    method: 'DELETE',
  });
}

/** 检票点新增修改 POST */
export async function addCheckaddr(params: any, options?: { [key: string]: any }) {
  return await request<API.RuleList>(`${scenicHost}/checkInPoint/info`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 根据 id 查询检票点详情 GET */
export async function getIddetails(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/checkInPoint/info/${options}`, {
    method: 'GET',
  });

  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  // data.isEnable += '';
  // console.log(data);

  return data;
}
/** 修改【检票点】状态 */
export function setCheckInPointStatus(params: any) {
  return request(`${scenicHost}/checkInPoint/status`, {
    method: 'POST',
    data: params,
  });
}

// 检票设备设备
/** 查询检票设备分页列表查询 GET */
export async function getCheckEquipmentPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.pageNum = params.current;
  const { code, data } = await request(`${scenicHost}/checkEquipment/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  // 处理启用状态的样式
  data.data.map((e: any) => {
    e.isEnable = {
      color: e.isEnable == '1' ? 'blue' : 'red',
      text: e.isEnable == '1' ? '启用' : '禁用',
    };
  });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 检票设备删除根据 id 删除 */
export async function delCheckEquipment(options?: { [key: string]: any }) {
  return request<API.RuleListItem>(`${scenicHost}/checkEquipment/del/${options}`, {
    method: 'DELETE',
  });
}

/** 检票设备新增修改 POST */
export async function AddCheckEquipment(params: any, options?: { [key: string]: any }) {
  return await request<API.RuleList>(`${scenicHost}/checkEquipment/info`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 根据 id 查询检票设备详情 GET */
export async function getCheckdetails(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/checkEquipment/info/${options}`, {
    method: 'GET',
  });

  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  // data.isEnable += '';
  // console.log(data);

  return data;
}

/** 根据景区 id 查询下拉列表检票点 GET */
export async function getcheckInPoint(options: { id: any }) {
  const { code, data }: any = await request<API.RuleListItem>(
    `${scenicHost}/checkInPoint/simpleList/${options.id}`,
    {
      method: 'GET',
    },
  );
  if (code == 20000) {
    return data.map((item: any) => {
      return {
        value: item.id,
        label: item.checkName,
      };
    });
  } else {
    return [];
  }
}
/** 修改【检票设备】状态 */
export function setCheckEquipmentStatus(params: any) {
  return request(`${scenicHost}/checkEquipment/status`, {
    method: 'POST',
    data: params,
  });
}

// 售票窗口模块
/** 查询售票窗口分页列表查询 GET */
export async function getTicketPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.pageNum = params.current;
  const { code, data } = await request(`${scenicHost}/ticketOffice/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  // 处理启用状态的样式
  data.data.map((e: any) => {
    e.isEnable = {
      color: e.isEnable == '1' ? 'blue' : 'red',
      text: e.isEnable == '1' ? '启用' : '禁用',
    };
  });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 售票窗口删除根据 id 删除 */
export async function delTicket(id: string) {
  return request(`${scenicHost}/ticketOffice/del/${id}`, {
    method: 'DELETE',
  });
}

/** 售票窗口新增修改 POST */
export async function AddTicketOffice(params: any, options?: { [key: string]: any }) {
  return await request<API.RuleList>(`${scenicHost}/ticketOffice/info`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 根据 id 查询售票窗口详情 GET */
export async function getTicketOffice(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/ticketOffice/info/${options}`, {
    method: 'GET',
  });

  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  // data.isEnable += '';
  // console.log(data);

  return data;
}

// /** 根据景区 id 查询 [检票点] 下拉列表 GET */
export async function getTicketOfficeList(options: { scenicId: any }) {
  const { code, data } = await request(
    `${scenicHost}/ticketOffice/simpleList/${options.scenicId}`,
    {
      method: 'GET',
    },
  );
  return code == 20000
    ? data.map((item: { id: any; name: any }) => {
        return {
          value: item.id,
          label: item.name,
        };
      })
    : [];
}

// 售票设备模块
/** 查询售票设备模块分页列表查询 GET */
export async function getTicketEquipmentList(params: {
  /** 当前的页码 */
  pageNum?: number;
  /** 页面的容量 */
  pageSize?: number;
  /* 景区 ID */
  scenicId?: string;
}) {
  const { code, data } = await request(`${scenicHost}/ticketEquipment/pageList`, {
    method: 'GET',
    params,
  });
  // 处理启用状态的样式
  data.data.map((e: any) => {
    e.isEnable = {
      color: e.isEnable == '1' ? 'blue' : 'red',
      text: e.isEnable == '1' ? '启用' : '禁用',
    };
  });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 售票设备删除根据 id 删除 */
export async function delTicketEquipment(id: any) {
  return request(`${scenicHost}/ticketEquipment/del/${id}`, {
    method: 'DELETE',
  });
}

/** 售票设备新增修改 POST */
export async function AddTicketEquipment(params: any, options?: { [key: string]: any }) {
  return await request(`${scenicHost}/ticketEquipment/info`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 根据 id 查询售票设备详情 GET */
export async function getTicketEquipments(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/ticketEquipment/info/${options}`, {
    method: 'GET',
  });

  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  data.beginTime = [data.beginTime, data.endTime];
  // data.isEnable += '';

  return data;
}

/** 修改【售票点】状态 */
export function setTicketOfficeStatus(params: any) {
  return request(`${scenicHost}/ticketOffice/status`, {
    method: 'POST',
    data: params,
  });
}
/** 修改【售票设备】状态 */
export function setTicketEquipmentStatus(params: any) {
  return request(`${scenicHost}/ticketEquipment/status`, {
    method: 'POST',
    data: params,
  });
}
/** 查看【售票设备销售权限】列表 */
export function getPermissionRetrieve(params: any) {
  return request(`${scenicHost}/permission/retrieve`, {
    method: 'GET',
    params,
  });
}
// ======== 票务模块 =========
// === 商品模块 ===
/** 查看【通过景区获取所有票权限名称】列表 */
export function getJurisdictionList(params: any) {
  return request(`${scenicHost}/simpleGoods/jurisdictionList/${params}`, {
    method: 'GET',
  });
}
/** 根据 id 删除 */
export function delSimpleGoods(id: string) {
  return request(`${scenicHost}/simpleGoods/del/${id}`, {
    method: 'DELETE',
  });
}
/** 商品详情 */
export function getSimpleGoods(id: string) {
  return request(`${scenicHost}/simpleGoods/info/${id}`, {
    method: 'GET',
  });
}

/** 商品新增 */
export async function AddSimpleGoods(params: any) {
  return await request(`${scenicHost}/simpleGoods/save`, {
    method: 'POST',
    data: params,
  });
}
/** 根据产品 id，统计对应商品数量 */
export async function AddSimpleGoodStaNum(params: any) {
  return await request(`${scenicHost}/simpleGoods/statisticalNum`, {
    method: 'GET',
    params,
  });
}

/** 修改状态 */
export async function AddSimpleGoodsStatus(params: any) {
  return await request(`${scenicHost}/simpleGoods/status`, {
    method: 'POST',
    data: params,
  });
}
/** 商品修改 */
export async function AddSimpleGoodsUpdate(params: any) {
  return await request(`${scenicHost}/simpleGoods/update`, {
    method: 'POST',
    data: params,
  });
}
// === 检票规则 ===

/** 修改和保存 */
export async function addTicketCheck(params: any) {
  return await request(`${scenicHost}/ticketCheck/info`, {
    method: 'POST',
    data: params,
  });
}
/** 详情 */
export async function TicketCheckXq(params: any) {
  return await request(`${scenicHost}/ticketCheck/info`, {
    method: 'GET',
    params,
  });
}
/** 根据 id 删除 */
export function delTicketCheck(id: string) {
  return request(`${scenicHost}/ticketCheck/info/${id}`, {
    method: 'DELETE',
  });
}
/** 简单列表 */
export function getTicketCheckList(params: any) {
  return request(`${scenicHost}/ticketCheck/list`, {
    method: 'GET',
    params,
  });
}
/** 首页列表 */
// export function getTicketCheckPageList(params: any) {
//   return request(`${scenicHost}/ticketCheck/pageList`, {
//     method: 'GET',
//     params,
//   });
// }

export async function getTicketCheckPageList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.current = params.current;
  const { code, data } = await request(`${scenicHost}/ticketCheck/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  console.log({
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total, //TODO:后端接口报错时页面会报错
  });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 禁用/启用 */
export async function getTicketCheckStatus(params: any) {
  return await request(`${scenicHost}/ticketCheck/status`, {
    method: 'POST',
    data: params,
  });
}

// === 设备权限 ===

/** 一键赋值保存结果 */
export async function Saveconfirm(params: any) {
  return await request(`${scenicHost}/relationGoodsEquipment/confirm`, {
    method: 'POST',
    data: params,
  });
}
/** 设备权限页面，表格数据 */
export async function AuthorityPageList(params: any) {
  return await request(`${scenicHost}/relationGoodsEquipment/equipmentIdAuthorityPageList`, {
    method: 'GET',
    params,
  });
}
/** 根据设备 id 获取销售权限 */
export function EquipmentList(params: any) {
  return request(`${scenicHost}/relationGoodsEquipment/equipmentList`, {
    method: 'GET',
    params,
  });
}
/** 设备新增修改 */
export async function AddEquipment(params: any) {
  return await request(`${scenicHost}/relationGoodsEquipment/equipmentSaveAndUpdate`, {
    method: 'POST',
    data: params,
  });
}
/** 设备权限列表表格 */
export async function getRoleList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.current = params.current;
  const { code, data } = await request(`${scenicHost}/relationGoodsEquipment/roleList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 商品列表页跳转根据商品 id 查询对应的所有设备 */
export function getgRPList(params: any) {
  return request(`${scenicHost}/relationGoodsEquipment/gRPList`, {
    method: 'GET',
    params,
  });
}
/** 商品列表页分配销售设备 保存操作 */
export async function saveAndUpdate(params: any) {
  return await request(`${scenicHost}/relationGoodsEquipment/saveAndUpdate`, {
    method: 'POST',
    data: params,
  });
}

// === 退票服务 ===
/** 详情 */
export function ticketRetreatXq(params: any) {
  return request(`${scenicHost}/ticketRetreat/info`, {
    method: 'GET',
    params,
  });
}
/** 规则新增 */
export async function AddTicketRetreat(params: any) {
  return await request(`${scenicHost}/ticketRetreat/info`, {
    method: 'POST',
    data: params,
  });
}
/** 简单列表 参数：景区 id */
export function ticketRetreatList(params: any) {
  return request(`${scenicHost}/ticketRetreat/list`, {
    method: 'GET',
    params,
  });
}
/** 首页列表 */
export async function ticketRetreatPageList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.current = params.current;
  const { code, data } = await request(`${scenicHost}/ticketRetreat/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 根据 id 删除 */
export function delTicketRetreat(id: string) {
  return request(`${scenicHost}/ticketRetreat/info/${id}`, {
    method: 'DELETE',
  });
}
/** 禁用/启用 */
export async function getTicketRetreatStatus(params: any) {
  return await request(`${scenicHost}/ticketRetreat/status`, {
    method: 'POST',
    data: params,
  });
}

// 票务打印模块
/** 分页列表 */
export async function PrintPageList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.current = params.current;
  const { code, data } = await request(`${scenicHost}/relationGoodsPrint/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 新增修改 */
export async function getRelationGoodsPrint(params: any) {
  return await request(`${scenicHost}/relationGoodsPrint/info`, {
    method: 'POST',
    data: params,
  });
}

// 单种票 (产品) 模块
/** 分页列表 */
export async function simpleTicketPageList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.current = params.current;
  const { code, data } = await request(`${scenicHost}/simpleTicket/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 根据 id 删除票务 */
export function delSimpleTicket(id: string) {
  return request(`${scenicHost}/simpleTicket/info/${id}`, {
    method: 'DELETE',
  });
}
/** 禁用/启用 */
export async function getSimpleTicketStatus(params: any) {
  return await request(`${scenicHost}/simpleTicket/status`, {
    method: 'POST',
    data: params,
  });
}
/** 下拉票务详情 */
export function getTicketDetail(id: string) {
  return request(`${scenicHost}/simpleTicket/info/${id}`, {
    method: 'GET',
  });
}
/** 通过商品 id 获取所有分时预约下拉列表 */
export function getSimpleLawsList(id: string) {
  return request(`${scenicHost}/simpleTicket/simpleLaws/${id}`, {
    method: 'GET',
  });
}
/** 通过景区 id 获取所有产品下拉列表 */
export async function getSimpleList(options: any) {
  console.log(options);

  const { code, data }: any = await request<API.RuleListItem>(
    `${scenicHost}/simpleTicket/simpleList/${options.id}`,
    {
      method: 'GET',
    },
  );
  if (code == 20000) {
    return data.map((item: any, index: any) => {
      return {
        value: index,
        label: item.name,
        type: (item.type *= 1),
      };
    });
  } else {
    return [];
  }
}
// export function getSimpleList(id: string) {
//   return request(`${scenicHost}/simpleTicket/simpleList/${id}`, {
//     method: 'GET',
//   });
//   if (code == 20000) {
//     return data.map((item) => {
//       return {
//         value: item.id,
//         label: item.checkName,
//       };
//     });
//   } else {
//     return [];
//   }
// }

/** 通过分时预约 id 获取对象数据 */
export function getSimpletimeInfo(id: string) {
  return request(`${scenicHost}/simpleTicket/timeShareInfo/${id}`, {
    method: 'GET',
  });
}

/** 新增修改票务 */
export async function getSimpleTicketInfo(params: any) {
  return await request(`${scenicHost}/simpleTicket/info`, {
    method: 'POST',
    data: params,
  });
}

// 出票规则模块
/** 出票规则新增 */
export async function AddTicketLssue(params: any) {
  return await request(`${scenicHost}/ticketLssue/info`, {
    method: 'POST',
    data: params,
  });
}
/** 禁用/启用 */
export async function getTicketLssueStatus(params: any) {
  return await request(`${scenicHost}/ticketLssue/status`, {
    method: 'POST',
    data: params,
  });
}
/** 根据 id 删除 */
export function delTicketLssue(id: string) {
  return request(`${scenicHost}/ticketLssue/info/${id}`, {
    method: 'DELETE',
  });
}
/** 简单列表 参数：景区 id */
export function ticketLssueList(params: any) {
  return request(`${scenicHost}/ticketLssue/list`, {
    method: 'GET',
    params,
  });
}
/** 简单列表 参数：景区 id */
export function ticketLssueXq(params: any) {
  return request(`${scenicHost}/ticketIssue/info`, {
    method: 'GET',
    params,
  });
}
/** 首页列表 */
export async function TicketLssuePageList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.current = params.current;
  const { code, data } = await request(`${scenicHost}/ticketLssue/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}

// 销售模块
/** 新增 */
export async function AddManageStock(params: any) {
  return await request(`${scenicHost}/manageStock/info`, {
    method: 'POST',
    data: params,
  });
}
/** 修改 */
export function UpManageStock(params: any) {
  return request(`${scenicHost}/manageStock/info`, {
    method: 'PUT',
    data: params,
  });
}
/** 详情 */
export function ManageStockXq(id: string) {
  return request(`${scenicHost}/manageStock/info/${id}`, {
    method: 'GET',
  });
}
/** 删除 */
export function delManageStock(dto: string) {
  return request(`${scenicHost}/manageStock/info/${dto}`, {
    method: 'DELETE',
  });
}
/** 禁用/启用 */
export async function getManageStockStatus(params: any) {
  return await request(`${scenicHost}/manageStock/status`, {
    method: 'PUT',
    data: params,
  });
}
/** 列表 */
export async function ManageStockList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.current = params.current;
  const { code, data } = await request(`${scenicHost}/manageStock/manageStockList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 调用区块链查询剩余库存明细 */
export function surplusStockNumber(params: any) {
  return request(`${scenicHost}/manageStock/surplusStockNumber`, {
    method: 'DELETE',
    params,
  });
}

/** 审批详情 */
export async function apiApproveConfInfo(params: any) {
  return await request(`${scenicHost}/ticketIssue/approveConfInfo/${params.approveId}`, {
    method: 'get',
  });
}

/** 权益列表 */
export async function downListApi() {
  try {
    const { data } = await request(`${scenicHost}/rightsService/rights/downList`);
    return data.map((item: any) => ({ value: item.rightsId, label: item.rightsName }));
  } catch (error) {}
}

/** 权益卡商品列表 */
export async function getTravelCardGoodsGoodsList(
  params: API.TravelCardGoodsListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.TravelCardGoodsListItem[]>>(
    `${scenicHost}/travelGoods/pageList`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/** 查询权益卡商品详情 */
export async function getTravelCardGoodsInfo(
  params: Pick<API.TravelCardGoodsListItem, 'id'>,
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.TravelCardGoodsListItem>>(
    `${scenicHost}/travelGoods/info/${params.id}`,
    {
      method: 'GET',
      ...options,
    },
  );
}

/**
 * @description: 查询采购产品列表
 */
export function getOrderProDetails(params: { orderId: string }) {
  return request<ResponseListData<API.OrderProDetailItem[]>>(
    `${scenicHost}/order/orderPurchaseProductPageList`,
    {
      params,
    },
  );
}

/**
 * @description: 查询采购商品列表
 */
export function getOrderGoodsDetails(params: { orderId: string }) {
  return request<
    ResponseListData<
      API.OrderGoodsDetailItem[],
      {
        sumDifferencesPrice: string;
      }
    >
  >(`${scenicHost}/order/orderPurchaseGoodsPageList`, {
    params,
  });
}

export function getChainInfo(params: { ticketNumber: string }) {
  return request<
    ResponseData<{
      id: string;
      txId: string;
    }>
  >(`${scenicHost}/ticketIssue/selectTxIdByTicketNumber`, {
    params,
    method: 'GET',
  });
}
