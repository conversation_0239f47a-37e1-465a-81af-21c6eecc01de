import { ticketTypeEnum } from '@/common/utils/enum';
import { getDistributorSellStatistic } from '@/services/api/performanceStatistics';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { But<PERSON>, Card } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import PerformanceGraph from './components/PerformanceGraph';
const currentCompanyId = localStorage.getItem('currentCompanyId');
const Dealer: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [resetFlag, setResetFlag] = useState(0); // 新增重置标志
  const [graphData, setGraphData] = useState<any>({
    id: 'g1',
    name: '总绩效',
    label: 0,
    rate: 1,
    status: 'B',
    variableValue: undefined, // 不展示该值
    children: [],
  });

  const columns: ProColumns<any>[] = [
    {
      title: '排名',
      dataIndex: '_index', // 内部索引字段
      valueType: 'index',
      width: 60,
      align: 'center',
      fixed: 'left', // 可选：固定在左侧
    },
    {
      title: '时间',
      dataIndex: 'date',
      valueType: 'dateRange',
      initialValue: [
        dayjs().clone().subtract(30, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      hideInTable: true,
      fieldProps: {
        presets: [
          // 使用函数返回动态时间范围
          {
            label: '昨天',
            value: () => [dayjs().subtract(1, 'days').startOf('day'), dayjs().endOf('day')],
          },
          {
            label: '近7天',
            value: () => [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
          },
          {
            label: '近30天',
            value: () => [dayjs().subtract(30, 'days').startOf('day'), dayjs().endOf('day')],
          },
        ],
      },
      search: {
        transform: (value) => {
          return {
            startDate: value[0],
            endDate: value[1],
          };
        },
      },
    },
    {
      title: '产品',
      dataIndex: 'productName',
      hideInTable: true,
    },
    {
      title: '商品',
      dataIndex: 'goodsName',
      hideInTable: true,
    },
    {
      title: '票种',
      dataIndex: 'ticketType',
      hideInTable: true,
      valueEnum: ticketTypeEnum,
    },
    {
      title: '经销商名称',
      dataIndex: 'distributorName',
      hideInSearch: true,
    },
    {
      title: '所属分组',
      dataIndex: 'distributorGroupName',
      hideInSearch: true,
    },
    {
      title: '回款采购⾦额(元)',
      dataIndex: 'returnPurchaseAmount',
      hideInSearch: true,
      tooltip: '统计时间内，经销商C端门票销售支付成功且未发生退票的门票对应采购总金额',
    },
    {
      title: '采购金额',
      dataIndex: 'purchaseAmount',
      hideInSearch: true,
    },
    {
      title: '实际销售人数',
      dataIndex: 'actualPeople',
      hideInSearch: true,
    },
    {
      title: '实际售票⾦额(元)',
      dataIndex: 'actualSellAmount',
      hideInSearch: true,
      tooltip: '统计时间内，经销商C端门票销售支付成功且未发生退票的门票售票总金额',
    },
    {
      title: '实际售票数',
      dataIndex: 'actualSellNum',
      hideInSearch: true,
      tooltip: '统计时间内，经销商C端门票销售支付成功且未发生退票的门票售票总数量',
    },
    {
      title: '采购票数',
      dataIndex: 'purchaseNum',
      hideInSearch: true,
    },
    {
      title: '售票单数',
      dataIndex: 'sellOrderNum',
      hideInSearch: true,
      tooltip: '统计时间内，经销商C端门票销售支付成功的订单总数',
    },
    {
      title: '退票率',
      dataIndex: 'refundRate',
      hideInSearch: true,
      tooltip: '统计时间内，经销商C端门票销售支付成功的退单总数占订单总数的百分比',
    },
  ];
  function transformData(data) {
    // 计算总绩效数据
    const totalReturnPurchase = data.reduce((sum, item) => sum + item.returnPurchaseAmount, 0);
    const totalPurchaseNum = data.reduce((sum, item) => sum + item.purchaseNum, 0);

    // 按分销商分组进行分组
    const groupMap = data.reduce((acc, item) => {
      const groupName = item.distributorGroupName;
      if (!acc[groupName]) {
        acc[groupName] = {
          returnSum: 0,
          purchaseSum: 0,
          purchaseNumSum: 0,
          items: [],
        };
      }
      acc[groupName].returnSum += item.returnPurchaseAmount;
      acc[groupName].purchaseSum += item.purchaseAmount;
      acc[groupName].purchaseNumSum += item.purchaseNum;
      acc[groupName].items.push(item);
      return acc;
    }, {});

    // 生成二级节点
    const children = Object.entries(groupMap).map(([groupName, groupData], index) => {
      // 计算分组级指标
      const groupVariableValue =
        groupData.purchaseSum === 0 ? 0 : groupData.returnSum / groupData.purchaseSum;

      // 生成三级节点
      const groupChildren = groupData.items.map((item, childIndex) => ({
        id: `g1-${index + 1}-${childIndex + 1}`,
        name: item.distributorName,
        count: item.purchaseNum,
        label: item.returnPurchaseAmount.toFixed(2),
        rate: item.purchaseAmount === 0 ? 0 : item.returnPurchaseAmount / item.purchaseAmount, // 示例数据中的固定值
        status: item.returnPurchaseAmount / item.purchaseAmount >= 0.2 ? 'B' : 'R', // 示例数据中的固定值
        currency: 'Yuan',
        variableName: 'V1', // 根据示例结构设置
        variableValue:
          item.purchaseAmount === 0 ? 0 : item.returnPurchaseAmount / item.purchaseAmount,
        // variableUp: false, // 示例中的固定值
        children: [],
      }));

      return {
        id: `g1-${index + 1}`,
        name: groupName,
        count: groupData.purchaseNumSum,
        label: groupData.returnSum.toFixed(2),
        rate: groupVariableValue,
        status: groupVariableValue >= 0.2 ? 'B' : 'R', // 示例中的模式
        currency: 'Yuan',
        variableName: 'V2', // 根据示例结构设置
        variableValue: groupVariableValue,
        // variableUp: groupVariableValue > 0.5, // 简单判断逻辑
        children: groupChildren,
      };
    });

    // 返回最终结构
    return {
      id: 'g1',
      name: '总绩效',
      count: totalPurchaseNum,
      label: totalReturnPurchase.toFixed(2),
      currency: 'Yuan',
      rate: 1,
      status: 'B',
      variableName: 'V1',
      variableValue: undefined, // 不展示该值
      // variableUp: false,
      children: children,
    };
  }
  return (
    <>
      <style>{`
         .ant-pro-query-filter.ant-pro-query-filter {
          margin-left:-40px;
        }
      `}</style>
      <ProTable
        style={{ width: '100%' }}
        actionRef={actionRef}
        rowKey={(record: any) => record.distributorId}
        columns={columns}
        headerTitle={'绩效排行'}
        tableRender={(_, defaultDom) => (
          <div>
            {/* 插入自定义卡片 */}
            <Card
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <div>
                    <span style={{ fontSize: '16px' }}>绩效统计</span>
                    <span style={{ marginLeft: '8px', fontSize: '14px', color: '#ccc' }}>
                      （回款采购金额/元）
                    </span>
                  </div>
                  <Button onClick={() => setResetFlag((v) => v + 1)}>回到默认视图</Button>
                </div>
              }
              style={{ margin: '16px 0', borderRadius: 8 }}
              headStyle={{ border: 'none' }}
            >
              <PerformanceGraph data={graphData} resetFlag={resetFlag} />
            </Card>

            {/* 默认表格渲染 */}
            {defaultDom}
          </div>
        )}
        request={async (params) => {
          const param = {
            startDate: params.startDate,
            endDate: params.endDate,
            productName: params.productName,
            goodsName: params.goodsName,
            ticketType: params.ticketType,
            supplierId: currentCompanyId,
          };
          try {
            const res = await getDistributorSellStatistic(param);
            const transformedData = transformData(res.data);
            setGraphData(transformedData);
            return {
              data: res?.data || [],
              total: res.data.length,
              success: true,
            };
          } catch (error) {
            return {
              data: [],
              total: 0,
            };
          }
        }}
      />
    </>
  );
};

export default Dealer;
