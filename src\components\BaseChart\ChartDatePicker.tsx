/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-08 15:40:14
 * @LastEditTime: 2023-09-11 10:09:02
 * @LastEditors: zhangfengfei
 */

import { DatePicker, Space, Tabs } from 'antd';
import type { Moment } from 'dayjs';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';

const { RangePicker } = DatePicker;

type DateType = 'date' | 'month';
type PickerType = 'rangePicker' | 'datePicker';

const options = [
  {
    label: '按日',
    key: 'date',
  },
  {
    label: '按月',
    key: 'month',
  },
];
export interface ChartDatePickerProps {
  sort?: DateType;
  defaultValue?: Moment | [Moment, Moment] | null;
  picker: PickerType;
  showMenu?: boolean;
  onValuesChange: (sort: DateType, value: Moment | [Moment, Moment] | null) => void;
  disabledDate?: (currentDate: Moment) => boolean;
}
const ChartDatePicker: FC<ChartDatePickerProps> = ({
  sort = 'date',
  defaultValue = null,
  picker = 'datePicker',
  showMenu = true,
  onValuesChange,
}) => {
  const [visible, setVisible] = useState(false);

  const ref = useRef(defaultValue);
  const [key, setKey] = useState<DateType>('date');
  const [dateValue, setDateValue] = useState<Moment | [Moment, Moment] | null>(null);

  // 动态禁选用
  const [currentDate, setCurrentDate] = useState<[Moment | null, Moment | null]>([null, null]);

  // 动态禁用七天 半年
  const banMap = {
    date: {
      num: 6,
      unit: 'days',
    },
    month: {
      num: 5,
      unit: 'months',
    },
  };
  const disabledRangeDate = (current: Moment) => {
    if (!currentDate) {
      return false;
    }

    const tooLate =
      currentDate[0] && current.diff(currentDate[0], banMap[key].unit) > banMap[key].num;
    const tooEarly =
      currentDate[1] && currentDate[1].diff(current, banMap[key].unit) > banMap[key].num;
    return !!tooEarly || !!tooLate;
  };

  const disabledDate = (current: Moment) => {
    // 禁选当天之后
    return current && current > dayjs().endOf('day');
  };

  useEffect(() => {
    setKey(sort);
  }, [sort]);

  useEffect(() => {
    if (defaultValue) {
      setDateValue(defaultValue);
    }
  }, [defaultValue]);

  useEffect(() => {
    if (visible) {
      setDateValue(null);
    } else {
      setDateValue(ref.current);
    }
    return () => {
      setCurrentDate([null, null]);
    };
  }, [visible]);

  return (
    <Space>
      {showMenu && (
        <Tabs
          size="small"
          tabBarGutter={20}
          activeKey={key}
          items={options}
          onChange={(key) => {
            setKey(key);
            setDateValue(null);
          }}
        />
      )}
      {picker === 'datePicker' ? (
        <DatePicker
          value={dateValue}
          picker={key}
          disabledDate={disabledDate}
          onChange={(value) => {
            setDateValue(value);
            onValuesChange(key, value);
          }}
        />
      ) : (
        <RangePicker
          picker={key}
          onOpenChange={(v) => {
            setVisible(v);
          }}
          value={dateValue}
          disabledDate={disabledRangeDate}
          onChange={(value) => {
            setDateValue(value);
            ref.current = value;
            onValuesChange(key, value);
          }}
          onCalendarChange={(values) => setCurrentDate(values)}
        />
      )}
    </Space>
  );
};

export default ChartDatePicker;
