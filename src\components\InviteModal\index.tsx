/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-15 17:11:06
 * @LastEditTime: 2025-05-28 14:58:42
 * @LastEditors: 李悍宇 zhou<PERSON>@hqyatu.com
 */
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getInviteUrl } from '@/services/api/companyJoin';
import { Button, message, Modal, Space } from 'antd';
import copy from 'copy-to-clipboard';
import { stringify } from 'querystring';
import type { FC } from 'react';
import { useEffect } from 'react';
import { useModel, useRequest } from '@umijs/max';

interface InviteModalProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  invitetype?: string;
}

const InviteModal: FC<InviteModalProps> = ({ visible, setVisible, invitetype }) => {
  const { initialState } = useModel('@@initialState');
  const { coId = '', coName = '' } = initialState?.currentCompany || {};
  const { userId } = initialState.userInfo;
  const { data, run, loading } = useRequest(getInviteUrl, {
    manual: true,
    onSuccess: (data, params) => {
      if (params[0].type === 2) {
        addOperationLogRequest({
          action: 'edit',
          content: `重置【${coName}】邀请链接`,
        });
        message.success('重置成功');
      }
    },
  });
  const searchParams = stringify({
    code: data?.code,
    coId,
    coName,
    type: invitetype,
    userId,
    route: encodeURIComponent('/companyJoin/apply'),
  });
  const url = `${window.location.href.split('#')[0]}#/companyJoin/apply?${searchParams}`;
  const onCopy = () => {
    copy(url);
    addOperationLogRequest({
      action: 'invite',
      content: `复制【${coName}】邀请链接`,
    });
    message.success('复制成功');
  };

  const onReset = () => {
    run({
      type: 2,
      distributorId: coId,
    });
  };

  useEffect(() => {
    if (visible)
      run({
        type: 1,
        distributorId: coId,
      });
  }, [visible]);

  return (
    <Modal
      open={visible}
      onCancel={() => setVisible(false)}
      onOk={onCopy}
      title="邀请链接"
      okText="复制"
    >
      <Space
        direction="vertical"
        style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
      >
        <h2>{coName}</h2>
        <a style={{ overflowWrap: 'anywhere', width: 400 }}>{url}</a>
        <span>
          该链接有效截止时间：{data?.validDate || '-'}
          <Button type="link" onClick={onReset}>
            重置
          </Button>
        </span>
        <span>分销商可以通过打开链接，申请成为下级分销商</span>
      </Space>
    </Modal>
  );
};

export default InviteModal;
