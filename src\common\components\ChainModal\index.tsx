import { ModelWidth } from '@/common/utils/enum';
import { getChainInfo } from '@/services/api/ticket';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-components';
import { Button, List, Tooltip, Typography } from 'antd';
import Paragraph from 'antd/lib/typography/Paragraph';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import { useRequest } from '@umijs/max';
import { getEnv } from '@/common/utils/getEnv';

const { Text } = Typography;

interface ChainModalProps {
  trigger?: JSX.Element;
  text?: string;
  ticketNumber?: string;
  chainData?: {
    txId?: string;
  };
}

const ChainModal: FC<ChainModalProps> = ({
  text = '区块链交易记录',
  trigger = <a>{text}</a>,
  ticketNumber,
  chainData,
}) => {
  const [visible, setVisible] = useState(false);

  const chainInfoReq = useRequest(getChainInfo, {
    manual: true,
  });

  const { CHAIN_URL } = getEnv();

  const dataSource = [
    // {
    //   id: '1',
    //   title: '智旅链浏览器',
    //   subTitle: CHAIN_URL,
    // },
    {
      id: '2',
      title: '交易哈希',
      subTitle: chainData?.txId || chainInfoReq.data?.txId || '-',
      extra: (
        <Button
          type="primary"
          onClick={() => {
            window.open(
              `${CHAIN_URL}/#/tx_list?tx=${chainData?.txId || chainInfoReq.data?.txId || ''}`,
            );
          }}
        >
          前往
        </Button>
      ),
    },
  ];

  useEffect(() => {
    if (visible && ticketNumber) {
      chainInfoReq.run({ ticketNumber });
    }
  }, [visible, ticketNumber]);

  return (
    <ModalForm
      width={ModelWidth.md}
      trigger={trigger}
      title={
        <>
          <Text>区块链交易记录</Text>
          <Tooltip
            color="#fff"
            placement="right"
            overlayInnerStyle={{ color: '#333' }}
            title="可通过票号和交易哈希在区块链浏览器中查询链上信息"
          >
            <QuestionCircleOutlined style={{ marginLeft: 8 }} />
          </Tooltip>
        </>
      }
      modalProps={{
        destroyOnClose: true,
        footer: false,
        closable: true,
      }}
      submitter={false}
      onOpenChange={setVisible}
      loading={chainInfoReq.loading}
    >
      <List
        dataSource={dataSource}
        size="small"
        renderItem={({ id, subTitle, title, extra }) => (
          <List.Item key={id} extra={extra}>
            <List.Item.Meta
              title={title}
              description={<Paragraph copyable={{ tooltips: false }}>{subTitle}</Paragraph>}
            />
          </List.Item>
        )}
      />
    </ModalForm>
  );
};

export default ChainModal;
