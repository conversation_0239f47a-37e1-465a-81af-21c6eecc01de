/*
 * @Author: AI Assistant
 * @Date: 2023-11-09
 * @LastEditTime: 2023-11-09
 * @LastEditors: AI Assistant
 */

import { tableConfig } from '@/common/utils/config';
import { modelWidth } from '@/common/utils/gConfig';
import type { ModalState } from '@/hooks/useModal';
import type { API } from '@/services/api/typings';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Modal, Space, DatePicker, message } from 'antd';
import type { FC } from 'react';
import React, { useRef, useState } from 'react';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

interface PriceSuggestionModalProps {
  goodsItem: API.PriceStrategyListItem;
  modalState: ModalState;
}

// 模拟的定价建议数据类型
interface PriceSuggestionItem {
  id: string;
  submitTime: string;
  currentPrice: number;
  suggestedPrice: number;
  priceChange: number;
  status: 'active' | 'expired' | 'updated';
}

const PriceSuggestionModal: FC<PriceSuggestionModalProps> = ({
  goodsItem,
  modalState: { visible, setVisible },
}) => {
  const actionRef = useRef<ActionType>();
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(1, 'month'),
    dayjs(),
  ]);

  // 模拟获取定价建议列表数据
  const getSuggestionList = async () => {
    // 模拟 API 请求延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    // 模拟数据
    const mockData: PriceSuggestionItem[] = [
      {
        id: '1',
        submitTime: '2022/08/18 15:37:19',
        currentPrice: 40,
        suggestedPrice: 50,
        priceChange: -5.29,
        status: 'active',
      },
      {
        id: '2',
        submitTime: '2022/08/18 15:37:19',
        currentPrice: 40,
        suggestedPrice: 50,
        priceChange: -5.29,
        status: 'expired',
      },
      {
        id: '3',
        submitTime: '2022/08/18 15:37:19',
        currentPrice: 40,
        suggestedPrice: 50,
        priceChange: -5.29,
        status: 'expired',
      },
      {
        id: '4',
        submitTime: '2022/08/18 15:37:19',
        currentPrice: 40,
        suggestedPrice: 50,
        priceChange: -5.29,
        status: 'updated',
      },
      {
        id: '5',
        submitTime: '2022/08/18 15:37:19',
        currentPrice: 40,
        suggestedPrice: 30,
        priceChange: 5.29,
        status: 'updated',
      },
      {
        id: '6',
        submitTime: '2022/08/18 15:37:19',
        currentPrice: 40,
        suggestedPrice: 30,
        priceChange: 5.29,
        status: 'expired',
      },
    ];

    return {
      data: mockData,
      total: mockData.length,
      success: true,
    };
  };

  // 处理刷新操作
  const handleRefresh = () => {
    actionRef.current?.reload();
    message.success('刷新成功');
  };

  // 处理更新操作
  const handleUpdate = (record: PriceSuggestionItem) => {
    message.success('更新成功');
    actionRef.current?.reload();
  };

  // 定义表格列
  const columns: ProColumns<PriceSuggestionItem>[] = [
    {
      title: '提交时间',
      dataIndex: 'submitTime',
      width: 180,
    },
    {
      title: '目前单买价格',
      dataIndex: 'currentPrice',
      renderText: (text) => `¥ ${text}`,
    },
    {
      title: '建议单买价格',
      dataIndex: 'suggestedPrice',
      renderText: (text) => `¥ ${text}`,
    },
    {
      title: '价格变化',
      dataIndex: 'priceChange',
      renderText: (text, record) => {
        const isIncrease = text > 0;
        return (
          <span style={{ color: isIncrease ? '#52c41a' : '#ff4d4f' }}>
            {isIncrease ? (
              <span>↑</span>
            ) : (
              <span>↓</span>
            )} {Math.abs(text)}%
          </span>
        );
      },
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      width: 80,
      render: (_, record) => {
        if (record.status === 'active') {
          return <a onClick={() => handleUpdate(record)}>更新</a>;
        } else if (record.status === 'expired') {
          return <span style={{ color: '#999' }}>已失效</span>;
        } else if (record.status === 'updated') {
          return <span style={{ color: '#999' }}>已更新</span>;
        }
        return null;
      },
    },
  ];

  return (
    <Modal
      width={modelWidth.lg}
      title="价格建议"
      open={visible}
      destroyOnClose
      onCancel={() => setVisible(false)}
      footer={[
        <Button key="cancel" onClick={() => setVisible(false)}>
          取消
        </Button>,
        <Button key="confirm" type="primary" onClick={() => setVisible(false)}>
          确定
        </Button>,
      ]}
    >
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <RangePicker
          value={dateRange}
          onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
          format="YYYY/MM/DD"
          allowClear={false}
        />
        <Space>
          <Button type="primary" onClick={() => actionRef.current?.reload()}>
            查询
          </Button>
          <Button onClick={handleRefresh}>刷新</Button>
        </Space>
      </div>

      <ProTable<PriceSuggestionItem>
        {...tableConfig}
        headerTitle={false}
        actionRef={actionRef}
        rowKey="id"
        search={false}
        options={false}
        pagination={{
          defaultPageSize: 6,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          pageSizeOptions: ['6', '10', '20', '50'],
        }}
        request={getSuggestionList}
        columns={columns}
      />
    </Modal>
  );
};

export default PriceSuggestionModal; 