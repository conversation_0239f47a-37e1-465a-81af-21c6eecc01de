import Delete from '@/common/components/Delete';
import Disabled from '@/common/components/Disabled';
import EditPop from '@/common/components/EditPop';
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { SaleGroupTabKeyContext } from '@/pages/partner/saleChannel/Group';
import {
  apiGroupAdd,
  apiGroupDel,
  apiGroupEdit,
  apiGroupList,
  getDealerApplicationList,
  updateDistributionGroupStatus,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { PlusOutlined } from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Space, message } from 'antd';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Access, useAccess, useModel, useRequest } from '@umijs/max';
import Distributor from './Distributor';
import ProductConfig from './ProductConfig';

const TableList = ({ modalState }: { modalState: any }) => {
  const { initialState } = useModel('@@initialState');
  const { coId, settlementId } = initialState?.currentCompany || {};
  const access = useAccess();

  const saleGroupTabKey = useContext(SaleGroupTabKeyContext);

  // 【收款场景】下拉列表
  const downListReq = useRequest(getDealerApplicationList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      return (res.data || []).map((item: any) => ({
        value: item.code,
        label: item.name,
      }));
    },
  });

  const collectionEnum = useMemo(() => {
    return downListReq.data?.reduce((prev, cur) => {
      prev[cur.value] = cur.label;
      return prev;
    }, {});
  }, [downListReq.data]);

  const logList = [
    {
      dataIndex: 'name',
      title: '分组名称',
    },
    {
      dataIndex: 'collectionCode',
      title: '收款场景',
      valueEnum: collectionEnum,
    },
  ];

  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      title: '分组名称',
      dataIndex: 'name',
    },
    {
      title: '分销商数量',
      dataIndex: 'distributorAmount',
      hideInSearch: true,
      render: (dom, entity) => (
        <a
          onClick={() => {
            setGroupId(entity.id);
            setGroupName(entity.name);
            setDistribVisible(true);
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '商品配置数量',
      dataIndex: 'distributorPriceAmount',
      hideInSearch: true,
    },

    {
      title: '收款场景',
      dataIndex: 'collectionCode',
      hideInSearch: true,
      valueType: 'select',
      fieldProps: {
        loading: downListReq.loading,
        options: downListReq.data,
      },
    },
    {
      title: '授权状态',
      dataIndex: 'status',
      hideInSearch: true,
      render: (_, entity: any) => (
        <Disabled
          access={access.canSupplierGroup_dealerGroupOpenClose}
          status={entity.status == 1}
          params={{ id: entity.id, status: entity.status == 1 ? 2 : 1 }}
          request={async (params: any) => {
            const data = await updateDistributionGroupStatus(params);
            console.log(1212112121);

            addOperationLogRequest({
              action: 'disable',
              module: saleGroupTabKey,
              content: `${entity.status == 1 ? '禁用' : '启用'}【${entity.name}】`,
            });

            return data;
          }}
          actionRef={actionRef}
        />
      ),
    },
    {
      width: 200,
      title: '操作',
      valueType: 'option',
      render: (_, entity) => (
        <Space size="large">
          <Access accessible={access.canSupplierGroup_dealerGroupEdit}>
            <a
              onClick={async () => {
                entity.isEnable = entity.status == 1 ? 1 : 0;

                setGroupId(entity.id);
                setDataSource(entity);
                // 编辑
                setEditVisible(true);
              }}
            >
              编辑
            </a>
          </Access>
          <Access key="configAccess" accessible={access.canSupplierGroup_insertProduct}>
            <a
              onClick={() => {
                setGroupId(entity.id);
                setGroupName(entity.name);
                setConfigVisible(true);
              }}
            >
              销售授权
            </a>
          </Access>
          <Delete
            access={access.canSupplierGroup_dealerGroupDelete}
            status={entity.status == 1}
            params={entity.id}
            request={async (params: any) => {
              const data = await apiGroupDel(params);
              addOperationLogRequest({
                action: 'del',
                module: saleGroupTabKey,
                content: `删除【${entity.name}】`,
              });
              return data;
            }}
            actionRef={actionRef}
          />
        </Space>
      ),
    },
  ];

  // 【表单】数据绑定
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const editColumns: ProFormColumnsType<any>[] = [
    {
      columns: [
        {
          title: '分组名称',
          dataIndex: 'name',
          formItemProps: {
            rules: [{ required: true, max: 30 }],
          },
        },
        {
          title: '收款场景',
          dataIndex: 'collectionCode',
          valueType: 'select',
          fieldProps: {
            loading: downListReq.loading,
            options: downListReq.data,
          },

          formItemProps: {
            rules: [{ required: true }],
          },
        },
      ],
    },
  ];

  const [groupId, setGroupId] = useState('');
  const [groupName, setGroupName] = useState('');

  // 【列表】数据绑定
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });

  // 【配置产品】数据绑定
  const [configVisible, setConfigVisible] = useState<boolean>(false);
  // 【分销商列表】数据绑定
  const [distribVisible, setDistribVisible] = useState<boolean>(false);

  useEffect(() => {
    downListReq.run({ merchantId: settlementId });
  }, []);

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        style={modalState.tableStyle}
        {...tableConfig}
        actionRef={actionRef}
        toolBarRender={() => [
          <Access key="Access" accessible={access.canSupplierGroup_dealerGroupInsert}>
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                // 初始化表单
                setDataSource({ id: '', isEnable: 0 });
                setEditVisible(true);
              }}
            >
              <PlusOutlined /> 新建
            </Button>
          </Access>,
        ]}
        params={{ distributorId: coId }}
        request={apiGroupList}
        columns={columns}
      />

      {/* 添加/修改分组 */}
      <EditPop
        title="经销商分组"
        visible={editVisible}
        setVisible={setEditVisible}
        columns={editColumns}
        dataSource={dataSource}
        // 新增/修改
        onFinish={async (val: any) => {
          if (dataSource.id) val.id = dataSource.id;
          const msgType = val.id ? '修改' : '添加';
          const hide = message.loading('正在' + msgType);
          val.distributorId = coId;
          try {
            if (dataSource.id) {
              await apiGroupEdit({ ...val });
              addOperationLogRequest({
                action: 'edit',
                module: saleGroupTabKey,
                changeConfig: {
                  list: logList,
                  beforeData: dataSource,
                  afterData: val,
                },
                content: `编辑【${val.name}】`,
              });
            } else {
              await apiGroupAdd({ ...val });
              addOperationLogRequest({
                action: 'add',
                module: saleGroupTabKey,
                content: `新增【${val.name}】`,
              });
            }
            message.success(msgType + '成功');
            // 关闭弹窗并刷新列表
            setEditVisible(false);
            actionRef?.current?.reload();
          } catch (error) {}
          hide();
        }}
      />

      {/* 分销商列表 */}
      <Distributor
        visible={distribVisible}
        setVisible={setDistribVisible}
        groupId={groupId}
        groupName={groupName}
        upActionRef={actionRef}
      />

      {/* 配置产品 */}
      <ProductConfig
        groupId={groupId}
        groupName={groupName}
        upActionRef={actionRef}
        modalState={modalState}
      />
    </>
  );
};

export default TableList;
