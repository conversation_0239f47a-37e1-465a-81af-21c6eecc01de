import DetailsPop from '@/common/components/DetailsPop';
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getUniqueId } from '@/common/utils/tool';
import InviteModal from '@/components/InviteModal';
import {
  apiAgentApplicationList,
  apiAgentCredentials,
  apiListAgents,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { UserAddOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Modal, Space } from 'antd';
import Paragraph from 'antd/lib/typography/Paragraph';
import { useContext, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import { SaleMangeTabKeyContext } from '../..';
import ConfigModal from './ConfigModal';

let isClick = true;
const TableList = () => {
  const access = useAccess();

  const [inviteVisible, setInviteVisible] = useState(false);

  const saleMangeTabKey = useContext(SaleMangeTabKeyContext);

  const { initialState } = useModel('@@initialState');
  const { coId, settlementId } = initialState?.currentCompany || {};

  const [tableListItem, setTableListItem] = useState<any>();
  const [configModalVisible, setConfigModalVisible] = useState(false);

  // 【表格】数据绑定
  const columnsTable: ProColumns[] = [
    {
      title: '代理商名称',
      dataIndex: 'coName',
    },
    {
      title: '分组名称',
      dataIndex: 'groupName',
      hideInSearch: true,
    },
    {
      title: '类型',
      dataIndex: 'agentType',
      hideInSearch: true,
      valueEnum: { 1: '公司', 2: '个人' },
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
    },
    {
      title: '手机号码',
      dataIndex: 'coPhone',
    },
    {
      title: '电子邮箱',
      dataIndex: 'coEmail',
    },
    {
      title: '收款场景',
      dataIndex: 'collectionCode',
      hideInSearch: true,
      valueType: 'select',
      params: { merchantId: settlementId },
      request: apiAgentApplicationList,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
      hideInSearch: true,
    },
    {
      width: 280,
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (dom: any, entity: any) => (
        <Space size="large">
          <a
            onClick={() => {
              setLoading(true);
              setDetailsVisible(true);
              setDataSource(entity);
              setLoading(false);
              addOperationLogRequest({
                action: 'info',
                module: saleMangeTabKey,
                content: `查看【${entity.coName}】详情`,
              });
            }}
          >
            查看
          </a>
          <Access accessible={access.canAgentGroupManagement_insertAppID}>
            <a
              onClick={async () => {
                try {
                  if (isClick) {
                    isClick = false;
                  } else {
                    return;
                  }
                  const { data } = await apiAgentCredentials({
                    upDistributorId: coId,
                    downDistributorId: entity.coId,
                    type: 2,
                  });

                  isClick = true;
                  Modal.info({
                    title: '以下是该代理商的授权码',
                    content: (
                      <Paragraph
                        copyable={{
                          text: `AppID: ${data.appId}; AppSecret: ${data.appSecret}`,
                          tooltips: ['复制', '已复制'],
                          onCopy: () => {
                            addOperationLogRequest({
                              action: 'copy',
                              module: saleMangeTabKey,
                              content: `复制【${entity.coName}】授权码`,
                            });
                          },
                        }}
                      >
                        <div>
                          <span
                            style={{ width: '70px', textAlign: 'end', display: 'inline-block' }}
                          >
                            AppID:{' '}
                          </span>
                          <code style={{ marginLeft: '8px' }}>{data.appId}</code>
                        </div>
                        <span>
                          <span
                            style={{ width: '70px', textAlign: 'end', display: 'inline-block' }}
                          >
                            AppSecret:{' '}
                          </span>
                          <code style={{ marginLeft: '8px' }}>{data.appSecret}</code>
                        </span>
                      </Paragraph>
                    ),
                    okText: '关闭',
                  });
                } catch (error) {}
              }}
            >
              生成授权码
            </a>
          </Access>
          <Access accessible={access.canAgentGroupManagement_setCommission}>
            <a
              onClick={() => {
                setTableListItem(entity);
                setConfigModalVisible(true);
              }}
            >
              配置佣金结算模式
            </a>
          </Access>
        </Space>
      ),
    },
  ];

  // 【详情】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });
  const columnsDetail = [
    {
      title: '基本信息',
      columns: [
        {
          title: '经销商名称',
          dataIndex: 'coName',
        },
        {
          title: '统一组织机构代码',
          dataIndex: 'coCode',
        },

        {
          title: '企业名称',
          dataIndex: 'coName',
        },
        {
          title: '企业地址',
          dataIndex: 'coAddressInfo',
          render: (_, { coAreaName, coCityName, coProvinceName, coAddressInfo }) => (
            <div>
              {coProvinceName}
              {coCityName}
              {coAreaName}
              {coAddressInfo}
            </div>
          ),
        },
      ],
    },
    {
      title: '联系信息',
      columns: [
        {
          title: '联系人',
          dataIndex: 'contactName',
        },
        {
          title: '手机号码',
          dataIndex: 'coPhone',
        },
        {
          title: '电子邮箱',
          dataIndex: 'coEmail',
        },
        {
          title: '用户账号',
          dataIndex: 'account',
        },
        {
          title: '用户密码',
          dataIndex: 'password',
        },
      ],
    },
  ];
  const columnsDetail2 = [
    {
      title: '基本信息',
      columns: [
        {
          title: '代理商名称',
          dataIndex: 'coName',
        },
      ],
    },
    {
      title: '联系信息',
      columns: [
        {
          title: '联系人',
          dataIndex: 'contactName',
        },
        {
          title: '手机号码',
          dataIndex: 'coPhone',
        },
        {
          title: '电子邮箱',
          dataIndex: 'coEmail',
        },
        {
          title: '身份证号',
          dataIndex: 'account',
        },
      ],
    },
  ];

  return (
    <>
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        params={{ distributorId: coId }}
        request={apiListAgents}
        columns={columnsTable}
        toolBarRender={() => [
          <Access key={getUniqueId()} accessible={access.canAgentGroupManagement_invite}>
            <Button
              key="invite"
              type="primary"
              onClick={() => {
                setInviteVisible(true);
              }}
            >
              <UserAddOutlined /> 邀请
            </Button>
          </Access>,
        ]}
      />

      {/* 详情 */}
      <DetailsPop
        title="代理商详情"
        visible={detailsVisible}
        isLoading={isLoading}
        setVisible={setDetailsVisible}
        columnsInitial={dataSource.agentType == 2 ? columnsDetail2 : columnsDetail}
        dataSource={dataSource}
      />
      <ConfigModal
        visible={configModalVisible}
        setVisible={setConfigModalVisible}
        currentItem={tableListItem}
      />

      {/* 邀请 */}
      <InviteModal visible={inviteVisible} setVisible={setInviteVisible} invitetype={'1'} />
    </>
  );
};

export default TableList;
