import { tableConfig } from '@/common/utils/config';
import { modelWidth } from '@/common/utils/gConfig';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import React, { useRef, useState } from 'react';
import styles from '../index.less';

interface vessel {
  type: string; //列表类型
  visible: boolean;
  width?: number;
  total: number;
  maskClosable?: boolean;
  setVisible: (bool: boolean) => void;
  request: Promise<API.RuleList>;
  columns: ProColumns<ActionType>[];
  amount?: Record<string, any>[];
  head?: Record<string, any>;
  headInfo?: Record<string, any>;
  setAmount?: (amount: any[]) => void;
}

const Vessel: React.FC<vessel> = ({
  width = modelWidth.md,
  maskClosable = true,
  head = {
    name: '',
    value: '',
  },
  ...props
}: vessel) => {
  const actionRef = useRef<ActionType>();

  const [params, setParams] = useState<Record<string, any>>({ size: 10, e: 1 });

  const handleOnCancel = () => {
    props.setVisible(false);
    props.setAmount([]);
  };

  return (
    <Modal
      title={`${props.type}`}
      visible={props.visible}
      width={width}
      // maskClosable={false}
      destroyOnClose={true}
      footer={null}
      onCancel={handleOnCancel}
    >
      <div>
        {head.name != '' ? (
          <div style={{ padding: '24px' }}>{`${head.name}：${head.value}`}</div>
        ) : (
          ''
        )}
        {props.headInfo ? (
          <div style={{ padding: '24px' }}>
            {props.headInfo.map((n: any, i: number) => {
              return (
                <div key={i} className={styles.head}>
                  {n.label}
                  <span>{n.value || '-'}</span>
                </div>
              );
            })}
          </div>
        ) : (
          ''
        )}
        <ProTable<ActionType, Record<string, any>>
          {...tableConfig}
          actionRef={actionRef}
          pagination={{
            defaultPageSize: 10,
            total: props.total,
          }}
          rowKey="key"
          params={params}
          request={props.request}
          columns={props.columns}
        />
        {props.amount && props.amount.length ? (
          <div style={{ padding: '24px', textAlign: 'right' }}>
            <span style={{ fontWeight: '600', marginRight: '10px' }}>合计：</span>
            {props.amount.map((n: any, i: number) => {
              return (
                <div key={i} className={styles.head}>
                  {n.label}
                  <span>{n.value || '-'}</span>
                </div>
              );
            })}
          </div>
        ) : (
          ''
        )}
      </div>
    </Modal>
  );
};

export default Vessel;
