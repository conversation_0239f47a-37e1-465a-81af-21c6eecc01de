/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-23 10:20:19
 * @LastEditTime: 2023-09-15 18:16:58
 * @LastEditors: zhangfengfei
 */

import { tableConfig } from '@/common/utils/config';
import useModal from '@/hooks/useModal';
import { getConsumerAccountList } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { Space } from 'antd';
import type { FC } from 'react';
import { useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import BillDetailModal from './BillDetailModal';

type CreditAccountProps = Record<string, never>;

/**
 * @description 财务管理->账户管理->授信账户
 * */
const CreditAccount: FC<CreditAccountProps> = () => {
  const { initialState } = useModel('@@initialState');
  const { settlementId = '' } = initialState?.currentCompany || {};

  const actionRef = useRef<ActionType>();

  const detailModalState = useModal();

  const [total, setTotal] = useState(0);
  const [tableListItem, setTableListItem] = useState<API.CreditAccountItem>();

  const tableListReq = async (params: API.CreditDefaultParams) => {
    const { data } = await getConsumerAccountList(params);
    setTotal(data.otherAttribute?.balance || 0);
    return {
      data: data.page,
      total: data.totalNumberOfResults,
    };
  };
  const access = useAccess();
  const columns: ProColumns<API.CreditAccountItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      hideInTable: true,
    },
    {
      title: '指定消费商家',
      dataIndex: 'legalName',
      renderText: (_, record) => record.provider?.legalName || '-',
    },
    {
      title: '信用余额（元）',
      dataIndex: 'balance',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      renderText: (_, record) => (
        <Space
          onClick={() => {
            setTableListItem(record);
          }}
        >
          <Access accessible={access.canCreditAccount_detail}>
            <a
              onClick={() => {
                detailModalState.setVisible(true);
              }}
            >
              收支明细
            </a>
          </Access>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.CreditAccountItem, API.CreditDefaultParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="id"
        headerTitle={`信用余额共计（元）：${total.toFixed(2)}`}
        columns={columns}
        params={{
          merchantId: settlementId,
        }}
        request={tableListReq}
        pagination={{
          defaultPageSize: 10,
        }}
        search={false}
        options={false}
      />
      {/* 收支明细 modal */}
      <BillDetailModal currentItem={tableListItem} modalState={detailModalState} />
    </>
  );
};

export default CreditAccount;
