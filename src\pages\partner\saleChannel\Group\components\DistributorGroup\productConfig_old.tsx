/*
 * 配置产品弹窗
 * */
import Delete from '@/common/components/Delete';
import { tableConfig } from '@/common/utils/config';
import { GuideStepStatus, productTypeEnum, ticketTypeEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { useGuide } from '@/hooks/useGuide';
import {
  getProductConfigList,
  getUnifyPullDownStock,
  saveProductConfig,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { LeftOutlined } from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-components';
import { ProCard } from '@ant-design/pro-components';
import type { EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import ProTable, { EditableProTable } from '@ant-design/pro-table';
import { Button, Flex, InputNumber, Modal, Space, Tag, Typography, message } from 'antd';
import dayjs from 'dayjs';
import type { Key } from 'react';
import { useContext, useRef, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';
import { SaleGroupTabKeyContext } from '../..';
import GoodsTable from './GoodsTable';

const { Text } = Typography;

const ProductConfig_old = ({
  groupId,
  upActionRef,
  groupName,
  modalState,
}: {
  groupId: string;
  upActionRef: any;
  groupName: string;
  modalState: any;
}) => {
  const { initialState } = useModel('@@initialState');
  const { coId = '' } = initialState?.currentCompany || {};
  const { updateGuideInfo } = useGuide();

  const actionRef = useRef<ActionType>();
  const formRef = useRef<EditableFormInstance<any>>();
  // 添加时的选择
  const sourceListRef = useRef<any[]>([]);
  // 数据绑定
  const [dataSource, setDataSource] = useState<API.ProductConfigListItem[]>([]);
  // 扩展项的 KEYS
  const [expandedRowKeys, setExpandedRowKeys] = useState<Key[]>([]);

  // 选择
  const [copyVisible, setCopyVisible] = useState(false);

  //景区名称下拉框数据
  const getSenicListReq = useRequest(getUnifyPullDownStock, {
    defaultParams: [{ getUnifyPullDown: coId, type: '1' }],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: key,
          label: res.data[key],
        };
      });
    },
  });
  //供应商名称下拉框数据
  const getSupplierListReq = useRequest(getUnifyPullDownStock, {
    defaultParams: [{ getUnifyPullDown: coId, type: '2' }],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: key,
          label: res.data[key],
        };
      });
    },
  });
  //产品名称下拉框数据
  const getProductListReq = useRequest(getUnifyPullDownStock, {
    defaultParams: [{ getUnifyPullDown: coId, type: '3' }],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: res.data[key],
          label: res.data[key],
        };
      });
    },
  });

  // 表格配置
  const tableColumns: ProColumns<API.ProductConfigListItem>[] = [
    {
      title: 'productId',
      dataIndex: 'productId',
      search: false,
      hideInTable: true,
    },
    {
      title: '库存批次号',
      width: 180,
      editable: false,
      dataIndex: 'batchId',
      fixed: 'left',
      search: true,
      render: (dom, record: any) => (
        <Space>
          <span>
            {dom}
            {record.isExchange == 1 && (
              <>
                {' '}
                <Tag color="blue">交易所</Tag>
              </>
            )}
          </span>
        </Space>
      ),
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSupplierListReq.data,
        showSearch: true,
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      search: false,
      editable: false,
      renderText: (_, record) =>
        (record.supplierInfoList ?? []).map((item) => item.supplierName).join(','),
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      editable: false,
      search: false,
    },
    {
      title: '景区名称',
      dataIndex: 'scenicId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSenicListReq.data,
        showSearch: true,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getProductListReq.data,
        showSearch: true,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'name',
      search: false,
      editable: false,
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      valueEnum: productTypeEnum,
      editable: false,
    },
    {
      title: '商品名称',
      dataIndex: 'ticketGoodsName',
      editable: false,
      search: true,
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      valueEnum: ticketTypeEnum,
      editable: false,
      search: true,
    },
    {
      title: '购买有效时间',
      hideInTable: true,
      dataIndex: 'time1',
      valueType: 'dateRange',
      editable: false,
      search: {
        transform: (value) => {
          return {
            purchaseBeginTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            purchaseEndTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '购买有效时间',
      hideInSearch: true,
      editable: false,
      render: (dom: any, entity: any) => {
        return `${dayjs(entity.purchaseBeginTime).format('YYYY-MM-DD')} 至 ${dayjs(
          entity.purchaseEndTime,
        ).format('YYYY-MM-DD')}`;
      },
    },
    {
      title: '入园有效时间',
      hideInSearch: true,
      editable: false,
      render: (dom: any, entity: any) => {
        return `${dayjs(entity.dayBegin).format('YYYY-MM-DD')} 至 ${dayjs(entity.dayEnd).format(
          'YYYY-MM-DD',
        )}`;
      },
    },
    {
      title: '入园有效时间',
      hideInTable: true,
      valueType: 'dateRange',
      dataIndex: 'time2',
      editable: false,
      search: {
        transform: (value) => {
          return {
            dayBegin: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            dayEnd: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '可用库存',
      dataIndex: 'totalAmount',
      hideInSearch: true,
      editable: false,
    },
    {
      title: '市场标准价（元）',
      dataIndex: 'marketPrice',
      fixed: 'right',
      hideInSearch: true,
      editable: false,
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '采购价（元）',
      dataIndex: 'purchasePrice',
      fixed: 'right',
      hideInSearch: true,
      editable: false,
      renderText: (dom: string[]) => {
        const items = dom.map((price: any) => {
          const p = parseFloat(price) ? parseFloat(price).toFixed(2) : price;
          return <div>{p}</div>;
        });
        return <div>{items}</div>;
      },
    },
    {
      title: '销售价（元）',
      dataIndex: 'salePrice',
      fixed: 'right',
      hideInSearch: true,
      formItemProps: { rules: [{ required: true }] },
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      renderFormItem: (_, { value }) => <InputNumber min={0} step={1} defaultValue={value} />,
    },
    {
      width: 120,
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (dom, record: any, index, action) => (
        <Space size="large">
          {record.isExchange == 0 ? (
            <a
              key="editable"
              onClick={() => {
                setDataSource((source) => {
                  const newData = [...source];
                  newData[index].editable = true; //把可编辑打开
                  return newData;
                });
                action?.startEditable?.(record.batchId, record);
                formRef?.current?.setRowData?.(record.batchId, record);
                setExpandedRowKeys((source) => {
                  return [...source, record.batchId];
                });
              }}
            >
              编辑
            </a>
          ) : (
            <a> </a>
          )}
          <Delete
            key="k3"
            access={true}
            status={false}
            params={{}}
            request={async () => {
              const subHasExchange = record.configGoodsDetail.some(
                (item: any) => item.isExchange === 1,
              );
              if ((record.isExchange === 1 || subHasExchange) && record.totalAmount != 0) {
                message.warning('此产品包含交易所数字资产，无法删除');
                return Promise.reject();
              }
              return actionDeleteProductConfig(record);
            }}
            actionRef={actionRef}
          />
        </Space>
      ),
    },
  ];

  // 添加库存批次配置
  // 表格配置
  const selectTableColumns: ProColumns<API.ProductConfigListItem>[] = [
    {
      title: 'productId',
      dataIndex: 'productId',
      search: false,
      hideInTable: true,
    },
    {
      title: '库存批次号',
      width: 180,
      editable: false,
      dataIndex: 'batchId',
      fixed: 'left',
      search: true,
      render: (dom, record: any) => (
        <Space>
          <span>
            {dom}
            {record.isExchange == 1 && (
              <>
                {' '}
                <Tag color="blue">交易所</Tag>
              </>
            )}
          </span>
        </Space>
      ),
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSupplierListReq.data,
        showSearch: true,
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      search: false,
      editable: false,
      renderText: (_, record) =>
        (record.supplierInfoList ?? []).map((item) => item.supplierName).join(','),
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      editable: false,
      search: false,
    },
    {
      title: '景区名称',
      dataIndex: 'scenicId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSenicListReq.data,
        showSearch: true,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getProductListReq.data,
        showSearch: true,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'name',
      search: false,
      editable: false,
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      valueEnum: productTypeEnum,
      editable: false,
    },
    {
      title: '商品名称',
      dataIndex: 'ticketGoodsName',
      editable: false,
      search: true,
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      valueEnum: ticketTypeEnum,
      editable: false,
      search: true,
    },
    {
      title: '购买有效时间',
      hideInTable: true,
      dataIndex: 'time1',
      valueType: 'dateRange',
      editable: false,
      search: {
        transform: (value) => {
          return {
            purchaseBeginTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            purchaseEndTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '购买有效时间',
      hideInSearch: true,
      editable: false,
      render: (dom: any, entity: any) => {
        return `${dayjs(entity.purchaseBeginTime).format('YYYY-MM-DD')} 至 ${dayjs(
          entity.purchaseEndTime,
        ).format('YYYY-MM-DD')}`;
      },
    },
    {
      title: '入园有效时间',
      hideInSearch: true,
      editable: false,
      render: (dom: any, entity: any) => {
        return `${dayjs(entity.dayBegin).format('YYYY-MM-DD')} 至 ${dayjs(entity.dayEnd).format(
          'YYYY-MM-DD',
        )}`;
      },
    },
    {
      title: '入园有效时间',
      hideInTable: true,
      valueType: 'dateRange',
      dataIndex: 'time2',
      editable: false,
      search: {
        transform: (value) => {
          return {
            dayBegin: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            dayEnd: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '可用库存',
      dataIndex: 'totalAmount',
      hideInSearch: true,
      editable: false,
    },
    {
      title: '采购价（元）',
      dataIndex: 'purchasePrice',
      hideInSearch: true,
      renderText: (dom) => {
        const items = dom.map((price: any) => {
          const p = parseFloat(price) ? parseFloat(price).toFixed(2) : price;
          return <div>{p}</div>;
        });
        return <div>{items}</div>;
      },
    },
  ];

  /**
   * 分销商分组 - 配置产品 修改
   */
  const actionSaveProductConfig = async (
    record: API.ProductConfigListItem & { index?: number | undefined },
  ) => {
    //把副表中的价格数据同步进来
    // record.configGoodsDetail = dataSource

    const data = { ...dataSource[record.index!!] };
    data.salePrice = record.salePrice; //同步价格设置
    // for (const item of data.configGoodsDetail) {
    //   if (item.price == null) {
    //     message.warning('保存失败，未设置价格');
    //     return;
    //   }
    // }

    await saveProductConfig({
      groupId,
      list: [data],
    });
    // 更新引导
    updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_2 });

    addOperationLogRequest({
      action: 'edit',
      module: saleGroupTabKey,
      content: `编辑【${groupName}】销售授权`,
    });

    upActionRef?.current?.reload();
    message.success('保存成功');
  };

  /**
   * 分销商分组 - 配置产品 删除
   */
  const actionDeleteProductConfig = async (record: API.ProductConfigListItem) => {
    const result: any = await saveProductConfig({
      groupId,
      batchId: record.batchId,
    });
    const success = result.code == 20000;
    if (success) {
      addOperationLogRequest({
        action: 'del',
        module: saleGroupTabKey,
        content: `删除【${groupName}】销售授权`,
      });
    }
    return success ? Promise.resolve() : Promise.reject();
  };
  const saleGroupTabKey = useContext(SaleGroupTabKeyContext);

  return (
    <>
      <ProCard
        title={
          <div
            className="flex align-items-center primary-color pointer"
            onClick={() => modalState.setType(null)}
          >
            <LeftOutlined style={{ marginRight: 10 }} />
            销售授权
          </div>
        }
        headerBordered
      >
        <EditableProTable<API.ProductConfigListItem, any>
          style={{ width: '100%' }}
          {...tableConfig}
          rowKey="batchId"
          params={{ distributorId: coId, groupId }}
          columns={tableColumns}
          actionRef={actionRef}
          editableFormRef={formRef}
          postData={(data: any[]) => {
            data.map((item) => {
              item.editable = false; //置为不可编辑，意为之前点过编辑的，刷新后全还原
              actionRef?.current?.cancelEditable(item.batchId); //编辑状态也还原
            });
            setExpandedRowKeys([]); //先清空之前的展开
            //先把之前没点保存的编辑模式全部退出
            if (sourceListRef.current.length > 0) {
              const newData = sourceListRef.current.concat(data);
              for (const info of sourceListRef.current) {
                actionRef?.current?.startEditable(info.batchId); //将增加的全部设置为编辑状态
                setExpandedRowKeys((source) => {
                  //将增加的全部设置为展开状态
                  return [...source, info.batchId];
                });
              }
              sourceListRef.current.length = 0; //加入到当前页数据后删除，达到刷新后删除之前增加数据的效果
              const pageSize = actionRef?.current?.pageInfo?.pageSize ?? 10;
              // 把数据量改为当前设置的每页数据页，防数据越界后导致分页功能失效
              if (newData.length > pageSize) {
                //只需在数据超出时
                newData.length = pageSize;
              }
              setDataSource(newData);
              return newData;
            }
            setDataSource(data);
            return data;
          }}
          recordCreatorProps={false}
          pagination={{
            defaultPageSize: 10,
          }}
          toolBarRender={() => {
            return [
              <Button
                type="primary"
                key="save"
                onClick={async () => {
                  setCopyVisible(true);
                }}
              >
                添加
              </Button>,
            ];
          }}
          editable={{
            type: 'multiple',
            deleteText: <></>,
            actionRender: (record, config, defaultDom) => {
              return (
                <Flex gap={24}>
                  {defaultDom.save}
                  <Button
                    type="link"
                    onClick={async () => {
                      setExpandedRowKeys((source) => {
                        return [...source].filter((i) => i != record?.batchId);
                      });
                      setDataSource((source) => {
                        const newData = [...source];
                        newData[record.index!!].editable = false;
                        return newData;
                      });
                      config?.cancelEditable?.(record?.batchId);
                    }}
                    style={{ padding: 0, height: 'auto' }}
                  >
                    取消
                  </Button>
                </Flex>
              );
            },
            onSave: async (key, record) => {
              actionSaveProductConfig(record);
              setDataSource((source) => {
                const newData = [...source];
                newData[record.index!!].editable = false;
                return newData;
              });
            },
          }}
          expandable={{
            expandedRowRender: (record: any, index: any, indent, expanded) => {
              //这一步关键，关掉后清掉渲染，再开启再绘制才能刷新数据
              return expanded ? (
                <GoodsTable
                  products={dataSource}
                  index={index}
                  expanded={expanded}
                  setProduct={setDataSource}
                />
              ) : (
                <></>
              );
            },
            showExpandColumn: true,
            rowExpandable: (record) => record?.proType !== 20,
            expandedRowKeys: expandedRowKeys,
            onExpand: (expanded, record: any) => {
              //手动设置展开
              const source = [...expandedRowKeys];
              if (expanded) {
                source.push(record.batchId);
              } else {
                source.splice(source.indexOf(record.batchId), 1);
              }
              setExpandedRowKeys(source);
            },
          }}
          request={async (param: any) => {
            const { data, code } = await getProductConfigList(param);
            return {
              data: data.data,
              success: code == 20000,
              total: data.total,
            };
          }}
        />
      </ProCard>

      {/* 选择 */}
      <Modal
        width={1400}
        title="添加库存批次"
        visible={copyVisible}
        onCancel={() => {
          setCopyVisible(false);
        }}
        footer={
          <Space>
            <Text strong>*添加后请填写销售价并保存，否则添加无效</Text>
            <Button
              onClick={() => {
                setCopyVisible(false);
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => {
                actionRef?.current?.reload(); //刷新当页数据，并在刷新结束后加上这次选择的数据
                setCopyVisible(false);
              }}
            >
              确定
            </Button>
          </Space>
        }
        destroyOnClose={true}
      >
        <ProTable<API.ProductConfigListItem, API.ProductConfigListParams>
          {...tableConfig}
          rowKey="batchId"
          params={{ distributorId: coId, groupIdForAdd: groupId }}
          request={async (params) => {
            const { data } = await getProductConfigList(params);
            return data;
          }}
          pagination={{
            defaultPageSize: 10,
          }}
          columns={selectTableColumns}
          // search={false}
          options={false}
          rowSelection={{
            type: 'checkbox',
            onChange: (_, selectDatas) => {
              sourceListRef.current = selectDatas;
            },
            getCheckboxProps: (record: any) => ({
              // 禁用已选
              disabled: record.isExchange == 1 || record.isAuthorization == 1,
            }),
          }}
        />
        {/* <ProductConfigTable /> */}
      </Modal>
    </>
  );
};

export default ProductConfig_old;
