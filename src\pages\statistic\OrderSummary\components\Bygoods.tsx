/*
 * @FilePath: Bygoods.tsx
 * @Author: chent<PERSON><PERSON><PERSON>
 * @Date: 2022-09-29 10:37:00
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-10-27 20:53:52
 * Copyright: 2022 xxxTech CO.,LTD. All Rights Reserved.
 * @Descripttion:
 */

import { tableConfig } from '@/common/utils/config';
import {
  baseProductTypeEnum,
  productTypeEnum,
  ticketStatusEnum,
  ticketTypeEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { ticketDetails } from '@/services/api/datareport';
import { byGoods, goodsReceiveTicket, goodsRetreatTicket } from '@/services/api/goods';
import type { ActionType, ProColumns, RequestData } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Descriptions, Modal, Tooltip } from 'antd';
import dayjs from 'dayjs';
import QRCode from 'qrcode.react';
import React, { useRef, useState } from 'react';
import { useModel } from '@umijs/max';
import styles from '../index.less';
import Vessel from './Vessel';

interface IHeadInfo {
  label: string;
  value: string | number;
}
const Byuser: React.FC = () => {
  const types = [
    { title: '出票列表', inlayTitle: '出票' },
    { title: '退票列表', inlayTitle: '退票' },
  ];

  const { initialState }: any = useModel('@@initialState');
  const [visible, setVisible] = useState<boolean>(false);
  const [goods, setGoods] = useState<Record<string, any>>({});
  const [index, setIndex] = useState<number>(0);
  const [total, setTotal] = useState<number>(0); // 总数
  const [amount, setAmount] = useState<Record<string, any>[]>([]); //合计
  const [headInfo, setHeadInfo] = useState<IHeadInfo[]>([]); // 弹窗头部信息

  /** 详情模块 */
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [detailsWidth, setDetailsWidth] = useState<number>(modelWidth.md);
  const [rowInfo, setRowInfo] = useState<Record<string, any>>({});
  const [dates, setDates] = useState<Record<string, any>[] | null>(null); //总表时间

  //弹框
  const circumstance = (item: any, i: number, type: number) => {
    const items = item.agentList[i];
    console.log('circumstance', item, i, type);
    const headInfoArr: IHeadInfo[] = [
      { label: '商品名称：', value: items.product_sku_name },
      { label: '票种：', value: ticketTypeEnum[items.ticket_type] },
      { label: '产品名称：', value: items.product_name },
      { label: '类型：', value: baseProductTypeEnum[items.pro_type * 1] },
      { label: '分时预约：', value: items.time_share },
      { label: '景区名称：', value: items.scenic_name },
      { label: '服务商：', value: items.service_provider_name },
      { label: '代理商：', value: items.agent_name },
    ];
    setGoods(items);
    setHeadInfo(headInfoArr);
    setIndex(type);
    setVisible(true);
  };

  const columns: ProColumns<ActionType>[] = [
    {
      title: '支付 / 退款日期',
      initialValue: [dayjs().startOf('months'), dayjs()],

      hideInForm: false,
      dataIndex: 'dates',
      key: 'dates',
      hideInSearch: false,
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '商品名称',
      hideInForm: false,
      dataIndex: 'product_sku_name',
      key: 'product_sku_name',
      hideInSearch: false,
      hideInTable: false,
    },
    {
      title: '票种',
      hideInForm: false,
      dataIndex: 'goodsType',
      key: 'goodsType',
      hideInSearch: true,
      hideInTable: false,
      render: (_, item: any) => <>{ticketTypeEnum[item.ticket_type]}</>,
    },
    {
      title: '产品名称',
      hideInForm: false,
      dataIndex: 'product_name',
      key: 'product_name',
      hideInSearch: true,
      hideInTable: false,
    },
    {
      title: '类型',
      hideInForm: false,
      dataIndex: 'pro_type',
      key: 'pro_type',
      hideInSearch: true,
      hideInTable: false,
      render: (_, item: any) => <>{baseProductTypeEnum[item.pro_type * 1]}</>,
    },
    {
      title: '分时预约',
      hideInForm: false,
      dataIndex: 'time_share',
      key: 'time_share',
      hideInSearch: true,
      hideInTable: false,
    },
    {
      title: '代理商',
      dataIndex: 'agentList',
      key: 'agent_name',
      hideInSearch: true,
      render: (_, item: Record<string, any>) => {
        return (
          <div className={styles.user}>
            {item.agentList.map((n: Record<string, any>, i: number) => {
              return (
                <div key={i} style={n.agent_name === '合计' ? { fontWeight: '600' } : {}}>
                  {n.agent_name || '-'}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '出票数',
      dataIndex: 'agentList',
      key: 'chu_ticket',
      hideInSearch: true,
      render: (_, item: Record<string, any>) => {
        return (
          <div className={styles.user}>
            {item.agentList.map((n: Record<string, any>, i: number) => {
              return n.agent_name === '合计' ? (
                <div key={i}>{n.chu_ticket}</div>
              ) : (
                <div
                  style={{ color: '#1890ff', cursor: 'pointer' }}
                  onClick={() => {
                    circumstance(item, i, 0);
                  }}
                  key={i}
                >
                  {n.chu_ticket * 1}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '实付金额',
      dataIndex: 'agentList',
      key: 'pay_amounts',
      hideInSearch: true,
      render: (_, item: Record<string, any>) => {
        return (
          <div className={styles.user}>
            {item.agentList.map((n: Record<string, any>, i: number) => {
              return <div key={i}>{n.pay_amounts}</div>;
            })}
          </div>
        );
      },
    },
    {
      title: '退票数',
      dataIndex: 'agentList',
      key: 're_ticket',
      hideInSearch: true,
      render: (_, item: Record<string, any>) => {
        return (
          <div className={styles.user}>
            {item.agentList.map((n: Record<string, any>, i: number) => {
              return n.agent_name === '合计' ? (
                <div key={i}>{n.re_ticket}</div>
              ) : (
                <div
                  style={{ color: '#1890ff', cursor: 'pointer' }}
                  onClick={() => {
                    circumstance(item, i, 1);
                  }}
                  key={i}
                >
                  {n.re_ticket * 1}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '退款金额',
      dataIndex: 'agentList',
      key: 're_amounts',
      hideInSearch: true,
      render: (_, item: Record<string, any>) => {
        return (
          <div className={styles.user}>
            {item.agentList.map((n: Record<string, any>, i: number) => {
              return <div key={i}>{n.re_amounts}</div>;
            })}
          </div>
        );
      },
    },
  ];

  const details = async (item: any) => {
    //门票详情
    try {
      const { code, data } = await ticketDetails(item.ticket_number);
      if (code == 20000) {
        setRowInfo(data);
      }
      setDetailsVisible(true);
    } catch (err) {
      console.log(err);
    }
  };

  const childColumns: ProColumns<ActionType>[][] = [
    [
      //出票
      {
        title: '票号',
        key: 'ticket_number',
        dataIndex: 'ticket_number',
        render: (_: any, item: any) => {
          if (item.ticket_number) {
            if (!item.ticket_number) {
              return '-';
            }
            const ticket_number = item.ticket_number?.replace(
              item.ticket_number.substring(2, item.ticket_number.length - 4),
              '****',
            );
            return (
              <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
                <Tooltip title={item.ticket_number}>{ticket_number}</Tooltip>
              </div>
            );
          } else {
            return <>-</>;
          }
        },
      },
      {
        title: '订单号',
        key: 'order_id',
        dataIndex: 'order_id',
      },
      {
        title: '入园时间',
        key: 'day',
        dataIndex: 'day',
        valueType: 'dateRange',
        hideInSearch: true,
        render: (_, item: any) => <>{item.day.split('.')[0]}</>,
      },
      {
        title: '实付金额',
        key: 'product_price',
        dataIndex: 'product_price',
        hideInSearch: true,
      },
      {
        title: '佣金',
        key: 'actual_com_amount',
        dataIndex: 'actual_com_amount',
        hideInSearch: true,
      },
      {
        title: '支付时间',
        key: 'paytime',
        dataIndex: 'paytime',
        valueType: 'dateRange',
        initialValue: dates,
        render: (_, item: any) => <>{item.paytime.split('.')[0]}</>,
      },
    ],
    [
      //退票
      {
        title: '票号',
        key: 'ticket_number',
        dataIndex: 'ticket_number',
        render: (_: any, item: any) => {
          if (item.ticket_number) {
            if (!item.ticket_number) {
              return '-';
            }
            const ticket_number = item.ticket_number?.replace(
              item.ticket_number.substring(2, item.ticket_number.length - 4),
              '****',
            );
            return (
              <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
                <Tooltip title={item.ticket_number}>{ticket_number}</Tooltip>
              </div>
            );
          } else {
            return <>-</>;
          }
        },
      },
      {
        title: '退单号',
        key: 'order_id',
        dataIndex: 'order_id',
      },
      {
        title: '入园时间',
        key: 'day',
        dataIndex: 'day',
        valueType: 'dateRange',
        hideInSearch: true,
        render: (_, item: any) => <>{item.day.split('.')[0]}</>,
      },
      {
        title: '退款金额',
        key: 'product_price',
        dataIndex: 'product_price',
        hideInSearch: true,
      },
      {
        title: '退佣金额',
        key: 'actual_com_amount',
        dataIndex: 'actual_com_amount',
        hideInSearch: true,
      },
      {
        title: '退款时间',
        key: 'paytime',
        dataIndex: 'paytime',
        valueType: 'dateRange',
        initialValue: dates,
        render: (_, item: any) => <>{item.paytime.split('.')[0]}</>,
      },
    ],
  ];

  const request =
    (fn: any, type: string) =>
    async (q: Record<string, any>): Promise<Partial<RequestData<ActionType>>> => {
      console.log(q, 'q');
      const params: any = {};
      if (type == 'receiveTicket') {
        params.start_year_month_day_pay = q.paytime ? q.paytime[0] : null;
        params.end_year_month_day_pay = q.paytime ? q.paytime[1] : null;
        params.order_id = q.order_id ? q.order_id : null;
      } else if (type == 'retreatTicket') {
        params.start_year_month_day = q.paytime ? q.paytime[0] : null;
        params.end_year_month_day = q.paytime ? q.paytime[1] : null;
        params.refund_id = q.order_id ? q.order_id : null;
      }

      const obj = {
        conf: {
          order_id: q.order_id ? q.order_id : null,
          seller_id: initialState.currentCompany.coId,
          product_name: goods.product_name,
          ticket_number: q.ticket_number ? q.ticket_number : null,
          order_product_id: goods.order_product_id,
          ticket_type: goods.ticket_type,
          pro_type: goods.pro_type,
          time_share: goods.time_share,
          scenic_name: goods.scenic_name,
          service_provider_id: goods.service_provider_id,
          agent_id: goods.agent_id,
          m: q.pageSize, //条数
          n: q.current, //页码
          ...params,
        },
      };

      const data: any = [];
      try {
        const { result } = await fn(obj);
        setAmount([]);
        setTotal(0);
        result.map((_n: any, i: any) => {
          if (_n.ticket_number == '合计') {
            let amount: Record<string, any>[] = [];
            if (type == 'receiveTicket') {
              amount = [
                { label: '实付金额：', value: `${(_n.product_price * 1).toFixed(2)}` },
                { label: '佣金：', value: `${(_n.actual_com_amount * 1).toFixed(2)}` },
              ];
            }
            if (type == 'retreatTicket') {
              amount = [
                { label: '退款金额：', value: `${(_n.product_price * 1).toFixed(2)}` },
                { label: '退佣金额：', value: `${(_n.actual_com_amount * 1).toFixed(2)}` },
              ];
            }
            setAmount(amount);
            setTotal(_n.num);
          } else {
            data.push(_n);
          }
        });
      } catch (err) {
        console.log(err);
      }
      return await new Promise((resolve, reject) => resolve({ data, total: total }));
    };

  const requests: any[] = [
    request(goodsReceiveTicket, 'receiveTicket'),
    request(goodsRetreatTicket, 'retreatTicket'),
  ];

  const actionRef = useRef<ActionType>();
  return (
    <>
      <ProTable<ActionType, ProColumns>
        {...tableConfig}
        bordered
        actionRef={actionRef}
        rowKey="key"
        // options={{ setting: false, density: false }}
        // search={{ labelWidth: 'auto' }}
        request={async (params: Record<string, any>): Promise<Partial<RequestData<any>>> => {
          const obj: Record<string, any> = {
            conf: {
              order_product_id: null,
              seller_id: initialState.currentCompany.coId,
              product_sku_name: params.product_sku_name ? params.product_sku_name : null,
              product_name: null,
              ticket_type: null,
              time_share: null,
              scenic_id: null,
              service_provider_id: null,
              agent_id: null,
              ticket_number: null,
              order_id: null,
              start_year_month_day: params.dates ? params.dates[0] : null,
              end_year_month_day: params.dates ? params.dates[1] : null,
              refund_id: null,
            },
          };
          setDates(params.dates ? params.dates : null);
          const { result } = await byGoods(obj);
          addOperationLogRequest({
            action: 'info',
            content: '查看面向代理商订单 - 按商品汇总',
          });
          const data: any = {
            success: true,
            total: 0,
          };
          const clash = {};
          // 根据商品 id 把数据分组，便于计算合计
          result.forEach((e: Record<string, any>) => {
            if (!clash[e.order_product_id]) {
              clash[e.order_product_id] = [];
            }
            clash[e.order_product_id].push(e);
          });
          const arr: any = [];
          // 前端组装数据，同一组基本数据都一样，区别在于代理商有多个，所以只需要拿到数组第一条作为基本数据，再拼接代理商数据
          Object.values(clash).forEach((items: any) => {
            arr.push({
              ...items[0],
              agentList: items,
            });
          });
          // 前端计算合计
          arr.forEach((e: Record<string, any>) => {
            e.agentList.push({
              agent_name: '合计',
              agent_id: '',
              chu_ticket: e.agentList
                .map((i: Record<string, any>) => i.chu_ticket * 1)
                .reduce((c: number, v: number) => c + v),
              pay_amounts: e.agentList
                .map((i: Record<string, any>) => i.pay_amounts * 1)
                .reduce((c: number, v: number) => c + v)
                .toFixed(2),
              re_ticket: e.agentList
                .map((i: Record<string, any>) => i.re_ticket * 1)
                .reduce((c: number, v: number) => c + v),
              re_amounts: e.agentList
                .map((i: Record<string, any>) => i.re_amounts * 1)
                .reduce((c: number, v: number) => c + v)
                .toFixed(2),
            });
          });
          data.data = arr;
          data.total = arr.length;
          return await new Promise((resolve, reject) => resolve(data));
        }}
        columns={columns}
      />

      {/* 弹窗 */}
      <Vessel
        type={types[index]?.title}
        width={modelWidth.xl}
        total={total}
        visible={visible}
        setVisible={setVisible}
        columns={childColumns[index]}
        amount={amount}
        request={requests[index]}
        headInfo={headInfo}
        setAmount={setAmount}
      />

      {/* 细节弹窗  */}
      <Modal
        title={`${types[index]?.inlayTitle}详情`}
        visible={detailsVisible}
        width={detailsWidth}
        destroyOnClose={true}
        footer={
          <>
            <Button onClick={() => setDetailsVisible(false)}>取消</Button>
          </>
        }
        onCancel={async () => {
          setDetailsVisible(false);
        }}
      >
        {/* 门票详情 */}
        <Descriptions title="基础信息" column={2}>
          <Descriptions.Item label="票号">{rowInfo.id}</Descriptions.Item>
          <Descriptions.Item label="服务商名称">{rowInfo.providerName}</Descriptions.Item>
          <Descriptions.Item label="产品名称">{rowInfo.proName}</Descriptions.Item>
          <Descriptions.Item label="产品类型">{productTypeEnum[rowInfo.proType]}</Descriptions.Item>
          <Descriptions.Item label="商品名称">{rowInfo.goodsName}</Descriptions.Item>
          <Descriptions.Item label="票种">{ticketTypeEnum[rowInfo.goodsType]}</Descriptions.Item>
          <Descriptions.Item label="购票人姓名">{rowInfo.buyerName}</Descriptions.Item>
          <Descriptions.Item label="购票人身份证号">{rowInfo.buyerId}</Descriptions.Item>
          <Descriptions.Item label="入园时间">{rowInfo.enterDate}</Descriptions.Item>
          <Descriptions.Item label="状态">{ticketStatusEnum[rowInfo.status]}</Descriptions.Item>
          <Descriptions.Item label="出票时间">{rowInfo.createTime}</Descriptions.Item>
          <Descriptions.Item label="订单号">{rowInfo.orderId}</Descriptions.Item>
          <Descriptions.Item label="二维码">
            <QRCode
              style={{ margin: '0px 0px', position: 'relative' }}
              value={rowInfo.printStr} //value 参数为生成二维码的链接 我这里是由后端返回
              size={120} //二维码的宽高尺寸
              fgColor="#000000" //二维码的颜色
            />
          </Descriptions.Item>
          <Descriptions.Item label="景区名称">{rowInfo.scenicName}</Descriptions.Item>
        </Descriptions>
      </Modal>
    </>
  );
};
export default Byuser;
