/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-26 10:22:59
 * @LastEditTime: 2023-06-01 10:35:13
 * @LastEditors: zhangfeng<PERSON>i
 */
import { tableConfig } from '@/common/utils/config';
import { orderTypeEnum } from '@/common/utils/enum';
import type { ModalState } from '@/hooks/useModal';
import type { API } from '@/services/api/typings';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { history } from '@umijs/max';

interface RefundModalProps {
  modalState: ModalState;
  dataSource: API.RefundOrderListItem[];
}

const RefundModal: FC<RefundModalProps> = ({ modalState: { visible, setVisible }, dataSource }) => {
  const columns: ProColumns<API.RefundOrderListItem>[] = [
    {
      title: '销售退单号',
      dataIndex: 'refundId',
    },
    {
      title: '分销商名称',
      dataIndex: 'buyerName',
    },

    {
      title: '退货日期',
      dataIndex: 'createTime',
      render: (dom: any) => dayjs(dom).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作人',
      dataIndex: 'username',
      hideInSearch: true,
    },
    {
      title: '退货金额（元）',
      dataIndex: 'refundAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '退单状态',
      dataIndex: 'refundStatus',
      valueEnum: orderTypeEnum,
    },
    {
      width: 120,
      title: '操作',
      key: 'option',
      fixed: 'right',
      render: (_, { refundId }) => (
        <a
          onClick={() => {
            history.push(`/partner/sale-channel/sale-order?orderId=${refundId}`);
          }}
        >
          查看
        </a>
      ),
    },
  ];
  return (
    <Modal
      title={'销售退单'}
      width={1000}
      visible={visible}
      onCancel={() => {
        setVisible(false);
      }}
      footer={false}
      closable
      maskClosable
    >
      <ProTable {...tableConfig} columns={columns} dataSource={dataSource} search={false} />
    </Modal>
  );
};

export default RefundModal;
