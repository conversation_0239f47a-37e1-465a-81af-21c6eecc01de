/**
 * 批量发权益卡
 */
import { request } from '@umijs/max';
import { scenicHost } from '.';
const url = (v: string) => scenicHost + v;
const successCode = 20000;

/** 新增批量 */
export async function batchCardAdd(data: any) {
  let result;
  try {
    result = await request(url('/batch/card/add'), {
      method: 'POST',
      data: data,
    });
  } catch (e) {}
  return result;
}

/** 导出失败信息 */
export async function batchCardExport(data: any) {
  return request<Blob>(url('/batch/card/export'), {
    method: 'POST',
    data: data,
    skipErrorHandler: true,
    responseType: 'blob',
  });
}

/** 修改审核状态 */
export function batchCardUpdateApproveState(data: any) {
  return request(url('/batch/card/update/approve/state'), {
    method: 'PUT',
    data,
  });
}

/** 详情 */
export async function batchCardinfo(params: any) {
  const { data, code } = await request(url('/batch/card/info'), { params });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total,
  };
}

/** 分页查询 */
export async function batchCardPage(params: any) {
  const { data, code } = await request(url('/batch/card/page'), { params });
  return {
    data: data.data,
    success: code == successCode,
    total: data.total,
  };
}

/** 根据店铺查询所有旅游卡简介 */
export async function travelCardAll(params: any) {
  const { data, code } = await request(url('/ticketStore/travelCardAll/' + params.storeId));
  if (code == successCode) {
    return data;
  } else {
    return [];
  }
}

/** 审核权限验证 */
export async function batchCardApproveAuthorize(params: any) {
  const { data, code } = await request(url('/batch/card/approve/authorize'), { params });
  if (code == successCode) {
    return data;
  } else {
    return false;
  }
}

async function readFileAsDataURL(file: Blob) {
  const result_base64 = await new Promise<string>((resolve) => {
    const fileReader = new FileReader();
    fileReader.readAsDataURL(file);
    fileReader.onload = () => typeof fileReader.result === 'string' && resolve(fileReader.result);
  });
  return result_base64.split('base64,')[1];
}

/** 获取文章排序列表 */
export async function batchCardUpload(travelCardGood: any, f: File, scheduleId: number) {
  const formData = new FormData();
  formData.append('file', f);
  formData.append('scheduleId', scheduleId.toString());
  formData.append('storeGoodsId', travelCardGood.storeGoodsId);
  formData.append('tradeCardName', travelCardGood.travelCardName);
  formData.append('travelCardGoodsId', travelCardGood.goodsId);

  return await request(url('/batch/card/upload'), {
    method: 'POST',
    requestType: 'form',
    data: formData,
    skipErrorHandler: true,
  });
}

/**
 * 表格处理进度
 * @param scheduleId 传一个随机码用来带身份
 * @returns 返回当前处理进度的百分比数字，一位小数
 */
export async function batchCardSchedule(scheduleId: number) {
  const { data, code } = await request(url('/batch/card/schedule'), { params: { scheduleId } });
  if (code == successCode) {
    return data;
  } else {
    return 0;
  }
}
