/*
 * 百度地图
 * @param width 宽度
 * @param height 高度
 * @param value 经纬度 {lng, lat}
 * @param onChange
 * */
import { useEffect, useRef } from 'react';

const BMap = window.BMap;

const BaiduMap = ({ width, height, cRef, onChange, value: point }) => {
  const mapRef = useRef<any>();
  const oldMarker = useRef();

  const setPoint = (point) => {
    console.log(point);
    console.log('114.029949114.029949114.029949');
    if (mapRef.current) {
      const points = new BMap.Point(point?.lng, point?.lat);
      mapRef.current?.centerAndZoom(points, 18);
      mapRef.current?.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
      mapRef.current?.removeOverlay(oldMarker.current);
      const marker = new BMap.Marker(points, { enableDragging: true });
      oldMarker.current = marker;
      mapRef.current.addOverlay(marker); // 将标注添加到地图中
      onChange(point);
      marker.addEventListener('dragend', function () {
        const nowPoint = marker.getPosition(); // 拖拽完成之后坐标的位置
        onChange(nowPoint);
      });
    }
  };

  useEffect(() => {
    mapRef.current = new BMap.Map('map-container');
    setPoint(point);
  }, []);

  useEffect(() => {
    setPoint(point);
  }, [point]);

  return <div id="map-container" style={{ width, height }} />;
};

export default BaiduMap;
