/*
 * 进货弹窗
 * */
import ProModal from '@/common/components/ProModal';
import useProModal from '@/common/components/ProModal/useProModal';
import TimeStore from '@/common/components/TimeStore';
import { tableConfig } from '@/common/utils/config';
import { productTypeEnum, ticketTypeEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getUniqueId } from '@/common/utils/tool';
import { apiDistributeRefund, apiRefundDistributorStockList } from '@/services/api/distribution';
import { EditOutlined, SettingOutlined } from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { TableProps } from 'antd';
import {
  But<PERSON>,
  Card,
  DatePicker,
  InputNumber,
  List,
  Modal,
  Space,
  Table,
  Tag,
  message,
} from 'antd';

import { round } from 'lodash';
import dayjs from 'dayjs';
import React, { useMemo, useState } from 'react';
import { useModel } from '@umijs/max';
// let supplierList: any = []
let selectDateList: any = [];

const logList = [
  {
    title: '退货总量',
    dataIndex: 'num',
  },
];

const Purchase = ({
  close,
  orderId,
  stockCertificate,
  currentRow,
}: {
  close: any;
  orderId: string;
  stockCertificate: string;
  currentRow: Record<string, any>;
}) => {
  // 分时库存
  const modalState = useProModal();
  const [modalData, setModalData] = useState<any>({});
  const [lawsList, setLawsList] = useState<any>([]);
  const lawsColumns = [
    {
      title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join('-')}`,
      render: () => {},
    },
    {
      title: () => '退货数量/可退库存',
      render: ({ purchaseNum = 0, stockAmount = 0 }) => `${purchaseNum}/${stockAmount}`,
    },
    {
      title: () => '退货金额（元）',
      render: ({ purchaseAmount = 0 }) => purchaseAmount,
    },
  ];
  const modalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '入园有效时间',
          valueType: 'dateRange',
          dataIndex: 'dateRange',
          initialValue: [modalData.enterStartTime, modalData.enterEndTime],
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '',
          dataIndex: 'timeLaws',
          colProps: { span: 24 },
          renderFormItem: () => (
            <TimeStore
              lawsColumns={lawsColumns}
              lawsList={lawsList}
              set={(id: any, date: any) => {
                setModalSetData({
                  dateType: 'date',
                  timeShareId: [id],
                  dateList: [date],
                });
                console.log(modalData, valueObj);

                setTimeTableData([
                  {
                    id,
                    goodsName: modalData.goodsName,
                    ticketGoodsType: modalData.ticketGoodsType,
                    salePrice: modalData.salePrice,
                    stockAmount: valueObj[date][id].stockAmount,
                    purchaseNum: valueObj[date][id].purchaseNum,
                    purchaseAmount: valueObj[date][id].purchaseAmount,
                  },
                ]);
                modalSetState.setType('edit');
              }}
            />
          ),
        },
        {
          title: '总退货数量',
          dataIndex: 'num',
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '总退货金额（元）',
          dataIndex: 'price',
          fieldProps: {
            disabled: true,
          },
        },
      ],
    },
  ];
  // 分时库存设置
  const modalSetState = useProModal();
  const [modalSetData, setModalSetData] = useState({});
  const timeEnum = useMemo(() => {
    const obj: Record<string, string> = {};
    lawsList?.forEach((item: any) => {
      obj[item.id] = [item.beginTime, item.endTime].join('-');
    });
    return obj;
  }, [lawsList]);
  const valueObj = useMemo(() => {
    const obj1: any = {};
    modalData.timeLaws?.forEach((item1: any) => {
      const obj2: any = {};
      item1.timeShareDateList.forEach((item2: any) => {
        obj2[item2.timeShareId] = item2;
      });
      obj1[item1.timeShareData] = obj2;
    });
    return obj1;
  }, [modalData]);
  const [timeTableData, setTimeTableData] = useState<any>([]);
  const timeColumns: TableProps['columns'] = [
    {
      title: '分时时段',
      dataIndex: 'id',
      fixed: 'left',
      render: (value) => timeEnum[value],
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      render: (value) => ticketTypeEnum[value],
    },
    {
      title: '可退库存',
      dataIndex: 'stockAmount',
      render: (value) => value ?? '-',
    },
    {
      title: '采购价（元）',
      dataIndex: 'salePrice',
    },
    {
      title: '退货数量',
      dataIndex: 'purchaseNum',
      fixed: 'right',
      render: (value, record, index) => (
        <InputNumber
          min={0}
          value={value}
          onChange={(v: any) => {
            const list = structuredClone(timeTableData);
            list[index].purchaseNum = v;
            list[index].purchaseAmount = parseFloat(String(v * modalData.salePrice)).toFixed(2);
            setTimeTableData(list);
          }}
        />
      ),
    },
    {
      title: '退货金额（元）',
      dataIndex: 'purchaseAmount',
      fixed: 'right',
    },
  ];
  const modalSetColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '时间选择',
          dataIndex: 'dateType',
          valueType: 'radio',
          valueEnum: {
            date: '按日期',
            week: '按星期',
          },
          initialValue: 'date',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          valueType: 'dependency',
          name: ['dateType'],
          columns: ({ dateType }) => [
            {
              title: '选择日期',
              dataIndex: 'dateList',
              valueType: 'date',
              hideInForm: dateType !== 'date',
              colProps: { xs: 24, sm: 12 },
              formItemProps: { rules: [{ required: true }] },
              fieldProps: (form) => {
                return {
                  multiple: true,
                  disabledDate: (current: any) => {
                    return (
                      current < dayjs(modalData.enterStartTime) ||
                      current > dayjs(modalData.enterEndTime)
                    );
                  },
                  onChange: (_: any, dateList: any) => {
                    const { dateType, timeShareId } = form.getFieldsValue();
                    setTimeTableData(
                      timeShareId?.map((id: any) => ({
                        id,
                        goodsName: modalData.goodsName,
                        ticketGoodsType: modalData.ticketGoodsType,
                        salePrice: modalData.salePrice,
                        ...(dateType == 'date' && dateList?.length == 1
                          ? {
                              stockAmount: valueObj[dateList[0]][id].stockAmount,
                              purchaseNum: valueObj[dateList[0]][id].purchaseNum,
                              purchaseAmount: valueObj[dateList[0]][id].purchaseAmount,
                            }
                          : {}),
                      })),
                    );
                  },
                };
              },
            },
            {
              title: '选择区间',
              dataIndex: 'dateRange',
              valueType: 'dateRange',
              hideInForm: dateType !== 'week',
              colProps: { xs: 24, sm: 12 },
              formItemProps: { rules: [{ required: true }] },
              fieldProps: {
                disabledDate: (current: any) => {
                  return (
                    current < dayjs(modalData.enterStartTime) ||
                    current > dayjs(modalData.enterEndTime)
                  );
                },
              },
            },
            {
              title: '设置星期',
              dataIndex: 'dateWeek',
              valueType: 'checkbox',
              valueEnum: {
                0: '每周日',
                1: '每周一',
                2: '每周二',
                3: '每周三',
                4: '每周四',
                5: '每周五',
                6: '每周六',
              },
              initialValue: ['0', '1', '2', '3', '4', '5', '6'],
              hideInForm: dateType !== 'week',
              formItemProps: { rules: [{ required: true }] },
            },
          ],
        },
      ],
    },
    {
      title: '',
      columns: [
        {
          title: '分时选择',
          dataIndex: 'timeShareId',
          valueEnum: timeEnum,
          fieldProps: (form) => {
            return {
              mode: 'multiple',
              onChange: (list: any) => {
                const { dateType, dateList } = form.getFieldsValue();
                setTimeTableData(
                  list?.map((id: any) => ({
                    id,
                    goodsName: modalData.goodsName,
                    ticketGoodsType: modalData.ticketGoodsType,
                    salePrice: modalData.salePrice,
                    ...(dateType == 'date' && dateList?.length == 1
                      ? {
                          stockAmount: valueObj[dateList[0]][id].stockAmount,
                          purchaseNum: valueObj[dateList[0]][id].purchaseNum,
                          purchaseAmount: valueObj[dateList[0]][id].purchaseAmount,
                        }
                      : {}),
                  })),
                );
              },
            };
          },
          colProps: { span: 24 },
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '',
          colProps: { span: 24 },
          renderFormItem: () => (
            <Table
              {...tableConfig}
              rowKey="id"
              columns={timeColumns}
              dataSource={timeTableData}
              pagination={false}
            />
          ),
        },
      ],
    },
  ];

  const [isClick, setIsClick] = useState(true);
  // 表单对象
  const actionRef = React.useRef();
  // 数据绑定
  const { initialState } = useModel('@@initialState');
  const { coId, coName } = initialState?.currentCompany;
  const [dataIndex, setDataIndex] = useState<any>(0);
  const [dataSource, setDataSource] = useState<any>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const [payAmount, setPayAmount] = useState<any>(0);

  // 总计
  const setTotal = (sum: any) => {
    let allPrice = 0;
    sum.map((item: any) => {
      if (item.price) allPrice += item.price * 1;
    });
    setPayAmount(round(allPrice, 2));
  };
  // 表格配置
  const tableColumns: ProColumns[] = [
    {
      title: '库存批次号',
      dataIndex: 'batchId',
      search: false,
      render: (dom: any, entity, index, __, schema: any) => (
        <span>
          {entity.enterDateList.length > 0 ? '-' : dom}{' '}
          {entity.isExchange === 1 && <Tag color="blue">交易所</Tag>}
        </span>
      ),
    },
    {
      title: '库存批次号',
      dataIndex: 'masterBatch',
      hideInTable: true,
    },
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      valueEnum: productTypeEnum,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      valueEnum: ticketTypeEnum,
    },
    {
      title: '购买有效时间',
      dataIndex: 'purchaseTime',
      hideInSearch: true,
      render: (dom: any, entity) => {
        const startString = dayjs(entity.purchaseBeginTime).format('YYYY-MM-DD');
        const endString = dayjs(entity.purchaseEndTime).format('YYYY-MM-DD');
        return `${startString} 至 ${endString}`;
      },
    },
    {
      title: '入园有效时间',
      dataIndex: 'day',
      hideInSearch: true,
      render: (dom: any, entity) => {
        const startString = dayjs(entity.enterStartTime).format('YYYY-MM-DD');
        const endString = dayjs(entity.enterEndTime).format('YYYY-MM-DD');
        return `${startString} 至 ${endString}`;
      },
    },
    {
      title: '可退库存',
      dataIndex: 'totalNumber',
      search: false,
      fixed: 'right',
      valueType: 'digit',
    },
    {
      title: '市场标准价（元）',
      dataIndex: 'marketPrice',
      search: false,
      fixed: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '采购价（元）',
      dataIndex: 'salePrice',
      search: false,
      fixed: 'right',
      render: (dom: any, entity: any) => (entity.enterDateList.length > 0 ? '-' : dom.toFixed(2)),
    },
    {
      title: '退货数量', // 非分时可编辑
      dataIndex: 'num',
      hideInSearch: true,
      fixed: 'right',
      renderText: (dom: any) => dom || 0,
      render: (dom: any, entity, index, _, schema: any) =>
        entity.timeLaws?.length ? (
          <Button
            icon={<EditOutlined />}
            iconPosition="end"
            block
            style={{ justifyContent: 'left' }}
            onClick={() => {
              const obj: any = {};
              // 遍历日期提取分时集
              entity?.timeLaws?.forEach(({ timeShareDateList }: any) => {
                timeShareDateList.forEach((item: any) => {
                  obj[item.timeShareId] = {
                    id: item.timeShareId,
                    beginTime: item.timeShareBeginTime,
                    endTime: item.timeShareEndTime,
                  };
                });
              });
              setDataIndex(index);
              setLawsList(Object.values(obj));
              setModalData(entity);
              modalState.setType('edit');
            }}
          >
            {dom ? dom : 0}
          </Button>
        ) : (
          <InputNumber
            min={0}
            max={entity.number}
            precision={0}
            defaultValue={dom}
            onChange={(e: any) => {
              console.log(e, index, schema.dataIndex);
              const sum = JSON.parse(JSON.stringify(dataSource));
              sum[index].num = e;
              sum[index].price = (e * dataSource[index].salePrice).toFixed(2);
              setDataSource(sum);
              setTotal(sum);
            }}
          />
        ),
    },
    {
      title: '退货金额（元）',
      dataIndex: 'price',
      search: false,
      align: 'right',
      fixed: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
      // render: (dom: any) => <Tag color={['red', 'blue'][dom]}>{['禁用', '启用'][dom]}</Tag>,
    },
  ];

  // // 表格修改（非分时）
  // const onChange = (e: any, index: number, dataIndex: number) => {
  //   console.log(e, index, dataIndex);
  // };

  // 分时数据绑定
  const [timeVisible, setTimeVisible] = useState<any>();
  // 分时序列 [表格序，列表序]
  const [timeIndex, setTimeIndex] = useState<any>([0, 0]);
  const [timeDataSource, setTimeDataSource] = useState<any>();
  // 分时表格配置
  const timeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '分时预约',
      dataIndex: 'timeBeginTime',
      render: (_, entity) =>
        entity.timeBeginTime && entity.timeEndTime ? (
          <span>{entity.timeBeginTime + ' 至 ' + entity.timeEndTime}</span>
        ) : (
          '-'
        ),
    },

    {
      title: '退货数量',
      dataIndex: 'num',
      render: (dom: any, entity: any, index, __, schema: any) => (
        <InputNumber
          min={0}
          max={entity.number}
          precision={0}
          defaultValue={dom || 0}
          onChange={(e) => {
            // onChange(e, index, schema.dataIndex);
            const sum = JSON.parse(JSON.stringify(timeDataSource));
            sum[index].num = e;
            sum[index].price = (e * sum[index].salePrice).toFixed(2);
            setTimeDataSource(sum);
            if (e == entity.number) {
              message.info('最大可退货量为' + entity.number);
            }
          }}
        />
      ),
      renderText: (dom: any) => dom || 0,
    },
    {
      title: '当前库存',
      dataIndex: 'number',
      align: 'right',
      valueType: 'digit',
    },
    {
      title: '单价（元）',
      dataIndex: 'salePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'price',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];
  // 一键复制弹窗
  const [useVisible, setUseVisible] = useState<any>();
  const [copyDateList, setCopyDateList] = useState<any>([]);
  function disabledDate(current: any) {
    return (
      copyDateList.filter((item: any) => dayjs(item).format('L') == current.format('L')).length == 0
    );
    // let isDis = true;
    // for (const item of dataSource[timeIndex[0]].enterDateList) {
    //   if (current == dayjs(item.enterStartTime)) isDis = false;
    //   break;
    // }
    // return isDis;
    // return (current && current < dayjs('2022-2-25')) || (current && current > dayjs('2022-2-25'));
  }
  const onChangeDate = (_, e: any) => {
    selectDateList = [];
    const startDate = dayjs(e[0]);
    const endDate = dayjs(e[1]);
    while (endDate > startDate || startDate.format('D') === endDate.format('D')) {
      selectDateList.push(startDate.format('YYYY-MM-DD'));
      startDate.add(1, 'day');
    }
  };
  const submit = async () => {
    setIsClick(false);
    const list: any = [];
    const product: any = [];
    dataSource.map((dataSource: any) => {
      // 过滤空退货量数据
      const timeLaws: any = [];
      dataSource.timeLaws.forEach((e1: any) => {
        const list: any = [];
        e1.timeShareDateList.forEach((e2: any) => {
          if (e2.purchaseNum) list.push(e2);
        });
        if (list.length) timeLaws.push({ ...e1, timeShareDateList: list });
      });
      if (dataSource.num) {
        list.push({
          distributorTicketStockId: dataSource.distributorTicketStockId,
          number: dataSource.num,
          timeLaws: timeLaws,
        });
        product.push({
          batchId: dataSource.batchId,
          dayBegin: dataSource.enterStartTime,
          dayEnd: dataSource.enterEndTime,
          distributorTicketStockId: dataSource.distributorTicketStockConfigId,
          num: dataSource.num,
          productId: dataSource.ticketId,
          productName: dataSource.proName,
          productPrice: dataSource.salePrice,
          productType: 0,
          scenicId: dataSource.scenicId,
          scenicName: dataSource.scenicName,
          timeLaws: timeLaws.length ? JSON.stringify(timeLaws) : null,
        });
      }
    });
    let number = 0;
    dataSource.forEach((item: any) => {
      number += item.number;
    });
    if (number == 0) {
      message.info('暂无库存可退');
      setTimeout(() => {
        setIsClick(true);
      }, 500);
      return;
    } else if (list.length == 0) {
      message.info('无法提交，请输入正确的退货数量');
      setTimeout(() => {
        setIsClick(true);
      }, 500);
      return;
    }
    try {
      const data = await apiDistributeRefund({
        credentialsId: stockCertificate,
        list,
        orderId,
        product,
        refundAmount: payAmount,
        remark: '',
        type: 0,
      });
      addOperationLogRequest({
        action: 'edit',
        changeConfig: {
          afterData: {
            num: list?.reduce((pre, next) => pre + next.number, 0),
          },
          beforeData: {
            num: null,
          },
          list: logList,
        },
        content: `【${orderId}】采购订单退货`,
      });
      close();
      setDataSource([]);
      message.success('提交成功');
      setIsClick(true);
    } catch (error) {
      setIsClick(true);
    }
  };

  return (
    <div>
      {/* 退货表格 */}
      <ProTable
        {...tableConfig}
        rowKey="id"
        headerTitle={<div style={{}}>采购订单号：{currentRow?.orderId}</div>}
        columns={tableColumns}
        params={{ credentialsId: stockCertificate }}
        request={async (e) => {
          const { data } = await apiRefundDistributorStockList(e);
          data.map((item: any) => {
            item.id = getUniqueId();
          });
          setDataSource(data);
        }}
        dataSource={dataSource}
        form={{
          ignoreRules: false,
        }}
        onSubmit={(val) => {
          console.log(val);
        }}
        expandable={{
          expandedRowRender: (record: any, indexTable: number) => (
            <>
              <span style={{ padding: '8px', display: 'block' }}>入园时段及分时预约信息：</span>
              <List
                style={{
                  padding: '4px 8px',
                  maxHeight: '300px',
                  overflowY: 'auto',
                  overflowX: 'hidden',
                }}
                grid={{
                  gutter: 16,
                  xs: 1,
                  sm: 2,
                  md: 4,
                  lg: 4,
                  xl: 5,
                  xxl: 8,
                }}
                dataSource={record.enterDateList}
                renderItem={(item: any, indexList: number) => (
                  <List.Item className="cardBox">
                    <Card
                      title={item.enterStartTime}
                      hoverable
                      size="small"
                      // extra={<a>详情</a>}
                      actions={[
                        <span
                          key="span1"
                          onClick={() => {
                            // 设置序列
                            setTimeIndex([indexTable, indexList]);
                            // 修改分时信息
                            setTimeDataSource(item.detail);
                            setTimeVisible(true);
                          }}
                        >
                          <EditOutlined key="edit" /> 编辑
                        </span>,
                        <span
                          key="span2"
                          onClick={() => {
                            // 设置序列
                            setTimeIndex([indexTable, indexList]);
                            const sum: any = [];
                            console.log(indexList, record.enterDateList);
                            record.enterDateList.map((item: any, index: any) => {
                              if (index != indexList) sum.push(item.enterStartTime);
                            });
                            selectDateList = [];
                            setCopyDateList(sum);
                            setUseVisible(true);
                          }}
                        >
                          <SettingOutlined key="setting" /> 应用
                        </span>,
                      ]}
                    >
                      <p>数量：{item.num || 0}</p>
                      <p>金额：￥ {item.price || 0}</p>
                    </Card>
                  </List.Item>
                )}
              />
            </>
          ),
          rowExpandable: (record: any) => record?.enterDateList?.length !== 0,
        }}
      />
      {/* 总计 */}
      <span style={{ display: 'flex', justifyContent: 'end', margin: '24px' }}>
        总计：{payAmount} 元
      </span>
      <div
        className="flex w-100 justify-content-center align-items-center"
        style={{
          position: 'sticky',
          height: 72,
          backgroundColor: 'white',
          bottom: 0,
          zIndex: 2,
          boxShadow: '0px -2px 9px -1px rgba(208,208,208,0.5)',
        }}
      >
        <Space>
          <Button onClick={close} key="1">
            取消
          </Button>
          <Button type="primary" onClick={submit} key="2">
            提交
          </Button>
        </Space>
      </div>
      {/* 分时预约详情弹窗 */}
      <Modal
        width={1200}
        title={'分时预约信息'}
        visible={timeVisible}
        destroyOnClose
        okText="确定"
        cancelText="取消"
        onCancel={() => {
          setTimeVisible(false);
        }}
        onOk={async () => {
          // 记录分时配置
          const ListObj = dataSource[timeIndex[0]].enterDateList[timeIndex[1]];
          ListObj.detail = timeDataSource;
          // ListObj.num = timeDataSource[0].num
          // ListObj.price = timeDataSource[0].price
          // 设置入园时段信息
          ListObj.num = 0;
          ListObj.price = 0;
          timeDataSource.map((item: any) => {
            if (item.price) {
              ListObj.num += item.num;
              ListObj.price += item.price * 1;
            }
          });
          // 设置产品信息
          dataSource[timeIndex[0]].num = 0;
          dataSource[timeIndex[0]].price = 0;
          dataSource[timeIndex[0]].enterDateList.map((item: any) => {
            if (item.num != undefined) {
              dataSource[timeIndex[0]].num += item.num;
              dataSource[timeIndex[0]].price += item.price;
            }
          });
          setDataSource(dataSource);
          // 总计
          let allPrice = 0;
          dataSource.map((item: any) => {
            if (item.price) allPrice += item.price * 1;
          });
          setPayAmount(round(allPrice, 2));
          setTimeVisible(false);
        }}
      >
        <ProTable
          {...tableConfig}
          rowKey="distributorTicketStockId"
          headerTitle={dataSource?.[timeIndex[0]]?.enterDateList[timeIndex[1]]?.enterStartTime}
          columns={timeTableColumns}
          // columnEmptyText={false}
          // dataSource={dataSource?.[timeIndex[0]]?.enterDateList[timeIndex[1]]?.detail}
          dataSource={timeDataSource}
          // toolBarRender={false}
          search={false}
        />
      </Modal>

      {/* 应用 */}
      <Modal
        // width={1200}
        title={'应用于其他日期'}
        visible={useVisible}
        destroyOnClose
        okText="确定"
        cancelText="取消"
        onCancel={() => {
          setUseVisible(false);
        }}
        onOk={async () => {
          // // 记录分时配置
          // const ListObj = dataSource[timeIndex[0]].enterDateList[timeIndex[1]]
          // ListObj.detail = timeDataSource
          // // ListObj.num = timeDataSource[0].num
          // // ListObj.price = timeDataSource[0].price
          // // 设置入园时段信息
          // ListObj.num = 0
          // ListObj.price = 0
          // timeDataSource.map((item: any) => {
          //   if (item.price) {
          //     ListObj.num += item.num
          //     ListObj.price += item.price * 1
          //   }
          // })
          // // 设置产品信息
          // dataSource[timeIndex[0]].num = 0
          // dataSource[timeIndex[0]].price = 0
          // dataSource[timeIndex[0]].enterDateList.map((item: any) => {
          //   if (item.num != undefined) {
          //     dataSource[timeIndex[0]].num += item.num
          //     dataSource[timeIndex[0]].price += item.price
          //   }
          // })
          // setDataSource(dataSource)
          // setUseVisible(false);
          if (selectDateList.length == 0) {
            message.info('请选择目标日期');
          } else {
            // 源日期
            const sourceDate = dataSource[timeIndex[0]].enterDateList[timeIndex[1]].detail;

            dataSource[timeIndex[0]].enterDateList.map((item: any) => {
              // 目标日期
              if (selectDateList.filter((itemS: any) => itemS == item.enterStartTime).length > 0) {
                console.log(item.enterStartTime);
                sourceDate.map((itemD1: any) => {
                  item.detail.map((itemD2: any) => {
                    // 对号入座
                    if (
                      itemD2.timeBeginTime == itemD1.timeBeginTime &&
                      itemD2.timeEndTime == itemD1.timeEndTime
                    ) {
                      itemD2.num = itemD1.num;
                      itemD2.price = itemD1.price;
                    }
                  });
                });
              }
            });

            // 小计
            dataSource[timeIndex[0]].enterDateList.map((ListObj: any) => {
              // // 记录分时配置
              // const ListObj = dataSource[timeIndex[0]].enterDateList[timeIndex[1]]
              // ListObj.detail = timeDataSource
              // 设置入园时段信息
              ListObj.num = 0;
              ListObj.price = 0;
              ListObj.detail.map((item: any) => {
                if (item.price) {
                  ListObj.num += item.num;
                  ListObj.price += item.price * 1;
                }
              });
              ListObj.price = parseFloat((ListObj.price * 1).toFixed(2));
              // 设置产品信息
              dataSource[timeIndex[0]].num = 0;
              dataSource[timeIndex[0]].price = 0;
              dataSource[timeIndex[0]].enterDateList.map((item: any) => {
                if (item.num != undefined) {
                  dataSource[timeIndex[0]].num += item.num;
                  dataSource[timeIndex[0]].price += parseFloat((item.price * 1).toFixed(2));
                }
              });
              dataSource[timeIndex[0]].price = parseFloat(
                (dataSource[timeIndex[0]].price * 1).toFixed(2),
              );
            });

            // 总计
            let allPrice = 0;
            dataSource.map((item: any) => {
              if (item.price) allPrice += item.price * 1;
            });
            setDataSource(dataSource);
            setPayAmount(round(allPrice, 2));
            setUseVisible(false);
          }
        }}
      >
        <DatePicker.RangePicker disabledDate={disabledDate} onChange={onChangeDate} />
      </Modal>
      {/* 分时库存 */}
      <ProModal
        {...modalState}
        fullTitle="分时预约退货数量"
        columns={modalColumns}
        layout="horizontal"
        dataSource={modalData}
        onFinish={async (v) => {
          setDataSource((list: any) => {
            list[dataIndex] = { ...list[dataIndex], ...v };
            setTotal(list);
            return list;
          });
          return true;
        }}
      />
      {/* 设置库存 */}
      <ProModal
        {...modalSetState}
        fullTitle="编辑退货数量"
        columns={modalSetColumns}
        layout="horizontal"
        dataSource={modalSetData}
        onFinish={async (v) => {
          const obj = structuredClone(valueObj);
          let dateList = [];
          if (v.dateType == 'date') {
            dateList = v.dateList;
          } else {
            let nowDate = dayjs(v.dateRange[0]);
            do {
              if (v.dateWeek.includes(String(nowDate.day()))) {
                dateList.push(nowDate.format('YYYY-MM-DD'));
              }
              nowDate = nowDate.add(1, 'day');
            } while (!nowDate.isAfter(dayjs(v.dateRange[1])));
          }
          dateList.forEach((d: string) => {
            v.timeShareId.forEach((t: string, i: number) => {
              if (obj[d]?.[t]) {
                obj[d][t].purchaseNum = timeTableData[i].purchaseNum;
                obj[d][t].purchaseAmount = timeTableData[i].purchaseAmount;
              }
            });
          });
          let [num, price] = [0, 0];
          const timeLaws = Object.keys(obj).map((item1) => ({
            timeShareData: item1,
            timeShareDateList: Object.values(obj[item1]),
          }));
          timeLaws.forEach(({ timeShareDateList }: any) => {
            timeShareDateList.forEach(({ purchaseNum, purchaseAmount }: any) => {
              if (purchaseNum) num += purchaseNum;
              if (purchaseAmount) price += +purchaseAmount;
            });
          });
          setModalData((v: any) => ({
            ...v,
            timeLaws,
            num,
            price,
          }));
          return true;
        }}
      />
    </div>
  );
};

export default Purchase;
