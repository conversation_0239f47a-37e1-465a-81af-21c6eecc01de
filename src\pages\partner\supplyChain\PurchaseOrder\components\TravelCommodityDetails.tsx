/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-08 17:56:08
 * @LastEditTime: 2022-09-14 17:47:19
 * @LastEditors: zhang<PERSON><PERSON>i
 */
import DetailsPop from '@/common/components/DetailsPop';
import IssueRuleDetails from '@/common/components/RuleDetails/IssueRuleDetails';
import { useTypeEnum } from '@/common/utils/enum';
import { getTravelCardGoodsInfo } from '@/services/api/ticket';
import { Space, Tag } from 'antd';
import { isEmpty, isNil } from 'lodash';
import { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';

const TravelDetail = ({ id, travelCardItem }: any) => {
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>({ id: '', isEnable: 0 });

  const detailColumns: any[] = [
    {
      title: '基础信息',
      columns: [
        {
          title: '所属权益卡名称',
          key: 'travelCardId',
          fieldProps: { disabled: true },
          render: () => travelCardItem.travelCardName,
        },
        {
          title: '商品名称',
          dataIndex: 'goodsName',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '使用方式',
          dataIndex: 'useType',
          valueEnum: useTypeEnum,
        },
        {
          title: '',
          dataIndex: 'effectiveTime',
          render: (dom: any) => (
            <span>
              生效日期起<span className="margin-left-small margin-right-small"> {dom ?? '-'}</span>
              天内有效（若填 1 天内有效则表示生效当天有效）
            </span>
          ),
        },
        {
          title: '',
          dataIndex: 'renewBeginTime',
          render: (dom: any) => (
            <span>
              购买之后 <span className="margin-left-small margin-right-small"> {dom ?? '-'}</span>
              天可续
            </span>
          ),
        },
        {
          title: '',
          dataIndex: 'renewEndTime',
          render: (dom: any) => (
            <span>
              失效 <span className="margin-left-small margin-right-small"> {dom ?? '-'}</span>
              天内可续
            </span>
          ),
        },
      ],
    },
    {
      title: '分时信息',
      columns: [
        {
          title: '分时预约',
          dataIndex: 'timeShareVoList',
          render: (_, { timeShareVoList }: any) => (isEmpty(timeShareVoList) ? '关' : '开'),
        },
        {
          title: '分时时段',
          dataIndex: 'timeShareVoList',
          render: (_, { timeShareVoList }: any) => {
            return timeShareVoList
              ? timeShareVoList.map((item: any) => (
                  <Tag key={item.id} style={{ marginBottom: 12 }}>
                    {[item.beginTime, item.endTime].join('-')}
                  </Tag>
                ))
              : '-';
          },
        },
      ],
    },
    {
      title: '价格信息',
      columns: [
        {
          title: `商品折扣率（%）`,
          renderText: (text: any, entity: any) => {
            const { overallDiscount, marketPrice } = entity;
            const discountPrice = marketPrice * (overallDiscount / 100);
            return (
              <Space direction="vertical">
                <span>{!isNil(overallDiscount) ? `${overallDiscount}%` : '-'}</span>
                <span>{`${marketPrice}*${overallDiscount}%=${discountPrice}`}</span>
              </Space>
            );
          },
        },
        {
          title: '分销折扣区间（%）',
          render: (_, entity: any) => {
            const { marketPrice, endDiscount, overallDiscount, beginDiscount } = entity;
            const discountPrice = marketPrice * (overallDiscount / 100);

            return (
              <Space direction="vertical">
                <span>{!isNil(beginDiscount) ? `${beginDiscount}% ~ ${endDiscount}%` : '-'}</span>
                <span>
                  {`${((beginDiscount / 100) * discountPrice).toFixed(2)}
                     ~
                     ${((endDiscount / 100) * discountPrice).toFixed(2)}`}
                </span>
              </Space>
            );
          },
        },
      ],
    },
    {
      title: '权益信息',
      columns: [
        {
          title: '是否首次创建权益',
          dataIndex: 'isFirst',
          valueType: 'select',
          valueEnum: {
            0: { text: '否' },
            1: { text: '是' },
          },

          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '权益名称',
          dataIndex: 'rightsName',
          formItemProps: { rules: [{ required: true }] },
        },
      ],
    },
    {
      title: '规则信息',
      columns: [
        {
          title: '出票规则',
          dataIndex: 'issueName',
          renderText: (dom: any, entity: any) => (
            <a
              onClick={() => {
                IssueRuleDetails.show(entity.issueId);
              }}
            >
              {dom}
            </a>
          ),
        },
      ],
    },
    {
      title: '说明信息',
      columns: [
        {
          title: '一些附加的说明信息',
          width: 688,
          dataIndex: 'remark',
          valueType: 'textarea',
        },
      ],
    },
  ];

  const init = async (e: string) => {
    setLoading(true);
    setDetailsVisible(true);
    const { data } = await getTravelCardGoodsInfo({ id: e });
    data.useType += '';
    setDataSource(data);
    setLoading(false);
  };
  useEffect(() => {
    init(id);
  }, [id]);

  return (
    <DetailsPop
      limit={{
        openClose: 'canTicketGoodSet_openClose',
        delete: 'canTicketGoodSet_delete',
        edit: 'canTicketGoodSet_edit',
      }}
      title="商品详情"
      visible={detailsVisible}
      isLoading={isLoading}
      setVisible={setDetailsVisible}
      columnsInitial={detailColumns}
      dataSource={dataSource}
    />
  );
};

TravelDetail.show = (id: string, entity) => {
  const detailBox = document.createElement('div');
  document.body.appendChild(detailBox);

  const root = createRoot(detailBox);
  root.render(<TravelDetail id={id} travelCardItem={entity} />);
};

export default TravelDetail;
