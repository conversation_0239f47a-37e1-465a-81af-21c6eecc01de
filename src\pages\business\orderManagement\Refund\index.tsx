import ChainModal from '@/common/components/ChainModal';
import Export, { columnsSet } from '@/common/components/Export';
import useExport from '@/common/components/Export/useExport';
import Tag from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import { chainStatusEnum, orderTypeEnum, payTypeEnum, saleChannelEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { formatTime } from '@/common/utils/tool';
import Blockchain from '@/components/Blockchain';
import useModal from '@/hooks/useModal';
import { apiOrderCustomDetail, apiOrderCustomList } from '@/services/api/erp';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Typography } from 'antd';
import { pick } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { useModel } from '@umijs/max';
import OrderManagement from '../Sale/OrderManagement';
import RefundManagement from './RefundManagement';
import TemporalInterval from './TemporalInterval';
import styles from './index.less';

const { Link } = Typography;
interface ScenicAreaDefault {
  value: any;
  flag: boolean;
  setStatus: boolean;
  resStatus: boolean;
  inItTicketInfo: [];
  particularData: [];
  orderParticulaStatus: [];
  timePars: number;
  setUnsubscribeMessage: [];
}

function Refund(param: ScenicAreaDefault) {
  //票务详情
  const [inItTicketInfo, setTicketInfo] = useState([]);
  //退订信息
  const [unsubscribeMessage, setUnsubscribeMessage] = useState([]);
  //当前订单详情
  const [particularData, setparticularData] = useState([]);
  //订单详情状态
  const [orderParticulaStatus, setOrderParticularStatus] = useState([]);
  //退订单 ID
  const [backOrderId, setbackOrderId] = useState(undefined);
  //服务商 ID
  const { initialState }: any = useModel('@@initialState');

  const { coId } = initialState.currentCompany;

  const actionRef = useRef<ActionType>();

  const detailModal = useModal();
  const [currentRow, setCurrentRow] = useState<Record<string, any>>();

  const [resStatus, setResStatus] = useState(false);

  //获取当前订单详情列表
  const getOrderCustomDetail = async (orderId: any) => {
    try {
      const res = await apiOrderCustomDetail(orderId);
      addOperationLogRequest({
        action: 'info',
        content: `查看退单【${orderId}】`,
      });
      setResStatus(true);
      // const res = await apiOderPageParticularsList();
      console.log('订单详情', res);
      const { data } = res;
      const { order, ticketInfoList, orderStatus, orderRefund }: any = data;
      const { data: val } = ticketInfoList;
      // const { order, ticketInfo, orderStatus }: any = res;

      setparticularData(order);
      setTicketInfo(val);
      setOrderParticularStatus(orderStatus);
      setUnsubscribeMessage(orderRefund);
    } catch (e) {
      console.error(e);
      return {};
    }
  };

  //绑定详情订单状态
  const [setStatus, showStatus] = useState(false);
  const [setStatus2, showStatus2] = useState(false);
  //订单编号绑定事件
  const titleNumberClick = (orderId: any, backOrderId2: any) => {
    getOrderCustomDetail(orderId);
    setbackOrderId(backOrderId2);
    showStatus(true);
  };
  const titleNumberClick2 = (orderId: any) => {
    getOrderCustomDetail(orderId);
    showStatus2(true);
  };
  //订单详情取消按钮
  const onClose = (flag: boolean) => {
    showStatus(flag);
    // setQRcodestatus(flag);
  };
  const onClose2 = (flag: boolean) => {
    showStatus2(flag);
    // setQRcodestatus(flag);
  };
  //获取时间状态
  const [searchType, setSearchType] = useState(0);

  const timeNume = (value: any) => {
    console.log('时间参数', value);
    setSearchType(value);
  };

  //退订管理信息
  const columns: ProColumns<any>[] = [
    {
      title: '退单号',
      dataIndex: 'refundId',
      fixed: 'left',
      width: 150,
      ellipsis: true,
      renderText: (text, record) => (
        <Link onClick={() => titleNumberClick(record.orderId, record.refundId)}>{text}</Link>
      ),
    },
    {
      title: '订单号',
      dataIndex: 'orderId',
      width: 150,
      fixed: 'left',
      ellipsis: true,
      renderText: (dom: any, record: any) => (
        <a
          onClick={() => {
            setCurrentRow(record);
            detailModal.setVisible(true);
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: '服务商',
      width: 150,
      dataIndex: 'serviceProviderName',
      search: false,
    },
    {
      title: '代理商',
      width: 150,
      dataIndex: 'distributorName',
      search: false,
    },
    {
      title: '申请日期',
      width: 150,
      dataIndex: 'createTime',
      search: false,
      render: (dom: any, record: any) => {
        return formatTime(dom);
      },
    },
    {
      title: '结算单号',
      width: 150,
      ellipsis: true,
      dataIndex: 'tradeNo',
      search: false,
    },
    {
      title: '售票终端类型',
      dataIndex: 'sourceType',
      width: 150,
      render: (dom: any) => saleChannelEnum[dom],
      hideInSearch: true,
    },
    {
      title: '售票设备名称',
      dataIndex: 'equipmentName',
      search: false,
    },

    {
      title: '支付方式',
      dataIndex: 'payType',
      width: 100,
      search: false,
      valueEnum: payTypeEnum,
    },
    {
      title: '下单时间',
      width: 150,
      dataIndex: 'createTime2',
      search: false,
      render: (dom: any, record: any) => {
        return formatTime(record.createTime);
      },
    },

    {
      title: '退款状态',
      width: 100,
      dataIndex: 'refundStatus',
      valueEnum: pick(orderTypeEnum, ['52', '50', '51']),
    },
    {
      title: '登录账号',
      width: 150,
      dataIndex: 'username',
      search: false,
    },
    {
      title: '佣金（元）',
      width: 100,
      dataIndex: 'commissionAmount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '订单金额（元）',
      width: 100,
      dataIndex: 'totalAmount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '退款金额（元）',
      width: 100,
      dataIndex: 'refundAmount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '交易上链',
      width: 100,
      dataIndex: 'isChainOrderRefund',
      valueEnum: chainStatusEnum,
      renderText: (dom) => <Tag type="chainStatus" value={dom} />,
    },
    {
      title: '操作',
      dataIndex: '_option',
      width: 80,
      valueType: 'option',
      fixed: 'right',
      render: (_, entity: any) => {
        return (
          <>
            {entity.txId ? (
              <ChainModal
                chainData={{
                  txId: entity.txId,
                }}
              />
            ) : (
              '-'
            )}
          </>
        );
      },
    },
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (date) => {
          return {
            startTime: date[0],
            endTime: date[1],
          };
        },
      },
      renderFormItem: () => {
        return <TemporalInterval timeNume={timeNume} />;
      },
    },
  ];
  const getOrderCustomList = async (params: any) => {
    const pars = { ...params, searchType: searchType, merchantId: coId };
    try {
      const {
        data: { records, total },
      }: any = await apiOrderCustomList(pars);
      records.forEach((item: any, index: any) => {
        item.key = index;
        // records.push(records[index])
      });
      return {
        data: records,
        success: true,
        total: total,
      };
    } catch (e) {
      console.error(e);
      return {
        success: false,
      };
    }
  };

  //查看区块链交易数据
  const [vray, setVray] = useState(false); //渲染
  const [cochainVisible, setCochainVisible] = useState(false);
  const [cochainDataDataSource, setCochainDataDataSource] = useState<any>({});
  const cochainColumns = [
    {
      title: '区块链账号',
      columns: [
        {
          title: '发行方',
          dataIndex: 'fromAccount',
          span: 5,
          render: (dom: any) => dom.length > 0 && dom.join('、'),
        },
        {
          title: '接收方',
          dataIndex: 'issuerAccount',
          span: 5,
          render: (dom: any) => dom.length > 0 && dom.join('、'),
        },
      ],
    },
    {
      title: '存证信息',
      columns: [
        {
          title: '存证时间',
          dataIndex: 'savedAt',
          span: 5,
        },
        {
          title: `存 证 号`,
          dataIndex: 'certId',
          span: 5,
        },

        {
          title: '存证内容',
          dataIndex: 'certContent',
          span: 5,
          render: (dom: any) => {
            return (
              <>
                <div className={!vray ? styles.dom : ''}>{dom}</div>
                <div
                  style={{ textAlign: 'right', cursor: 'pointer', color: '#1890ff' }}
                  onClick={() => setVray(!vray)}
                >
                  {vray ? '收起 ' : '展开 '}
                  {vray ? <UpOutlined /> : <DownOutlined />}
                </div>
              </>
            );
          },
        },
        {
          title: '交易哈希',
          dataIndex: 'txId',
          span: 5,
        },
      ],
    },
  ];

  useEffect(() => {
    // setTicketInfo(inItTicketInfo);
  }, []);
  const exportState = useExport({
    columns,
    modulePath: 'E-commerce_RefundOrderManage',
    params: { searchType: searchType, merchantId: coId },
  });

  return (
    <div>
      <>
        {/* <Qrcode flag={QRcodestatus} onClose={onClose} QRcodestatusID={QRcodestatusID} particularData={particularData} /> */}
        <>
          <ProTable
            {...tableConfig}
            columns={columns}
            actionRef={actionRef}
            pagination={{
              defaultPageSize: 10,
            }}
            request={getOrderCustomList}
            formRef={exportState.formRef}
            columnsState={columnsSet(exportState)}
            toolBarRender={() => [<Export key="export" {...exportState} />]}
            rowKey="refundId"
            // search={{
            //   labelWidth: 120,
            //   collapseRender: false,
            //   collapsed: false,
            // }}
            // options={{ setting: false, density: false }}
            // scroll={{ x: 1300 }}
            // scroll={{ x: 'max-content' }}
          />

          {/* 查看区块链交易记录 */}
          <Blockchain
            cochainVisible={cochainVisible}
            setCochainVisible={setCochainVisible}
            cochainColumns={cochainColumns}
            cochainDataDataSource={cochainDataDataSource}
          />
          {/* 订单详情 */}
          <OrderManagement modalState={detailModal} currentRow={currentRow} />
          {/* 退订详情 */}
          <RefundManagement
            flag={setStatus}
            resStatus={resStatus}
            onClose={onClose}
            orderParticular={particularData}
            inItTicketInfoData={inItTicketInfo}
            unsubscribeMessageData={unsubscribeMessage}
            backOrderId={backOrderId}
            orderParticulaStatus={orderParticulaStatus}
            // countNum={1}
          />
        </>
      </>
    </div>
  );
}
export default Refund;
