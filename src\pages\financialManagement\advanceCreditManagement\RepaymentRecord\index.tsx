import { tableConfig } from '@/common/utils/config';
import { repaymentStatusEnum } from '@/common/utils/enum';
import { precreditRepayments } from '@/services/api/precredit';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Popover } from 'antd';
import { useModel, useRequest } from '@umijs/max';

export default () => {
  const {
    initialState: {
      currentCompany: { settlementId },
    },
  }: any = useModel('@@initialState');
  const tableReq = useRequest(precreditRepayments, {
    manual: true,
    formatResult(res) {
      return {
        data: res.data.data,
        success: res.code == 20000,
        total: res.data.total,
      };
    },
  });
  const columns: ProColumns[] = [
    {
      title: '还款ID',
      dataIndex: 'id',
    },
    {
      title: '收款方名称',
      dataIndex: 'creditorName',
    },
    {
      title: '付款方名称',
      dataIndex: 'debtorName',
    },
    {
      title: '交易号',
      dataIndex: 'tradeNo',
      renderText: (text) => text.split('-').at(-2),
    },
    {
      title: '总期数',
      dataIndex: 'totalPeriods',
    },
    {
      title: '当前期数',
      dataIndex: 'periods',
    },
    {
      title: '总还款金额',
      dataIndex: 'totalRepayment',
      valueType: 'money',
    },
    {
      title: '还款金额',
      dataIndex: 'amount',
      valueType: 'money',
    },
    {
      title: '交易哈希',
      dataIndex: 'hash',
      renderText: (text) => (
        <Popover content={text}>{text ? text.slice(0, 20) + (text[20] ? '...' : '') : '-'}</Popover>
      ),
    },
    {
      title: '还款日期',
      dataIndex: 'repaymentDate',
      valueType: 'date',
    },
    {
      title: '还款状态',
      dataIndex: 'repaymentStatus',
      valueEnum: repaymentStatusEnum,
    },
  ];
  return (
    <ProTable
      {...tableConfig}
      columns={columns}
      search={false}
      params={{ settlementId }}
      request={tableReq.run}
    />
  );
};
