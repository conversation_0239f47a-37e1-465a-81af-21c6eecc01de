/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-14 11:08:17
 * @LastEditTime: 2025-07-25 14:21:53
 * @LastEditors: 李悍宇 zhou<PERSON>@hqyatu.com
 */
import { tableConfig } from '@/common/utils/config';
import { ProTable } from '@ant-design/pro-table';
import { InputNumber, message, Space, Tag } from 'antd';
import { useEffect, useState } from 'react';
import { useModel } from 'umi';

const GoodsTable = ({
  products,
  setProduct,
  index,
  actionDeleteProductConfig,
  actionRef,
  actionSaveProductConfig,
  // 新增批量编辑相关属性
  batchEditing,
  newAddedBatchIds,
  clearNewBatch,
}) => {
  const goodsDetail = products[index]?.batchData || [];
  const { initialState } = useModel('@@initialState');
  const { coId = '' } = initialState?.currentCompany || {};

  // 状态管理当前编辑的批次ID
  const [editingBatchId, setEditingBatchId] = useState<string | null>(null);
  const [editingBatchIds, setEditingBatchIds] = useState<string[]>([]);

  // 保存原始价格
  const [originalPrices, setOriginalPrices] = useState<Record<string, number>>({});

  // 处理单个保存（同步更新父组件数据源）
  const handleSave = async (record: any) => {
    if (!(record.productAuthorizeMigrateSalePrice > 0)) {
      message.info('请输入正确的销售价');
      return;
    }
    try {
      setEditingBatchId(null);
      const updatedBatch = {
        ...record,
        productId: products[index].ticketProductId,
        salePrice: record.productAuthorizeMigrateSalePrice,
        configGoodsDetail: [
          {
            goodsId: products[index].ticketGoodsId,
            goodsName: products[index].ticketGoodsName,
          },
        ],
      };

      // 调整为传入数组格式
      await actionSaveProductConfig([updatedBatch]);
      setEditingBatchIds((prev) => prev.filter((id) => id !== record.batchId));

      // 清除新增标记
      if (newAddedBatchIds.has(record.batchId)) {
        clearNewBatch(record.batchId);
      }
    } catch (error) {
      // ...错误处理
    }
  };

  // 处理编辑操作
  const handleEdit = (record: any) => {
    // 保存原始价格
    setOriginalPrices((prev) => ({
      ...prev,
      [record.batchId]: record.productAuthorizeMigrateSalePrice,
    }));

    // 添加批次到编辑状态
    if (!editingBatchIds.includes(record.batchId)) {
      setEditingBatchIds((prev) => [...prev, record.batchId]);
    }
  };

  // 取消编辑
  const handleCancel = (record: any) => {
    // 还原原始价格
    setProduct((prev) => {
      const newProducts = [...prev];
      const batchIndex = newProducts[index].batchData.findIndex(
        (b) => b.batchId === record.batchId,
      );

      if (batchIndex !== -1) {
        newProducts[index].batchData[batchIndex].productAuthorizeMigrateSalePrice =
          originalPrices[record.batchId];
      }
      return newProducts;
    });

    // 从编辑状态中移除
    setEditingBatchIds((prev) => prev.filter((id) => id !== record.batchId));

    // 如果是新增批次，清除标记
    if (newAddedBatchIds.has(record.batchId)) {
      clearNewBatch(record.batchId);
    }
  };
  // 处理删除操作
  const handleDelete = async (record: any) => {
    if (record.isExchange === 1 && record.availableNumber != 0) {
      message.warning('该批次是交易所数字资产，无法删除');
      return;
    }
    if (actionDeleteProductConfig(record)) {
      message.success('删除成功');
      actionRef.current?.reload();
    }
  };

  useEffect(() => {
    // 找到所有新增批次中属于当前商品的
    const currentNewBatches = goodsDetail.filter((batch) => newAddedBatchIds.has(batch.batchId));

    // 如果有新增批次
    if (currentNewBatches.length > 0) {
      // 保存所有新增批次的原始价格
      setOriginalPrices((prev) => {
        const newPrices = { ...prev };
        currentNewBatches.forEach((batch) => {
          newPrices[batch.batchId] = batch.productAuthorizeMigrateSalePrice;
        });
        return newPrices;
      });

      // 将所有新增批次添加到编辑状态
      const newBatchIds = currentNewBatches.map((batch) => batch.batchId);
      setEditingBatchIds((prev) => [...new Set([...prev, ...newBatchIds])]);
    }
  }, [newAddedBatchIds]);

  useEffect(() => {
    if (!batchEditing) {
      // 批量编辑结束时，清除所有编辑状态
      setEditingBatchIds([]);
    }
  }, [batchEditing]);

  // 检查批次是否在编辑中
  const isBatchEditing = (batchId: string) => editingBatchIds.includes(batchId);

  const columns = [
    {
      title: '库存批次号',
      dataIndex: 'batchId',
      render: (dom, record: any) => (
        <Space>
          <span>
            {dom}
            {record.isExchange == 1 && (
              <>
                {' '}
                <Tag color="blue">交易所</Tag>
              </>
            )}
          </span>
        </Space>
      ),
    },
    {
      title: '购买有效时间',
      dataIndex: 'purchaseTime',
      render: (_, record) => (
        <span>{`${record.purchaseBeginTime}至${record.purchaseEndTime}`}</span>
      ),
    },
    {
      title: '入园有效时间',
      dataIndex: 'dayTime',
      render: (_, record) => <span>{`${record.dayBegin}至${record.dayEnd}`}</span>,
    },
    {
      title: '可用库存',
      dataIndex: 'availableNumber',
    },
    {
      title: '采购价（元）',
      dataIndex: 'purchasePrice',
      fixed: 'right',
      hideInSearch: true,
      renderText: (dom) => {
        const items = dom.map((price: any) => {
          const p = parseFloat(price) ? parseFloat(price).toFixed(2) : price;
          return <div>{p}</div>;
        });
        return <div>{items.length > 0 ? items : '-'}</div>;
      },
    },
    {
      title: '销售价（元）',
      dataIndex: 'productAuthorizeMigrateSalePrice',
      fixed: 'right',
      render: (text, record) => {
        // 批量编辑模式...
        if (batchEditing) {
          return (
            <InputNumber
              min={0}
              step={0.01}
              precision={2}
              value={text}
              // onChange={(value) => {
              //   // 更新父组件数据源（同步影响父表格）
              //   setProduct((prev) => {
              //     const newProducts = [...prev];
              //     const batchIndex = newProducts[index].batchData.findIndex(
              //       (b) => b.batchId === record.batchId,
              //     );

              //     if (batchIndex !== -1) {
              //       newProducts[index].batchData[batchIndex].productAuthorizeMigrateSalePrice =
              //         value;
              //     }
              //     return newProducts;
              //   });
              // }}
              // disabled={batchSaving}
              disabled={true}
              style={{ width: 120 }}
            />
          );
        }

        // 新增批次或正在编辑的批次
        const isNewBatch = newAddedBatchIds.has(record.batchId);
        const isEditing = isBatchEditing(record.batchId);

        if (isNewBatch || isEditing) {
          return (
            <InputNumber
              min={0}
              step={0.01}
              precision={2}
              value={text == '-' ? null : text}
              onChange={(value) => {
                setProduct((prev) => {
                  const newProducts = [...prev];
                  const batchIndex = newProducts[index].batchData.findIndex(
                    (b) => b.batchId === record.batchId,
                  );

                  if (batchIndex !== -1) {
                    newProducts[index].batchData[batchIndex].productAuthorizeMigrateSalePrice =
                      value;
                  }
                  return newProducts;
                });
              }}
              autoFocus={editingBatchIds[0] === record.batchId} // 第一个编辑框自动聚焦
            />
          );
        }
        return text;
      },
    },
    {
      width: 120,
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_, record) => {
        if (batchEditing) return null;

        const isNew = newAddedBatchIds.has(record.batchId);
        const isEditing = isBatchEditing(record.batchId);

        return (
          <Space size="large">
            {isNew || isEditing ? (
              <>
                <a key="save" onClick={() => handleSave(record)}>
                  {isNew ? '保存新增' : '保存'}
                </a>
                {!isNew && ( // 新增批次不允许取消
                  <a key="cancel" onClick={() => handleCancel(record)}>
                    取消
                  </a>
                )}
              </>
            ) : (
              <>
                <a key="editable" onClick={() => handleEdit(record)}>
                  编辑
                </a>
                <a key="delete" style={{ color: 'red' }} onClick={() => handleDelete(record)}>
                  删除
                </a>
              </>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <ProTable
      {...tableConfig}
      columns={columns}
      rowKey="batchId"
      dataSource={goodsDetail}
      search={false}
      options={false}
      pagination={false}
    />
  );
};

export default GoodsTable;
