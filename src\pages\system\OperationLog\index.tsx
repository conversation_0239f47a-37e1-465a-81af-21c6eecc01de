import { tableConfig } from '@/common/utils/config';
import { modelWidth } from '@/common/utils/gConfig';
import {
  actionType,
  addOperationLogRequest,
  deepMapRoutes,
  findNamePathByID,
} from '@/common/utils/operationLog';
import { transformArrToString } from '@/common/utils/tool';
import { apiSensorLogList } from '@/services/api/settings';
import type { API } from '@/services/api/typings';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import React, { useMemo, useState } from 'react';
import { useModel } from '@umijs/max';
import Routes from '../../../../config/routes';
import { getEnv } from '@/common/utils/getEnv';

const TableList: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [visible, setVisible] = useState(false);
  const { coId } = initialState?.currentCompany || {};
  const appId = getEnv().APPID;

  const [currentRow, setCurrentRow] = useState<API.OperationLogItem>();
  const getSensorLogList = async (params: any) => {
    const _params = {
      ...params,
      module: transformArrToString(params?.module),
      function: transformArrToString(params?.function),
    };
    const { data } = await apiSensorLogList(_params);
    return {
      total: data.total || 0,
      data: data?.records || [],
    };
  };

  //  生成树
  const operationMenuTree = useMemo(() => deepMapRoutes(Routes), []);
  const columns: ProColumns<API.OperationLogItem>[] = [
    {
      title: '操作时间',
      dataIndex: 'time',
      valueType: 'dateTimeRange',
      hideInTable: true,
      search: {
        transform: (value: any) => ({ startTime: value[0], endTime: value[1] }),
      },
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      search: false,
      renderText: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '账号',
      dataIndex: 'creator',
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
    },
    {
      title: '操作模块',
      dataIndex: 'module',
      hideInTable: true,
      valueType: 'treeSelect',
      fieldProps: {
        treeCheckable: true,
        options: operationMenuTree,
        placeholder: '请输入',
        allowClear: true,
        treeDefaultExpandedKeys: ['xU9qK5pJ'],
        fieldNames: {
          label: 'title',
        },
        maxTagCount: 3,
      },
    },
    {
      title: '操作模块',
      search: false,
      dataIndex: 'module',
      renderText: (text) => findNamePathByID(operationMenuTree || [], text),
    },
    {
      title: '设备 IP',
      dataIndex: 'deviceIp',
    },
    {
      title: '操作类型',
      dataIndex: 'function',
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: actionType,
    },
    {
      title: '操作内容',
      dataIndex: 'content',
      // width: 400,
      ellipsis: true,
      search: false,
      // renderText: (text) =>|| '-',
    },
    {
      title: '操作详情',
      valueType: 'option',
      key: 'operation',
      render: (_, record) => {
        if (isEmpty(JSON.parse(record.contentDetails || '[]'))) {
          return <>--</>;
        }
        return (
          <a
            onClick={() => {
              setCurrentRow(record);
              setVisible(true);
              addOperationLogRequest({
                action: 'info',
                content: `查看【${record.content}】详情`,
              });
            }}
          >
            查看
          </a>
        );
      },
    },
  ];

  const logColumns: ProColumns<API.OperationLogItem>[] = [
    {
      dataIndex: 'key',
      hideInTable: true,
    },
    {
      title: '字段',
      dataIndex: 'name',
    },
    {
      title: '编辑前',
      dataIndex: 'before',
    },
    {
      title: '编辑后',
      dataIndex: 'after',
    },
  ];

  return (
    <>
      <ProTable
        {...tableConfig}
        rowKey="id"
        params={{
          project: coId,
          app: appId,
        }}
        request={getSensorLogList}
        columns={columns}
      />
      <Modal
        title="操作详情"
        width={modelWidth.md}
        open={visible}
        onCancel={() => setVisible(false)}
        footer={false}
      >
        <ProTable
          rowKey="key"
          dataSource={JSON.parse(currentRow?.contentDetails || '[]')}
          columns={logColumns}
          pagination={false}
          search={false}
          options={false}
          bordered
        />
        {/* 待处理数据：{currentRow && currentRow.contentDetails} */}
      </Modal>
    </>
  );
};

export default TableList;
