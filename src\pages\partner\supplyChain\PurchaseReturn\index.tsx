import ChainModal from '@/common/components/ChainModal';
import Export, { columnsSet } from '@/common/components/Export';
import useExport from '@/common/components/Export/useExport';
import ProModal from '@/common/components/ProModal';
import {
  default as useModal,
  default as useProModal,
} from '@/common/components/ProModal/useProModal';
import Tags from '@/common/components/Tag';
import TimeStore from '@/common/components/TimeStore';
import { tableConfig } from '@/common/utils/config';
import {
  chainStatusEnum,
  orderTypeEnum,
  productTypeEnum,
  ticketTypeEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { formatTime, getUniqueId } from '@/common/utils/tool';
import Blockchain from '@/components/Blockchain';
import {
  apiCancellationOrder,
  apiDistributeRefundInfo,
  apiPurchaseRefundOrderPageList,
  getUnifyPullDown,
} from '@/services/api/distribution';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Card, List, Modal, Steps, Table, Tag, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';

const TableList: React.FC = () => {
  // 分时库存
  const timeModalState = useProModal();
  const [timeModalData, setTimeModalData] = useState<any>({});
  const [lawsList, setLawsList] = useState<any>([]);
  const lawsColumns = [
    {
      title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join('-')}`,
      render: () => {},
    },
    {
      title: () => '退货数量/采购数量',
      render: ({ purchaseNum = 0, stockAmount = 0 }) => `${purchaseNum}/${stockAmount}`,
    },
    {
      title: () => '退货金额（元）',
      render: ({ purchaseAmount = 0 }) => purchaseAmount,
    },
  ];
  const timeModalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '入园有效时间',
          valueType: 'dateRange',
          dataIndex: 'dateRange',
          initialValue: [timeModalData.enterStartTime, timeModalData.enterEndTime],
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '',
          dataIndex: 'timeLaws',
          colProps: { span: 24 },
          renderFormItem: () => <TimeStore lawsColumns={lawsColumns} lawsList={lawsList} />,
        },
        {
          title: '总退货数量',
          dataIndex: 'num',
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: ' 总退货金额（元）',
          dataIndex: 'price',
          fieldProps: {
            disabled: true,
          },
        },
      ],
    },
  ];

  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany;

  const [orderId, setOrderId] = useState('');
  const [counts, setCounts] = useState(0);
  const modalState = useModal();

  const getAllSuppliersReq = useRequest(getUnifyPullDown, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return res.data.map((name: any) => {
        return {
          value: name,
          label: name,
        };
      });
    },
  });

  const closeOrder = async () => {
    message.success('取消订单成功');
    setDetailsVisible(false);
    try {
      const res = await apiCancellationOrder({ orderId: orderId });
      // console.log('取消订单', res, orderId);
    } catch (e) {
      console.error(e);
    }
  };
  const getUsernameListReq = useRequest(getUnifyPullDown, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return res.data.map((name: any) => {
        return {
          value: name,
          label: name,
        };
      });
    },
  });

  useEffect(() => {
    if (coId) {
      getUsernameListReq.run({ getUnifyPullDown: coId, type: '4' });
      getAllSuppliersReq.run({ getUnifyPullDown: coId, type: '2' });
    }
  }, [coId]);

  // 【表格】数据绑定
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      title: '采购退单号',
      dataIndex: 'refundId',
      fixed: 'left',
    },
    {
      title: '采购订单号',
      dataIndex: 'orderId',
    },

    {
      title: '供应商名称',
      dataIndex: 'sellerName',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getAllSuppliersReq.data,
        showSearch: true,
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'sellerName',
      hideInSearch: true,
    },
    {
      title: '结算单号',
      dataIndex: 'tradeNo',
    },

    {
      title: '退货总金额（元）',
      dataIndex: 'refundAmount',
      hideInSearch: true,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '退货时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      hideInTable: true,
      initialValue: [dayjs().startOf('year'), dayjs().endOf('day')],
      search: {
        transform: (value) => {
          return {
            refundStartTime: value[0],
            refundEndTime: value[1],
          };
        },
      },
    },
    {
      title: '退货时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      render: (dom: any) => dayjs(dom).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作人',
      dataIndex: 'username',
      hideInSearch: true,
    },
    {
      title: '退货总数',
      dataIndex: 'num',
      hideInSearch: true,
    },
    {
      title: '操作人',
      dataIndex: 'username',
      valueType: 'select',
      fieldProps: {
        options: getUsernameListReq.data,
        showSearch: true,
      },
      hideInTable: true,
    },
    {
      title: '退单状态',
      dataIndex: 'refundStatus',
      hideInSearch: true,
      fixed: 'right',
      // valueEnum: orderTypeEnum,
      render: (dom) => <Tags type="orderStatus" value={dom} />,
    },

    {
      title: '交易上链',
      dataIndex: 'isChainOrder',
      valueEnum: chainStatusEnum,
      fixed: 'right',
      renderText: (dom) => <Tags type="chainStatus" value={dom} />,
    },
    {
      title: '操作',
      width: 215,
      dataIndex: '_option',
      valueType: 'option',
      fixed: 'right',
      render: (_, entity) => [
        <a
          key="optionKey"
          onClick={async () => {
            setTxId(entity.txId);
            setOrderId(entity.refundId);
            modalState.setType('info');
            // setDetailsVisible(true);
          }}
        >
          查看
        </a>,
        entity.txId ? (
          <ChainModal
            chainData={{
              txId: entity.txId,
            }}
          />
        ) : (
          ''
        ),
      ],
    },
  ];

  //查看区块链交易数据
  const [txid, setTxId] = useState('');
  const [txIds, setTxIds] = useState<any>([]);
  const [tokenIds, setTokenIds] = useState<any>([]);
  const [cochainVisible, setCochainVisible] = useState(false);
  const [cochainDataDataSource, setCochainDataDataSource] = useState<any>({});
  const cochainColumns = [
    {
      title: '区块链账号',
      columns: [
        {
          title: '发行方',
          dataIndex: 'fromAccount',
          span: 5,
          render: (dom: any) => dom,
        },
        {
          title: '接收方',
          dataIndex: 'toAccount',
          span: 5,
          render: (dom: any) => dom,
        },
      ],
    },
    {
      title: '存证信息',
      columns: [
        {
          title: '存证时间',
          dataIndex: 'transactionAt',
          span: 5,
        },
        {
          title: 'TokenID',
          dataIndex: 'tokenIds',
          span: 5,
          render: (dom: any) => {
            return dom && dom.data.length > 0
              ? dom?.data.map((item: any, index: number) => (
                  <>
                    <span>{item}</span>
                    <br />
                    {index + 1 == dom.data.length && dom.data.length > 1 ? (
                      <span
                        style={{ color: '#1890ff', cursor: 'pointer' }}
                        onClick={() => {
                          const obj = { ...cochainDataDataSource };
                          if (dom.unfold) {
                            obj.tokenIds = {
                              unfold: false,
                              data: cochainDataDataSource.tokenIds.data.slice(0, 5),
                            };
                            setCochainDataDataSource(obj);
                          } else {
                            obj.tokenIds = { unfold: true, data: tokenIds };
                            setCochainDataDataSource(obj);
                          }
                        }}
                      >
                        {dom.unfold ? '收起 ' : '展开 '}
                        {dom.unfold ? <UpOutlined /> : <DownOutlined />}
                      </span>
                    ) : (
                      ''
                    )}
                  </>
                ))
              : '-';
          },
        },
        {
          title: '交易哈希',
          dataIndex: 'txId',
          span: 5,
          render: (dom: any) => {
            return dom && dom.data.length > 0
              ? dom?.data.map((item: any, index: number) => (
                  <>
                    <span>{item}</span>
                    <br />
                    {index + 1 == dom.data.length && dom.data.length > 1 ? (
                      <span
                        style={{ color: '#1890ff', cursor: 'pointer' }}
                        onClick={() => {
                          const obj = { ...cochainDataDataSource };
                          if (dom.unfold) {
                            obj.txId = {
                              unfold: false,
                              data: cochainDataDataSource.txId.data.slice(0, 5),
                            };
                            setCochainDataDataSource(obj);
                          } else {
                            obj.txId = { unfold: true, data: txIds };
                            setCochainDataDataSource(obj);
                          }
                        }}
                      >
                        {dom.unfold ? '收起 ' : '展开 '}
                        {dom.unfold ? <UpOutlined /> : <DownOutlined />}
                      </span>
                    ) : (
                      ''
                    )}
                  </>
                ))
              : '-';
          },
        },
      ],
    },
  ];

  // 【列表】数据绑定
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);

  const columnsInitial = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      hideInSearch: true,
      valueEnum: productTypeEnum,
    },
    {
      title: '入园日期',
      dataIndex: 'dayBegin',
      hideInSearch: true,
      render: (dom: any, entity: any) =>
        entity.noDateList ? (
          <a
            onClick={() => {
              setUnTimeDataSource(entity.noDateList);
              addOperationLogRequest({
                action: 'info',
                content: `查看【${orderId}】入园日期信息`,
              });
              setUnTimeVisible(true);
            }}
          >
            详情
          </a>
        ) : (
          '-'
        ),
    },
    {
      title: '退货总量',
      dataIndex: 'totalNum',
      hideInSearch: true,
    },
    {
      title: '退款金额（元）',
      dataIndex: 'totalAmount',
      hideInSearch: true,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  // 分时数据绑定
  const [timeVisible, setTimeVisible] = useState<any>();
  // 分时序列 [表格序，列表序]
  const [timeTitle, setTimeTitle] = useState<any>();
  const [timeDataSource, setTimeDataSource] = useState<any>();
  // 分时表格配置
  const timeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '分时预约',
      dataIndex: 'timeShare',
      // render: (_, entity) =>
      //   entity.timeBeginTime && entity.timeEndTime ? (
      //     <span>{entity.timeBeginTime + ' 至 ' + entity.timeEndTime}</span>
      //   ) : (
      //     '-'
      //   ),
    },
    // {
    //   title: '当前库存',
    //   dataIndex: 'number',
    // },
    {
      title: '退货数量',
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];
  const { Step } = Steps;
  // 非分时数据绑定
  const [unTimeVisible, setUnTimeVisible] = useState<boolean>(false);
  const [unTimeDataSource, setUnTimeDataSource] = useState<any>();
  const unTimeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '入园日期',
      dataIndex: 'dayBegin',
      hideInSearch: true,
      render: (dom: any, entity: any) => entity.dayBegin + ' 至 ' + entity.dayEnd,
    },
    {
      title: '退货数量',
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];
  const exportState = useExport({
    columns,
    modulePath: 'E-commerce_PurchaseRefundOrder',
    params: { buyerId: coId },
  });
  const columnsRefundInitial: ProColumns[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      render: (dom) => <span>{productTypeEnum[dom]}</span>,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      dataIndex: 'ticketGoodsType',
      render: (dom) => <span>{ticketTypeEnum[dom]}</span>,
    },
    {
      title: '库存批次号',
      width: 180,
      dataIndex: 'batchId',
      render: (dom, record) => (
        <span>
          {dom}
          {record.isExchange == '1' && <Tag color="blue">交易所</Tag>}
        </span>
      ),
    },
    {
      title: '购买有效时间',
      width: 200,
      dataIndex: 'buyStartTime',
      render: (dom, record) => (
        <span>
          {dayjs(record.buyStartTime).format('YYYY-MM-DD')}至
          {dayjs(record.buyEndTime).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: '入园有效时间',
      width: 200,
      dataIndex: 'dayBegin',
      render: (dom, record) => (
        <span>
          {dayjs(record.dayBegin).format('YYYY-MM-DD')}至{dayjs(record.dayEnd).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: '分时时段',
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList?.length
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '市场标准价（元）',
      width: 150,
      dataIndex: 'marketPrice',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '有效时长天数（天）',
      width: 150,
      dataIndex: 'validDay',
    },
    {
      title: '可入园天数',
      dataIndex: 'enterDay',
    },
    {
      title: '使用次数',
      width: 80,
      dataIndex: 'useTimes',
      render: (dom) => <span>每天{dom}次</span>,
    },
    {
      title: '采购数量',
      dataIndex: 'purchaseNum',
    },
    {
      title: '退货数量',
      dataIndex: 'refundNum',
    },
    {
      title: '单价（元）',
      dataIndex: 'unitPrice',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '退货金额（元）',
      dataIndex: 'refundMoney',
      render: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, entity: any) =>
        entity.timeLaws && (
          <a
            key={getUniqueId()}
            onClick={() => {
              setLawsList(entity.timeShareVoList);
              setTimeModalData({
                enterStartTime: entity.dayBegin,
                enterEndTime: entity.dayEnd,
                timeLaws: JSON.parse(entity.timeLaws),
                num: entity.refundNum,
                price: entity.refundMoney,
              });
              timeModalState.setType('edit');
            }}
          >
            分时详情
          </a>
        ),
    },
  ];
  const modalColumns: ProFormColumnsType[] = [
    {
      title: '基本信息',
      columns: [
        {
          title: '采购退单号',
          dataIndex: 'refundId',
        },
        {
          title: '采购订单号',
          dataIndex: 'orderId',
        },
        {
          title: '供应商名称',
          dataIndex: 'sellerName',
        },
        {
          title: '退货时间',
          dataIndex: 'refundTime',
          renderText: (dom) => formatTime(dom),
        },
        {
          title: '交易上链',
          valueEnum: chainStatusEnum,
          dataIndex: 'isChain',
        },
        {
          title: '操作人',
          dataIndex: 'username',
        },
        {
          title: '退单状态',
          dataIndex: 'refundStatus',
          valueEnum: orderTypeEnum,
        },
      ],
    },
    {
      title: '退货信息',
      columns: [
        {
          colSize: 3,
          dataIndex: 'productList',
          title: '',
          render: (col, record) => (
            <Table
              {...tableConfig}
              bordered
              size="middle"
              style={{ width: '100%' }}
              // scroll={{ x: 2000 }}
              columns={columnsRefundInitial}
              dataSource={col}
              footer={() => (
                <div style={{ textAlign: 'right' }}>
                  退货总数：{record.refundNum}
                  &nbsp;&nbsp;&nbsp;&nbsp; 退货总金额（元）：
                  {record.refundAmount}
                </div>
              )}
              pagination={false}
            />
          ),
        },
      ],
    },
  ];

  return (
    <>
      <ProTable
        {...tableConfig}
        actionRef={actionRef}
        style={modalState.tableStyle}
        params={{ buyerId: coId }}
        request={apiPurchaseRefundOrderPageList}
        columns={columns}
        formRef={exportState.formRef}
        columnsState={columnsSet(exportState)}
        toolBarRender={() => [<Export key="export" {...exportState} />]}
      />
      <ProModal
        page
        {...modalState}
        title="采购退单"
        actionRef={actionRef}
        columns={modalColumns}
        params={{ orderId: [orderId] }}
        infoRequest={async (params) => {
          const res: any = await apiDistributeRefundInfo(params);
          res.data = res.data.distributeRefund?.[0]; // 只取第一个
          // 添加退货总数和金额
          res.data.refundAmount = res.data.productList.reduce((prev: any, next: any) => {
            return prev + next.refundMoney * 1;
          }, 0);
          res.data.refundAmount = parseFloat(res.data.refundAmount).toFixed(2);
          res.data.refundNum = res.data.productList.reduce((prev: any, next: any) => {
            return prev + next.refundNum * 1;
          }, 0);
          addOperationLogRequest({
            action: 'info',
            content: `查看【${orderId}】采购退单详情`,
          });
          return res;
        }}
      />
      <Modal
        width={1200}
        title={'采购退单详情'}
        open={detailsVisible}
        footer={false}
        onCancel={() => {
          setDetailsVisible(false);
        }}
        destroyOnClose
      >
        {/* 商品表格 */}
        <ProTable
          {...tableConfig}
          rowKey="id"
          actionRef={actionRef}
          params={{ orderId }}
          request={async (e) => {
            const res: any = await apiDistributeRefundInfo({
              orderId: [e],
            });

            addOperationLogRequest({
              action: 'info',
              content: `查看【${orderId}】采购退单详情`,
            });

            setCounts(res.data.payAmount);
            res.data = res.data.productList;
            res.data.map((item: any) => {
              item.id = getUniqueId();
            });
            return res;
          }}
          // request={apiDistributeRefundInfo}
          // postData={(e) => {
          //   let sum = 0;
          //   e.map((item) => {
          //     sum += item.totalPrice;
          //   });
          //   setCounts(sum);
          //   return e;
          // }}
          columns={columnsInitial}
          headerTitle={<div style={{}}>采购退单号：{orderId}&emsp;&emsp;&emsp;</div>}
          expandable={{
            expandedRowRender: (record: any, indexTable: number) => (
              <>
                <span style={{ padding: '8px', display: 'block' }}>入园日期及分时预约信息：</span>
                <List
                  style={{
                    padding: '4px 8px',
                    maxHeight: '300px',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                  }}
                  grid={{
                    gutter: 16,
                    xs: 1,
                    sm: 2,
                    md: 4,
                    lg: 4,
                    xl: 5,
                    xxl: 5,
                  }}
                  dataSource={record.dateList}
                  renderItem={(item: any, indexList: number) => (
                    <List.Item className="cardBox">
                      <Card
                        title={item.dayBegin}
                        hoverable
                        size="small"
                        extra={
                          <a
                            onClick={() => {
                              // 修改分时信息
                              setTimeDataSource(item.timeShareDetail);
                              // setTimeTitle(item.dayBegin)
                              setTimeVisible(true);
                            }}
                          >
                            详情
                          </a>
                        }
                      >
                        <p>数量：{item.totalNum}</p>
                        <p>金额：￥ {item.totalAmount}</p>
                      </Card>
                    </List.Item>
                  )}
                />
              </>
            ),
            rowExpandable: (record: any) => record.dateList,
          }}
        />
        {/* 查看区块链交易记录 */}
        <Blockchain
          cochainVisible={cochainVisible}
          setCochainVisible={setCochainVisible}
          cochainColumns={cochainColumns}
          cochainDataDataSource={cochainDataDataSource}
        />
        {/* 分时预约详情弹窗 */}
        <Modal
          width={modelWidth.lg}
          title={'分时预约信息'}
          visible={timeVisible}
          destroyOnClose
          footer={null}
          onCancel={() => {
            setTimeVisible(false);
          }}
        >
          <ProTable
            {...tableConfig}
            rowKey="distributorTicketStockId"
            headerTitle={timeTitle}
            columns={timeTableColumns}
            dataSource={timeDataSource}
            search={false}
            // options={false}
          />
        </Modal>

        {/* 非分时预约详情弹窗 */}
        <Modal
          width={modelWidth.lg}
          title={'入园日期信息'}
          visible={unTimeVisible}
          destroyOnClose
          footer={null}
          onCancel={() => {
            setUnTimeVisible(false);
          }}
        >
          <ProTable
            {...tableConfig}
            rowKey="distributorTicketStockId"
            // headerTitle={timeTitle}
            columns={unTimeTableColumns}
            dataSource={unTimeDataSource}
            search={false}
          />
        </Modal>

        {/* <div
          style={{ marginRight: '40px', textAlign: 'right', fontWeight: '400', fontSize: '20px' }}
        >
          总计：{counts}元
        </div> */}
      </Modal>
      {/* 分时库存 */}
      <ProModal
        {...timeModalState}
        fullTitle="分时预约详情"
        columns={timeModalColumns}
        layout="horizontal"
        dataSource={timeModalData}
        onFinish={async () => true}
      />
    </>
  );
};

export default TableList;
