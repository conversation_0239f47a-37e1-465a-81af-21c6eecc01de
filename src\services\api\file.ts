/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-29 15:49:28
 * @LastEditTime: 2022-07-29 15:49:29
 * @LastEditors: zhangfengfei
 */
import { request } from '@umijs/max';
import { getEnv } from '@/common/utils/getEnv';

interface FileDataItem {
  size: number;
  path: string;
  name: string;
  type: string;
  mtime: string;
}
// 上传图片或者文件 支持多选
export async function uploadFile(files: File[]) {
  const formData = new FormData();
  for (const file of files) {
    formData.append('file', file);
    console.log(file);
  }

  return request<FileDataItem[]>(`${getEnv().UPLOAD_HOST}`, {
    method: 'POST',
    requestType: 'form',
    data: formData,
    skipErrorHandler: true,
  });
}
