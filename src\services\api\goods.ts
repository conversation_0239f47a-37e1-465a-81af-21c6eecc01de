/**
 * 电商数据报表接口
 */
import { getData } from '@/common/utils/tool';
import { request } from '@umijs/max';
import { getEnv } from '@/common/utils/getEnv';

let part: string = 'scenic_order_test';
const { DATA_HOST } = getEnv();
if (DATA_HOST == 'https://prod.shukeyun.com/data/api/model/exec') part = 'ticket_order_db';

/** 面向商品 */
export async function byGoods(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_agent_by_good/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

/** 面向商品出票 */
export async function goodsReceiveTicket(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_agent_by_good_ticket_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

/** 面向商品退票 */
export async function goodsRetreatTicket(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_agent_by_good_re_ticket_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

/** 面向供应商 */
export async function byDistributor(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_user_by_provider/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

/** 面向供应商出票 */
export async function distributorReceiveTicket(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_user_by_provider_ticket_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}

/** 面向供应商退票 */
export async function distributorRetreatTicket(params: any) {
  return getData(
    await request(`${DATA_HOST}/face_user_by_provider_re_ticket_list/scenic_ads_dw/`, {
      method: 'POST',
      data: { ...params, update_frequency: '0.0' },
      skipErrorHandler: true, //不走错误处理
    }),
  );
}
