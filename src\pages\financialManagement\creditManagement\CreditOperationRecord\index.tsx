import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getOperationRecordList } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useRef } from 'react';
import { useModel } from '@umijs/max';

type CreditOperationRecordProps = Record<string, never>;

/**
 * @description 财务管理->授信管理->授信操作记录
 * */
const CreditOperationRecord: FC<CreditOperationRecordProps> = () => {
  const { initialState } = useModel('@@initialState');
  const settlementId = initialState?.currentCompany.settlementId || '';

  const actionRef = useRef<ActionType>();

  const tableListReq = async (params: API.OperationRecordListParams) => {
    try {
      const { data } = await getOperationRecordList(params);
      addOperationLogRequest({
        action: 'info',
        content: `查看授信操作记录`,
      });
      return {
        data: data.page,
        total: data.totalNumberOfResults,
      };
    } catch (error) {
      return {
        data: [],
        total: 0,
      };
    }
  };

  const columns: ProColumns<API.OperationRecordItem>[] = [
    {
      title: '操作时间',
      dataIndex: 'creationDate',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      search: false,
    },
    {
      title: '操作时间',
      key: 'time',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            creationFromDate: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD HH:mm:ss'),
            // creationFromDate: value[0],
            creationToDate: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD HH:mm:ss'),
          };
        },
      },
    },
    {
      title: '操作人',
      dataIndex: 'operator',
    },
    {
      title: '授信客户账号',
      dataIndex: 'consumeName',
      renderText: (text, record) => record.account?.consumer?.registrationName || '-',
    },
    {
      title: '授信金额（元）',
      dataIndex: 'amount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '授信前金额（元）',
      dataIndex: 'beforeBalance',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '授信后金额（元）',
      dataIndex: 'afterBalance',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  return (
    <>
      <ProTable<API.OperationRecordItem, API.OperationRecordListParams>
        {...tableConfig}
        actionRef={actionRef}
        columns={columns}
        params={{
          merchantId: settlementId,
        }}
        request={tableListReq}
        pagination={{
          defaultPageSize: 10,
        }}
      />
    </>
  );
};

export default CreditOperationRecord;
