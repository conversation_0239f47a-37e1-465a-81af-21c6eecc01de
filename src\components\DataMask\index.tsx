import { addOperationLogRequest } from '@/common/utils/operationLog';
import { Switch } from 'antd';
import { useEffect, useRef, useState } from 'react';

interface DataMaskProps {
  onDataMaskChange?: (showFullData: boolean) => void;
  logContent?: string;
  throttleTime?: number; // 节流时间，默认为 5 分钟
}

export default ({
  onDataMaskChange,
  logContent = '查看用户隐私信息',
  throttleTime = 5 * 60 * 1000, // 默认 5 分钟
}: DataMaskProps) => {
  const [showFullData, setShowFullData] = useState(false);
  const lastLogTimeRef = useRef<number>(0);

  const handleChange = (checked: boolean) => {
    setShowFullData(checked);
    onDataMaskChange?.(checked);

    if (checked) {
      const now = Date.now();
      // 检查是否已经超过节流时间
      if (now - lastLogTimeRef.current > throttleTime) {
        addOperationLogRequest({
          action: 'info',
          content: logContent,
        });
        // 更新最后一次记录的时间
        lastLogTimeRef.current = now;
      }
    }
  };

  // 组件卸载时清除引用
  useEffect(() => {
    return () => {
      lastLogTimeRef.current = 0;
    };
  }, []);

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
      <span
        style={{
          fontSize: '14px',
          color: '#666666',
          lineHeight: '1.5',
        }}
      >
        该页面涉及用户隐私信息，请注意数据安全，如需显示全部用户信息请点击右侧按钮
      </span>
      <Switch checked={showFullData} onChange={handleChange} />
    </div>
  );
};
