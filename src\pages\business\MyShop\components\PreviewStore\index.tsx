import { addOperationLogRequest } from '@/common/utils/operationLog';
import { ProCard } from '@ant-design/pro-components';
import Paragraph from 'antd/lib/typography/Paragraph';
import QRCode from 'qrcode.react';
import { useContext } from 'react';
import { TabKeyContext } from '../..';
import { getEnv } from '@/common/utils/getEnv';

interface FCNameProps {
  store: Record<string, any>;
}

export default ({ store }: FCNameProps) => {
  const { MALL_HOST } = getEnv();
  const newLinkUrl = `${MALL_HOST}?storeId=${store?.value}`;
  const tabKey = useContext(TabKeyContext);

  return (
    <ProCard>
      <QRCode
        style={{ margin: '40px auto', position: 'relative' }}
        value={newLinkUrl} // value 参数为生成二维码的链接
        size={200} // 二维码的宽高尺寸
        fgColor="#000000" // 二维码的颜色
      />
      <Paragraph
        style={{ textAlign: 'center' }}
        copyable={{
          text: newLinkUrl,
          tooltips: ['复制', '已复制'],
          onCopy(event) {
            addOperationLogRequest({
              action: 'copy',
              module: tabKey,
              content: `复制【${store.label}】预览链接`,
            });
          },
        }}
      >
        {newLinkUrl}
      </Paragraph>
    </ProCard>
  );
};
