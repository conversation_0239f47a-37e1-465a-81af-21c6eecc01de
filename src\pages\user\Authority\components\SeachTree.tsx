import type { TreeProps } from 'antd';
import { Input, Tag, Tree, Typography } from 'antd';
import React from 'react';

import { modelWidth } from '@/common/utils/gConfig';
import type { DataNode, Key } from 'rc-tree/lib/interface';

const { Search } = Input;
const { Text } = Typography;

const gData: DataNode[] = [];
const generateData = (data: DataNode[] | undefined) => {
  if (data) {
    gData.push(...data);
  }
};

const dataList: DataNode[] = [];
const generateList = (data: DataNode[]) => {
  for (let i = 0; i < data.length; i++) {
    const node = data[i];
    const { key, title } = node;
    dataList.push({ key, title: title });
    if (node.children) {
      generateList(node.children);
    }
  }
};
function clearData() {
  gData.length = 0;
  dataList.length = 0;
}

const getParentKey = (key: Key, tree: DataNode[]): Key | undefined => {
  let parent<PERSON>ey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey;
};

interface SearchTreeProps extends TreeProps {
  searchPlaceholder: string;
  setCheck?: React.Dispatch<React.SetStateAction<React.Key[] | undefined>>;
}

class SearchTree extends React.Component<SearchTreeProps> {
  constructor(props: SearchTreeProps) {
    super(props);
    clearData();
    generateData(props.treeData);
    generateList(gData);
  }

  state = {
    expandedKeys: this.props.expandedKeys,
    searchValue: '',
    autoExpandParent: true,
  };

  onExpand = (expandedKeys: Key[]) => {
    this.setState({
      expandedKeys,
      autoExpandParent: false,
    });
  };

  onSearch = (value: string) => {
    //过滤无需查询
    let input = value.trim();
    if (input.length == 0 || input === '/') {
      input = '';
    }

    const expandedKeys = dataList
      .map((item: DataNode) => {
        if (input.length != 0 && String(item.title).indexOf(input) > -1) {
          return getParentKey(item.key!, gData);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i); //过滤掉null
    this.setState({
      expandedKeys,
      searchValue: input,
      autoExpandParent: true,
    });
  };

  render() {
    const { searchValue, expandedKeys, autoExpandParent } = this.state;
    const loop = (data: DataNode[]): DataNode[] =>
      data.map((item: DataNode) => {
        const t = String(item.title!);
        const index = t.indexOf(searchValue);
        const beforeStr = t.slice(0, index);
        const afterStr = t.slice(index + searchValue.length);
        //输入查询到的高亮
        const title =
          index > -1 && searchValue.length > 0 ? (
            <span>
              {beforeStr}
              <Tag color="blue">{searchValue}</Tag>
              {afterStr}
            </span>
          ) : (
            <span>{t}</span>
          );
        if (item.children) {
          return { title, key: item.key, children: loop(item.children) };
        }
        return {
          title,
          key: item.key,
        };
      });
    return (
      <div>
        <Search
          style={{ marginBottom: 8 }}
          placeholder={this.props.searchPlaceholder}
          onSearch={this.onSearch}
        />
        <Tree
          height={modelWidth.sm}
          checkable={true}
          multiple={true}
          selectable={false}
          onExpand={this.onExpand}
          expandedKeys={expandedKeys}
          defaultCheckedKeys={this.props.defaultCheckedKeys}
          autoExpandParent={autoExpandParent}
          treeData={loop(gData)}
          onCheck={this.props.onCheck}
        />
      </div>
    );
  }
}

export default SearchTree;
