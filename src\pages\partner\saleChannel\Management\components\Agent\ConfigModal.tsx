/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-15 09:49:31
 * @LastEditTime: 2022-12-16 13:53:48
 * @LastEditors: Please set LastEditors
 */
import { AgentTypeEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getCommissionCredit, updateCommissionCredit } from '@/services/api/store';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormRadio } from '@ant-design/pro-form';
import { message } from 'antd';
import type { FC } from 'react';
import { useEffect, useRef } from 'react';
import { useModel, useRequest } from '@umijs/max';

const logList = [
  {
    title: '现付佣金结算方式',
    dataIndex: 'paymentType',
    valueEnum: {
      1: '前结算(空中分账)',
      2: '后结算(按核销)',
    },
  },
  {
    title: '授信佣金结算方式',
    dataIndex: 'creditType',
    valueEnum: {
      1: '前结算(扣佣支付)',
      2: '后结算(按核销)',
    },
  },
];

interface ConfigModalProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  currentItem?: Record<string, any>;
}

const ConfigModal: FC<ConfigModalProps> = ({ visible, setVisible, currentItem }) => {
  const { initialState } = useModel('@@initialState');
  const { coId = '' } = initialState?.currentCompany || {};

  const formRef = useRef<ProFormInstance>();

  const getCreditReq = useRequest(getCommissionCredit, {
    manual: true,
    onSuccess(data) {
      formRef.current?.setFieldsValue(data);
    },
  });

  const updateCreditReq = useRequest(updateCommissionCredit, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: 'edit',
        changeConfig: {
          list: logList,
          beforeData: getCreditReq.data,
          afterData: params[0],
        },
        content: `配置【${currentItem?.coName}】佣金结算模式`,
      });

      message.success('操作成功');
      setVisible(false);
    },
  });

  const onFinish = async (values: { paymentType: number; creditType: number }) => {
    if (currentItem) {
      updateCreditReq.run({
        ...values,
        agentId: currentItem.coId,
        distributorId: coId,
        agentType: currentItem?.agentType,
      });
    }
  };

  useEffect(() => {
    if (visible) {
      getCreditReq.run({
        distributorId: coId,
        agentId: currentItem?.coId,
        agentType: currentItem?.agentType,
      });
    }
  }, [visible, currentItem?.coId]);

  return (
    <ModalForm
      width={800}
      formRef={formRef}
      title="配置佣金结算模式"
      visible={visible}
      onVisibleChange={setVisible}
      // onValuesChange={({ paymentType, creditType }) => {
      //   formRef.current?.setFieldsValue({
      //     [creditType ? 'paymentType' : 'creditType']: undefined,
      //   });
      // }}
      onFinish={onFinish}
      layout="inline"
      modalProps={{
        destroyOnClose: true,
        okButtonProps: {
          loading: updateCreditReq.loading,
        },
      }}
    >
      <ProForm.Group>
        <ProForm.Item style={{ width: 216 }} label="代理商类型" name="agentType">
          {AgentTypeEnum[currentItem?.agentType]}
        </ProForm.Item>
        <ProForm.Item label="设置佣金结算方式" required rules={[{ message: '请选择' }]}>
          <div style={{ marginBottom: 16 }}>
            <ProFormRadio.Group
              name="paymentType"
              label="现付"
              colon={false}
              // rules={[
              //   {
              //     required: true,
              //     message: '请选择',
              //   },
              // ]}
              options={[
                {
                  label: '前结算(空中分账)',
                  value: 1,
                  disabled: currentItem?.agentType === AgentTypeEnum.个人,
                },
                {
                  label: '后结算(按核销)',
                  value: 2,
                },
              ]}
            />
            {AgentTypeEnum.企业 === currentItem?.agentType && (
              <ProFormRadio.Group
                name="creditType"
                label="授信"
                colon={false}
                // rules={[
                //   {
                //     required: true,
                //     message: '请选择',
                //   },
                // ]}
                options={[
                  {
                    label: '前结算(扣佣支付)',
                    value: 1,
                  },
                  {
                    label: '后结算(按核销)',
                    value: 2,
                  },
                ]}
              />
            )}
          </div>
        </ProForm.Item>
      </ProForm.Group>
    </ModalForm>
  );
};

export default ConfigModal;
