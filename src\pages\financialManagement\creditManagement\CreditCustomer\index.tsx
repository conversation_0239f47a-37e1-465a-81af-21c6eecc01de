/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-23 14:25:33
 * @LastEditTime: 2023-09-27 14:59:03
 * @LastEditors: zhangfengfei
 */
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import useModal from '@/hooks/useModal';
import { getCreditAccountList } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Space, message } from 'antd';
import type { FC } from 'react';
import { useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import BillDetailModal from './components/BillDetailModal';
import CreditAccountModal from './components/CreditAccountModal';
import CreditModal from './components/CreditModal';

type CreditCustomerProps = Record<string, never>;

/**
 * @description  财务管理->授信管理->授信客户
 * */
const CreditCustomer: FC<CreditCustomerProps> = () => {
  const { initialState } = useModel('@@initialState');
  const settlementId = initialState?.currentCompany.settlementId || '';
  const access = useAccess();

  const actionRef = useRef<ActionType>();

  /** 授信 modal */
  const creditModalState = useModal();

  /** 收支明细 modal */
  const billDetailModalState = useModal();
  /** 授信客户 modal */
  const creditAccountModalState = useModal();

  const [tableListItem, setTableListItem] = useState<API.CreditAccountItem>();

  const tableListReq = async (params: API.CreditAccountListParams) => {
    if (params.merchantId) {
      const { data } = await getCreditAccountList(params);
      return {
        data: data.page,
        total: data.totalNumberOfResults,
      };
    } else {
      message.warning('企业未认证商户号为空，请先进行企业认证');
      return {
        data: [],
        total: 0,
      };
    }
  };

  const columns: ProColumns<API.CreditAccountItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      search: false,
    },
    {
      title: '授信客户账号',
      dataIndex: ['consumer', 'registrationName'],
      search: false,
    },

    {
      title: '授信客户名称',
      dataIndex: ['consumer', 'legalName'],
      search: {
        transform: (val) => {
          return {
            consumerName: val,
          };
        },
      },
    },

    {
      title: '预警值',
      dataIndex: 'warningAmount',
      search: false,
    },
    {
      title: '授信余额（元）',
      dataIndex: 'balance',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      renderText: (_, record) => (
        <Space
          onClick={() => {
            setTableListItem(record);
          }}
        >
          <a
            onClick={() => {
              creditAccountModalState.setTypeWithVisible('info');

              addOperationLogRequest({
                action: 'info',
                content: `查看授信账户【${record?.consumer?.registrationName}】详情`,
              });
            }}
          >
            查看
          </a>
          <Access accessible={access.canCreditCustomerManage_edit}>
            <a
              onClick={() => {
                creditAccountModalState.setTypeWithVisible('update');
              }}
            >
              编辑
            </a>
          </Access>
          <Access accessible={access.canCreditCustomerManage_credit}>
            <a
              onClick={() => {
                creditModalState.setVisible(true);
              }}
            >
              授信
            </a>
          </Access>
          <Access accessible={access.canCreditCustomerManage_detail}>
            <a
              onClick={() => {
                billDetailModalState.setVisible(true);
              }}
            >
              收支明细
            </a>
          </Access>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.CreditAccountItem, API.CreditAccountListParams>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={tableListReq}
        pagination={{
          defaultPageSize: 10,
        }}
        params={{
          merchantId: settlementId,
        }}
        toolBarRender={() => [
          <Access key="add" accessible={access.canCreditCustomerManage_insert}>
            <Button
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                creditAccountModalState.setTypeWithVisible('add');
              }}
            >
              新增
            </Button>
          </Access>,
        ]}
      />
      {/* 授信 */}
      <CreditModal
        actionRef={actionRef}
        currentItem={tableListItem}
        modalState={creditModalState}
      />
      {/* 收支明细 */}
      <BillDetailModal currentItem={tableListItem} modalState={billDetailModalState} />
      {/* 授信客户新增、详情、修改 */}
      <CreditAccountModal
        actionRef={actionRef}
        currentItem={tableListItem}
        modalState={creditAccountModalState}
      />
    </>
  );
};

export default CreditCustomer;
