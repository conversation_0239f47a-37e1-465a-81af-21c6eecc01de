/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-21 10:38:29
 * @LastEditTime: 2022-10-10 16:24:12
 * @LastEditors: zhang<PERSON><PERSON>i
 */
import { GuideStepStatus } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { useGuide } from '@/hooks/useGuide';
import type { ModalState } from '@/hooks/useModal';
import {
  addPriceStrategy,
  getPriceStrategyByAgent,
  updatePriceStrategy,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { QuestionCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, {
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import type { ActionType } from '@ant-design/pro-table';
import {
  Modal,
  Space,
  Tooltip,
  message,
  Button,
  Card,
  Form,
  Select,
  InputNumber,
  DatePicker,
  Switch,
} from 'antd';
import { ceil, isNil } from 'lodash';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';
import { ProFormList, ProFormDateRangePicker } from '@ant-design/pro-form';
import PriceHistoryChart from './PriceHistoryChart';
import PriceAdjustmentList from './PriceAdjustmentList';
import EntranceStatisticsChart from './EntranceStatisticsChart';

const rules = [
  {
    required: true,
  },
];

// 自定义 StrategyListItemType 类型
interface StrategyListItemType {
  priceId: string;
  unitId?: string;
  discount: number;
  composeDiscount: number;
  isCompose: boolean;
  salePrice: number;
  composePrice: number;
  commissionRate: number;
  commissionType: number;
  commissionAmount: number;
  group?: any[];
  // 动态价格相关字段
  isDynamicPrice?: boolean;
  weatherStrategyEnabled?: boolean;
  capacityStrategyEnabled?: boolean;
  priceStrategies?: StrategyItem[];
  basePrice?: number;
  maxPrice?: number;
  minPrice?: number;
  pricingRules?: string;
  pricingFormula?: string;
  costLimitValue?: number;
  costLimitUnit?: string;
  updateMode?: string;
}

interface StrategyModalProps {
  goodsItem: API.PriceStrategyListItem;
  currentItem?: StrategyListItemType;
  modalState: ModalState;
  showInfo?: boolean;
  actionRef?: React.MutableRefObject<ActionType | undefined>;
  onFinish?: () => void;
}

// 固定定价表单类型
interface FixedPriceFormType {
  isCompose: '0' | '1';
  discount: number;
  salePrice: number;
  composeDiscount: number;
  composePrice: number;
  commissionRate: number;
  saleRenewPrice: number;
  renewDiscount: number;
}

// 动态定价表单类型
interface DynamicPriceFormType {
  pricingRules: string;
  pricingFormula: string;
  basePrice: number;
  maxPrice: number;
  minPrice: number;
}

// 策略项类型定义
interface StrategyItem {
  type: 'weather' | 'capacity' | 'season';
  condition: string;
  value?: string | number;
  tempOperator?: string;
  priceChange?: string;
  adjustment?: number;
  adjustmentUnit?: string;
  startDate?: string;
  endDate?: string;
  startDay?: string;
  endDay?: string;
  weekdays?: string[];
  dateRange?: any;
}

// 完整表单类型
interface FormType extends FixedPriceFormType, DynamicPriceFormType {
  groupId: string[];
  isDynamicPrice: boolean;
  priceStrategies?: StrategyItem[];
  costLimitValue?: number;
  costLimitUnit?: string;
  updateMode?: string;
}

// 添加一个独立的策略行组件，用于处理每行的状态
const StrategyRow = ({ record, index, action, type, formRef }) => {
  // 使用 React 状态跟踪当前行的 condition 值
  const [rowCondition, setRowCondition] = useState(record?.condition || '');

  return (
    <Card
      size="small"
      style={{ marginBottom: '8px' }}
      bordered={false}
      bodyStyle={{ padding: '12px', backgroundColor: '#f5f5f5' }}
      extra={null}
    >
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ flex: 1 }}>
          {/* 自定义表单项显示，不使用 listDom */}
          <div
            style={{
              display: 'flex',
              flexWrap: 'wrap',
              alignItems: 'center',
              gap: '8px',
            }}
          >
            {/* 天气策略表单项 */}
            {record?.type === 'weather' && (
              <>
                <div>当</div>
                <Form.Item name={[index, 'condition']} noStyle>
                  <Select
                    placeholder="请选择"
                    options={[
                      { label: '出游指数', value: '出游指数' },
                      { label: '平均气温', value: '平均气温' },
                    ]}
                    bordered={false}
                    style={{ width: '100px' }}
                    disabled={type === 'info'}
                    onChange={(value) => {
                      console.log('选择的条件:', value);
                      setRowCondition(value);
                    }}
                  />
                </Form.Item>
                {rowCondition === '出游指数' && (
                  <>
                    <div>为</div>
                    <Form.Item name={[index, 'value']} noStyle>
                      <Select
                        placeholder="请选择"
                        options={[
                          { label: '适宜', value: '适宜' },
                          { label: '较适宜', value: '较适宜' },
                          { label: '较不适宜', value: '较不适宜' },
                          { label: '不适宜', value: '不适宜' },
                        ]}
                        bordered={false}
                        style={{ width: '110px' }}
                        disabled={type === 'info'}
                      />
                    </Form.Item>
                  </>
                )}

                {rowCondition === '平均气温' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Form.Item name={[index, 'tempOperator']} noStyle>
                      <Select
                        placeholder="请选择"
                        options={[
                          { label: '大于等于', value: '大于等于' },
                          { label: '小于等于', value: '小于等于' },
                        ]}
                        bordered={false}
                        style={{ width: '100px' }}
                        disabled={type === 'info'}
                      />
                    </Form.Item>
                    <Form.Item name={[index, 'value']} noStyle>
                      <InputNumber style={{ width: '60px' }} disabled={type === 'info'} />
                    </Form.Item>
                    <div style={{ marginLeft: '4px' }}>℃</div>
                  </div>
                )}

                <div>时</div>
                <Form.Item name={[index, 'priceChange']} noStyle>
                  <Select
                    placeholder="请选择"
                    options={[
                      { label: '上涨', value: '上涨' },
                      { label: '下调', value: '下调' },
                    ]}
                    bordered={false}
                    style={{ width: '80px' }}
                    disabled={type === 'info'}
                  />
                </Form.Item>
                <Form.Item name={[index, 'adjustment']} noStyle>
                  <InputNumber style={{ width: '80px' }} disabled={type === 'info'} />
                </Form.Item>
                <Form.Item name={[index, 'adjustmentUnit']} noStyle initialValue="%">
                  <Select
                    placeholder="请选择"
                    options={[
                      { label: '%', value: '%' },
                      { label: '元', value: '元' },
                    ]}
                    bordered={false}
                    style={{ width: '60px' }}
                    disabled={type === 'info'}
                  />
                </Form.Item>
              </>
            )}

            {record?.type === 'capacity' && (
              <>
                <div>当</div>
                <Form.Item name={[index, 'condition']} noStyle initialValue="日销售量">
                  <Select
                    placeholder="请选择"
                    options={[
                      { label: '日销售量', value: '日销售量' },
                      { label: '日入园人数', value: '日入园人数' },
                    ]}
                    bordered={false}
                    style={{ width: '110px' }}
                    disabled={type === 'info'}
                  />
                </Form.Item>
                <div>为日承载量的</div>
                <Form.Item name={[index, 'value']} noStyle>
                  <InputNumber style={{ width: '60px' }} disabled={type === 'info'} />
                </Form.Item>
                <div>% 时上涨</div>
                <Form.Item name={[index, 'adjustment']} noStyle>
                  <InputNumber style={{ width: '80px' }} disabled={type === 'info'} />
                </Form.Item>
                <Form.Item name={[index, 'adjustmentUnit']} noStyle initialValue="%">
                  <Select
                    placeholder="请选择"
                    options={[
                      { label: '%', value: '%' },
                      { label: '元', value: '元' },
                    ]}
                    bordered={false}
                    style={{ width: '60px' }}
                    disabled={type === 'info'}
                  />
                </Form.Item>
              </>
            )}

            {/* 淡旺季策略表单项 */}
            {record?.type === 'season' && (
              <>
                <div>当</div>
                <Form.Item name={[index, 'condition']} noStyle initialValue="日期">
                  <Select
                    placeholder="请选择"
                    options={[
                      { label: '日期', value: '日期' },
                      { label: '星期', value: '星期' },
                    ]}
                    bordered={false}
                    style={{ width: '80px' }}
                    disabled={type === 'info'}
                    onChange={(value) => {
                      setRowCondition(value);
                    }}
                  />
                </Form.Item>
                <div>为</div>

                {rowCondition === '日期' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Form.Item name={[index, 'dateRange']} noStyle>
                      <DatePicker.RangePicker
                        format="YYYY/MM/DD"
                        style={{ width: '220px' }}
                        disabled={type === 'info'}
                        placeholder={['开始日期', '结束日期']}
                        onChange={(dates) => {
                          if (dates && dates[0] && dates[1]) {
                            // 更新 startDate 和 endDate 字段
                            const [startDate, endDate] = dates;
                            const startDateStr = startDate.format('YYYY-MM-DD');
                            const endDateStr = endDate.format('YYYY-MM-DD');

                            setTimeout(() => {
                              const form = formRef.current;
                              form?.setFields([
                                { name: [index, 'startDate'], value: startDateStr },
                                { name: [index, 'endDate'], value: endDateStr },
                              ]);
                            }, 0);
                          }
                        }}
                      />
                    </Form.Item>

                    {/* 保留原字段但隐藏，用于存储数据 */}
                    <Form.Item name={[index, 'startDate']} hidden noStyle />
                    <Form.Item name={[index, 'endDate']} hidden noStyle />
                  </div>
                )}

                {rowCondition === '星期' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Form.Item name={[index, 'weekdays']} noStyle>
                      <Select
                        placeholder="请选择星期"
                        mode="multiple"
                        options={[
                          { label: '星期一', value: '星期一' },
                          { label: '星期二', value: '星期二' },
                          { label: '星期三', value: '星期三' },
                          { label: '星期四', value: '星期四' },
                          { label: '星期五', value: '星期五' },
                          { label: '星期六', value: '星期六' },
                          { label: '星期日', value: '星期日' },
                        ]}
                        style={{ width: '210px' }}
                        bordered={false}
                        disabled={type === 'info'}
                        maxTagCount={3}
                      />
                    </Form.Item>

                    {/* 保留原字段但隐藏，用于兼容性 */}
                    <Form.Item name={[index, 'startDay']} hidden noStyle />
                    <Form.Item name={[index, 'endDay']} hidden noStyle />
                  </div>
                )}

                <div>时</div>
                <Form.Item name={[index, 'priceChange']} noStyle initialValue="上涨">
                  <Select
                    placeholder="请选择"
                    options={[
                      { label: '上涨', value: '上涨' },
                      { label: '下调', value: '下调' },
                    ]}
                    bordered={false}
                    style={{ width: '80px' }}
                    disabled={type === 'info'}
                  />
                </Form.Item>
                <Form.Item name={[index, 'adjustment']} noStyle>
                  <InputNumber style={{ width: '80px' }} disabled={type === 'info'} />
                </Form.Item>
                <Form.Item name={[index, 'adjustmentUnit']} noStyle initialValue="%">
                  <Select
                    placeholder="请选择"
                    options={[
                      { label: '%', value: '%' },
                      { label: '元', value: '元' },
                    ]}
                    bordered={false}
                    style={{ width: '60px' }}
                    disabled={type === 'info'}
                  />
                </Form.Item>
              </>
            )}
          </div>
        </div>
        {type !== 'info' && (
          <MinusCircleOutlined
            style={{
              color: '#ff4d4f',
              fontSize: '16px',
              marginLeft: '8px',
              cursor: 'pointer',
            }}
            onClick={() => {
              if (action && typeof action.remove === 'function') {
                // 使用 field.name 而不是 index 或 record.name
                action.remove(index);
              } else {
                console.error('action.remove is not a function', action);
                // 尝试替代方案：直接从表单中删除
                const priceStrategies = formRef.current?.getFieldValue('priceStrategies') || [];
                const newStrategies = [...priceStrategies];
                newStrategies.splice(index, 1);
                formRef.current?.setFieldsValue({ priceStrategies: newStrategies });
              }
            }}
          />
        )}
      </div>
    </Card>
  );
};

const DynamicPriceStrategyModal: FC<StrategyModalProps> = ({
  modalState: { setVisible, visible, type },
  currentItem: priceItem,
  goodsItem,
  actionRef,
  onFinish,
  showInfo = true,
}) => {
  // 控制动态定价开关状态
  const [isDynamicPricing, setIsDynamicPricing] = useState(false);

  // 添加天气策略和日承载量策略的状态控制
  const [weatherStrategyEnabled, setWeatherStrategyEnabled] = useState(false);
  const [capacityStrategyEnabled, setCapacityStrategyEnabled] = useState(false);

  // 是否权益卡
  const isRightsCard = goodsItem?.unitType == 2 || goodsItem?.ruleType == 2;

  // 是否权益票
  const isRightsTicket = goodsItem?.ruleType == 1;

  // 商品折扣价
  const price = (goodsItem?.overallDiscount * goodsItem?.marketPrice) / 100;

  const formRef = useRef<ProFormInstance<FormType>>();
  const { updateGuideInfo } = useGuide();

  const [beginPrice, endPrice] = [
    ceil((goodsItem?.beginDiscount * price) / 100, 2),
    ceil((goodsItem?.endDiscount * price) / 100, 2),
  ];

  const { initialState } = useModel('@@initialState');
  const { coId = '', coName = '' } = initialState?.currentCompany || {};

  // 模拟接口请求函数
  const mockFetchGoodsInfo = () => {
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            name: '鼓浪时韵',
            category: '成人票',
            marketPrice: 100.0,
            discount: '100%',
            discountRange: '0%~100%',
            purchasePrice: 0.01,
          },
        });
      }, 500);
    });
  };

  const [goodsInfo, setGoodsInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // 获取商品信息
  useEffect(() => {
    if (visible) {
      setLoading(true);
      mockFetchGoodsInfo().then((res) => {
        if (res.success) {
          setGoodsInfo(res.data);
        }
        setLoading(false);
      });
    }
  }, [visible]);

  const logList = [
    {
      title: '单买价格（元）',
      dataIndex: 'salePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '组合销售',
      dataIndex: 'isCompose',
      valueEnum: {
        0: '否',
        1: '是',
      },
    },
    {
      title: '组合价格（元）',
      dataIndex: 'composePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '单买续费价格（元）',
      dataIndex: 'saleRenewPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '动态定价',
      dataIndex: 'isDynamicPrice',
      valueEnum: {
        false: '否',
        true: '是',
      },
    },
  ];

  const getPriceStrategyByAgentReq = useRequest(getPriceStrategyByAgent, {
    manual: true,
    onSuccess(data, params) {
      const { isCompose, discount, composeDiscount, composePrice, salePrice, isDynamicPrice } =
        data.price || {};

      const isDynamic = isDynamicPrice || false;
      setIsDynamicPricing(isDynamic);

      formRef.current?.setFieldsValue({
        ...data.price,
        isCompose: isCompose === true ? '1' : '0',
        discount: discount * 100,
        salePrice: salePrice,
        composeDiscount: composeDiscount * 100,
        composePrice: composePrice,
        isDynamicPrice: isDynamic,
      });
    },
  });

  const addPriceStrategyReq = useRequest(addPriceStrategy, {
    manual: true,
    onSuccess(data, params) {
      message.success('修改成功');
      actionRef?.current?.reload();
      setVisible(false);
      if (onFinish) {
        onFinish();
      }
    },
  });
  const updatePriceStrategyReq = useRequest(updatePriceStrategy, {
    manual: true,
    onSuccess(data, params) {
      message.success('修改成功');
      actionRef?.current?.reload();
      setVisible(false);
      if (onFinish) {
        onFinish();
      }
    },
  });

  const infoList = [
    {
      title: '市场标准价',
      render: goodsItem?.marketPrice,
    },
    {
      title: (
        <>
          商品折扣
          <Tooltip
            placement="top"
            title={
              <>
                <div>门票 - 全价票为网订折扣率</div>
                <div>门票 - 团体票为团体折扣率</div>
                <div>门票 - 其他票种为特殊折扣率</div>
                <div>权益卡为商品折扣率</div>
              </>
            }
          >
            <QuestionCircleOutlined style={{ marginLeft: 4 }} />
          </Tooltip>
        </>
      ),
      render: (
        <Space direction="vertical">
          <span> {goodsItem?.overallDiscount}%</span>
          <span> {(goodsItem?.marketPrice * goodsItem?.overallDiscount) / 100}</span>
        </Space>
      ),
    },
    {
      title: '分销折扣区间',
      render: (
        <Space direction="vertical">
          <span>
            {goodsItem?.beginDiscount}% ~ {goodsItem?.endDiscount}%
          </span>
          <span>{`${beginPrice} ~ ${endPrice}`}</span>
        </Space>
      ),
    },
    {
      title: '进货价',
      render: goodsItem?.purchasePrice?.join('/'),
    },
  ];

  // 处理动态定价开关变化
  const handleDynamicPricingChange = (checked: boolean) => {
    setIsDynamicPricing(checked);

    // 如果关闭总开关，同时关闭子策略开关
    if (!checked) {
      setWeatherStrategyEnabled(false);
      setCapacityStrategyEnabled(false);
    }
  };

  // 处理子策略开关变化
  const handleSubStrategyChange = (type: 'weather' | 'capacity', checked: boolean) => {
    if (type === 'weather') {
      setWeatherStrategyEnabled(checked);
    } else if (type === 'capacity') {
      setCapacityStrategyEnabled(checked);
    }

    // 如果任一子策略开启，则总开关也开启
    if (checked) {
      setIsDynamicPricing(true);
    } else {
      // 如果两个子策略都关闭，则总开关也关闭
      const isAnyStrategyEnabled =
        (type === 'weather' ? false : weatherStrategyEnabled) ||
        (type === 'capacity' ? false : capacityStrategyEnabled);

      if (!isAnyStrategyEnabled) {
        setIsDynamicPricing(false);
      }
    }
  };

  // 提交
  const onSubmit = async () => {
    // 获取表单实例
    const form = formRef.current;

    // 如果动态定价关闭，只验证基本字段
    const fieldsToValidate = isDynamicPricing
      ? undefined // 验证所有字段
      : [
          'discount',
          'salePrice',
          'isCompose',
          'composeDiscount',
          'composePrice',
          'commissionRate',
          'saleRenewPrice',
          'renewDiscount',
        ];

    try {
      // 先处理表单获取到的值（根据动态定价状态决定验证哪些字段）
      const values = await form?.validateFields(fieldsToValidate);

      // 处理星期数据，将 weekdays 数组转换为 startDay 和 endDay
      if (isDynamicPricing && values.priceStrategies && values.priceStrategies.length > 0) {
        const processedStrategies = values.priceStrategies.map((strategy) => {
          if (
            strategy.type === 'season' &&
            strategy.condition === '星期' &&
            strategy.weekdays?.length > 0
          ) {
            // 获取选中的星期，按照星期一到星期日的顺序排序
            const weekOrder = [
              '星期一',
              '星期二',
              '星期三',
              '星期四',
              '星期五',
              '星期六',
              '星期日',
            ];
            const sortedWeekdays = strategy.weekdays.sort(
              (a, b) => weekOrder.indexOf(a) - weekOrder.indexOf(b),
            );

            // 设置起始和结束星期
            return {
              ...strategy,
              startDay: sortedWeekdays[0],
              endDay: sortedWeekdays[sortedWeekdays.length - 1],
            };
          }
          return strategy;
        });

        // 更新表单值
        values.priceStrategies = processedStrategies;
      }

      // 获取原始数据
      const originalIsCompose = priceItem?.isCompose;
      const originalDiscount = priceItem?.discount;
      const originalComposeDiscount = priceItem?.composeDiscount;

      // 操作日志数据
      const logChangeConfig = {
        list: logList,
        beforeData: {
          ...priceItem,
          isCompose: originalIsCompose === true ? '1' : '0',
          discount: originalDiscount * 100,
          composeDiscount: originalComposeDiscount * 100,
          isDynamicPrice: priceItem?.isDynamicPrice || false,
        },
        afterData: values,
      };

      // 根据是否动态定价决定提交的数据
      const submitData = {
        discount: ceil(values.discount / 100, 4),
        composeDiscount: values.composeDiscount ? ceil(values.composeDiscount / 100, 4) : undefined,
        isCompose: values.isCompose === '1' ? true : false,
        salePrice: values.salePrice,
        composePrice: values.composePrice,
        commissionRate: values.commissionRate,
        saleRenewPrice: values.saleRenewPrice,
        renewDiscount: values.renewDiscount,
        isDynamicPrice: isDynamicPricing,
        // 只有在动态定价开启时才提交相关数据
        ...(isDynamicPricing
          ? {
              weatherStrategyEnabled,
              capacityStrategyEnabled,
              priceStrategies: values.priceStrategies,
              costLimitValue: values?.costLimitValue as number | undefined,
              costLimitUnit: values?.costLimitUnit as string | undefined,
              updateMode: values?.updateMode as string | undefined,
            }
          : {}),
      };

      if (type === 'add' && values) {
        const { groupId = [], ...rest } = values || {};

        await addPriceStrategyReq.run(
          {
            distributorId: coId,
            groupId: [],
            price: submitData,
            goodsId: goodsItem?.goodsId,
            unitId: goodsItem.unitId,
          },
          true,
        );
        // 更新引导
        updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_2 });
        // 添加操作日志
        addOperationLogRequest({
          action: 'add',
          content: `新增【${goodsItem.name}】直销价格策略`,
        });
        return;
      }
      if (type === 'update' && priceItem && values) {
        const { groupId = [], ...rest } = values || {};

        await updatePriceStrategyReq.run({
          distributorId: coId,
          goodsId: goodsItem?.goodsId,
          addGroupId: [],
          delGroupId: [],
          price: submitData,
          priceId: priceItem.priceId,
          unitId: goodsItem.unitId, // 商品 id
        });
        // 更新引导
        updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_2 });
        // 添加操作日志
        addOperationLogRequest({
          action: 'edit',
          changeConfig: logChangeConfig,
          content: `编辑【${goodsItem.name}】直销价格策略`,
        });
      }
    } catch (error) {
      console.error('表单验证失败：', error);
    }
  };

  // 添加重置  详情、编辑赋值
  useEffect(() => {
    if (visible) {
      console.log(priceItem);
      if (type === 'add') {
        formRef.current?.resetFields();
        setIsDynamicPricing(false);
        setWeatherStrategyEnabled(false);
        setCapacityStrategyEnabled(false);
      } else if (type === 'update' && priceItem) {
        const { isCompose, discount, composeDiscount } = priceItem || {};

        const isDynamic = priceItem?.isDynamicPrice || false;
        setIsDynamicPricing(isDynamic);

        // 设置子策略状态
        setWeatherStrategyEnabled(priceItem?.weatherStrategyEnabled || false);
        setCapacityStrategyEnabled(priceItem?.capacityStrategyEnabled || false);

        // 深拷贝 priceItem 避免直接修改原对象
        const formData = { ...priceItem };

        // 处理星期条件项
        if (formData.priceStrategies && formData.priceStrategies.length > 0) {
          formData.priceStrategies = formData.priceStrategies.map((strategy) => {
            if (strategy.type === 'season' && strategy.condition === '星期') {
              // 创建星期日到星期六的顺序数组
              const weekOrder = [
                '星期一',
                '星期二',
                '星期三',
                '星期四',
                '星期五',
                '星期六',
                '星期日',
              ];
              const startIndex = weekOrder.indexOf(strategy.startDay);
              const endIndex = weekOrder.indexOf(strategy.endDay);

              // 如果有起始和结束日，创建对应的 weekdays 数组
              if (startIndex >= 0 && endIndex >= 0) {
                // 创建包含起始日到结束日的所有星期
                const weekdays = [];
                for (let i = startIndex; i <= endIndex; i++) {
                  weekdays.push(weekOrder[i]);
                }
                return { ...strategy, weekdays };
              }
            }
            return strategy;
          });
        }

        formRef.current?.setFieldsValue({
          ...formData,
          isCompose: isCompose === true ? '1' : '0',
          discount: discount * 100,
          composeDiscount: composeDiscount * 100,
          isDynamicPrice: isDynamic,
        });
      } else if (type === 'info') {
        getPriceStrategyByAgentReq.run({
          distributorId: coId,
          unitId: goodsItem?.unitId,
          priceId: goodsItem?.priceId,
        });
      }
    }
  }, [
    visible,
    type,
    coId,
    getPriceStrategyByAgentReq,
    goodsItem?.priceId,
    goodsItem?.unitId,
    priceItem,
  ]);

  // 渲染固定定价表单
  const renderFixedPriceForm = () => {
    return (
      <>
        <ProForm.Item label="单买价格" required style={{ margin: 0 }}>
          <Space>
            <ProFormDigit
              width="xs"
              name="discount"
              fieldProps={{
                precision: 2,
              }}
              rules={rules}
              min={goodsItem?.beginDiscount}
              max={goodsItem?.endDiscount}
              addonAfter="%"
              disabled={type === 'info'}
            />
            <ProFormDigit
              width="xs"
              addonAfter="元"
              name="salePrice"
              fieldProps={{
                precision: 2,
              }}
              min={beginPrice || 0}
              max={endPrice || undefined}
            />
          </Space>
        </ProForm.Item>

        {isRightsCard && (
          <ProForm.Item label="单买续费价格" required style={{ margin: 0 }}>
            <Space>
              <ProFormDigit
                width="xs"
                name="renewDiscount"
                fieldProps={{
                  precision: 2,
                }}
                rules={rules}
                min={goodsItem?.beginDiscount}
                max={goodsItem?.endDiscount}
                addonAfter="%"
                disabled={type === 'info'}
              />
              <ProFormDigit
                width="xs"
                addonAfter="元"
                name="saleRenewPrice"
                fieldProps={{
                  precision: 2,
                }}
                min={beginPrice || 0}
                max={endPrice || undefined}
              />
            </Space>
          </ProForm.Item>
        )}

        {!isRightsCard && !isRightsTicket && (
          <>
            <ProFormSelect
              label="组合销售"
              name="isCompose"
              width="sm"
              disabled={type === 'info'}
              valueEnum={{
                0: '否',
                1: '是',
              }}
              rules={rules}
            />
            <ProFormDependency name={['isCompose']}>
              {({ isCompose }) => {
                return isCompose === '1' ? (
                  <>
                    <ProForm.Item label={'组合价格'} required style={{ margin: 0 }}>
                      <Space>
                        <ProFormDigit
                          disabled={type === 'info'}
                          addonAfter="%"
                          width="xs"
                          name="composeDiscount"
                          fieldProps={{
                            precision: 2,
                          }}
                          rules={rules}
                          min={goodsItem?.beginDiscount}
                          max={goodsItem?.endDiscount}
                        />
                        <ProFormDigit
                          width="xs"
                          addonAfter="元"
                          name="composePrice"
                          fieldProps={{
                            precision: 2,
                          }}
                          min={beginPrice || 0}
                          max={endPrice || undefined}
                        />
                      </Space>
                    </ProForm.Item>
                  </>
                ) : null;
              }}
            </ProFormDependency>
          </>
        )}
      </>
    );
  };

  // 渲染动态定价表单
  const renderDynamicPriceForm = () => {
    return (
      <>
        <ProForm.Item label="单买价格" required style={{ margin: '0 0 16px 0' }}>
          <Space>
            <ProFormDigit
              width="xs"
              name="discount"
              fieldProps={{
                precision: 2,
              }}
              rules={rules}
              min={goodsItem?.beginDiscount}
              max={goodsItem?.endDiscount}
              addonAfter="%"
              disabled={type === 'info'}
            />
            <ProFormDigit
              width="xs"
              addonAfter="元"
              name="salePrice"
              fieldProps={{
                precision: 2,
              }}
              min={beginPrice || 0}
              max={endPrice || undefined}
            />
          </Space>
        </ProForm.Item>

        <ProForm.Item label="价格策略" style={{ margin: '0 0 16px 0' }}>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              type="text"
              style={{
                padding: '4px 8px',
                height: 'auto',
                fontSize: '13px',
                color: '#1890ff',
                borderRadius: '3px',
                border: '1px dashed #91d5ff',
                display: 'flex',
                alignItems: 'center',
              }}
              onClick={() => {
                const newStrategies = [
                  ...(formRef.current?.getFieldValue('priceStrategies') || []),
                ];
                newStrategies.push({
                  type: 'weather',
                  condition: '出游指数',
                  operator: '为',
                  value: '适宜',
                  priceChange: '上涨',
                  adjustment: '',
                  unit: '%',
                });
                formRef.current?.setFieldsValue({ priceStrategies: newStrategies });
              }}
              disabled={type === 'info'}
            >
              <span style={{ fontSize: '12px', marginRight: '3px' }}>+</span>
              天气策略
            </Button>

            <Button
              type="text"
              style={{
                padding: '4px 8px',
                height: 'auto',
                fontSize: '13px',
                color: '#52c41a',
                borderRadius: '3px',
                border: '1px dashed #b7eb8f',
                display: 'flex',
                alignItems: 'center',
              }}
              onClick={() => {
                const newStrategies = [
                  ...(formRef.current?.getFieldValue('priceStrategies') || []),
                ];
                newStrategies.push({
                  type: 'capacity',
                  condition: '日销售量',
                  operator: '为日承载量的',
                  value: '',
                  unit: '%',
                  priceChange: '上涨',
                  adjustment: '',
                  adjustmentUnit: '%',
                });
                formRef.current?.setFieldsValue({ priceStrategies: newStrategies });
              }}
              disabled={type === 'info'}
            >
              <span style={{ fontSize: '12px', marginRight: '3px' }}>+</span>
              日承载量策略
            </Button>

            <Button
              type="text"
              style={{
                padding: '4px 8px',
                height: 'auto',
                fontSize: '13px',
                color: '#fa8c16',
                borderRadius: '3px',
                border: '1px dashed #ffd591',
                display: 'flex',
                alignItems: 'center',
              }}
              onClick={() => {
                const newStrategies = [
                  ...(formRef.current?.getFieldValue('priceStrategies') || []),
                ];
                newStrategies.push({
                  type: 'season',
                  condition: '日期',
                  operator: '为',
                  startDate: '',
                  endDate: '',
                  priceChange: '上涨',
                  adjustment: '',
                  adjustmentUnit: '%',
                });
                formRef.current?.setFieldsValue({ priceStrategies: newStrategies });
              }}
              disabled={type === 'info'}
            >
              <span style={{ fontSize: '12px', marginRight: '3px' }}>+</span>
              淡旺季策略
            </Button>
          </div>
        </ProForm.Item>

        {/* 显示已添加的策略列表 */}
        <ProFormDependency name={['priceStrategies']}>
          {({ priceStrategies }) =>
            priceStrategies && priceStrategies.length > 0 ? (
              <ProFormList
                name="priceStrategies"
                creatorButtonProps={false}
                min={0}
                copyIconProps={false}
                style={{ margin: '0 0 16px 24px' }}
                itemRender={({ listDom, action }, { record, index }) => (
                  <StrategyRow
                    record={record}
                    index={index}
                    action={action}
                    type={type}
                    formRef={formRef}
                  />
                )}
              />
            ) : null
          }
        </ProFormDependency>

        <ProForm.Item label="成本限制" required style={{ margin: '0 0 16px 0' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div>不低于进货价的</div>
            <Form.Item name="costLimitValue" noStyle>
              <InputNumber
                style={{ width: '80px', margin: '0 8px' }}
                min={0}
                precision={2}
                disabled={type === 'info'}
              />
            </Form.Item>
            <Form.Item name="costLimitUnit" initialValue="%" noStyle>
              <Select
                style={{ width: '70px', marginRight: '8px' }}
                options={[
                  { label: '%', value: '%' },
                  { label: '元', value: '元' },
                ]}
                disabled={type === 'info'}
              />
            </Form.Item>
            <div>且不高于市场标准价</div>
          </div>
        </ProForm.Item>

        {!isRightsCard && !isRightsTicket && (
          <>
            <ProFormSelect
              label="组合销售"
              name="isCompose"
              width="sm"
              disabled={type === 'info'}
              valueEnum={{
                0: '否',
                1: '是',
              }}
              rules={rules}
              fieldProps={{
                style: { marginBottom: '16px' },
              }}
            />
            <ProFormDependency name={['isCompose']}>
              {({ isCompose }) => {
                return isCompose === '1' ? (
                  <>
                    <ProForm.Item label={'组合价格'} required style={{ margin: '0 0 16px 0' }}>
                      <Space>
                        <ProFormDigit
                          disabled={type === 'info'}
                          addonAfter="%"
                          width="xs"
                          name="composeDiscount"
                          fieldProps={{
                            precision: 2,
                          }}
                          rules={rules}
                          min={goodsItem?.beginDiscount}
                          max={goodsItem?.endDiscount}
                        />
                        <ProFormDigit
                          width="xs"
                          addonAfter="元"
                          name="composePrice"
                          fieldProps={{
                            precision: 2,
                          }}
                          min={beginPrice || 0}
                          max={endPrice || undefined}
                        />
                      </Space>
                    </ProForm.Item>
                  </>
                ) : null;
              }}
            </ProFormDependency>
          </>
        )}

        <ProFormSelect
          label="更新方式"
          name="updateMode"
          width="sm"
          disabled={type === 'info'}
          valueEnum={{
            auto: '自动更新',
            manual: '手动更新',
          }}
          initialValue="auto"
          rules={rules}
          fieldProps={{
            style: { marginBottom: '16px' },
          }}
        />
      </>
    );
  };

  return (
    <Modal
      width={modelWidth.lg}
      title="编辑动态定价"
      open={visible}
      maskClosable={false}
      onCancel={() => setVisible(false)}
      onOk={() => setVisible(false)}
      okText="保存"
      cancelText="取消"
    >
      {goodsInfo && (
        <div style={{ marginBottom: 20 }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ borderBottom: '1px solid #f0f0f0' }}>
                <th style={{ padding: '8px', textAlign: 'left' }}>商品名称</th>
                <th style={{ padding: '8px', textAlign: 'left' }}>票种</th>
                <th style={{ padding: '8px', textAlign: 'left' }}>市场标准价</th>
                <th style={{ padding: '8px', textAlign: 'left' }}>商品折扣</th>
                <th style={{ padding: '8px', textAlign: 'left' }}>分销折扣区间</th>
                <th style={{ padding: '8px', textAlign: 'left' }}>进货价</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td style={{ padding: '8px' }}>{goodsInfo.name}</td>
                <td style={{ padding: '8px' }}>{goodsInfo.category}</td>
                <td style={{ padding: '8px' }}>{goodsInfo.marketPrice.toFixed(2)}</td>
                <td style={{ padding: '8px' }}>
                  <div>{goodsInfo.discount}</div>
                  <div>({((goodsInfo.marketPrice * 100) / 100).toFixed(2)})</div>
                </td>
                <td style={{ padding: '8px' }}>
                  <div>{goodsInfo.discountRange}</div>
                  <div>(0~100)</div>
                </td>
                <td style={{ padding: '8px' }}>{goodsInfo.purchasePrice.toFixed(2)}</td>
              </tr>
            </tbody>
          </table>
        </div>
      )}

      {/* 表单部分 */}
      <div style={{ padding: '0 16px' }}>
        <ProForm
          formRef={formRef}
          layout="horizontal"
          labelAlign="right"
          labelCol={{ span: 4 }}
          submitter={false}
          preserve={false}
        >
          {/* 动态定价开关 */}
          <div className="section-header" style={{ marginBottom: 24 }}>
            <div
              className="section-title-wrapper"
              style={{ display: 'flex', alignItems: 'center' }}
            >
              <div
                className="section-indicator"
                style={{
                  width: '4px',
                  height: '16px',
                  backgroundColor: '#bfbfbf',
                  marginRight: '8px',
                  borderRadius: '2px',
                }}
              />
              <div
                style={{
                  fontWeight: 'bold',
                  fontSize: '16px',
                  marginRight: '12px',
                }}
              >
                动态定价:
              </div>
              <ProFormSwitch
                name="isDynamicPrice"
                noStyle
                fieldProps={{
                  checked: isDynamicPricing,
                  onChange: handleDynamicPricingChange,
                  checkedChildren: '启用',
                  unCheckedChildren: '禁用',
                  disabled: type === 'info',
                }}
              />
            </div>
          </div>

          {/* 价格策略 */}
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ color: isDynamicPricing ? '#f5222d' : '#bfbfbf', marginRight: 4 }}>
                *
              </div>
              <div style={{ fontWeight: 500, color: isDynamicPricing ? 'inherit' : '#bfbfbf' }}>
                价格策略：
              </div>
            </div>
          </div>

          {/* 天气策略 */}
          <div
            style={{
              marginBottom: 16,
              padding: 16,
              backgroundColor: '#f9f9f9',
              borderRadius: 4,
              opacity: isDynamicPricing ? 1 : 0.6,
              pointerEvents: isDynamicPricing ? 'auto' : 'none',
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <div
                style={{
                  color: isDynamicPricing && weatherStrategyEnabled ? '#f5222d' : '#bfbfbf',
                  marginRight: 4,
                  marginLeft: 0,
                }}
              >
                *
              </div>
              <div style={{ color: isDynamicPricing ? 'inherit' : '#bfbfbf' }}>天气策略：</div>
              <div style={{ marginLeft: 'auto' }}>
                <Switch
                  checked={weatherStrategyEnabled}
                  onChange={(checked) => handleSubStrategyChange('weather', checked)}
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                  disabled={!isDynamicPricing || type === 'info'}
                />
              </div>
            </div>

            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                flex: 1,
                marginLeft: 20,
                opacity: weatherStrategyEnabled ? 1 : 0.6,
                pointerEvents: weatherStrategyEnabled ? 'auto' : 'none',
              }}
            >
              <div>当</div>
              <Select
                defaultValue="出游指数"
                style={{ width: 100, margin: '0 8px' }}
                options={[
                  { label: '出游指数', value: '出游指数' },
                  { label: '平均气温', value: '平均气温' },
                ]}
                bordered={false}
                disabled={!weatherStrategyEnabled || type === 'info'}
              />
              <div>为</div>
              <Select
                defaultValue="适宜"
                style={{ width: 100, margin: '0 8px' }}
                options={[
                  { label: '适宜', value: '适宜' },
                  { label: '较适宜', value: '较适宜' },
                  { label: '较不适宜', value: '较不适宜' },
                  { label: '不适宜', value: '不适宜' },
                ]}
                bordered={false}
                disabled={!weatherStrategyEnabled || type === 'info'}
              />
              <div>时</div>
              <Select
                defaultValue="上涨"
                style={{ width: 80, margin: '0 8px' }}
                options={[
                  { label: '上涨', value: '上涨' },
                  { label: '下调', value: '下调' },
                ]}
                bordered={false}
                disabled={!weatherStrategyEnabled || type === 'info'}
              />
              <InputNumber
                style={{ width: 80, margin: '0 8px' }}
                disabled={!weatherStrategyEnabled || type === 'info'}
              />
              <Select
                defaultValue="%"
                style={{ width: 60 }}
                options={[
                  { label: '%', value: '%' },
                  { label: '元', value: '元' },
                ]}
                bordered={false}
                disabled={!weatherStrategyEnabled || type === 'info'}
              />
            </div>
          </div>

          {/* 日承载量策略 */}
          <div
            style={{
              marginBottom: 24,
              padding: 16,
              backgroundColor: '#f9f9f9',
              borderRadius: 4,
              opacity: isDynamicPricing ? 1 : 0.6,
              pointerEvents: isDynamicPricing ? 'auto' : 'none',
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <div
                style={{
                  color: isDynamicPricing && capacityStrategyEnabled ? '#f5222d' : '#bfbfbf',
                  marginRight: 4,
                  marginLeft: 0,
                }}
              >
                *
              </div>
              <div style={{ color: isDynamicPricing ? 'inherit' : '#bfbfbf' }}>日承载量策略：</div>
              <div style={{ marginLeft: 'auto' }}>
                <Switch
                  checked={capacityStrategyEnabled}
                  onChange={(checked) => handleSubStrategyChange('capacity', checked)}
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                  disabled={!isDynamicPricing || type === 'info'}
                />
              </div>
            </div>

            <div
              style={{
                marginLeft: 20,
                display: 'flex',
                alignItems: 'center',
                marginBottom: 16,
                opacity: capacityStrategyEnabled ? 1 : 0.6,
                pointerEvents: capacityStrategyEnabled ? 'auto' : 'none',
              }}
            >
              <div>当</div>
              <Select
                defaultValue="日销售量"
                style={{ width: 100, margin: '0 8px' }}
                options={[
                  { label: '日销售量', value: '日销售量' },
                  { label: '日入园人数', value: '日入园人数' },
                ]}
                bordered={false}
                disabled={!capacityStrategyEnabled || type === 'info'}
              />
              <div>为日承载量的</div>
              <InputNumber
                style={{ width: 80, margin: '0 8px' }}
                disabled={!capacityStrategyEnabled || type === 'info'}
              />
              <div>%</div>
              <div style={{ margin: '0 8px' }}>时</div>
              <Select
                defaultValue="上涨"
                style={{ width: 80, margin: '0 8px' }}
                options={[
                  { label: '上涨', value: '上涨' },
                  { label: '下调', value: '下调' },
                ]}
                bordered={false}
                disabled={!capacityStrategyEnabled || type === 'info'}
              />
              <InputNumber
                style={{ width: 80, margin: '0 8px' }}
                disabled={!capacityStrategyEnabled || type === 'info'}
              />
              <Select
                defaultValue="%"
                style={{ width: 60 }}
                options={[
                  { label: '%', value: '%' },
                  { label: '元', value: '元' },
                ]}
                bordered={false}
                disabled={!capacityStrategyEnabled || type === 'info'}
              />
            </div>
          </div>

          {/* 成本限制 */}
          <div
            style={{
              marginBottom: 24,
              opacity: isDynamicPricing ? 1 : 0.6,
              pointerEvents: isDynamicPricing ? 'auto' : 'none',
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <div style={{ color: isDynamicPricing ? '#f5222d' : '#bfbfbf', marginRight: 4 }}>
                *
              </div>
              <div style={{ fontWeight: 500, color: isDynamicPricing ? 'inherit' : '#bfbfbf' }}>
                成本限制：
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', marginLeft: 20 }}>
              <div>价格不低于进货价</div>
              <InputNumber
                style={{ width: 100, margin: '0 8px' }}
                placeholder="请输入百分比"
                disabled={!isDynamicPricing || type === 'info'}
              />
              <Select
                defaultValue="%"
                style={{ width: 60 }}
                options={[
                  { label: '%', value: '%' },
                  { label: '元', value: '元' },
                ]}
                disabled={!isDynamicPricing || type === 'info'}
              />
            </div>
          </div>

          {/* 更新方式 */}
          <div
            style={{
              marginBottom: 24,
              opacity: isDynamicPricing ? 1 : 0.6,
              pointerEvents: isDynamicPricing ? 'auto' : 'none',
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <div style={{ color: isDynamicPricing ? '#f5222d' : '#bfbfbf', marginRight: 4 }}>
                *
              </div>
              <div style={{ fontWeight: 500, color: isDynamicPricing ? 'inherit' : '#bfbfbf' }}>
                更新方式：
              </div>
            </div>
            <div style={{ marginLeft: 20 }}>
              <Select
                defaultValue="手动更新"
                style={{ width: 180 }}
                options={[
                  { label: '手动更新', value: '手动更新' },
                  { label: '自动更新', value: '自动更新' },
                ]}
                disabled={!isDynamicPricing || type === 'info'}
              />
            </div>
          </div>
        </ProForm>
      </div>
    </Modal>
  );
};

export default DynamicPriceStrategyModal;
