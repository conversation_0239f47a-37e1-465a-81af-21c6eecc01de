.guideIcon {
  position: fixed;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background-color: #fff;
  cursor: pointer;
  box-shadow: 0px 0px 22px -2px rgba(57,58,64,0.2);
  font-size: 12px;
  color: #2F3033;

  backdrop-filter: saturate(180%) blur(20px);
  pointer-events: auto;
  inset-block-start: 240px;
  inset-inline-end: 0px;
  border-end-start-radius: 8px;
  border-start-start-radius: 8px;
  -webkit-backdropilter: saturate(180%) blur(20px);

  :global {
    .ant-divider-horizontal {
      margin: 10px 0;
    }
  }
}

.MessageDrawer {
  top: 56px !important;

  :global {
    .ant-drawer-mask {
      background-color: transparent;
    }
    .ant-drawer-content-wrapper {
      box-shadow: 0px 2px 4px 0px rgba(160,159,159,0.5);
    }
  }
}