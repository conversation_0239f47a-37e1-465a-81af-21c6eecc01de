/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-09 10:01:45
 * @LastEditTime: 2023-10-09 10:17:15
 * @LastEditors: zhangfeng<PERSON>i
 */
import { tableConfig } from '@/common/utils/config';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Tag } from 'antd';
import StorkInfoExpand from './StorkInfoExpand';

export default ({ salesGoodsList }: { salesGoodsList: any }) => {
  const columns: ProColumns[] = [
    {
      title: '库存批次号',
      dataIndex: 'batchId',
      render: (_: any, record) =>
        record?.isExchange == 1 ? (
          <>
            {record?.batchId || ''} <Tag color="blue">交易所</Tag>
          </>
        ) : (
          record?.batchId
        ),
    },
    {
      title: '购买有效时间',
      dataIndex: 'purchaseTime',
      render: (_: any, record) =>
        record.purchaseBeginTime ? (
          <span>
            {record.purchaseBeginTime} 至 {record.purchaseEndTime}
          </span>
        ) : (
          '-'
        ),
    },
    {
      title: '入园有效时间',
      dataIndex: 'time',
      render: (_: any, record) =>
        record.dayBegin ? (
          <span>
            {record.dayBegin} 至 {record.dayEnd}
          </span>
        ) : (
          '-'
        ),
    },
    {
      title: '可用库存',
      dataIndex: 'availableNumber',
    },
  ];

  return (
    <ProTable
      {...tableConfig}
      style={{ width: '100%' }}
      rowKey="stockId"
      search={false}
      options={false}
      pagination={false}
      columns={columns}
      dataSource={salesGoodsList}
      expandable={{
        expandedRowRender: (record, _, __, expanded) => (
          <StorkInfoExpand dataItem={record} expanded={expanded} />
        ),
        rowExpandable: (record) => record.timeShareId && record.timeShareId != 0,
      }}
      className={'detailProTable'}
    />
  );
};
