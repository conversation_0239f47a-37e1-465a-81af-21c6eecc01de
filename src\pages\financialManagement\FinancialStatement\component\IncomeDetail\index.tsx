import { payTypeEnum } from '@/common/utils/enum';
import { apiIncomeDetail } from '@/services/api/financialStatement';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Col, Row } from 'antd';
import { useRef, useState } from 'react';
import { useModel } from '@umijs/max';

import { tableConfig } from '@/common/utils/config';
import useModal from '@/hooks/useModal';
import RefundManagement from '@/pages/business/orderManagement/Refund/RefundManagement';
import OrderManagement from '@/pages/business/orderManagement/Sale/OrderManagement';
import { apiOderPageParticularsList } from '@/services/api/erp';
import dayjs from 'dayjs';
interface IIncomeDetailParams {
  startTime: string;
  endTime: string;
  user_name: string;
  order_id: string;
  trade_no: string;
  settlement_name: string;
  transaction_direction: string;
  pay_type: string;
}

interface DataItem {
  order_id: string;
  trade_no: string;
  transaction_direction: string;
  pay_type: string;
  user_name: string;
  settlement_name: string;
  sub_amount: number;
}

const PAY_DIRECTION = {
  0: '收入',
  1: '支出',
};

export default () => {
  const actionRef = useRef<ActionType>();
  const [amount, setAmount] = useState(0);
  // 订单详情显示标志
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  // 退单详情显示标志
  const [refundDetailVisible, setRefundDetailVisible] = useState<boolean>(false);
  const [resStatus, setResStatus] = useState(false);
  // 退订信息
  const [unsubscribeMessage, setUnsubscribeMessage] = useState([]);
  // 退订单 ID
  const [backOrderId, setbackOrderId] = useState('');
  //当前订单详情
  const [particularData, setparticularData] = useState({});
  //票务详情
  const [inItTicketInfo, setTicketInfo] = useState([]);
  //订单详情状态
  const [orderParticulaStatus, setOrderParticularStatus] = useState({});
  // 获取企业 ID
  const { initialState }: any = useModel('@@initialState');
  const companyID = initialState?.currentCompany?.coId;

  //绑定详情订单状态
  const detailModal = useModal();
  const [currentRow, setCurrentRow] = useState<Record<string, any>>();

  //获取当前订单详情列表
  const apiOderPageParticularsData = async (orderId: any) => {
    try {
      const res = await apiOderPageParticularsList({ orderId, pageSize: 999 });
      setResStatus(true);
      console.log('订单详情', res);
      const { data } = res;
      const { order, ticketInfoList, orderStatus, orderRefund }: any = data;
      const { data: val } = ticketInfoList;

      setparticularData(order);
      setTicketInfo(val);
      setOrderParticularStatus(orderStatus);
      setUnsubscribeMessage(orderRefund);
    } catch (e) {
      console.error(e);
      return {};
    }
  };

  const titleNumberClick = (orderId: string, id: any) => {
    if (id.startsWith('RF')) {
      setbackOrderId(id);
      setRefundDetailVisible(true);
      apiOderPageParticularsData(orderId);
    } else {
      detailModal.setVisible(true);
    }
  };

  // 退单详情取消按钮
  const onRefundClose = (flag: boolean) => {
    setRefundDetailVisible(flag);
  };
  const columns: ProColumns<DataItem>[] = [
    {
      title: '统计日期',
      dataIndex: 'date',
      valueType: 'dateRange',
      hideInTable: true,
      initialValue: [dayjs().startOf('months').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '订单号',
      dataIndex: 'order_id',
      render: (dom: any, record: any) => {
        // 这后端我服了
        const { orderid: orderId, order_id } = record;
        return (
          <a
            onClick={() => {
              titleNumberClick(orderId, order_id);
              setCurrentRow({ ...record, orderId });
            }}
          >
            {dom}
          </a>
        );
      },
    },

    {
      title: '结算单号',
      dataIndex: 'trade_no',
    },
    {
      title: '交易时间',
      dataIndex: 'transaction_time',
      search: false,
    },
    {
      title: '交易方向',
      dataIndex: 'reverse_direction',
      valueEnum: PAY_DIRECTION,
    },
    {
      title: '支付方式',
      dataIndex: 'pay_type',
      valueEnum: payTypeEnum,
      render: (_, record) => {
        return <>{payTypeEnum[record?.pay_type] || '其他'}</>;
      },
    },
    {
      title: '交易对象',
      dataIndex: 'transaction_name',
    },
    {
      title: '商户号',
      dataIndex: 'settlement_name',
    },
    {
      title: '金额（元）',
      dataIndex: 'sub_amount',
      search: false,
      align: 'right',
      render: (_, record) => {
        return (
          <div>
            <span>{record.sub_amount >= 0 ? '+' : ''}</span>
            {Number(record.sub_amount).toFixed(2)}
          </div>
        );
      },
    },
  ];

  const getIncomeDetailList = async (params: Record<string, IIncomeDetailParams>) => {
    const {
      startTime = '',
      endTime = '',
      order_id = '',
      trade_no = '',
      settlement_name = '',
      reverse_direction = '',
      pay_type = '',
      current,
      pageSize,
      transaction_name = '',
    } = params;

    try {
      const query = {
        co_id: companyID,
        start_day: startTime,
        end_day: endTime,
        user_name: transaction_name,
        order_id,
        trade_no,
        settlement_name,
        pay_type,
        transaction_direction: reverse_direction,
        m: pageSize, //pageSize
        n: current, //page
      };

      // 2024年7月31日 接口三合一

      const { result } = await apiIncomeDetail(query);
      let total = 0; //数据总条数
      let infoIndex = 0; //合计项的索引
      for (; infoIndex < result.length; infoIndex++) {
        const _n = result[infoIndex];
        if (_n.id == '合计' || _n.refund_id == '合计') {
          // 总价
          setAmount(_n.sub_amount || 0);
          if (_n.total != 0) {
            total = _n.total;
          }
          break;
        }
      }

      result.splice(infoIndex, 1); //排除掉合计项
      return {
        success: true,
        data: result || [],
        total: total,
      };
    } catch (e: any) {
      console.error('错误 111', e);
      return {
        success: false,
        data: [],
        total: 0,
      };
    }
  };

  return (
    <div>
      <ProTable<DataItem>
        {...tableConfig}
        columns={columns}
        actionRef={actionRef}
        cardBordered
        request={getIncomeDetailList}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          onChange(value) {},
        }}
        pagination={{
          defaultPageSize: 10,
          onChange: (page) => {},
        }}
        dateFormatter="string"
      />
      <Row justify="end" style={{ backgroundColor: '#fff', paddingTop: '10px' }}>
        <Col pull={1}>
          <h2>总计：{Number(amount).toFixed(2)}元</h2>
        </Col>
      </Row>
      {/* 订单详情 */}
      <OrderManagement modalState={detailModal} currentRow={currentRow} />
      {/* <OrderManagement
        flag={detailVisible}
        resStatus={resStatus}
        onClose={onClose}
        orderParticular={particularData}
        inItTicketInfoData={inItTicketInfo}
        orderParticulaStatus={orderParticulaStatus}
      /> */}
      {/* 退订详情 */}
      <RefundManagement
        flag={refundDetailVisible}
        resStatus={resStatus}
        onClose={onRefundClose}
        orderParticular={particularData}
        inItTicketInfoData={inItTicketInfo}
        unsubscribeMessageData={unsubscribeMessage}
        backOrderId={backOrderId}
        orderParticulaStatus={orderParticulaStatus}
        // countNum={1}
      />
    </div>
  );
};
