/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-14 17:34:10
 * @LastEditTime: 2023-09-27 10:56:36
 * @LastEditors: zhangfengfei
 */
import { tableConfig } from '@/common/utils/config';
import { productTypeEnum, ticketTypeEnum } from '@/common/utils/enum';
import { getUniqueId } from '@/common/utils/tool';
import { apiStoreCommissionDetail } from '@/services/api/store';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Button, Modal } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import type { BillType, TabKeyType } from '.';

interface BillModalProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  currentItem: any;
  settlementType: TabKeyType;
  bill: BillType;
  timeRange?: {
    startDate: string;
    endDate: string;
    dateType: string;
  };
}

const BillModal: FC<BillModalProps> = ({
  visible,
  setVisible,
  currentItem,
  settlementType,
  bill,
  timeRange,
}) => {
  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany || {};
  const [tabKey, setTabKey] = useState<'ticketList' | 'travelCardList'>('ticketList');

  const [info, setInfo] = useState<any>();
  const tableListRequest = async (params: any) => {
    const { data } = await apiStoreCommissionDetail(timeRange?.dateType, params);
    setInfo({
      totalAmount: data.totalAmount,
      num: data.num,
      date: data.date,
    });
    return {
      data: data[tabKey],
    };
  };

  const ticketColumns: ProColumns<any>[] = [
    {
      title: '票号',
      dataIndex: 'ticketNumber',
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      valueEnum: productTypeEnum,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      dataIndex: 'goodsType',
      valueEnum: ticketTypeEnum,
    },

    {
      title: '分时预约',
      dataIndex: 'timeShare',
      renderText: (text, record) =>
        record.timeShareBeginTime
          ? `${record.timeShareBeginTime} - ${record.timeShareEndTime}`
          : '-',
    },
    {
      title: '出票时间',
      dataIndex: 'issueTime',
      hideInTable: bill === 'return',
      renderText: (dom) => {
        if (dom) {
          return <span>{dayjs(dom).format('YYYY-MM-DD HH:mm:ss')}</span>;
        }
        return '-';
      },
    },
    {
      title: '核销时间',
      dataIndex: 'checkTime',
      hideInTable: bill === 'return',
      renderText: (text, record) => text || record.payTime || '-',
    },
    {
      title: '结算类型',
      dataIndex: 'settlementType',
      renderText: () => (settlementType === '1' ? '前结算' : '后结算'),
    },
    {
      title: '佣金金额（元）',
      dataIndex: 'amount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '是否已生成结算账单',
      hideInTable: settlementType === '1',
      dataIndex: 'isSettlement',
      valueType: 'select',
      valueEnum: {
        0: '否',
        1: '是',
      },
    },
  ];
  const travelCardColumns: ProColumns<any>[] = [
    {
      title: '产品名称',
      dataIndex: 'travelCardName',
    },
    {
      title: '产品类型',
      key: 'type',
      renderText: () => '权益卡',
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '结算类型',
      dataIndex: 'settlementType',
      renderText: () => (settlementType === '1' ? '前结算' : '后结算'),
    },
    {
      title: '核销时间',
      dataIndex: 'checkTime',
      renderText: (text, record) => text || record.payTime || '-',
    },

    {
      title: '佣金金额（元）',
      dataIndex: 'amount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '是否已生成结算账单',
      hideInTable: settlementType === '1',
      dataIndex: 'isSettlement',
      valueType: 'select',
      valueEnum: {
        0: '否',
        1: '是',
      },
    },
  ];
  useEffect(() => {
    if (visible) setTabKey('ticketList');
  }, [visible]);

  return (
    <Modal
      width={1200}
      title={bill === 'divide' ? '分佣明细' : '退佣明细'}
      visible={visible}
      destroyOnClose
      footer={
        <>
          <Button
            onClick={() => {
              setVisible(false);
            }}
          >
            取消
          </Button>
        </>
      }
      onCancel={() => {
        setVisible(false);
      }}
    >
      <div style={{ textAlign: 'center', margin: '24px 0', fontSize: 24, fontWeight: 'bold' }}>
        {info?.date}
      </div>
      <ProTable
        {...tableConfig}
        rowKey={() => getUniqueId()}
        request={tableListRequest}
        columns={tabKey === 'ticketList' ? ticketColumns : travelCardColumns}
        params={{
          settlementType,
          storeId: currentItem?.storeId,
          date: currentItem?.date,
          supplierId: currentItem?.supplierId,
          tabKey,
          bill,
        }}
        pagination={false}
        search={false}
        options={false}
        toolbar={
          bill === 'divide'
            ? {
                menu: {
                  type: 'tab',
                  activeKey: tabKey,
                  items: [
                    {
                      label: '门票',
                      key: 'ticketList',
                    },
                    {
                      label: '权益卡',
                      key: 'travelCardList',
                    },
                  ],
                  onChange: (key: any) => {
                    setTabKey(key);
                  },
                },
              }
            : undefined
        }
      />

      <div style={{ textAlign: 'right' }}>
        <span>金额总计：{info?.totalAmount?.toFixed(2) ?? '-'}（元）</span>
        <span style={{ margin: '0 24px' }}>数量总计：{info?.num ?? '-'}（张）</span>
      </div>
    </Modal>
  );
};

export default BillModal;
