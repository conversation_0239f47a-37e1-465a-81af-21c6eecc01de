import type { DataMaskTypeEnum } from '@/common/utils/tool';
import { dataMask } from '@/common/utils/tool';
import { useState } from 'react';

/**
 * useMask Hook - 用于处理数据脱敏显示的自定义 Hook
 *
 * @description
 * 这个 Hook 用于管理数据脱敏状态和处理数据脱敏逻辑。主要用于处理敏感信息的显示，
 * 如手机号、身份证、姓名等。可以通过开关来控制是否显示完整信息。
 *
 * @returns {[(showFull: boolean) => void, (text: string, type?: DataMaskTypeEnum) => string]}
 * - [0] handleMaskChange: (showFull: boolean) => void - 切换数据显示状态的处理函数
 * - [1] maskData: (text: string, type?: DataMaskTypeEnum) => string - 数据脱敏处理函数
 *
 * @example
 * ```typescript
 * // 基础用法
 * const [handleMaskChange, maskData] = useMask();
 *
 * // 在 DataMask 组件中使用
 * <DataMask onDataMaskChange={handleMaskChange} />
 *
 * // 处理不同类型的数据脱敏
 * maskData('13812345678', DataMaskTypeEnum.PHONE)     // 输出：138****5678
 * maskData('张三', DataMaskTypeEnum.NAME)             // 输出：张*
 * maskData('110101199001011234', DataMaskTypeEnum.ID_CARD) // 输出：110101****1234
 *
 * // 在表格列中使用
 * const columns = [
 *   {
 *     title: '手机号',
 *     dataIndex: 'phone',
 *     render: (text) => maskData(text, DataMaskTypeEnum.PHONE)
 *   }
 * ];
 * ```
 *
 * @tips
 * 1. 建议每个独立的数据展示区域使用独立的 useMask 实例
 * 2. 可以配合 DataMask 组件使用，实现 UI 上的开关控制
 * 3. 支持的脱敏类型包括：手机号 (PHONE)、身份证 (ID_CARD)、姓名 (NAME)
 */

type UseMaskResult = [
  (showFull: boolean) => void,
  (text: string, type?: DataMaskTypeEnum) => string,
];

export function useMask(): UseMaskResult {
  const [showFullData, setShowFullData] = useState(false);

  const handleMaskChange = (showFull: boolean) => {
    setShowFullData(showFull);
  };

  const maskData = (text: string, type?: DataMaskTypeEnum) => {
    if (!text) return '-';
    return showFullData ? text : dataMask(text, type);
  };

  return [handleMaskChange, maskData];
}

export default useMask;
