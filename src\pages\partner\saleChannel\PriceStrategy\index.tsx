/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-20 17:05:29
 * @LastEditTime: 2022-12-21 16:14:04
 * @LastEditors: zhangfengfei
 */
import { tableConfig } from '@/common/utils/config';
import { ticketTypeEnum, whetherEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import useModal from '@/hooks/useModal';
import { getAgentGroupList } from '@/services/api/auditManage';
import { getPriceStrategyList } from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { DownOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Dropdown, Menu, Space, Tag, Tooltip } from 'antd';
import { ceil, isEmpty, isNil, round } from 'lodash';
import type { FC } from 'react';
import { useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import CheckRuleDetails from '../../../../common/components/RuleDetails/CheckRuleDetails';
import IssueRuleDetails from '../../../../common/components/RuleDetails/IssueRuleDetails';
import RetreatRuleDetails from '../../../../common/components/RuleDetails/RetreatRuleDetails';
import StrategyListModal from './StrategyListModal';
import { history } from '@umijs/max';

const PriceStrategy: FC = () => {
  const { initialState } = useModel('@@initialState');
  const { coId = '', coName = '' } = initialState?.currentCompany || {};
  const access = useAccess();
  const modalState = useModal();
  const actionRef = useRef<ActionType>();

  const [currentItem, setCurrentItem] = useState<API.PriceStrategyListItem>();

  const tableListReq = async (params: API.PriceStrategyListParams) => {
    const { data } = await getPriceStrategyList(params);
    return {
      data: data.data,
      total: data.total,
    };
  };

  const getIssueDetails = (id: string) => {
    IssueRuleDetails.show(id);
  };
  const getRetreatDetails = (id: string) => {
    RetreatRuleDetails.show(id);
  };
  const getCheckDetails = (id: string) => {
    CheckRuleDetails.show(id);
  };

  const columns: ProColumns<API.PriceStrategyListItem>[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
    },
    {
      title: '产品名称',
      dataIndex: 'proName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      hideInTable: true,
    },
    {
      title: '商品名称',
      dataIndex: 'name',
    },
    {
      title: '代理商分组',
      dataIndex: 'groupId',
      valueType: 'select',
      hideInTable: true,
      request: async () => {
        const { data } = await getAgentGroupList({
          distributorId: coId,
        });
        const res = (data ?? []).map((item) => ({
          label: item.name,
          value: item.id,
        }));

        return [
          ...res,
          {
            label: '我的直销',
            value: '-1',
          },
        ];
      },
    },

    {
      title: '类型',
      dataIndex: 'unitType',
      search: false,
      valueEnum: { 0: '组合票', 1: '单票', 2: '权益卡' },
    },
    {
      title: '票种',
      dataIndex: 'goodsType',
      valueEnum: ticketTypeEnum,
      search: false,
    },

    {
      title: '数字资产',
      dataIndex: 'isDigit',
      valueEnum: whetherEnum,
    },
    {
      title: '分时时段',
      hideInSearch: true,
      dataIndex: 'timeShareVoList',
      render: (_, { timeShareVoList }: any) => {
        return timeShareVoList
          ? timeShareVoList.map((item: any) => (
              <Tag key={item.id}>{[item.beginTime, item.endTime].join('-')}</Tag>
            ))
          : '-';
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      search: false,
      renderText: (_, record) => {
        return (
          (record.supplierInfoList ?? [])
            .map((item) => item.supplierName)
            .filter((item: string) => item !== '')
            .join(',') || '-'
        );
      },
    },
    {
      title: '市场标准价（元）',
      dataIndex: 'marketPrice',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: (
        <>
          商品折扣
          <Tooltip
            placement="top"
            title={
              <>
                <div>门票 - 全价票为网订折扣率</div>
                <div>门票 - 团体票为团体折扣率</div>
                <div>门票 - 其他票种为特殊折扣率</div>
                <div>权益卡为商品折扣率</div>
              </>
            }
          >
            <QuestionCircleOutlined style={{ marginLeft: 4 }} />
          </Tooltip>
        </>
      ),
      search: false,
      dataIndex: 'overallDiscount',
      renderText: (text: number, record) => {
        const { marketPrice } = record;
        const isValid = !isNil(text ?? marketPrice);
        return isValid ? (
          <Space direction="vertical" style={{ textAlign: 'center' }}>
            <span> {text}%</span>
            <span> {round((marketPrice * text) / 100, 2)}</span>
          </Space>
        ) : (
          '-'
        );
      },
    },

    {
      title: '分销折扣区间',
      search: false,
      dataIndex: 'discountRange',
      renderText: (_, record) => {
        const { beginDiscount, endDiscount, marketPrice, overallDiscount } = record;
        const price = (overallDiscount * marketPrice) / 100;

        return !isNil(beginDiscount) ? (
          <Space direction="vertical" style={{ textAlign: 'center' }}>
            <span>
              {beginDiscount}% ~ {endDiscount}%
            </span>
            <span>
              {round((beginDiscount * price) / 100, 2)} ~ {round((endDiscount * price) / 100, 2)}
            </span>
          </Space>
        ) : (
          '-'
        );
      },
    },
    {
      title: '进货价',
      search: false,
      dataIndex: 'purchasePrice',
      renderText: (dom: string[]) => {
        const items = dom.map((price: any) => {
          const p = parseFloat(price) ? parseFloat(price).toFixed(2) : price;
          return <div>{p}</div>;
        });
        return <div>{items}</div>;
      },
    },
    {
      title: '价格策略',
      dataIndex: 'salePrice',
      ellipsis: true,
      search: false,
      renderText: (text: number[], record) => {
        const { marketPrice, overallDiscount } = record;
        const isValid = !isNil(marketPrice) || !isEmpty(text);
        if (!isValid) {
          return '-';
        }
        const price = (overallDiscount * marketPrice) / 100;

        return text.map((item) => `${price ? ceil((item / price) * 100, 2) : 0}%`).join('/');
      },
    },
    {
      width: 190,
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      renderText: (_, record) => (
        <Space>
          <Access accessible={access.canPriceStrategy_setPrice}>
            <a
              onClick={() => {
                localStorage.setItem('priceStrategyItem', JSON.stringify(record));
                history.push(`/partner/sale-channel/price-strategy/strategy-list/${record.unitId}`);
              }}
            >
              设置价格策略
            </a>
          </Access>
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item
                  key={'1'}
                  onClick={() => {
                    getIssueDetails(record.issueId);
                    addOperationLogRequest({
                      action: 'info',
                      content: `查看${record.name}出票规则`,
                    });
                  }}
                >
                  出票规则
                </Menu.Item>
                {record.unitType != 2 && (
                  <>
                    <Menu.Item
                      key={'2'}
                      onClick={() => {
                        getRetreatDetails(record.retreatId);
                        addOperationLogRequest({
                          action: 'info',
                          content: `查看${record.name}退票规则`,
                        });
                      }}
                    >
                      退票规则
                    </Menu.Item>
                    <Menu.Item
                      key={'3'}
                      onClick={() => {
                        getCheckDetails(record.checkId);
                        addOperationLogRequest({
                          action: 'info',
                          content: `查看${record.name}检票规则`,
                        });
                      }}
                    >
                      检票规则
                    </Menu.Item>
                  </>
                )}
              </Menu>
            }
            trigger={['click']}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                关联规则
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.PriceStrategyListItem, API.PriceStrategyListParams>
        {...tableConfig}
        actionRef={actionRef}
        params={{
          distributorId: coId,
        }}
        // pagination={false}
        request={tableListReq}
        columns={columns}
      />
    </>
  );
};

export default PriceStrategy;
