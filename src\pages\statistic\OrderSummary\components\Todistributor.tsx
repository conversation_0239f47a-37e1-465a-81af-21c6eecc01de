import type { ActionType, ProColumns, RequestData } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Card, List, Modal, Select } from 'antd';
import React, { useRef, useState } from 'react';
import { useModel } from '@umijs/max';

import { productTypeEnum, ticketTypeEnum } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { apiDistributeBuyOrder, apiDistributeRefundInfo } from '@/services/api/distribution';
import Vessel from './Vessel';

import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import {
  belowDistributorReceiveInfo,
  belowDistributorRetreatInfo,
  distributor,
  distributorReceiveInfo,
  distributorRetreatInfo,
} from '@/services/api/datareport';
import dayjs from 'dayjs';

const Todistributor: React.FC<Record<string, any>> = (props) => {
  // console.log('props', props);

  const actionRef = useRef<ActionType>();

  const { initialState }: any = useModel('@@initialState');
  const columns: ProColumns<ActionType>[] = [
    {
      title: '订单类型',
      dataIndex: 'order_type',
      key: 'order_type',
      hideInSearch: true,
      render: (_, item: any, i: number) => {
        return (
          <div
            style={{ color: '#1890ff', cursor: 'pointer' }}
            onClick={() => circumstance(item, i)}
          >
            {item.order_type}
          </div>
        );
      },
    },
    {
      title: '订单数',
      dataIndex: 'orders',
      key: 'orders',
      hideInSearch: true,
    },
    {
      title: `${'进货数'}  ${'/'}  ${'退货数'}`,
      dataIndex: 'nums',
      key: 'nums',
      hideInSearch: true,
    },
    {
      title: '订单金额',
      dataIndex: 'amounts',
      key: 'amounts',
      hideInSearch: true,
    },
    {
      title: '支付 / 退款日期',
      dataIndex: 'dates',
      initialValue: [dayjs().startOf('months'), dayjs()],

      key: 'dates',
      hideInSearch: false,
      valueType: 'dateRange',
      hideInTable: true,
    },
  ];

  const [index, setIndex] = useState<number>(0);

  const [rowInfo, setRowInfo] = useState<Record<string, any>>({});

  /** 模块 */
  const [visible, setVisible] = useState<boolean>(false);

  const types = [
    { title: '我的进货单', inlayTitle: '采购订单' },
    { title: '我的退货单', inlayTitle: '采购退单' },
    { title: '下游进货单', inlayTitle: '销售订单' },
    { title: '下游退货单', inlayTitle: '销售退单' },
  ];

  // 弹框
  const circumstance = (item: any, i: number) => {
    // console.log('circumstance', item, i);
    setIndex(i);
    setVisible(true);
    setRowInfo({});
  };

  const [amount, setAmount] = useState<Record<string, any>[]>([]); //合计
  const [total, setTotal] = useState<number>(0); //总条数
  const [dates, setDates] = useState<Record<string, any>[] | null>(null); //总表时间

  const childColumns: ProColumns<ActionType>[][] = [
    [
      //进货单
      {
        title: '采购订单号',
        dataIndex: 'id',
        key: 'id',
        hideInSearch: true,
        render: (_, item: any) => {
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              {item.id}
            </div>
          );
        },
      },
      {
        title: '供应商',
        dataIndex: 'seller_name',
        key: 'seller_name',
        hideInSearch: false,
        renderFormItem: (config) => {
          return (
            <Select
              {...config}
              mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择"
              defaultValue={[]}
            >
              {props.supplierList.map((n: any) => {
                return <Select.Option key={n.coId}>{n.coName}</Select.Option>;
              })}
            </Select>
          );
        },
      },
      {
        title: '支付时间',
        dataIndex: 'paytime',
        key: 'paytime',
        hideInSearch: true,
        renderText: (text) => {
          if (!text) {
            return '-';
          }
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '进货数量',
        dataIndex: 'nums',
        key: 'nums',
        hideInSearch: true,
      },
      {
        title: '采购金额',
        dataIndex: 'amounts',
        key: 'amounts',
        hideInSearch: true,
      },
      {
        title: '支付日期',
        dataIndex: 'dates',
        key: 'dates',
        valueType: 'dateRange',
        hideInSearch: false,
        hideInTable: true,
        initialValue: dates,
      },
    ],
    [
      //退货单
      {
        title: '退货订单号',
        dataIndex: 'id',
        key: 'id',
        hideInSearch: true,
        render: (_, item: any) => {
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              {item.id}
            </div>
          );
        },
      },
      {
        title: '供应商',
        dataIndex: 'seller_name',
        key: 'seller_name',
        hideInSearch: false,
        renderFormItem: (config) => {
          return (
            <Select
              {...config}
              mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择"
              defaultValue={[]}
            >
              {props.supplierList.map((n: any) => {
                return <Select.Option key={n.coId}>{n.coName}</Select.Option>;
              })}
            </Select>
          );
        },
      },
      {
        title: '退款时间',
        dataIndex: 'refundtime',
        key: 'refundtime',
        hideInSearch: true,
        renderText: (text) => {
          if (!text) {
            return '-';
          }
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '退款数量',
        dataIndex: 'nums',
        key: 'nums',
        hideInSearch: true,
      },
      {
        title: '退款金额',
        dataIndex: 'amounts',
        key: 'amounts',
        hideInSearch: true,
      },
      {
        title: '退款日期',
        dataIndex: 'dates',
        key: 'dates',
        valueType: 'dateRange',
        hideInSearch: false,
        hideInTable: true,
        initialValue: dates,
      },
    ],
    [
      //下游进货单
      {
        title: '采购订单号',
        dataIndex: 'id',
        key: 'id',
        hideInSearch: true,
        render: (_, item: any) => {
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              {item.id}
            </div>
          );
        },
      },
      {
        title: '下游经销商',
        dataIndex: 'buyer_name',
        key: 'buyer_name',
        hideInSearch: false,
        renderFormItem: (config) => {
          return (
            <Select
              {...config}
              mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择"
              defaultValue={[]}
            >
              {props.distributorList.map((n: any) => {
                return <Select.Option key={n.coId}>{n.coName}</Select.Option>;
              })}
            </Select>
          );
        },
      },
      {
        title: '支付时间',
        dataIndex: 'paytime',
        key: 'paytime',
        hideInSearch: true,
        renderText: (text) => {
          if (!text) {
            return '-';
          }
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '进货数量',
        dataIndex: 'nums',
        key: 'nums',
        hideInSearch: true,
      },
      {
        title: '采购金额',
        dataIndex: 'amounts',
        key: 'amounts',
        hideInSearch: true,
      },
      {
        title: '支付日期',
        dataIndex: 'dates',
        key: 'dates',
        valueType: 'dateRange',
        hideInSearch: false,
        hideInTable: true,
        initialValue: dates,
      },
    ],
    [
      //下游退货单
      {
        title: '退货订单号',
        dataIndex: 'id',
        key: 'id',
        hideInSearch: true,
        render: (_, item: any) => {
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              {item.id}
            </div>
          );
        },
      },
      {
        title: '下游经销商',
        dataIndex: 'buyer_name',
        key: 'buyer_name',
        hideInSearch: false,
        renderFormItem: (config) => {
          return (
            <Select
              {...config}
              mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择"
              defaultValue={[]}
            >
              {props.distributorList.map((n: any) => {
                return <Select.Option key={n.coId}>{n.coName}</Select.Option>;
              })}
            </Select>
          );
        },
      },
      {
        title: '退款时间',
        dataIndex: 'refundtime',
        key: 'refundtime',
        hideInSearch: true,
        renderText: (text) => {
          if (!text) {
            return '-';
          }
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '退款数量',
        dataIndex: 'nums',
        key: 'nums',
        hideInSearch: true,
      },
      {
        title: '退款金额',
        dataIndex: 'amounts',
        key: 'amounts',
        hideInSearch: true,
      },
      {
        title: '退款日期',
        dataIndex: 'dates',
        key: 'dates',
        valueType: 'dateRange',
        hideInSearch: false,
        hideInTable: true,
        initialValue: dates,
      },
    ],
  ];

  const request =
    (key: string, fn: any) =>
    async (q: Record<string, any>): Promise<Partial<RequestData<ActionType>>> => {
      // console.log('q', q);
      let param: any = {};
      if (!q[key] || q[key].length == 0) q[key] = null;
      if (key == 'seller_name') {
        param = {
          seller_id: q[key], //卖方
          buyer_id: initialState.currentCompany.coId, //买方
        };
      } else if (key == 'buyer_name') {
        param = {
          seller_id: initialState.currentCompany.coId, //卖方
          buyer_id: q[key], //买方
        };
      }
      const obj = {
        conf: {
          ...param,
          m: q.pageSize, //条数
          n: q.current, //页码
          start_year_month_day: q?.dates ? q.dates[0] : null,
          end_year_month_day: q?.dates ? q.dates[1] : null,
        },
      };
      // console.log('dates', dates);

      const data: ActionType[] = [];
      try {
        const { result } = await fn(obj);
        setAmount([]);
        setTotal(0);
        result.map((_n: any, i: any) => {
          if (_n.id == '合计') {
            const amount = [
              { label: '数量：', value: _n.nums },
              { label: '金额：', value: `${(_n.amounts * 1).toFixed(2)}` },
            ];
            setAmount(amount);
          } else {
            data.push(_n);
          }
          if (_n.total != 0) {
            setTotal(_n.total);
          }
        });
      } catch (err) {
        console.log('err', err);
      }
      return new Promise((resolve, reject) => {
        resolve({ data });
      });
    };

  const requests: any[] = [
    request('seller_name', distributorReceiveInfo),
    request('seller_name', distributorRetreatInfo),
    request('buyer_name', belowDistributorReceiveInfo),
    request('buyer_name', belowDistributorRetreatInfo),
  ];

  /** 详情模块 */
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);

  const [detailsWidth, setDetailsWidth] = useState<number>(modelWidth.xl);

  const [counts, setCounts] = useState<number>(0);

  /** *********** */
  // 分时数据绑定
  const [timeVisible, setTimeVisible] = useState<any>();
  const [timeDataSource, setTimeDataSource] = useState<any>();
  const timeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '分时预约',
      dataIndex: 'timeShare',
    },

    {
      title: `${index % 2 != 0 ? '退货' : '进货'}数量`,
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  // 非分时数据绑定
  const [unTimeVisible, setUnTimeVisible] = useState<boolean>(false);
  const [unTimeDataSource, setUnTimeDataSource] = useState<any>();
  const unTimeTableColumns: ProColumns[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '入园日期',
      dataIndex: 'dayBegin',
      hideInSearch: true,
      render: (dom: any, entity: any) => entity.dayBegin + ' 至 ' + entity.dayEnd,
    },
    {
      title: '进货数量',
      dataIndex: 'num',
    },
    {
      title: '单价（元）',
      dataIndex: 'productPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '金额小计（元）',
      dataIndex: 'totalAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  //详情弹框
  const details = async (item: any) => {
    setRowInfo(item);
    setDetailsVisible(true);
  };

  const detailsColumns: ProColumns<ActionType>[][] = [
    [
      {
        title: '景区名称',
        dataIndex: 'scenicName',
        search: false,
      },
      {
        title: '产品名称',
        dataIndex: 'productName',
      },
      {
        title: '产品类型',
        dataIndex: 'productType',
        hideInSearch: true,
        valueEnum: productTypeEnum,
      },
      {
        title: '商品名称',
        dataIndex: 'goodsName',
      },
      {
        title: '票种',
        dataIndex: 'ticketGoodsType',
        render: (dom) => <span>{ticketTypeEnum[dom]}</span>,
      },
      {
        title: '入园日期',
        dataIndex: 'dayBegin',
        hideInSearch: true,
        render: (dom: any, { dayBegin, dayEnd }: any) =>
          dayBegin && dayEnd
            ? `${dayBegin} 至 ${dayEnd}`
            : // <a
              //   onClick={() => {
              //     setUnTimeDataSource(entity.noDateList);
              //     setUnTimeVisible(true);
              //   }}
              // >
              //   详情
              // </a>
              '-',
      },
      {
        title: `${index % 2 != 0 ? '退货' : '进货'}总量`,
        hideInSearch: true,
        render: (_, { purchaseNum, refundNum }: any) => (index % 2 != 0 ? refundNum : purchaseNum),
      },
      {
        title: `${index % 2 != 0 ? '退货' : '进货'}金额（元）`,
        hideInSearch: true,
        render: (_, { buyMoney, refundMoney }: any) => (index % 2 != 0 ? refundMoney : buyMoney),
      },
    ],
  ];

  return (
    <>
      <ProTable<ActionType, ProColumns>
        {...tableConfig}
        actionRef={actionRef}
        rowKey="id"
        request={async (params: Record<string, any>): Promise<Partial<RequestData<ActionType>>> => {
          const obj = {
            conf: {
              update_frequency: '0.0',
              buyer_id: initialState.currentCompany.coId,
              seller_id: initialState.currentCompany.coId,
              start_year_month_day: params.dates ? params.dates[0] : null,
              end_year_month_day: params.dates ? params.dates[1] : null,
            },
          };
          setDates(params?.dates ? params.dates : null);
          const data: ActionType[] = [];
          try {
            const { result } = await distributor(obj);

            addOperationLogRequest({
              action: 'info',
              content: `查看面向渠道商订单汇总列表`,
            });
            data.push(result[result.findIndex((n: any) => n.order_type == '我的进货单')]);
            data.push(result[result.findIndex((n: any) => n.order_type == '我的退货单')]);
            data.push(result[result.findIndex((n: any) => n.order_type == '下游进货单')]);
            data.push(result[result.findIndex((n: any) => n.order_type == '下游退货单')]);
          } catch (err) {
            console.log('err', err);
          }
          return await new Promise((resolve, reject) => {
            resolve({ data });
          });
        }}
        columns={columns}
      />

      {/* 弹窗 */}
      <Vessel
        type={types[index]?.title}
        width={modelWidth.xl}
        visible={visible}
        setVisible={setVisible}
        columns={childColumns[index]}
        request={requests[index]}
        amount={amount}
        total={total}
        setAmount={setAmount}
      />

      {/* 细节弹窗  */}
      <Modal
        title={`${types[index]?.inlayTitle}详情`}
        visible={detailsVisible}
        width={detailsWidth}
        destroyOnClose={true}
        footer={null}
        onCancel={async () => {
          setDetailsVisible(false);
        }}
      >
        {/* 商品表格 */}
        <ProTable<API.RuleListItem, API.PageParams>
          style={{ margin: '20px 0' }}
          {...tableConfig}
          // actionRef={actionRef}
          rowKey="productId"
          // options={{ setting: false, density: false }}
          editable={{}}
          // search={{ labelWidth: 'auto' }}
          toolBarRender={() => []}
          params={{ orderId: rowInfo.id }}
          request={async (e) => {
            let res: any = null;
            const isBuy = index % 2 == 0;
            if (isBuy) {
              res = await apiDistributeBuyOrder(e);
              res.data.buyProductList = res.data.buyProductList.map((item: any) => ({
                ...item,
                goodsName: item.goodsInfoVO.goodsName,
                ticketGoodsType: item.goodsInfoVO.type,
              }));
            } else {
              res = await apiDistributeRefundInfo({
                orderId: [e.orderId],
              });
            }
            setCounts(
              isBuy
                ? res.data.orderTab.totalAmount
                : res.data.distributeRefund[0].productList.reduce((prev: any, next: any) => {
                    return prev + next.refundMoney * 1;
                  }, 0),
            );
            res.data = isBuy ? res.data.buyProductList : res.data.distributeRefund[0].productList;
            return res;
          }}
          search={false}
          pagination={false}
          columns={detailsColumns[0]}
          headerTitle={
            <div style={{}}>
              {types[index]?.inlayTitle}号：{rowInfo.id}
            </div>
          }
          expandable={{
            expandedRowRender: (record: any, indexTable: number) => (
              <>
                <span style={{ padding: '8px', display: 'block' }}>入园日期及分时预约信息：</span>
                <List
                  style={{
                    padding: '4px 8px',
                    maxHeight: '300px',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                  }}
                  grid={{
                    gutter: 16,
                    xs: 1,
                    sm: 2,
                    md: 4,
                    lg: 4,
                    xl: 5,
                    xxl: 5,
                  }}
                  dataSource={record.dateList}
                  renderItem={(item: any, indexList: number) => (
                    <List.Item className="cardBox">
                      <Card
                        title={item.dayBegin}
                        hoverable
                        size="small"
                        extra={
                          <a
                            onClick={() => {
                              // 修改分时信息
                              setTimeDataSource(item.timeShareDetail);
                              // setTimeTitle(item.dayBegin)
                              setTimeVisible(true);
                            }}
                          >
                            详情
                          </a>
                        }
                      >
                        <p>数量：{item.totalNum}</p>
                        <p>金额：￥ {item.totalAmount}</p>
                      </Card>
                    </List.Item>
                  )}
                />
              </>
            ),
            rowExpandable: (record: any) => record.dateList,
          }}
        />
        <div
          style={{ marginRight: '24px', textAlign: 'right', fontWeight: '400', fontSize: '20px' }}
        >
          总计: {Number(counts).toFixed(2)} 元 <br /> <br />
        </div>
      </Modal>

      {/* 分时预约详情弹窗 */}
      <Modal
        width={modelWidth.lg}
        title={'分时预约信息'}
        visible={timeVisible}
        destroyOnClose
        footer={null}
        onCancel={() => setTimeVisible(false)}
      >
        <ProTable
          rowKey="distributorTicketStockId"
          columns={timeTableColumns}
          dataSource={timeDataSource}
          search={false}
          options={false}
        />
      </Modal>

      {/* 非分时预约详情弹窗 */}
      <Modal
        width={modelWidth.lg}
        title={'入园日期信息'}
        visible={unTimeVisible}
        destroyOnClose
        footer={null}
        onCancel={() => setUnTimeVisible(false)}
      >
        <ProTable
          {...tableConfig}
          rowKey="distributorTicketStockId"
          // headerTitle={timeTitle}
          columns={unTimeTableColumns}
          dataSource={unTimeDataSource}
          search={false}
        />
      </Modal>
    </>
  );
};

export default Todistributor;
