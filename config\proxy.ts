/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */

export default {
  dev: {
    '/bigDataApi': {
      target: 'https://dev.shukeyun.com/data/api',
      changeOrigin: true,
      pathRewrite: { '^/bigDataApi': '' },
    },
    '/api': {
      target: 'https://dev.shukeyun.com/common/common-gateway',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/chartApi': {
      target: 'https://test.shukeyun.com/data/api/model/exec',
      changeOrigin: true,
      pathRewrite: { '^/chartApi': '' },
    },
    '/casApi': {
      target: 'https://dev.shukeyun.com/cas/api/v1',
      changeOrigin: true,
      pathRewrite: { '^/casApi': '' },
    },
    '/loginHost': {
      //登录页地址
      target: 'https://dev.shukeyun.com/cas/login',
      changeOrigin: true,
      pathRewrite: { '^/loginHost': '' },
    },
    '/upload': {
      target: 'http://test.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/upload': '' },
    },
    '/previewImg': {
      target: 'http://test.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/previewImg': '' },
    },
    '/operationApi': {
      target: 'https://dev.shukeyun.com/data/sensor/db',
      changeOrigin: true,
      pathRewrite: { '^/operationApi': '' },
    },
    '/aiApi': {
      target: 'https://dev-gcluster.shukeyun.com',
      changeOrigin: true,
      pathRewrite: { '^/aiApi': '' },
      // 添加以下配置
      ws: true, // 支持 WebSocket
      secure: false, // 接受无效证书
      onProxyRes: (proxyRes) => {
        // 确保不缓冲响应
        proxyRes.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
        proxyRes.headers['Pragma'] = 'no-cache';
        proxyRes.headers['Expires'] = '0';
        // 确保保持连接活动
        proxyRes.headers['Connection'] = 'keep-alive';
      },
    },
  },
  test: {
    '/bigDataApi': {
      target: 'https://test.shukeyun.com/data/api',
      changeOrigin: true,
      pathRewrite: { '^/bigDataApi': '' },
    },
    '/api': {
      target: 'https://test.shukeyun.com/common/common-gateway',
      changeOrigin: true,
      // logLevel: 'debug',
      pathRewrite: { '^/api': '' },
    },
    '/casApi': {
      target: 'https://test.shukeyun.com/cas/api/v1',
      changeOrigin: true,
      pathRewrite: { '^/casApi': '' },
    },
    '/loginHost': {
      //登录页地址
      target: 'https://test.shukeyun.com/cas/login',
      changeOrigin: true,
      pathRewrite: { '^/loginHost': '' },
    },
    '/upload': {
      target: 'http://test.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/upload': '' },
    },
    '/previewImg': {
      target: 'http://test.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/previewImg': '' },
    },
    '/chartApi': {
      target: 'https://test.shukeyun.com/data/api/model/exec',
      changeOrigin: true,
      pathRewrite: { '^/chartApi': '' },
    },
    '/operationApi': {
      target: 'https://test.shukeyun.com/data/sensor/db',
      changeOrigin: true,
      pathRewrite: { '^/operationApi': '' },
    },
    '/aiApi': {
      target: 'https://test-gcluster.shukeyun.com',
      changeOrigin: true,
      pathRewrite: { '^/aiApi': '' },
    },
  },
  canary: {
    '/bigDataApi': {
      target: 'https://canary.shukeyun.com/data/api',
      changeOrigin: true,
      pathRewrite: { '^/bigDataApi': '' },
    },
    '/api': {
      target: 'https://canary.shukeyun.com/common/common-gateway',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/casApi': {
      target: 'https://canary.shukeyun.com/cas/api/v1',
      changeOrigin: true,
      pathRewrite: { '^/casApi': '' },
    },
    '/loginHost': {
      //登录页地址
      target: 'https://canary.shukeyun.com/cas/login',
      changeOrigin: true,
      pathRewrite: { '^/loginHost': '' },
    },
    '/upload': {
      target: 'https://yeahtrip.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/upload': '' },
    },
    '/previewImg': {
      target: 'https://yeahtrip.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/previewImg': '' },
    },
    '/chartApi': {
      target: 'https://prod.shukeyun.com/data/api/model/exec',
      changeOrigin: true,
      pathRewrite: { '^/chartApi': '' },
    },
    '/operationApi': {
      target: 'https://canary.shukeyun.com/data/sensor/db',
      changeOrigin: true,
      pathRewrite: { '^/operationApi': '' },
    },
    '/aiApi': {
      target: 'https://canary-gcluster.shukeyun.com',
      changeOrigin: true,
      pathRewrite: { '^/aiApi': '' },
    },
  },
  prod: {
    '/bigDataApi': {
      target: 'https://prod.shukeyun.com/data/api',
      changeOrigin: true,
      pathRewrite: { '^/bigDataApi': '' },
    },
    '/api': {
      target: 'https://yeahtrip.com/common/common-gateway',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/casApi': {
      target: 'https://prod.shukeyun.com/cas/api/v1',
      changeOrigin: true,
      pathRewrite: { '^/casApi': '' },
    },
    '/loginHost': {
      //登录页地址
      target: 'https://prod.shukeyun.com/cas/login',
      changeOrigin: true,
      pathRewrite: { '^/loginHost': '' },
    },
    '/upload': {
      target: 'http://prod.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/upload': '' },
    },
    '/previewImg': {
      target: 'http://prod.shukeyun.com/maintenance/deepfile/',
      changeOrigin: true,
      pathRewrite: { '^/previewImg': '' },
    },
    '/chartApi': {
      target: 'https://prod.shukeyun.com/data/api/model/exec',
      changeOrigin: true,
      pathRewrite: { '^/chartApi': '' },
    },
    '/operationApi': {
      target: 'https://prod.shukeyun.com/data/sensor/db',
      changeOrigin: true,
      pathRewrite: { '^/operationApi': '' },
    },
    '/aiApi': {
      target: 'https://gcluster.shukeyun.com',
      changeOrigin: true,
      pathRewrite: { '^/aiApi': '' },
    },
  },
};
