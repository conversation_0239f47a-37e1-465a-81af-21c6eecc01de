// @ts-ignore
/* eslint-disable */
import { message } from 'antd';
import { request } from '@umijs/max';
import { scenicHost } from '.';

/** 新建规则 PUT /api/rule */
export async function updateRule(options?: { [key: string]: any }) {
  return request<API.RuleListItem>(`${scenicHost}/rule`, {
    method: 'PUT',
    ...(options || {}),
  });
}

// 检票点
/** 检票点分页列表查询 GET */
export async function getCheckPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.pageNum = params.current;
  const { code, data } = await request(`${scenicHost}/checkInPoint/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  // 处理启用状态的样式
  data.data.map((e: any) => {
    e.isEnable = {
      color: e.isEnable == '1' ? 'blue' : 'red',
      text: e.isEnable == '1' ? '启用' : '禁用',
    };
  });
  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 检票点根据 id 删除 */
export async function delCheckaddr(id: string) {
  return request(`${scenicHost}/checkInPoint/del/${id}`, {
    method: 'DELETE',
  });
}

/** 检票点新增修改 POST */
export async function addCheckaddr(params: any, options?: { [key: string]: any }) {
  return await request<API.RuleList>(`${scenicHost}/checkInPoint/info`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 根据 id 查询检票点详情 GET */
export async function getIddetails(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/checkInPoint/info/${options}`, {
    method: 'GET',
  });

  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  // data.isEnable += '';
  // console.log(data);

  return data;
}
/** 修改【检票点】状态 */
export function setCheckInPointStatus(params: any) {
  return request(`${scenicHost}/checkInPoint/status`, {
    method: 'POST',
    data: params,
  });
}

// 检票设备设备
/** 查询检票设备分页列表查询 GET */
export async function getCheckEquipmentPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.pageNum = params.current;
  const { code, data } = await request(`${scenicHost}/checkEquipment/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  // 处理启用状态的样式
  data.data.map((e: any) => {
    e.isEnable = {
      color: e.isEnable == '1' ? 'blue' : 'red',
      text: e.isEnable == '1' ? '启用' : '禁用',
    };
  });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 检票设备删除根据 id 删除 */
export async function delCheckEquipment(options?: { [key: string]: any }) {
  return request<API.RuleListItem>(`${scenicHost}/checkEquipment/del/${options}`, {
    method: 'DELETE',
  });
}

/** 检票设备新增修改 POST */
export async function AddCheckEquipment(params: any, options?: { [key: string]: any }) {
  return await request<API.RuleList>(`${scenicHost}/checkEquipment/info`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 根据 id 查询检票设备详情 GET */
export async function getCheckdetails(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/checkEquipment/info/${options}`, {
    method: 'GET',
  });

  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  // data.isEnable += '';
  // console.log(data);

  return data;
}

/** 根据景区 id 查询下拉列表检票点 GET */
export async function getcheckInPoint(options: { id: any }) {
  const { code, data }: any = await request<API.RuleListItem>(
    `${scenicHost}/checkInPoint/simpleList/${options.id}`,
    {
      method: 'GET',
    },
  );
  if (code == 20000) {
    return data.map((item: any) => {
      return {
        value: item.id,
        label: item.checkName,
      };
    });
  } else {
    return [];
  }
}
/** 修改【检票设备】状态 */
export function setCheckEquipmentStatus(params: any) {
  return request(`${scenicHost}/checkEquipment/status`, {
    method: 'POST',
    data: params,
  });
}

// 售票窗口模块
/** 查询售票窗口分页列表查询 GET */
export async function getTicketPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  params.pageNum = params.current;
  const { code, data } = await request(`${scenicHost}/ticketOffice/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  // 处理启用状态的样式
  data.data.map((e: any) => {
    e.isEnable = {
      color: e.isEnable == '1' ? 'blue' : 'red',
      text: e.isEnable == '1' ? '启用' : '禁用',
    };
  });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 售票窗口删除根据 id 删除 */
export async function delTicket(id: string) {
  return request(`${scenicHost}/ticketOffice/del/${id}`, {
    method: 'DELETE',
  });
}

/** 售票窗口新增修改 POST */
export async function AddTicketOffice(params: any, options?: { [key: string]: any }) {
  return await request<API.RuleList>(`${scenicHost}/ticketOffice/info`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 根据 id 查询售票窗口详情 GET */
export async function getTicketOffice(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/ticketOffice/info/${options}`, {
    method: 'GET',
  });

  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  // data.isEnable += '';
  // console.log(data);

  return data;
}

// /** 根据景区 id 查询 [检票点] 下拉列表 GET */
export async function getTicketOfficeList(options: { scenicId: any }) {
  const { code, data } = await request(
    `${scenicHost}/ticketOffice/simpleList/${options.scenicId}`,
    {
      method: 'GET',
    },
  );
  return code == 20000
    ? data.map((item: { id: any; name: any }) => {
        return {
          value: item.id,
          label: item.name,
        };
      })
    : [];
}

// 售票设备模块
/** 查询售票设备模块分页列表查询 GET */
export async function getTicketEquipmentList(params: {
  /** 当前的页码 */
  pageNum?: number;
  /** 页面的容量 */
  pageSize?: number;
  /* 景区 ID */
  scenicId?: string;
}) {
  const { code, data } = await request(`${scenicHost}/ticketEquipment/pageList`, {
    method: 'GET',
    params,
  });
  // 处理启用状态的样式
  // data.data.map((e) => {
  //   e.isEnable = {
  //     color: e.isEnable == '1' ? 'blue' : 'red',
  //     text: e.isEnable == '1' ? '启用' : '禁用',
  //   };
  // });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}
/** 售票设备删除根据 id 删除 */
export async function delTicketEquipment(id: any) {
  return request(`${scenicHost}/ticketEquipment/del/${id}`, {
    method: 'DELETE',
  });
}

/** 售票设备新增修改 POST */
export async function AddTicketEquipment(params: any, options?: { [key: string]: any }) {
  return await request(`${scenicHost}/ticketEquipment/info`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 【售票设备】详情 */
export async function getTicketEquipments(options: any) {
  const { code, msg, data } = await request(`${scenicHost}/ticketEquipment/info/${options}`, {
    method: 'GET',
  });

  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  // data.scenicId = '1456171644658683906';
  data.beginTime = [data.beginTime, data.endTime];
  // data.isEnable += '';

  return data;
}

/** 修改【售票点】状态 */
export function setTicketOfficeStatus(params: any) {
  return request(`${scenicHost}/ticketOffice/status`, {
    method: 'POST',
    data: params,
  });
}
/** 修改【售票设备】状态 */
export function setTicketEquipmentStatus(params: any) {
  return request(`${scenicHost}/ticketEquipment/status`, {
    method: 'POST',
    data: params,
  });
}
/** 查看【售票设备销售权限】列表 */
export function getPermissionRetrieve(params: any) {
  return request(`${scenicHost}/permission/retrieve`, {
    method: 'GET',
    params,
  });
}
