.price-setting-container {
  padding: 8px;
  width: 100%;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
}

.setting-section {
  margin-bottom: 20px;
}

.setting-label {
  margin-bottom: 10px;
}

.setting-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.date-range {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.separator {
  margin: 0 10px;
}

.days-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.days-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.price-label {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 10px;
}

.info-icon {
  color: #bfbfbf;
  font-size: 14px;
}

.price-inputs {
  display: flex;
  align-items: center;
}

.unit {
  margin: 0 10px;
}

.discount-section {
  display: flex;
  align-items: center;
  gap: 5px;
}

.percentage {
  margin-left: 5px;
}

/* 添加日期选择相关样式 */
.date-picker-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.selected-dates {
  min-height: 32px;
  padding: 4px 0;
}

.selected-dates .ant-tag {
  margin: 4px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
}

/* 新增价格历史图表容器样式 */
.price-history-container {
  padding: 8px;
  border-left: 1px solid #f0f0f0;
  height: 600px;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* 弹窗内容区域样式 */
.price-setting-modal-content {
  margin-bottom: 60px;
}

/* 固定在底部的按钮样式 */
.price-setting-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 24px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  text-align: right;
  z-index: 10;
}

/* 确保表单内容不被底部按钮遮挡 */
.price-setting-form {
  padding-bottom: 16px;
}

/* 确保行布局正确 */
.price-setting-row {
  display: flex;
  flex-wrap: wrap;
}

/* 表格容器样式 */
.goods-table-container {
  width: 100%;
  overflow-x: auto;
}

/* 表格单元格样式 */
.ant-table-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .price-history-container {
    border-left: none;
    border-top: 1px solid #f0f0f0;
    margin-top: 16px;
    padding-top: 16px;
  }
} 