/**
 * 预授信管理
 */
import { request } from '@umijs/max';
import { scenicHost } from '.';
const url = (v: string) => scenicHost + '/precredit' + v;

/** 授信机构 */
export function precreditAccounts(data: any) {
  return request(url('/accounts'), { data, method: 'POST' });
}
/** 交易记录 */
export function precreditTransactions(data: any) {
  return request(url('/transactions'), { data, method: 'POST' });
}
/** 还款记录 */
export function precreditRepayments(data: any) {
  return request(url('/repayments'), { data, method: 'POST' });
}
/** 余额记录 */
export function precreditAccountHistory(data: any) {
  return request(url('/accountHistory'), { data, method: 'POST' });
}
/** 额度记录 */
export function precreditCreditLineHistory(data: any) {
  return request(url('/credit-line-history'), { data, method: 'POST' });
}
