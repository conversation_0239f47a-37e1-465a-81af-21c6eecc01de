import useModal from '@/common/components/ProModal/useProModal';
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { downloadBlobFile } from '@/common/utils/tool';
import {
  batchCardAdd,
  batchCardExport,
  batchCardSchedule,
  batchCardUpload,
  travelCardAll,
} from '@/services/api/saCard';
import { apiShopList } from '@/services/api/store';
import {
  CheckCircleFilled,
  DownloadOutlined,
  InboxOutlined,
  InfoCircleFilled,
} from '@ant-design/icons';
import type { ParamsType } from '@ant-design/pro-components';
import { ProCard } from '@ant-design/pro-components';
import { ProFormSelect } from '@ant-design/pro-form';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useRequest } from 'ahooks';
import {
  Alert,
  Button,
  Divider,
  Form,
  Progress,
  Result,
  Row,
  Space,
  Steps,
  Typography,
  Upload,
  message,
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import type { RcFile } from 'antd/lib/upload';
import { useRef, useState } from 'react';
import { history, useModel, useRequest as useUmiRequest } from '@umijs/max';

const { Text } = Typography;

//批量添加权益卡后返回的结果
interface UploadExcelResultEntity {
  buyerCompanyName: string;
  buyerIdentity: string;
  buyerName: string;
  buyerPhone: string;
  checkResult: string;
  checkResultState: number;
  sendCardBatchId: number;
  tradeCardName: string;
}

/*
 * @Author: bankewei
 * @Date: 2023 年 12 月 21 日
 */
export default () => {
  const { initialState } = useModel('@@initialState');
  const { coId }: any = initialState?.currentCompany || {};

  const [current, setCurrent] = useState(0); //第几步

  const [firstForm] = useForm(); //第一步中的表单
  const [xls, setXls] = useState<RcFile>(); //暂存上传的文件

  const modalState = useModal();
  const actionRef = useRef<ActionType>();
  const [uploadProgress, setUploadProgress] = useState(0); //上传进度
  const [failNumber, setFailNumber] = useState(0); //批量导入失败数
  const [successNumber, setSuccessNumber] = useState(0); //批量导入成功数
  const [importDataSource, setImportDataSource] = useState<UploadExcelResultEntity[]>([]);
  const [importSeachSource, setImportSeachSource] = useState<UploadExcelResultEntity[]>(); //详情本地搜索的数据，当为 undefined 时显示 importDataSource

  const [TCard, setTCard] = useState('');
  const detailColumns: ProColumnType[] = [
    {
      title: '序号',
      valueType: 'index',
    },
    {
      title: '发卡批次号',
      dataIndex: 'sendCardBatchId',
      hideInSearch: true,
    },
    {
      title: '姓名',
      dataIndex: 'buyerName',
    },
    {
      title: '身份证号',
      dataIndex: 'buyerIdentity',
    },
    {
      title: '手机号',
      dataIndex: 'buyerPhone',
    },
    {
      title: '所属企业',
      dataIndex: 'buyerCompanyName',
      hideInSearch: true,
    },
    {
      title: '权益卡名称',
      dataIndex: 'tradeCardName',
      hideInSearch: true,
    },
    {
      title: '校验结果',
      dataIndex: 'checkResult',
      hideInSearch: true,
    },
  ];

  /** 获取店铺信息 */
  const getStoreInfoRequest = useUmiRequest(apiShopList, {
    defaultParams: [{ id: coId, pageSize: 999 }],
    formatResult({ data }: any) {
      if (data.length) {
        return data.map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
      } else {
        return [];
      }
    },
  });

  /** 获取权益卡信息 */
  const getTravelCardAllRequest = useUmiRequest(travelCardAll, {
    manual: true,
    formatResult(data: any) {
      if (data.length) {
        return data.map((item: any) => ({
          key: item.storeGoodsId,
          label: item.travelCardName,
          value: JSON.stringify(item),
        }));
      } else {
        return [];
      }
    },
  });

  /** 跳到第一步并重置数据 */
  const resetToFirstStep = () => {
    firstForm.resetFields(); //清空第一步的数据
    setXls(undefined);
    setUploadProgress(0);
    setCurrent(0);
    setTCard('');
  };

  /** 第一步提交表单后 */
  const handleFirstFormFinish = (values: any) => {
    if (xls) {
      const r = Math.floor(Math.random() * 10000000);
      updateExcelRequest.run(JSON.parse(values.travelCardGood.value), xls, r); //如果数据量大，这一步的请求会一直阻塞来等待
      updateScheduleRequest.run(r);
    } else {
      message.info('请选择文件');
    }
  };

  /** 上传 excel 文件的请求 */
  const updateExcelRequest = useRequest(batchCardUpload, {
    manual: true,
    onSuccess(r: any) {
      if (r && r.code == 20000) {
        const { failNumber, successNumber, list } = r.data;
        jumpSecondStep(failNumber, successNumber, list); //只要成功就跳转，无关进度
      } else {
        message.warn(r.msg);
        updateScheduleRequest.cancel(); //取消处理进度轮询
      }
    },
    onError(e) {
      message.warn('文件已发生改变，请重新选择');
      updateScheduleRequest.cancel(); //取消处理进度轮询
    },
  });

  /** 上传文件处理进度查询 */
  const updateScheduleRequest = useRequest(batchCardSchedule, {
    manual: true,
    pollingInterval: 1000, //轮询时间
    loadingDelay: 1000, //延时加载
    onSuccess(r: any) {
      setUploadProgress(r);
      if (r == 100) {
        updateScheduleRequest.cancel(); //取消处理进度轮询
      }
    },
  });

  /**
   * 上传成功后跳转的第二步
   * @param failNumber
   * @param successNumber
   * @param list
   */
  const jumpSecondStep = (
    failNumber: number,
    successNumber: number,
    list: UploadExcelResultEntity[],
  ) => {
    setFailNumber(failNumber);
    setSuccessNumber(successNumber);
    setImportDataSource(list);
    setImportSeachSource(undefined);
    setCurrent(1);
  };

  /** 批量发权益卡新增的提交 */
  const batchCardAddRequest = useRequest(batchCardAdd, {
    manual: true,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: 'add',
        module: 'hJ8vT5rD',
        content: `批量发权益卡【${TCard}】`,
      });
      message.success('提交成功');
      setCurrent(2);
    },
    onError() {},
  });

  /** 导出失败信息 */
  const batchCardExportRequest = useUmiRequest(batchCardExport, {
    manual: true,
    formatResult(r) {
      downloadBlobFile(r, '批量导入权益卡失败记录.xlsx', 'vnd.ms-excel');
    },
  });

  /** 本地过滤 */
  const customSearch = (params: ParamsType) => {
    if (Object.keys(params).length == 0) {
      //重置
      setImportSeachSource(undefined);
    } else {
      //有查询条件
      setImportSeachSource(
        importDataSource.filter((item) => {
          let r = 1;
          Object.keys(params).forEach((k) => {
            if (params[k].length != 0) {
              r &= params[k] == item[k] ? 1 : 0;
            }
          });
          return r == 1;
        }),
      );
    }
  };

  /** 第二步的提交 */
  const submitBatchAdd = () => {
    const useInfo = initialState?.userInfo;
    const coId = initialState?.currentCompany?.coId;
    if (successNumber > 0 && failNumber == 0 && useInfo && coId) {
      //提交的判定
      const travelCardGood = JSON.parse(firstForm.getFieldValue('travelCardGood').value);
      const store = firstForm.getFieldValue('storeId');

      //组数据提交
      const data = {
        batchDto: {
          accountName: useInfo.username,
          companyIds: coId,
          nickName: useInfo.nickname,
          sendCardBatchId: importDataSource[0].sendCardBatchId,
          storeGoodsId: travelCardGood.storeGoodsId,
          storeId: store.value,
          storeName: store.label,
          travelCardGoodsId: travelCardGood.goodsId,
          travelCardGoodsName: travelCardGood.travelCardName,
        },
        batchUserList: importDataSource.map((item) => {
          return {
            ...item,
            travelCardGoodsId: travelCardGood.goodsId,
            travelCardGoodsName: travelCardGood.travelCardName,
          };
        }),
        travelCardGood,
      };
      batchCardAddRequest.run(data);
    } else {
      //下载失败记录
      batchCardExportRequest.run(importDataSource.filter((item) => item.checkResultState == 2));
    }
  };

  /** 获取第一页上传 excel 页面 */
  const getUploadExcelPage = (
    <>
      <ProCard.Group direction="row">
        <ProCard colSpan="40%" title=" ">
          <Form
            layout="vertical"
            form={firstForm}
            autoComplete="off"
            size="large"
            onFinish={handleFirstFormFinish}
          >
            <Form.Item label="选择导入店铺" name="storeId" rules={[{ required: true }]}>
              <ProFormSelect
                options={getStoreInfoRequest.data}
                fieldProps={{
                  labelInValue: true,
                  loading: getStoreInfoRequest.loading,
                  onChange: (data) => {
                    getTravelCardAllRequest.run({ storeId: data.value });
                  },
                }}
              />
            </Form.Item>
            <Form.Item label="选择导入权益卡" name="travelCardGood" rules={[{ required: true }]}>
              <ProFormSelect
                options={getTravelCardAllRequest.data}
                fieldProps={{
                  labelInValue: true,
                  loading: getTravelCardAllRequest.loading,
                  onChange(value, option) {
                    setTCard(value.label);
                  },
                }}
              />
            </Form.Item>
          </Form>
        </ProCard>
        <ProCard colSpan="60%" title=" ">
          <Upload.Dragger
            defaultFileList={xls ? [xls] : []}
            accept=".xls , .xlsx"
            maxCount={1}
            multiple={false}
            beforeUpload={(file) => {
              const limit = file.size / 1024 / 1024 < 10;
              if (!limit) {
                message.error('文件大小不超过 10MB');
                return Upload.LIST_IGNORE;
              } else {
                setXls(file);
              }
              return false;
            }}
            onRemove={(_) => {
              setXls(undefined);
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或将文件拖拽到这里上传</p>
            <p className="ant-upload-hint">支持扩展名：.xls .xlsx</p>
          </Upload.Dragger>
          <div>
            <DownloadOutlined />
            为了保证数据导入顺利，推荐您下载使用{' '}
            <a href={`${location.origin}${location.pathname}csv/权益卡批量导入模板.xlsx`} download>
              导入模板
            </a>
          </div>
        </ProCard>
      </ProCard.Group>
      <Progress
        percent={uploadProgress}
        style={{ visibility: `${uploadProgress > 0 ? 'visible' : 'hidden'}` }}
      />
      <Row justify={'center'}>
        <Button
          key="k1"
          type="primary"
          onClick={() => {
            firstForm.submit(); //提交表单，不知会不会之后的取值，先提交
          }}
          loading={updateExcelRequest.loading || uploadProgress > 0}
        >
          下一步
        </Button>
      </Row>
      <Divider />
      <Text strong disabled>
        上传的 Excel 文件需注意：
      </Text>
      <br />
      <br />
      <Text disabled>
        文件大小不超过 10MB，且单个 sheet 页数据量不超过 5000 行；
        <br />
        文件格式仅支持 ( .xls 和 .xlsx )；
        <br />
        请确保您需要导入的数据都在第一个 sheet 页当中，并且 sheet 表头中不包含空的单元格，否则该
        sheet 页数据系统将不做导入；
        <br />
        批量导入的数据不支持以“内置变量”作为条件的过滤；
        <br />
        导入文件不支持 Excel 公式计算，如 SUM，=H2*J2 等。
      </Text>
    </>
  );

  /** 获取导入数据页 */
  const getImportDataPage = () => (
    <>
      <ProTable
        title={() => (
          <Alert
            message={`成功导入${successNumber}人，失败${failNumber}人${
              failNumber > 0 ? '，您可以下载失败记录，修改后重新上传' : ''
            }`}
            type="info"
            showIcon
            icon={
              failNumber == 0 ? (
                <CheckCircleFilled style={{ color: '#33AF06' }} />
              ) : (
                <InfoCircleFilled style={{ color: '#EA9518' }} />
              )
            }
          />
        )}
        {...tableConfig}
        style={modalState.tableStyle}
        columns={detailColumns}
        rowKey="index"
        actionRef={actionRef}
        pagination={{
          showQuickJumper: false,
          pageSize: 10,
        }}
        onSubmit={customSearch}
        dataSource={importSeachSource ? importSeachSource : importDataSource}
      />
      <Row justify="end">
        <Space>
          <Button
            key="k1"
            type="primary"
            onClick={submitBatchAdd}
            loading={batchCardAddRequest.loading || batchCardExportRequest.loading}
          >
            {successNumber > 0 && failNumber == 0 ? '提交' : '下载失败记录'}
          </Button>
          <Button
            key="k2"
            onClick={() => {
              setUploadProgress(0);
              setCurrent(0);
            }}
          >
            {failNumber == 0 ? '上一步' : '再次上传'}
          </Button>
        </Space>
      </Row>
    </>
  );

  /** 成功页 */
  const getFinalPage = () => (
    <>
      <Result
        status="success"
        title="提交成功"
        subTitle="已提交审核，请联系超级管理员尽快进行审核，超过48小时未审核将批量导入失败"
      />
      <div style={{ height: '150px' }} />
      <Row justify="center">
        <Space>
          <Button key="k1" type="primary" onClick={resetToFirstStep}>
            再次导入
          </Button>
          <Button key="k2" onClick={() => history.back()}>
            返回
          </Button>
        </Space>
      </Row>
    </>
  );

  const steps = [
    {
      title: '上传表格',
      content: getUploadExcelPage,
    },
    {
      title: '导入数据',
      content: getImportDataPage(),
    },
    {
      title: '提交审核',
      content: getFinalPage(),
    },
  ];

  return (
    <ProCard direction="column">
      <Steps
        current={current}
        items={steps.map((item) => ({ key: item.title, title: item.title }))}
      />
      {steps[current].content}
    </ProCard>
  );
};
