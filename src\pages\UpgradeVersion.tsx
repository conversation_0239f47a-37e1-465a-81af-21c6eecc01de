import { Result, Row } from 'antd';
import React from 'react';

const NoFoundPage: React.FC = () => {
  const pageModel = {
    noPermissions: {
      status: 403,
      subTitle: '没有权限，请联系管理员',
    },
    network: {
      status: 500,
      subTitle: '网络错误，请刷新重试',
    },
    empty: {
      status: 404,
      subTitle: '找不到该景区，请联系管理员',
    },
    disable: {
      status: 403,
      subTitle: '该景区已禁用，请联系管理员',
    },
  };

  return (
    <>
      <Row justify="center" align="middle" style={{ height: '100%' }}>
        <Result
          status={403}
          title="浏览器版本太低，请升级浏览器版本"
          // extra={
          //   <Button
          //     type="primary"
          //     onClick={async () => {
          //       const appId = JSON.parse(sessionStorage.getItem('scenicInfo'))?.appId;
          //       await logout({
          //         appId,
          //       });
          //       const scenicCode = getScenicIdentifier();
          //       goToLogin({
          //         scenicCode,
          //       });
          //     }}
          //   >
          //     去登录
          //   </Button>
          // }
        />
      </Row>
    </>
  );
};

export default NoFoundPage;
