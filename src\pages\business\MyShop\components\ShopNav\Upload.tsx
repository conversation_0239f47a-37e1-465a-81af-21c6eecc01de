import ProSvg from '@/common/components/ProSvg';
import { scenicHost } from '@/services/api';
import { PlusOutlined } from '@ant-design/icons';
import { Upload, message } from 'antd';
import type { RcFile } from 'antd/lib/upload';
import { getEnv } from '@/common/utils/getEnv';

export default ({ value, onChange, shopConfig }: any) => {
  const { FILE_HOST } = getEnv();
  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只允许上传 jpeg,png 格式的图片！');
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('图片必须小于10MB!');
    }
    return isJpgOrPng && isLt10M;
  };

  return (
    <Upload
      accept="image/png, image/jpeg"
      listType="picture-card"
      showUploadList={false}
      action={scenicHost + '/aws/uploadFile'}
      beforeUpload={beforeUpload}
      onChange={({ file: { status, response } }) => {
        if (status == 'done') {
          onChange(FILE_HOST + response.data.path);
        }
      }}
    >
      {value ? (
        value.includes('http') ? (
          <img src={value} style={{ width: '100%', height: '100%' }} />
        ) : (
          <ProSvg src={value} color={shopConfig.shopStyle?.color} width="100%" height="100%" />
        )
      ) : (
        <PlusOutlined />
      )}
    </Upload>
  );
};
