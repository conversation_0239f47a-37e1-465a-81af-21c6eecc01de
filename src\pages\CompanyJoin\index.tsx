/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-15 15:01:55
 * @LastEditTime: 2023-04-06 10:16:34
 * @LastEditors: zhangfengfei
 */
import { verifyCode } from '@/services/api/companyJoin';
import { Outlet } from '@umijs/max';
import { useSize } from 'ahooks';
import { Flex, message } from 'antd';
import qs from 'qs';
import type { FC } from 'react';
import { useRef } from 'react';
import { history, useLocation, useRequest } from '@umijs/max';

export interface URLParams {
  code: string;
  coId: string;
  coName: string;
  route: string;
}

interface CompanyJoinProps {}

const CompanyJoin: FC<CompanyJoinProps> = (props) => {
  const ref = useRef(null);
  const size = useSize(ref);
  const { search } = useLocation();

  const { code, coId } = qs.parse(search, {
    ignoreQueryPrefix: true,
  }) as unknown as URLParams;

  useRequest(
    () =>
      verifyCode({
        code,
        distributorId: coId,
      }),
    {
      refreshDeps: [code],
      onSuccess: (data) => {
        if (!data) {
          message.error('链接无效');
          setTimeout(() => {
            history.push('/enterprise');
          }, 1000);
        } else {
          // realNameInfoReq.run({ userId });
        }
      },
    },
  );

  return (
    <Flex align="center" justify="center">
      <Outlet />
    </Flex>
  );
};

export default CompanyJoin;
