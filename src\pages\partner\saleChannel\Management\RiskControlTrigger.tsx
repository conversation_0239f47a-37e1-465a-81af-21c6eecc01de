/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-17 10:48:48
 * @LastEditTime: 2022-10-19 16:50:17
 * @LastEditors: zhangfengfei
 */
import type { ConditionListItem, RiskTargetType } from '@/services/api/erp';
import { enableRisk, getRiskInfo, setRiskInfo } from '@/services/api/erp';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormRadio,
  ProFormSelect,
} from '@ant-design/pro-form';
import { Button, message, Skeleton } from 'antd';
import { useForm } from 'antd/es/form/Form';
import type { FormListOperation } from 'antd/lib/form/FormList';
import { isEmpty } from 'lodash';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';

interface FormValues {
  conditionList: ConditionListItem[];
  trigger: number;
}

const initialValues: FormValues = {
  trigger: 1,
  conditionList: [{ attribute: undefined, val: undefined }] as unknown as ConditionListItem[],
};

interface RiskControlTriggerProps {
  type: RiskTargetType;
}

/**
 * @description: 风控弹窗
 */
const RiskControlTrigger: FC<RiskControlTriggerProps> = ({ type }) => {
  const { initialState } = useModel('@@initialState') || {};
  const { coId = '' } = initialState?.currentCompany || {};
  const [visible, setVisible] = useState(false);
  // formList操作ref
  const actionRef = useRef<FormListOperation>();
  const [form] = useForm<FormValues>();
  // 风控详情请求
  const getRiskInfoReq = useRequest(getRiskInfo, {
    manual: true,
    onSuccess(data, params) {
      // 赋值操作
      const { trigger, conditionList } = data;
      form.setFieldsValue({
        conditionList: isEmpty(conditionList) ? initialValues.conditionList : conditionList,
        trigger: trigger || initialValues.trigger,
      });
    },
  });
  // 风控禁用启用
  const enableRiskReq = useRequest(enableRisk, {
    manual: true,
    onSuccess(data, params) {
      message.success('操作成功');
      setVisible(false);
    },
  });

  const title = type === 'agent' ? '代理风控' : '经销风控';
  const options =
    type === 'agent'
      ? [
          {
            label: '单次最大出票数量',
            value: 'quantityTicketOnce',
          },
          {
            label: '单次最大出票金额',
            value: 'amountTicketOnce',
          },
        ]
      : [
          {
            label: '单次进货数量',
            value: 'quantityPurchaseOnce',
          },
          {
            label: '单次进货金额',
            value: 'amountPurchaseOnce',
          },
        ];

  // 启用/禁用
  const onEnable = () => {
    const { data, loading } = getRiskInfoReq;
    if (!loading && data) {
      enableRiskReq.run({
        groupCode: 'e-commerce',
        relationId: coId,
        riskTarget: type,
        status: data.status === 1 ? 2 : 1,
      });
    }
  };

  const onFinish = async (values: FormValues) => {
    // 风控条件重复验证
    const riskTypeArr = values.conditionList.map((item) => item.attribute);
    if (riskTypeArr.length !== new Set(riskTypeArr).size) {
      message.warning('风控条件重复');
      return false;
    }
    const { trigger, conditionList } = values;
    // 数据处理 前端找出新增修改和删除 (=_=)
    // 没id就是新增的 否者就是修改的
    const addList = conditionList
      .filter((item) => !Object.hasOwn(item, 'id'))
      .map((item) => {
        const { attribute, val } = item;
        return {
          attribute,
          val,
          condition: 'gt',
        };
      });
    const editList = conditionList.filter((item) => Object.hasOwn(item, 'id'));
    const sourceId = getRiskInfoReq.data?.conditionList.map((item) => item.id) || [];
    // 跟源数据对比 有id非编辑的就是删除的
    const deleteId = sourceId.filter((item) => !editList.map((i) => i.id).includes(item));

    await setRiskInfo({
      conditionSetList: {
        add: addList,
        del: deleteId,
        edit: editList,
      },
      groupCode: 'e-commerce',
      relationId: coId,
      trigger,
      riskTarget: type,
    });
    message.success('修改成功');
    return true;
  };
  useEffect(() => {
    if (visible) {
      getRiskInfoReq.run({
        groupCode: 'e-commerce',
        relationId: coId,
        riskTarget: type,
      });
    }
  }, [visible]);

  return (
    <ModalForm<FormValues>
      form={form}
      width={568}
      visible={visible}
      layout="horizontal"
      trigger={<Button>{title}</Button>}
      initialValues={initialValues}
      preserve={false}
      submitter={{
        render: (props, defaultDoms) => {
          const { status } = getRiskInfoReq.data || {};
          return [
            <Button key="enable" loading={enableRiskReq.loading} onClick={onEnable} danger>
              {status === 1 ? '禁用' : '启用'}
            </Button>,
            ...defaultDoms.reverse(),
          ];
        },
        searchConfig: {
          submitText: '保存',
        },
      }}
      modalProps={{
        title,
        // destroyOnClose: true,
      }}
      onFinish={onFinish}
      onVisibleChange={setVisible}
    >
      <Skeleton loading={getRiskInfoReq.loading} active>
        <ProFormList
          name="conditionList"
          actionRef={actionRef}
          label={<span className="required">风控条件</span>}
          creatorButtonProps={false}
          copyIconProps={false}
          deleteIconProps={false}
          style={{ marginBottom: 0 }}
          itemContainerRender={(dom, { field, fields, index }) => {
            return (
              <ProFormGroup size="small">
                <ProFormSelect
                  addonBefore="当"
                  width={180}
                  name="attribute"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  options={options}
                />
                <ProFormDigit
                  addonBefore="大于"
                  width="xs"
                  name="val"
                  min={0}
                  max={1_000_000}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                />
                {fields.length < options.length && (
                  <PlusCircleOutlined
                    style={{ fontSize: 18, marginTop: 5 }}
                    onClick={() => {
                      actionRef.current?.add();
                    }}
                  />
                )}

                {fields.length > 1 && (
                  <MinusCircleOutlined
                    style={{ fontSize: 18, marginTop: 5 }}
                    onClick={() => {
                      actionRef.current?.remove(field.name);
                    }}
                  />
                )}
              </ProFormGroup>
            );
          }}
        >
          {null}
        </ProFormList>
        <ProFormRadio.Group
          name={'trigger'}
          label="触发拦截"
          rules={[
            {
              required: true,
            },
          ]}
          width={'md'}
          options={[
            {
              label: '均满足时拦截',
              value: 1,
            },
            {
              label: '满足任意条件拦截',
              value: 2,
            },
          ]}
        />
      </Skeleton>
    </ModalForm>
  );
};

export default RiskControlTrigger;
