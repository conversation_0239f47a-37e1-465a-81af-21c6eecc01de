import React from 'react';
import { Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { ticketTypeEnum } from '@/common/utils/enum';

// 定义数据类型
interface DataType {
  key: string;
  productName: string;
  ticketType: string;
  marketPrice: number | string;
  priceRatio: string;
  priceRange: string;
  purchasePrice: number | string;
}

// 商品信息类型
interface GoodsItemType {
  name?: string;
  marketPrice?: number;
  overallDiscount?: number;
  beginDiscount?: number;
  endDiscount?: number;
  purchasePrice?: string[];
  goodsId?: string;
  unitId?: string;
  unitType?: number;
  ruleType?: number;
  goodsType?: string;
  isCompose?: boolean;
  discount?: number;
  composeDiscount?: number;
  salePrice?: number;
  composePrice?: number;
  commissionRate?: number;
  group?: any[];
}

// 组件 Props 类型
interface GoodsTableProps {
  goodsItem: GoodsItemType;
  beginPrice: number;
  endPrice: number;
}

const GoodsTable: React.FC<GoodsTableProps> = ({ goodsItem, beginPrice, endPrice }) => {
  // 表格数据
  const dataSource: DataType[] = [
    {
      key: '1',
      productName: goodsItem.name || '-',
      ticketType: ticketTypeEnum[goodsItem.goodsType as string] || '-',
      marketPrice: goodsItem.marketPrice || '-',
      priceRatio: goodsItem.overallDiscount ? `${goodsItem.overallDiscount}%` : '-',
      priceRange: `${goodsItem.beginDiscount}%~${goodsItem.endDiscount}%`,
      purchasePrice: goodsItem.purchasePrice ? goodsItem.purchasePrice.join('/') : '-',
    },
  ];

  // 表格列定义
  const columns: ColumnsType<DataType> = [
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
      align: 'center',
      width: '16%',
    },
    {
      title: '票种',
      dataIndex: 'ticketType',
      key: 'ticketType',
      align: 'center',
      width: '16%',
    },
    {
      title: '市场标准价（元）',
      dataIndex: 'marketPrice',
      key: 'marketPrice',
      align: 'center',
      width: '16%',
    },
    {
      title: '商品折扣',
      dataIndex: 'priceRatio',
      key: 'priceRatio',
      align: 'center',
      width: '16%',
      render: (text) => (
        <div>
          {text}
          {goodsItem.marketPrice && goodsItem.overallDiscount ? (
            <div style={{ color: '#999', fontSize: '12px' }}>
              ({((goodsItem.marketPrice * goodsItem.overallDiscount) / 100).toFixed(2)})
            </div>
          ) : null}
        </div>
      ),
    },
    {
      title: '分销折扣区间',
      dataIndex: 'priceRange',
      key: 'priceRange',
      align: 'center',
      width: '18%',
      render: (text) => (
        <div>
          {text}
          {(beginPrice || beginPrice === 0) && (endPrice || endPrice === 0) ? (
            <div style={{ color: '#999', fontSize: '12px' }}>({`${beginPrice} ~ ${endPrice}`})</div>
          ) : null}
        </div>
      ),
    },
    {
      title: '进货价',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      align: 'center',
      width: '18%',
    },
  ];

  return (
    <Table 
      dataSource={dataSource} 
      columns={columns} 
      pagination={false}
      bordered
      size="small"
      style={{ width: '100%' }}
    />
  );
};

export default GoodsTable; 