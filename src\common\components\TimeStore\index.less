.timeStore {
  display: flex;
  :global {
    .time-share-head {
      position: relative;
      z-index: 1;
      flex: none;
      width: 180px;
      border-bottom: 1px solid #ddd;
    }
    .time-share-content {
      .month {
        display: flex;
        align-items: center;
        justify-content: center;
        width: calc(100% + 180px);
        height: 36px;
        border-color: #ddd;
        border-style: solid;
        border-width: 1px 1px 0;
        transform: translate(-180px);
      }
      .week {
        position: absolute;
        right: 100%;
        width: 180px;
        height: 22px;
        pointer-events: none;
      }
      .day {
        width: 100%;
        height: 22px;
        text-align: right;
        background: #f2f2f2;
      }
      .ant-radio-group {
        display: none;
      }
      .ant-picker-calendar-date-content {
        height: auto;
      }
      .ant-picker-cell-inner {
        height: auto;
        margin: 0;
        padding: 0;
        border-top: none;
        .ant-picker-calendar-date-value {
          border-top: 1px solid #ddd;
          border-left: 1px solid #ddd;
        }
      }
      .d1,
      .d2 {
        padding: 0 4px;
        border-top: 1px solid #ddd;
        border-left: 1px solid #ddd;
      }
      .d1 {
        background: #f2f2f2;
      }
      .d2 {
        position: relative;
      }
      .left {
        position: absolute;
        right: 100%;
        width: 180px;
        color: rgba(0, 0, 0, 0.88);
        text-align: left;
      }
      .left,
      .content {
        pointer-events: none;
        > div > div {
          height: 22px;
        }
      }
      .set,
      .get {
        pointer-events: all;
        &:hover {
          &::after {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            background: #40a9ff;
            inset: 0;
          }
        }
      }
      .set:hover {
        &::after {
          content: '编辑';
        }
      }
      .get:hover {
        &::after {
          content: '详情';
        }
      }
      .ant-picker-content {
        th {
          position: relative;
          padding: 0 4px !important;
          line-height: 22px !important;
          &::after {
            position: absolute;
            border-top: 1px solid #ddd;
            border-left: 1px solid #ddd;
            content: '';
            inset: 0;
          }
        }
        th:first-child::before {
          position: absolute;
          top: 0;
          right: 100%;
          width: 180px;
          height: 22px;
          border-top: 1px solid #ddd;
          border-left: 1px solid #ddd;
          content: '';
        }
        th:last-child {
          border-right: 1px solid #ddd;
        }
        tbody {
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
        }
      }
      .ant-picker-body {
        padding: 0;
      }
    }
  }
}
