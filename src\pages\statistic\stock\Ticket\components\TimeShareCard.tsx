/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-06-05 10:50:56
 * @LastEditTime: 2023-06-05 16:38:30
 * @LastEditors: zhangfengfei
 */
import { addOperationLogRequest } from '@/common/utils/operationLog';
import useModal from '@/hooks/useModal';
import type { API } from '@/services/api/typings';
import { Card } from 'antd';
import type { FC } from 'react';
import DetailModal from './DetailModal';

interface TimeShareCardProps {
  dataItem: API.EnterTimeGroup;
  productName: string;
}

const TimeShareCard: FC<TimeShareCardProps> = ({ dataItem, productName }) => {
  const { totalNumber, enterStartTime } = dataItem;
  const detailModal = useModal();

  return (
    <>
      <Card
        title={enterStartTime}
        hoverable
        size="small"
        extra={
          <a
            onClick={() => {
              detailModal.setVisible(true);
              addOperationLogRequest({
                action: 'info',
                content: `查看产品【${productName}】票务库存详情`,
              });
            }}
          >
            详情
          </a>
        }
      >
        <p>数量：{totalNumber}</p>
      </Card>
      <DetailModal
        modalState={detailModal}
        isTimeShare={true}
        dataSource={dataItem.detailList}
        headerTitle={enterStartTime}
      />
    </>
  );
};

export default TimeShareCard;
