import { tableConfig } from '@/common/utils/config';
import { precreditAccounts } from '@/services/api/precredit';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel, useRequest } from '@umijs/max';

export default () => {
  const {
    initialState: {
      currentCompany: { settlementId },
    },
  }: any = useModel('@@initialState');
  const tableReq = useRequest(precreditAccounts, {
    manual: true,
    formatResult(res) {
      return {
        data: res.data.data,
        success: res.code == 20000,
        total: res.data.total,
      };
    },
  });
  const columns: ProColumns[] = [
    {
      title: '机构ID',
      dataIndex: 'path',
      renderText: (text: any) => text.split('.').at(-2),
    },
    {
      title: '账户所有者',
      dataIndex: 'owner',
    },
    {
      title: '账户名称',
      dataIndex: 'name',
    },
    {
      title: '账户代码',
      dataIndex: 'path',
    },
    {
      title: '账户余额',
      dataIndex: 'balance',
      valueType: 'money',
    },
  ];
  return (
    <ProTable
      {...tableConfig}
      columns={columns}
      search={false}
      params={{ settlementId }}
      request={tableReq.run}
    />
  );
};
