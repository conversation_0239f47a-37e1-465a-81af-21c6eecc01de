/*
 * 分销商弹窗
 * */
import { tableConfig } from '@/common/utils/config';
import { getUniqueId } from '@/common/utils/tool';
import { apiDistribGroupList, apiRemoveDistributor } from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal, Popconfirm, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';

// key
function Key(e: any) {
  return e.id + '-' + e.ticketId + '-' + e.timeShareId;
}

const Distributor = ({
  visible,
  setVisible,
  groupId,
  groupName,
  upActionRef,
}: {
  visible: boolean;
  setVisible: Function;
  groupId: string;
  groupName: string;
  upActionRef: any;
}) => {
  const access = useAccess();
  // 表单对象
  const actionRef = React.useRef<ActionType>();
  // 数据绑定
  const [dataSource, setDataSource] = useState<any>();
  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany;
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);

  // 表格配置
  const columns: ProColumns[] = [
    {
      title: '分销商名称',
      dataIndex: 'coName',
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
      hideInSearch: true,
    },
    {
      title: '手机号码',
      dataIndex: 'coPhone',
      hideInSearch: true,
    },
    {
      title: '电子邮箱',
      dataIndex: 'coEmail',
      hideInSearch: true,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
      hideInSearch: true,
    },
    {
      title: '商户账号',
      dataIndex: 'settlementName',
      hideInSearch: true,
    },
    {
      width: 120,
      title: '操作',
      valueType: 'option',
      // hideInTable: !(useAccess().canDisDisGroupDelDis),
      fixed: 'right',
      render: (_, entity) => [
        <Access key={getUniqueId()} accessible={access.canSupplierGroup_deleteDealer}>
          <a onClick={() => {}}>
            <Popconfirm
              placement="top"
              onConfirm={async () => {
                try {
                  await apiRemoveDistributor({
                    downDistributorId: entity.coId,
                    upDistributorId: coId,
                  });
                  message.success('删除成功');
                  actionRef?.current?.reload();
                  upActionRef?.current?.reload();
                } catch (error) {}
              }}
              okText="是"
              cancelText="否"
              title={'确定删除吗？'}
            >
              <span style={{ color: 'red' }}>删除</span>
            </Popconfirm>
          </a>
        </Access>,
      ],
    },
  ];

  useEffect(() => {
    if (visible) {
      // console.log(groupId);
      (async () => {
        try {
          // const { data } = await apiTicketConfigList({ coId });
          // setDataSource(data);
          // setEditableRowKeys(data.map((e: any) => Key(e)));
        } catch (error) {}
      })();
    }
    return () => {};
  }, [visible]);

  return (
    <Modal
      width={1200}
      title={'分销商数量详情页'}
      visible={visible}
      destroyOnClose
      footer={null}
      // okText="保存"
      // cancelText="关闭"
      onCancel={() => {
        setVisible(false);
      }}
    >
      <ProTable<API.RuleListItem, API.PageParams>
        {...tableConfig}
        headerTitle={<span>当前分组名称：{groupName}</span>}
        actionRef={actionRef}
        params={{ groupId }}
        request={apiDistribGroupList}
        columns={columns}
      />
    </Modal>
  );
};

export default Distributor;
