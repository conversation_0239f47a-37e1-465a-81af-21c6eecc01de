import { tableConfig } from '@/common/utils/config';
import { precreditCreditLineHistory } from '@/services/api/precredit';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel, useRequest } from '@umijs/max';

export default () => {
  const {
    initialState: {
      currentCompany: { settlementId },
    },
  }: any = useModel('@@initialState');
  const tableReq = useRequest(precreditCreditLineHistory, {
    manual: true,
    formatResult(res) {
      return {
        data: res.data.data,
        success: res.code == 20000,
        total: res.data.total,
      };
    },
  });
  const columns: ProColumns[] = [
    {
      title: '操作时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
    },
    {
      title: '机构ID',
      dataIndex: 'creditorId',
    },
    {
      title: '商户ID',
      dataIndex: 'debtorId',
    },
    {
      title: '额度下限',
      render: (_, entity) => (
        <div>
          <div>修改前：¥{entity.beforeMinNumBalance}</div>
          <div>修改后：¥{entity.afterMinNumBalance}</div>
        </div>
      ),
    },
    {
      title: '额度上限',
      render: (_, entity) => (
        <div>
          <div>修改前：¥{entity.beforeMaxNumBalance}</div>
          <div>修改后：¥{entity.afterMaxNumBalance}</div>
        </div>
      ),
    },
    {
      title: '操作人',
      dataIndex: 'operatorId',
    },
  ];
  return (
    <ProTable
      {...tableConfig}
      columns={columns}
      search={false}
      params={{ settlementId }}
      request={tableReq.run}
    />
  );
};
