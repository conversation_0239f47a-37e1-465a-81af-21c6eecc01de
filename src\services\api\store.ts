/**
 * 店铺管理模块接口
 */
import { omit } from 'lodash';
import { request } from '@umijs/max';
import { scenicHost } from '.';
import type { API, ResponseData, ResponseListData, ResponseListData2 } from './typings';

// 店铺列表
export async function apiShopList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/ticketAgent/shopList`, {
      method: 'GET',
      params,
    });
    return {
      data: data,
      success: true,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}
// 店铺列表（员工查看）
export async function apiAuthorityShopList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/ticketAgent/authorityShopList/${params.id}`, {
      method: 'GET',
      //  params,
    });
    return {
      data: data,
      success: true,
      // total: data.total
    };
  } catch (error) {
    console.log(error);

    return {};
  }
}
// 店铺列表 - 员工
export async function apiStoreList(params: any) {
  return await request(`${scenicHost}/ticketStore/storeList/${params.id}`);
}
// 设置权限 - 员工
export function updateStoreAuthorize(params: API.StoreAuthorizeParams) {
  return request(`${scenicHost}/ticketStore/storeAuthorize`, {
    method: 'POST',
    data: params,
  });
}
// 创建店铺
export async function apiShopAdd(params: any) {
  await request(`${scenicHost}/ticketAgent/shop`, {
    method: 'POST',
    data: params,
  });
}
// 修改店铺
export async function apiDistributorShop(params: any) {
  await request(`${scenicHost}/ticketAgent/distributorShop`, {
    method: 'PUT',
    data: params,
  });
}
// 获取店铺详情
export async function apiShopDetail(storeId: string) {
  return await request(`${scenicHost}/ticketStore/noAuth/store/${storeId}`, {
    method: 'GET',
  });
}
// 单品源列表
export async function apiAgentGoodsList(params: any) {
  const { data } = await request<ResponseListData2<any[]>>(
    `${scenicHost}/ticketAgent/agentGoodsList`,
    {
      method: 'GET',
      params,
    },
  );
  return data;
}
// 添加商品
export async function apiStoreGoodsAdd(params: any) {
  await request(`${scenicHost}/ticketAgent/storeGoods`, {
    method: 'POST',
    data: params,
  });
}
// 编辑组合商品
export async function apiModifyComposeGoods(params: any) {
  await request(`${scenicHost}/ticketAgent/modifyComposeGoods`, {
    method: 'POST',
    data: params,
  });
}
// 编辑单品
export async function apiModifySimpleGoods(params: any) {
  await request(`${scenicHost}/ticketAgent/modifySimpleGoods`, {
    method: 'PUT',
    data: params,
  });
}
// 店铺商品列表
export async function apiStoreGoodList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/ticketAgent/storeGoodList`, {
      method: 'GET',
      params,
    });
    return {
      data: data,
      success: true,
      // total: data.total
    };
  } catch (error) {
    return {};
  }
}

// 店铺商品列表 (新)
export function getShopGoodsPageList(params: API.PageParams & API.ShopGoodsPageListParams) {
  return request<ResponseListData2<API.ShopGoodsPageListItem[]>>(
    `${scenicHost}/ticketAgent/storeGoodsPage`,
    {
      method: 'GET',
      params,
    },
  );
}

// 上下架商品
export async function apiUpDownStoreGoods(params: any) {
  await request(`${scenicHost}/ticketAgent/upDownStoreGoods`, {
    method: 'PUT',
    data: params,
  });
}
// 推荐商品
export async function apiRecommend(params: any) {
  await request(`${scenicHost}/ticketAgent/recommend`, {
    method: 'POST',
    data: params,
  });
}

// 官网推荐景区商品列表
export async function apiOfficialRecommendList(params: any) {
  return await request(`${scenicHost}/ticketAgent/officialRecommendScenicGoods`, {
    method: 'POST',
    data: params,
  });
}

// 官网推荐
export async function apiOfficialRecommend(params: any) {
  await request(`${scenicHost}/ticketAgent/officialRecommendScenicGoodsState`, {
    method: 'POST',
    data: params,
  });
}

// 根据企业 id 验证企业是否包含运营，如无运营则不显示官网推荐按钮
export async function apiOfficialRecommendEnable(id: any) {
  return request(`${scenicHost}/co/isOperate/${id}`, {
    method: 'GET',
  });
}

// 单品详情
export async function apiQuerySimpleGoodsDetail(params: any) {
  return await request(`${scenicHost}/ticketAgent/querySimpleGoodsDetail2`, { params });
}
// 组合商品详情
export async function apiComposeGoodsDetail(params: any) {
  return await request(`${scenicHost}/ticketAgent/composeGoodsDetail2/${params.id}`);
}

// 删除单品
export async function deleteStoreGoods(id: any) {
  return request(`${scenicHost}/ticketAgent/storeGoods/${id}`, {
    method: 'DELETE',
  });
}

// 订单列表
export function apiStoreOrderList(params: any) {
  return request(`${scenicHost}/order/toC/ticketOrderList`, {
    method: 'GET',
    params,
  });
}
// 订单列表 - 报表导出
export function apiStoreOrderListExport(params: any) {
  return request(`${scenicHost}/order/toC/ticketOrderList/export`, {
    method: 'GET',
    params,
  });
}
// 订单详情
export async function apiStoreOrderInfo(params: any) {
  return request(`${scenicHost}/order/info`, { params });
}
// 父订单详情
export async function apiGroupTicketOrder(params: any) {
  return await request(`${scenicHost}/order/GroupTicketOrder`, { params });
}

// 退单列表
export async function apiStoreReturnOrderList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/order/toC/refundList`, {
      method: 'GET',
      params,
    });
    return {
      total: data.total,
      data: data.records,
      success: true,
    };
  } catch (error) {
    return {};
  }
}
// 退单详情
export async function apiStoreReturnOrderInfo(params: any) {
  return await request(`${scenicHost}/order/toC/refundOrder`, {
    params,
  });
}

// 佣金报表列表
export async function apiStoreCommission(params: any) {
  const { dateType, ...rest } = params;
  return request(`${scenicHost}/report/${dateType === 'date' ? 'day' : 'month'}/storeCommission`, {
    method: 'GET',
    params: rest,
  });
}

// 分佣/退佣明细
export async function apiStoreCommissionDetail(dateType: any, params: any) {
  const url = params.bill === 'divide' ? 'storeCommissionDetail' : 'storeCommissionRefundDetail';
  console.log(params);

  return request(`${scenicHost}/report/${dateType == 'date' ? 'day' : 'month'}/${url}`, {
    method: 'GET',
    params: omit(params, ['bill', 'tabKey']),
  });
}
// 退佣明细
export async function apiStoreCommissionDetailReturn(dateType: any, params: any) {
  return await request(`${scenicHost}/report/${dateType == 'date' ? 'day' : 'month'}/`, {
    params,
  });
}

//窗口交易总览
export async function apiWindowTradeList(params: any) {
  return await request(`${scenicHost}/report/windowTradeList`, {
    method: 'GET',
    params,
  });
}
//窗口交易明细
export async function apiWindowsTradeDetail(params: any) {
  return await request(`${scenicHost}/report/windowsTradeDetail`, {
    method: 'GET',
    params,
  });
}
//窗口交易明细统计总和
export async function apiWindowsTradeDetailTotal(params: any) {
  return await request(`${scenicHost}/report/windowsTradeDetailTotal`, {
    method: 'GET',
    params,
  });
}

// 个人交易统计（按支付方式）
export function apiWindowPaymentList(params: API.WindowPaymenntListParams) {
  return request<ResponseData<any[]>>(`${scenicHost}/report/windowsTradeReport/payType`, {
    method: 'GET',
    params,
  });
}

//授信主体
export async function apiSupplierList(params: any) {
  return await request(`${scenicHost}/ticketStore/supplierList/${params}`, {
    method: 'GET',
  });
}
/**
 * 店铺装修
 */
// banner 列表
export async function apiShopBannerList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/banner/list`, {
      params,
    });
    return {
      data: data,
      success: true,
    };
  } catch (error) {
    return {};
  }
}
// 创建 banner
export async function apiShopBannerAdd(params: any) {
  await request(`${scenicHost}/banner/info`, {
    method: 'POST',
    data: params,
  });
}
// 修改 banner
export async function apiShopBannerEdit(params: any) {
  await request(`${scenicHost}/banner/info`, {
    method: 'PUT',
    data: params,
  });
}
// 删除 banner
export async function apiShopBannerDel(id: any) {
  await request(`${scenicHost}/banner/info/${id}`, {
    method: 'DELETE',
  });
}

// 客服列表
export async function apiShopContactList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/contractStaff/list/${params.id}`);
    return {
      data: data,
      success: true,
    };
  } catch (error) {
    return {};
  }
}
// 创建客服
export async function apiShopContactAdd(params: any) {
  await request(`${scenicHost}/contractStaff/info`, {
    method: 'POST',
    data: params,
  });
}
// 修改客服
export async function apiShopContactEdit(params: any) {
  await request(`${scenicHost}/contractStaff/info`, {
    method: 'PUT',
    data: params,
  });
}
// 删除客服
export async function apiShopContactDel(id: any) {
  await request(`${scenicHost}/contractStaff/info/${id}`, {
    method: 'DELETE',
  });
}

// 标签列表
export async function apiShopLabelList(params: any) {
  try {
    const { data } = await request(`${scenicHost}/label/list`, {
      method: 'GET',
      params,
    });
    return {
      data: data,
      success: true,
    };
  } catch (error) {
    return {};
  }
}
// 添加分组
export async function apiShopLabelGroupAdd(params: any) {
  await request(`${scenicHost}/label/group`, {
    method: 'POST',
    data: params,
  });
}
// 添加标签
export async function apiShopLabelAdd(params: any) {
  await request(`${scenicHost}/label/info`, {
    method: 'POST',
    data: params,
  });
}
// 删除标签
export async function apiShopLabelDel(id: any) {
  await request(`${scenicHost}/label/info/${id}`, {
    method: 'DELETE',
  });
}
// 删除分组
export async function apiShopLabelGroupDel(id: any) {
  await request(`${scenicHost}/label/group/${id}`, {
    method: 'DELETE',
  });
}
// 设置标签
export async function apiShopLabelSet(params: any) {
  await request(`${scenicHost}/label/info`, {
    method: 'PUT',
    data: params,
  });
}

// 配置佣金结算模式
export function updateCommissionCredit(params: API.UpdateCommissionCreditParams) {
  return request<ResponseData<any>>(`${scenicHost}/ticketAgent/commissionCredit`, {
    method: 'POST',
    data: params,
  });
}
// 店铺风格 - 查询
export async function getStoreStyle(params: any) {
  return await request(`${scenicHost}/store/style/info`, {
    method: 'GET',
    params,
  });
}
// 店铺风格 - 创建
export function addOrUpdateStoreStyle(params: any) {
  return request<ResponseData<any>>(`${scenicHost}/store/style/addOrUpdate`, {
    method: 'POST',
    data: params,
  });
}
// 查询佣金结算模式
export function getCommissionCredit(params: {
  agentId: string;
  distributorId: string;
  agentType: 1 | 2;
}) {
  return request<
    ResponseData<{
      agentId: string;
      creditType: number;
      distributorId: string;
      paymentType: number;
    }>
  >(`${scenicHost}/ticketAgent/commissionCredit`, {
    method: 'GET',
    params,
  });
}

// 店铺自定义图标 - 列表
export function getStoreIconList(params: { storeId: string | number }) {
  return request<ResponseData<{ id: number; name: string; selectedIcon: string; unselectedIcon: string }[]>>(
    `${scenicHost}/store/icon/list`,
    {
      method: 'GET',
      params,
    },
  );
}

// 店铺自定义图标 - 新增或修改
export function addOrUpdateStoreIcon(params: {
  id?: string | number;
  storeId: string | number;
  name: string;
  selectedIcon: string;
  unselectedIcon: string;
}) {
  return request<ResponseData<any>>(`${scenicHost}/store/icon/addOrUpdate`, {
    method: 'POST',
    data: params,
  });
}

// 店铺自定义图标 - 删除
export function deleteStoreIcon(iconId: string | number) {
  return request<ResponseData<any>>(`${scenicHost}/store/icon/${iconId}`, {
    method: 'DELETE',
  });
}

// 店铺商品库存编号列表 (单票)
export function getStoreGoodsNumberPageList(params: API.IStoreGoodsPageListParams) {
  return request<ResponseListData2<API.IStoreGoodsNumberPageListItem[]>>(
    `${scenicHost}/ticketAgent/StoreGoodsNumberPageList`,
    {
      method: 'GET',
      params,
    },
  );
}

// 店铺商品分时预约库存列表 (单票 :票)
export function getStoreTimeSharePageList(params: API.IStoreGoodsTimeSharePageListParams) {
  return request<ResponseListData2<API.IStoreGoodsTimeSharePageListItem[]>>(
    `${scenicHost}/ticketAgent/StoreTimeSharePageList`,
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * @description: 店铺配置权限分页列表
 */
export function getShopPermissionList(params: { userId: string; distributorId: string }) {
  return request<ResponseListData<API.ShopPermissionListItem>>(
    `${scenicHost}/ticketStore/shopPermissionList`,
    {
      method: 'GET',
      params,
    },
  );
}
/**
 * @description: 企业绑定景区验证
 */
export function checkEnterpriseScenic(coId: string) {
  return request<ResponseData<any>>(`${scenicHost}/co/scenic/binding/verify`, {
    method: 'GET',
    params: { coId },
  });
}
