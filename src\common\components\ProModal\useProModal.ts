import { useEffect, useState } from 'react';

export interface ProModalState {
  type: 'add' | 'edit' | 'info' | null;
  setType: React.Dispatch<React.SetStateAction<'add' | 'edit' | 'info' | null>>;
  tableStyle: React.CSSProperties;
  setTableStyle: React.Dispatch<React.SetStateAction<React.CSSProperties>>;
}

export default function useProModal(): ProModalState {
  const [type, setType] = useState<'add' | 'edit' | 'info' | null>(null);
  const [tableStyle, setTableStyle] = useState<React.CSSProperties>({});

  useEffect(() => {
    setTableStyle(type ? { display: 'none' } : {});
  }, [type]);

  return {
    /** 弹窗类型：{ add: '新增', edit: '编辑', info: '详情', null: '关闭' } */
    type,
    /** 设置弹窗类型 */
    setType,
    /** 表格样式 */
    tableStyle,
    /** 设置表格样式 */
    setTableStyle,
  };
}
