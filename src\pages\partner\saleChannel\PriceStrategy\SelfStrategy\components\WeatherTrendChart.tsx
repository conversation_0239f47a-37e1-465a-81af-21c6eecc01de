import React, { useEffect, useState } from 'react';
import Chart from '@/components/Chart';
import { Empty, Spin } from 'antd';
import { apiWeatherForecast, apiWeatherForecastBigData } from '@/services/api/distribution';

// 整合 CSS 样式为组件内部样式对象
const styles = {
  container: {
    padding: '16px',
    background: '#fff',
    borderRadius: '4px',
    marginTop: '16px',
  },
  chartContainer: {
    height: '300px',
    position: 'relative' as 'relative',
  },
  emptyContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '300px',
    background: '#f9f9f9',
    borderRadius: '4px',
  },
  sectionTitle: {
    fontSize: '16px',
    fontWeight: 500,
    marginBottom: '16px',
  },
};

interface WeatherDataItem {
  date: string;
  max: number;
  min: number;
  weather: string;
  aqi: number;
  city: string;
  fire_risk_level: string;
  humidity: number;
  img_url: string;
  precipitation: number;
  real_time_num: number;
  real_time_weather: string;
  tag: string;
  unit: string;
  week_day: string;
  wind_direction: string;
  wind_level: string;
  wind_speed: string;
}

interface WeatherTrendChartProps {
  containerStyle?: React.CSSProperties;
  goodsId: string;
}

/**
 * 天气趋势图组件
 */
const WeatherTrendChart: React.FC<WeatherTrendChartProps> = ({ 
  containerStyle,
  goodsId,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [weatherData, setWeatherData] = useState<WeatherDataItem[]>([]);
  
  // 当 goodsId 变化时获取经纬度数据和天气数据
  useEffect(() => {
    const fetchWeatherData = async () => {
      if (!goodsId) return;
      
      setLoading(true);
      try {
        const response = await apiWeatherForecast({ goodsId });
        if (response.data) {
          console.log('apiWeatherForecast', response.data);
          const {data} = await apiWeatherForecastBigData({
            lng: response.data.longitude,
            lat: response.data.latitude,
            daily_steps: 7,
          });
          console.log('apiWeatherForecastBigData', data);
          if (data && Array.isArray(data)) {
            setWeatherData(data);
          }
        }
      } catch (error) {
        console.error('获取天气数据失败：', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWeatherData();
  }, [goodsId]);
  
  // 天气图标映射
  const weatherIcons: Record<string, string> = {
    '晴': '☀️',
    '多云': '⛅',
    '阴': '☁️',
    '雨': '🌧️',
    '雪': '❄️',
  };

  // 根据天气描述获取对应图标
  const getWeatherIcon = (weatherDesc: string): string => {
    const matchedKey = Object.keys(weatherIcons).find(key => weatherDesc.includes(key));
    return matchedKey ? weatherIcons[matchedKey] : '';
  };

  // 构建图表配置
  const chartOptions = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const maxTemp = params[0];
        const minTemp = params[1];
        const index = maxTemp.dataIndex;
        const item = weatherData[index];
        if (!item) return '';
        
        const weatherIcon = getWeatherIcon(item.weather);
        return `${item.date.replace('年', '.').replace('月', '.').replace('日', '')} ${weatherIcon}<br/>
                最高气温：${item.max}${item.unit}<br/>
                最低气温：${item.min}${item.unit}`;
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      textStyle: {
        color: '#fff'
      },
      confine: true
    },
    legend: {
      data: ['最高气温', '最低气温'],
      right: '10%',
      top: 0,
    },
    grid: {
      left: '6%',
      right: '8%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: weatherData.map(item => item.date.replace('年', '.').replace('月', '.').replace('日', '')),
      axisLine: {
        lineStyle: {
          color: '#eaeaea'
        }
      },
      axisLabel: {
        color: '#666',
        formatter: (value: string, index: number) => {
          const item = weatherData[index];
          if (!item) return value;
          return `${value}\n${getWeatherIcon(item.weather)}`;
        },
        rich: {
          value: {
            lineHeight: 20,
          },
          icon: {
            height: 20,
            align: 'center',
          }
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '每日气温/℃',
      nameLocation: 'middle',
      nameGap: 40,
      nameRotate: 90,
      nameTextStyle: {
        color: '#666',
        padding: [0, 0, 0, 0],
        align: 'center',
        verticalAlign: 'middle'
      },
      axisLabel: {
        formatter: '{value}',
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#eaeaea'
        }
      }
    },
    series: [
      {
        name: '最高气温',
        type: 'line',
        data: weatherData.map(item => item.max),
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: true,
        itemStyle: {
          color: '#ff7300'
        },
        lineStyle: {
          width: 2,
          color: '#ff7300'
        },
      },
      {
        name: '最低气温',
        type: 'line',
        data: weatherData.map(item => item.min),
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: true,
        itemStyle: {
          color: '#1890ff'
        },
        lineStyle: {
          width: 2,
          color: '#1890ff'
        },
      }
    ]
  };

  return (
    <div style={{...styles.container, ...containerStyle}}>
      <Spin spinning={loading}>
        {weatherData.length > 0 ? (
          <div style={styles.chartContainer}>
            <Chart options={chartOptions} />
          </div>
        ) : (
          <div style={styles.emptyContainer}>
            <Empty description="暂无天气数据" />
          </div>
        )}
      </Spin>
    </div>
  );
};

export default WeatherTrendChart;
