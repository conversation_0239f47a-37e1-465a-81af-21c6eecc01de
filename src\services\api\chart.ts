/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-15 15:31:54
 * @LastEditTime: 2023-09-21 10:12:24
 * @LastEditors: zhangfengfei
 */
import { request } from '@umijs/max';
import { getEnv } from '@/common/utils/getEnv';

/** 首页图表接口 */
export async function getTicket(url: string) {
  return request(`${getEnv().CHART_HOST}/${url}`, {
    method: 'POST',
    skipErrorHandler: true,
    withCredentials: false,
  });
}
