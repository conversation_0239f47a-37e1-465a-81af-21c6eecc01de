/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-15 15:31:53
 * @LastEditTime: 2023-09-19 14:05:57
 * @LastEditors: zhangfengfei
 */
import { apiAppOrderPrintStr, sendH5Message } from '@/services/api/erp';
import { Button, Modal, Spin, Tooltip, message } from 'antd';
import QRCode from 'qrcode.react';
import { useEffect, useState } from 'react';
import CopyOut from './CopyOut';

export default function Tickets(props: any) {
  const { children, onTooltipId, orderParticular } = props;
  const [visible, setVisible] = useState(false);

  const [loading, setLoading] = useState(true);

  const [QRcodestatusID, setQRcodestatusID] = useState();
  const [qrText, setQrText] = useState('');

  const apiAppOrderPrintStrDate = async (id: any) => {
    const pars = { ticketNumber: id };
    try {
      const res = await apiAppOrderPrintStr(pars);
      setLoading(false);
      const { data } = res;
      console.log('hujiajia', data);
      setQrText(data);
    } catch (e) {
      console.error(e);
    }
  };

  //二维码
  const QRcode = (id: any) => {
    setQRcodestatusID(id);
    setVisible(true);
    apiAppOrderPrintStrDate(onTooltipId);
  };

  const handleCancel = () => {
    setVisible(false);
  };

  const sendMessage = async (params: any) => {
    const { data } = await sendH5Message(params);
    message.success('发送成功');
  };

  useEffect(() => {
    if (!visible) {
      setLoading(true);
    }
  }, [visible]);

  return (
    <>
      <Modal
        open={visible}
        onCancel={handleCancel}
        footer={[
          <>
            {orderParticular.sourceType !== 5 && (
              <>
                <Button
                  key="btn1"
                  type="primary"
                  onClick={() => {
                    if (orderParticular.pilotPhone) {
                      sendMessage({
                        phone: orderParticular.pilotPhone,
                        subOrderId: orderParticular.orderId,
                      });
                    } else {
                      message.error('手机号为空');
                    }
                  }}
                >
                  重发短信
                </Button>
              </>
            )}

            <Button key="btn2" onClick={handleCancel}>
              关闭
            </Button>
          </>,
        ]}
      >
        <Spin spinning={loading}>
          <QRCode
            style={{ margin: '40px auto', position: 'relative' }}
            value={qrText} //value参数为生成二维码的链接 我这里是由后端返回
            size={200} //二维码的宽高尺寸
            fgColor="#000000" //二维码的颜色
          />
          <h4 className="piaoHao" style={{ display: 'flex', justifyContent: 'center' }}>
            {QRcodestatusID}
          </h4>
        </Spin>
      </Modal>
      <a style={{ textDecoration: 'none' }} onClick={() => QRcode(onTooltipId)}>
        <Tooltip title={onTooltipId}>{children + ' '}</Tooltip>
      </a>
      <a>
        <CopyOut exchangeShop={onTooltipId} />
      </a>
    </>
  );
}
