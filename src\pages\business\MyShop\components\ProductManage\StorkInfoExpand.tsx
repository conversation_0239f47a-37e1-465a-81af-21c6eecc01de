/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-09 10:01:45
 * @LastEditTime: 2023-10-09 10:24:52
 * @LastEditors: zhang<PERSON><PERSON>i
 */
import { getStoreTimeSharePageList } from '@/services/api/store';
import { useRequest } from '@umijs/max';
import { Card, List, Spin } from 'antd';
import type { FC } from 'react';
import { useEffect } from 'react';

interface ExpandedContentProps {
  dataItem: any;
  expanded: boolean;
}

// 展开的分时预约信息
const ExpandedContent: FC<ExpandedContentProps> = ({
  dataItem: { storeGoodsId, stockId, ticketId },
  expanded,
}) => {
  const { loading, data, run } = useRequest(getStoreTimeSharePageList, {
    manual: true,
    formatResult(res: any) {
      return res.data?.data || [];
    },
    initialData: [],
  });

  useEffect(() => {
    if (expanded) {
      run({
        storeGoodsId,
        stockId,
        ticketId,
        pageSize: 999,
      });
    }
  }, [expanded]);

  return (
    <Spin spinning={loading}>
      <span style={{ padding: '8px', display: 'block' }}>入园时间库存信息：</span>
      <List
        style={{
          padding: '4px 8px',
          maxHeight: '300px',
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
        grid={{
          gutter: 16,
          xs: 1,
          sm: 2,
          md: 4,
          lg: 4,
          xl: 5,
          xxl: 5,
        }}
        dataSource={data}
        renderItem={(item: any) => (
          <Card title={item.dayBegin} size="small">
            <p>数量：{item.stockNumber}</p>
          </Card>
        )}
      />
    </Spin>
  );
};

export default ExpandedContent;
