/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-23 14:25:33
 * @LastEditTime: 2022-06-10 14:41:53
 * @LastEditors: zhangfengfei
 */
import { BusinessTypeEnum, EntryTypeEnum, OriginTypeEnum } from '@/common/utils/enum';
import { getUniqueId } from '@/common/utils/tool';
import type { ModalState } from '@/hooks/useModal';
import { getBillDetail } from '@/services/api/settlement';
import type { API } from '@/services/api/typings';
import ProDescriptions from '@ant-design/pro-descriptions';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Modal } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useState } from 'react';
import { useModel } from '@umijs/max';

interface BillDetailModalProps {
  currentItem?: API.CreditAccountItem;
  modalState: ModalState;
}

/**授信客户-收支明细 */
const BillDetailModal: FC<BillDetailModalProps> = ({
  currentItem,
  modalState: { visible, setVisible },
}) => {
  const { initialState } = useModel('@@initialState');
  const settlementId = initialState?.currentCompany.settlementId || '';
  // 附加属性
  const [otherAttribute, setOtherAttribute] = useState<{ income: number; outcome: number }>();

  const tableListReq = async (params: API.BillDetailParams) => {
    const { data } = await getBillDetail(params);
    setOtherAttribute(data.otherAttribute);
    return {
      data: data.page,
      total: data.totalNumberOfResults,
    };
  };

  const list = [
    {
      label: '编号',
      content: currentItem?.id,
    },
    {
      label: '授信客户账号',
      content: currentItem?.consumer?.registrationName,
    },
    {
      label: '授信客户名称',
      content: currentItem?.consumer?.legalName,
    },
    {
      label: '授信余额（元）',
      content: currentItem?.balance,
    },
    {
      label: '合计收入（元）',
      content: otherAttribute?.income,
    },
    {
      label: '合计支出（元）',
      content: otherAttribute?.outcome,
    },
  ];

  const columns: ProColumns<API.BillDetailItem>[] = [
    {
      title: '日期',
      dataIndex: 'creationDate',
      renderText: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '收/支',
      dataIndex: 'entryType',
      valueType: 'select',
      valueEnum: EntryTypeEnum,
    },

    {
      title: '业务类型',
      dataIndex: 'businessType',
      valueType: 'select',
      valueEnum: BusinessTypeEnum,
    },
    {
      title: '源单号',
      dataIndex: 'outTradeNo',
    },
    {
      title: '源单类型',
      dataIndex: 'originType',
      valueType: 'select',
      valueEnum: OriginTypeEnum,
    },
    {
      title: '金额（元）',
      dataIndex: 'amount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '完成后余额（元）',
      dataIndex: 'afterBalance',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  return (
    <Modal
      width={1200}
      title={'授信客户收支明细'}
      visible={visible}
      maskClosable
      destroyOnClose
      onCancel={() => setVisible(false)}
      footer={<Button onClick={() => setVisible(false)}>返回</Button>}
    >
      <ProDescriptions style={{ padding: '0 24px' }} column={3}>
        {list.map((item) => (
          <ProDescriptions.Item key={item.label} label={item.label}>
            {item.content ?? '-'}
          </ProDescriptions.Item>
        ))}
      </ProDescriptions>
      <ProTable<API.BillDetailItem, API.BillDetailParams>
        style={{ marginTop: 32 }}
        rowKey={getUniqueId}
        options={false}
        search={false}
        params={{
          merchantId: settlementId,
          creditAccountId: currentItem?.id || '',
        }}
        pagination={{
          defaultPageSize: 10,
        }}
        request={tableListReq}
        columns={columns}
      />
    </Modal>
  );
};

export default BillDetailModal;
