import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getOrgStructureApproval, getUserApproval } from '@/services/api/cas';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ModalForm, ProForm, ProFormTextArea } from '@ant-design/pro-components';
import type { ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Popconfirm, Space, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import { getEnv } from '@/common/utils/getEnv';

export default function UserApproval() {
  const access = useAccess();
  const actionRef = useRef();
  const [modalVisit, setModalVisit] = useState(false);
  //企业下拉框
  const [companyData, setCompanyData] = useState([]);
  //企业下拉框 value
  const [companyValue, setCompanyValue] = useState('');
  const { initialState }: any = useModel('@@initialState');
  const [formObj] = ProForm.useForm();
  console.log('pppppppppp', initialState);
  //审批人
  const { nickname, phone, username, userId } = initialState.userInfo;
  console.log('userId', userId, initialState);
  //当前企业 id
  const { coId, coName } = initialState.currentCompany;
  console.log('jjjjjjjjjjjjjj', coId);
  //拒绝理由
  const [reason, setReason] = useState('');
  //拒绝理由参数
  const [reasonObj, setReasonObj] = useState({});
  const { APPID } = getEnv();
  //同意
  const onAgreement = async (val: any) => {
    console.log('kkkkkkkkkkkkk', val);
    const pars = {
      approvalUserName: username,
      companyName: val.companyName,
      companyId: val.companyId,
      // refuseReason:'',
      phone: val.phone,
      status: 1,
      userId: val.userId,
      userNickname: val.applyName,
      permissionCode: [
        {
          group: 'e-commerce/user_status',
          code: val.companyId,
          action: '',
        },
      ],
      appId: APPID,
    };
    try {
      const result = await getOrgStructureApproval(pars);
      addOperationLogRequest({
        action: 'audit',
        content: `同意${val.applyName}用户审批`,
      });
      // console.log(pars);
      message.success('已同意');
      // getNoticeList()
      // 刷新
      actionRef?.current?.reload();
    } catch (e) {
      console.error(e);
    }
  };

  //拒绝
  const refuse = async (val: any) => {
    // console.log(val);
    setReasonObj(val);
    setModalVisit(true);
    // try {
    //   //  const result = await getOrgStructureApproval(pars);
    //   //  message.success('已拒绝');
    //   // setModalVisit(true);
    //   // // 刷新
    //   // actionRef?.current?.reload();
    // } catch (e) {
    //   console.log(e);
    // }
  };

  //理由
  const getReason = async (val) => {
    const pars = {
      approvalUserName: username,
      companyId: reasonObj.companyId,
      companyName: reasonObj.companyName,
      refuseReason: val.text,
      phone: reasonObj.phone,
      status: 2,
      userId: reasonObj.userId,
      userNickname: reasonObj.applyName,
      permissionCode: [
        {
          group: 'e-commerce/user_status',
          code: reasonObj.companyId,
          action: '',
        },
      ],
      appId: APPID,
    };
    try {
      if (val.text) {
        console.log(val);
        const result = await getOrgStructureApproval(pars);
        addOperationLogRequest({
          action: 'audit',
          content: `拒绝${reasonObj.applyName}用户审批`,
        });
        setModalVisit(false);
        // setReason(val)
        message.success('已拒绝');
        formObj.setFieldsValue({ text: '' });
        // return true;
      } else {
        message.info('请填写拒绝的原因');
        setModalVisit(false);
        // return false;
      }

      // 刷新
      actionRef?.current?.reload();
    } catch (e) {
      console.error(e);
    }
  };

  const cancel = (e) => {
    console.log(e);
    // message.error('Click on No');
  };
  const columns: ProColumnType[] = [
    {
      title: '编号',
      dataIndex: 'number',
      hideInSearch: true,
      // render:(dom,recoder)=>{
      //   // let num= 0
      //   recoder.number =1

      // }
      // valueType: 'search',
      // fieldProps: {
      //   // options: [{ '1': '1' }, { '2': '2' }],
      // },
      //   render: (dome: any, record: any) => {
      //     return record.scenicName;
      //   },
      //   transform: (id) => {
      //     return {
      //       scenicId: id,
      //     };
      //   },
      //   renderFormItem: () => {
      //     return <Serviceprovider />;
      //   },
    },
    {
      title: '企业',
      dataIndex: 'companyIds',
      valueType: 'select',
      hideInTable: true,
      initialValue: coName,

      fieldProps: {
        // placeholder: '请输入企业',
        // options:children2
        disabled: true,
        // options: companyData,
        options: [
          {
            coId: coName,
          },
        ],

        onChange: (value) => {
          console.log('ttttttttttt', value);
          setCompanyValue(value);
        },
        // renderText:(dom,record)=>{
        //     console.log('==============',record)
        // }
      },
      // hideInSearch: true,
      // renderFormItem: () => {
      //   return <ServiceProvider />
      // },
    },
    {
      title: '企业',
      dataIndex: 'companyName',
      hideInSearch: true,
    },
    {
      title: '申请人姓名',
      dataIndex: 'applyName',
      hideInSearch: true,
    },
    {
      title: '申请人手机号',
      dataIndex: 'phone',
      hideInSearch: true,
    },
    // {
    //   title: '企业',
    //   dataIndex: 'proposerPhone',
    //   valuetype:'select',
    //   // valueEnum:{},
    //   // colSize:1,
    //   // width:'15%',
    //   fieldProps: {
    //     placeholder: '请输入企业',
    //     options:[]
    //   },
    // },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      valueType: 'date',
    },
    {
      title: '审核人',
      dataIndex: 'approvalUserName',
      hideInSearch: true,
    },
    {
      title: '审核时间',
      dataIndex: 'approvalTime',
      hideInSearch: true,
    },
    // {
    //   title: '开始日期',
    //   dataIndex: 'Time',
    //   valueType: 'dateRange',
    //   key: 'Time',
    //   hideInTable: true,
    //   transform: (date: any) => {
    //     console.log(date[0], date[1]);
    //     return {
    //       beginTime: date[0] + '' + '00:00:00',
    //       endTime: date[1] + '' + '23:59:59',
    //     };
    //   },
    // },
    {
      width: 'auto',
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (dom: any, record: any) => {
        return (
          <Space>
            {record.approvalStatus == 0 ? (
              <Access accessible={access.canUserApprove_refuseAgree}>
                <Space>
                  <Popconfirm
                    title={`您确定同意吗?`}
                    onConfirm={() => onAgreement(record)}
                    icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                    onCancel={cancel}
                    okText="确认"
                    cancelText="取消"
                  >
                    <a>同意</a>
                  </Popconfirm>
                  {/* <Popconfirm
                      title="您确定拒绝吗?"
                      onConfirm={() => refuse(record.noticeId)}
                      icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                      onCancel={cancel}
                      okText="确认"
                      cancelText="取消"
                    >
                        </Popconfirm> */}
                  <a onClick={() => refuse(record)}>拒绝 </a>
                </Space>
              </Access>
            ) : record.approvalStatus == 1 ? (
              <a>已同意</a>
            ) : (
              <a>已拒绝</a>
            )}

            {/*
            <a href="javascript:;" onClick={() => refuse(record)}>
              待审批
            </a> */}
          </Space>
        );
      },
    },
  ];

  //获取分页列表
  const [dataMessge, setDataMessage] = useState([]);
  const getUserList = async (params: any) => {
    delete params.companyIds;
    const pars = {
      companyId: coId,
      systemTypes: 3,
    };
    // if(companyValue !=='') {
    //   pars.companyId = companyValue
    // }

    try {
      const { data } = await getUserApproval({ ...pars, ...params });
      //  delete pars.companyId
      const { data: applyList } = data;

      console.log(applyList);
      applyList.forEach((item, index) => {
        item.number = index + 1;
      });
      setDataMessage(applyList);
      return {
        ...data,
        data: applyList,
      };
    } catch (e) {
      console.error(e);
    }
  };

  //获取企业列表
  // const getCompanyList = async () => {
  //   try {
  //     const result = await getCoPageList({});
  //     const { data } = result;

  //     // const children = data.map((item: any) => {
  //     //   // return <Option key={item.coId}>{item.coName}</Option>;
  //     //   return {
  //     //     value: item.coId,
  //     //     label: item.coName,
  //     //   };
  //     // });

  //     setCompanyData(children);
  //     console.log('ooooooooooooo', result);
  //   } catch (e) {
  //     console.error(e);
  //   }
  // };

  useEffect(() => {
    //企业列表
    // setCompanyData({value:coId,label:coName});
    // getCompanyList();
    // getUserApprovalList().then(res=>{
    //     console.log('yyds',res.data)
    // })
  }, []);

  return (
    <>
      <ModalForm
        title="用户审批"
        visible={modalVisit}
        form={formObj}
        width={600}
        onFinish={(val) => getReason(val)}
        onVisibleChange={setModalVisit}
      >
        <ProForm.Group>
          <ProFormTextArea
            width={600}
            name="text"
            label="请填写拒绝的原因"
            placeholder="请输入拒绝的原因"
            fieldProps={{ autoSize: { minRows: 2, maxRows: 6 } }}
          />
        </ProForm.Group>
      </ModalForm>

      <ProTable
        {...tableConfig}
        columns={columns}
        actionRef={actionRef}
        request={getUserList}
        // dataSource={data}
        rowKey="number"
        pagination={{
          showQuickJumper: true,
          pageSize: 10,
        }}
        toolBarRender={false}
      />
    </>
  );
}
