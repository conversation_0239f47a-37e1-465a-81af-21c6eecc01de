/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-16 15:10:06
 * @LastEditTime: 2025-05-29 11:22:59
 * @LastEditors: 李悍宇 <EMAIL>
 */
import { SettlementStatusType } from '@/common/utils/enum';
import {
  getCoInfo,
  getIMerchantList,
  getPersonInfo,
  submitApproval,
} from '@/services/api/companyJoin';
import type { API } from '@/services/api/typings';
import ProDescriptions from '@ant-design/pro-descriptions';
import ProForm from '@ant-design/pro-form';
import type { ProColumns } from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Button, Modal, Select, message } from 'antd';
import qs from 'qs';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { history, useLocation, useRequest } from '@umijs/max';
import type { URLParams } from '.';

interface CommitProps {}

type OptionType = {
  value: string;
  label: string;
  type: number;
  settlementId: string;
};

const Commit: FC<CommitProps> = () => {
  const { search } = useLocation();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const { initialState } = useModel('@@initialState');
  const formRef = useRef<any>();
  const [current, setCurrent] = useState<OptionType>();

  const { code, coId, coName } = qs.parse(search, {
    ignoreQueryPrefix: true,
  }) as unknown as URLParams;

  const downListReq = useRequest(getIMerchantList, {
    formatResult: (res) => {
      return (res.data ?? []).map((item) => ({
        value: item.id,
        label: item.name,
        type: item.type,
        settlementId: item.settlementId,
      }));
    },
  });

  // 需根据 current.type 判断是个人还是企业
  const personInfoReq = useRequest(getPersonInfo, {
    manual: true,
  });
  // 需根据 current.type 判断是个人还是企业
  const coInfoReq = useRequest(getCoInfo, {
    manual: true,
  });

  // 提交审批
  const submitApprovalReq = useRequest(submitApproval, {
    manual: true,
    onSuccess(data, params) {
      const { upDistributorId, applyDistributorId, type, userId } = params[0];
      const searchParams = {
        code,
        coId: upDistributorId,
        applyDistributorId,
        applyId: data,
        coName,
        type,
        userId,
      };
      message.success('提交成功');
      history.push(`/companyJoin/result?${qs.stringify(searchParams)}`);
    },
  });

  const onSelect = (value: string, option: OptionType | any) => {
    setCurrent(option);
  };

  const confirm = () => {
    Modal.confirm({
      title: '您当前处于未开户状态',
      type: 'warning',
      content: (
        <>
          <span style={{ color: 'red' }}>要先前往开户</span>，你还要继续吗
        </>
      ),
      onOk() {
        history.push('/enterprise');
      },
      okText: '去开户',
    });
  };

  const onSubmit = () => {
    if (current) {
      const { value, type, settlementId } = current;
      // 自己
      if (coId === value) {
        message.warning('企业无法申请成为自己的下级经销商或代理商，请重新选择');
        return;
      }
      // 未开户
      if (!settlementId) {
        confirm();
        return;
      }
      // 已有审批或者已是分销商、代理商
      if (type === 1 && coInfoReq.data?.settlementStatus != SettlementStatusType.审批通过) {
        message.warning(SettlementStatusType[coInfoReq.data?.settlementStatus]);
        return;
      }

      submitApprovalReq.run({
        code,
        applyDistributorId: value,
        upDistributorId: coId,
        type: searchParams.get('type'),
        userId: searchParams.get('userId'),
      });
    }
  };

  const columns: ProColumns<API.CoInfo>[] = [
    {
      title: '名称',
      dataIndex: 'coName',
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'coCode',
    },
    {
      title: '手机号',
      dataIndex: 'coPhone',
    },
    {
      title: '电子邮箱',
      dataIndex: 'coEmail',
    },
    {
      title: '地址',
      dataIndex: 'coAddressInfo',
    },
    {
      title: '商户账号',
      dataIndex: 'settlementName',
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
    },
  ];

  const personColumns: ProColumns<API.PersonInfo>[] = [
    {
      title: '名称',
      dataIndex: 'coName',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
    },
    {
      title: '电子邮箱',
      dataIndex: 'email',
    },

    {
      title: '联系人',
      dataIndex: 'contact',
    },
  ];

  useEffect(() => {
    if (current) {
      const { type, value } = current;
      if (type === 1) {
        coInfoReq.run({
          id: value,
        });
      } else {
        personInfoReq.run({
          useId: value,
        });
      }
    }
  }, [current, current?.value]);

  console.log('feiefefi');

  return (
    <div style={{ width: 736, minHeight: 600 }}>
      <ProForm formRef={formRef} layout="vertical" submitter={false}>
        <ProForm.Group title="基本信息" titleStyle={{ fontSize: 16 }}>
          <ProForm.Item
            label="请选择"
            name="type"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Select
              allowClear
              style={{ width: 216 }}
              options={downListReq.data}
              loading={downListReq.loading}
              onChange={onSelect}
            />
          </ProForm.Item>
        </ProForm.Group>
      </ProForm>

      {current && (
        <>
          <ProDescriptions<any>
            column={2}
            title="详细信息"
            layout="vertical"
            colon={false}
            dataSource={current.type === 1 ? coInfoReq.data : personInfoReq.data}
            columns={current.type == 1 ? columns : personColumns}
          />
          <div style={{ textAlign: 'center', marginTop: 30 }}>
            <Button
              type="primary"
              style={{ width: 216 }}
              onClick={onSubmit}
              loading={submitApprovalReq.loading}
            >
              提交申请
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default Commit;
