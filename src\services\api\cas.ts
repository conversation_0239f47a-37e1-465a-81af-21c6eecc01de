import { message } from 'antd';
import { request } from '@umijs/max';
import { scenicHost } from '.';
import { getEnv } from '@/common/utils/getEnv';
import type { API, ResponseData, ResponseListData2 } from './typings';

export const platformId = '03'; //01 慧旅云平台 02 景区 03 分销

/** 登录接口 POST /user/no_auth/login */
export async function login(body: API.LoginParams, options?: Record<string, any>) {
  return request<API.LoginResult>(`${scenicHost}/user/noAuth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: { autoRegister: true, ...body },
    ...(options || {}),
  });
}
/** 退出登录 */
export async function logout(params) {
  return request<API.LoginResult>(`${scenicHost}/user/logout`, {
    method: 'PUT',
    data: params,
  });
}
//通过公司 ID 获取部门
export async function getDepartment(params: any) {
  return request(`${scenicHost}/orgStructure/deptList`, {
    method: 'POST',
    data: params,
  });
}
//添加部门
export async function addDepartment(params: any) {
  return request(`${scenicHost}/orgStructure/addDept`, {
    method: 'POST',
    data: params,
  });
}

//修改部门信息
export async function setDepartment(params: any) {
  return request(`${scenicHost}/orgStructure/editDept`, {
    method: 'POST',
    data: params,
  });
}

//添加用户
export async function addUser(params: any) {
  return request(`${scenicHost}/orgStructure/addUser`, {
    method: 'POST',
    data: params,
  });
}

//用户禁用 or 启用
export async function disableUser(params: any) {
  return request(`${scenicHost}/orgStructure/disableUser`, {
    method: 'POST',
    data: params,
  });
}

//授权权限
export async function postPermissionAuthorize(params: any) {
  return request(`${scenicHost}/permission/authorize`, {
    method: 'POST',
    data: params,
  });
}

// 开通微服务
export async function servicePackage(params: any) {
  return request(`${scenicHost}/servicePackage/activateEc`, {
    method: 'POST',
    data: params,
  });
}

//撤销权限
export async function postPermissionRevoke(params: any) {
  return request(`${scenicHost}/permission/revoke`, {
    method: 'DELETE',
    data: params,
  });
}

//禁用用户并登出
export async function putUserDisable(params: any) {
  return request(`${scenicHost}/user/disable`, {
    method: 'PUT',
    data: params,
  });
}

//修改用户信息
export async function setUser(params: any) {
  return request(`${scenicHost}/orgStructure/editUser`, {
    method: 'POST',
    data: params,
  });
}

//查看用户信息
export async function checkUser(params: any) {
  return request(`${scenicHost}/orgStructure/userCoInfo`, {
    method: 'GET',
    params: { ...params },
  });
}

//重置密码
export async function resetUserPasswork(params: any) {
  console.log(JSON.stringify(params));
  return request(`${scenicHost}/user/resetPassword`, {
    method: 'POST',
    data: params,
  });
}

//查询权限列表（post）
export async function getPermissionListByPost(params: any) {
  return request(`${scenicHost}/permission/retrieve`, {
    method: 'POST',
    data: params,
  });
}

//查询公司所有用户
export function getCoAllUsers(params: {
  companyId: string;
  scenicId?: string;
  userStatus?: number;
}) {
  const { companyId, ...rest } = params;
  return request<ResponseData<API.UserPageListItem[]>>(
    `${scenicHost}/orgStructure/${companyId}/allUsers`,
    {
      method: 'GET',
      params: rest,
    },
  );
}

/**
 * @description: 获取用户列表（新）
 */
export function getUserPageList(params: any) {
  return request<ResponseListData2<API.UserPageListItem[]>>(
    `${scenicHost}/orgStructure/userPageList2`,
    {
      params,
    },
  );
}

//增加角色
export async function addRole(params: any) {
  params.type = platformId; //type，01 智旅云平台，非 01，type，relationId
  return request(`${scenicHost}/role/add`, {
    method: 'POST',
    data: params,
  });
}

//删除角色
export async function deleteRole(roleId: string) {
  return request(`${scenicHost}/role/del/${roleId}`, {
    method: 'DELETE',
  });
}

//修改角色
export async function setRole(params: any) {
  return request(`${scenicHost}/role/edit`, {
    method: 'POST',
    data: params,
  });
}

//获取角色列表
export async function getRoleList(params: Record<string, any>) {
  return request<ResponseListData2<API.RoleListItem[]>>(`${scenicHost}/role/list`, {
    method: 'GET',
    params,
  });
}

/** 查询企业角色列表 */
export function getRoleDownList(params) {
  return request<ResponseData<API.RoleListItem[]>>(`${scenicHost}/role/listAll`, {
    method: 'GET',
    params,
  });
}
//根据用户 id 获取对应角色
export function getRoleInfoList(params: { type: string; userId: string; relationId: string }) {
  return request<ResponseData<API.RoleInfoListItem[]>>(`${scenicHost}/role/roleInfoList`, {
    method: 'GET',
    params,
  });
}

/**
 * @description: 查询用户所在部门
 */
export function getUserDept(params: { companyId: string; userId: string }) {
  return request<ResponseData<API.DepartmentItem[]>>(`${scenicHost}/orgStructure/queryUserDept`, {
    params,
  });
}

//useless 2021 年 11 月 9 日
export async function getRoleListByPermission(q: string[]) {
  const r: any = await request<API.RuleListItem>(`${scenicHost}/role/rolePermissionList`, {
    method: 'GET',
    params: { permission: q },
  });
  console.log(r);
  if (r.code === 20000) {
    return r.data.filter((e: any) => {
      return e.status === 1; //只要启用的
    });
  }
  return [];
}

//通过角色 ID 获取权限列表，如不传则为取全部权限，全部权限用做新建
export async function getPermissionList(param: API.GetPermissionListParam) {
  if (!param.type) {
    param.type = platformId;
  }

  return request(`${scenicHost}/role/listPermission`, {
    method: 'GET',
    params: param,
    skipErrorHandler: true,
  });
}

//查询权限列表（菜单权限）,2022 年 9 月 16 日
export async function getPermissionListNew(params: any) {
  if (!params.type) {
    params.type = platformId;
  }
  return request(`${scenicHost}/servicePackage/scenicPermissionList`, {
    method: 'GET',
    params,
    skipErrorHandler: true,
  });
}

//通过 user ID 获取权限列表，如不传则为取自己的权限
export async function getPermissionListByUserId(users: any[], groups?: any[]) {
  return request(`${scenicHost}/permission/retrieve`, {
    method: 'GET',
    params: { users, groups, appId: getEnv().APPID },
  });
}

//查询某个权限是否存在
export async function confirmPermission(params: any) {
  return request(`${scenicHost}/permission/confirm`, {
    method: 'POST',
    data: params,
  });
}

//授权基础默认权限
export async function roleBasePermission(params: any) {
  return request(`${scenicHost}/role/basePermission`, {
    method: 'PUT',
    data: params,
  });
}

// ================== 个人信息  ===================
//获取用户 social 信息
export async function getUserSocial() {
  return request(`${scenicHost}/user/userSocial`, {
    method: 'GET',
  });
}

//修改密码
export async function updatePassword(params: object) {
  return request(`${scenicHost}/user/updatePassword`, {
    method: 'POST',
    data: params,
  });
}

//修改手机
export async function updatePhone(params: object) {
  return request(`${scenicHost}/user/bindPhone`, {
    method: 'POST',
    data: params,
  });
}

//手机邮箱验证码
export async function getOtp(phone: string, type: string) {
  const t = type === 'phone' ? 2 : 1;
  return request(`${scenicHost}/user/otp`, {
    method: 'GET',
    params: {
      type: t,
      credential: phone,
    },
  });
}

//邮箱忘记密码验证码
export async function getResetOtp(email: string) {
  return request(`${scenicHost}/user/otp`, {
    method: 'GET',
    params: {
      type: 1,
      credential: email,
      template: 't0001',
    },
  });
}

//查询是否已经注册
export async function checkAccountExist(account: string) {
  return request(`${scenicHost}/user/checkCredential`, {
    method: 'GET',
    params: {
      credential: account,
    },
    skipErrorHandler: true,
  });
}

//修改邮箱
export async function updateEmail(params: object) {
  return request(`${scenicHost}/user/bindEmail`, {
    method: 'POST',
    data: params,
  });
}

//获取角色列表 模拟
export async function getUserListSim() {
  const d = request(`${scenicHost}/role/list`);
  return d;
}

export async function addUserSim(options?: Record<string, any>) {
  return request<API.RuleListItem>('/api/ban/userAdd', {
    method: 'PUT',
    params: options,
  });
}

export async function delUserSim(options?: Record<string, any>) {
  return request<API.RuleListItem>('/api/ban/userDelete', {
    method: 'DELETE',
    params: options,
  });
}

/**
 * //操作之类的统一提示请求，包括添加删除修改
 *
 * @param netWork 网络请求的操作
 * @param successDeal 请求成功之后的动作
 * @param doingRemind 正在操作的提示字符串
 * @param successRemind 操作成功之后的提示字符串
 * @param failRemind 操作失败之后的提示字符串
 */
export async function operationRequest(
  netWork: Promise<any>,
  successDeal?: () => void,
  doingRemind?: string,
  successRemind?: string,
  failRemind?: string,
) {
  const hide = message.loading(doingRemind ? doingRemind : '正在操作'); //带默认值提示修改
  try {
    await netWork; //等待请求完成，加了 errorConfig 之后，非 200 会直接抛出异常
    hide();
    message.success(successRemind ? successRemind : '操作成功');
    if (successDeal) {
      successDeal(); //请求完成后需要的处理
    }
  } catch (e: any) {
    hide();
    if (e.name === 'BizError') {
      /**
       * 系统默认有提示
       * @see /app.tsx
       */
    } else {
      message.error(`${failRemind ? failRemind : '操作失败'}:${e}`);
    }
  }
}

export async function setCookie(id: string) {
  return request<API.RuleListItem>(`${scenicHost}/user/noAuth/setCookie/${id}`, {
    method: 'PUT',
    // params: options,
  });
}

//用户审批列表
export function getUserApproval(params: any) {
  return request<ResponseListData2<any[]>>(`${scenicHost}/orgStructure/applyList`, {
    method: 'GET',
    params,
  });
}

//用户邀请
export async function getOrgStructure(params: any) {
  return request<API.RuleListItem>(`${scenicHost}/orgStructure/invite`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

//用户申请加入
export async function getUserApply(params: any) {
  return request(`${scenicHost}/orgStructure/apply`, {
    method: 'POST',
    data: params,
  });
}
//用户审批
export async function getOrgStructureApproval(params: any) {
  return request(`${scenicHost}/orgStructure/approval`, {
    method: 'POST',
    data: params,
  });
}
//验证
export async function getOrgStructureVerifyCert(params: any) {
  return request(`${scenicHost}/orgStructure/verifyCert`, {
    method: 'PUT',
    // params:{
    //   ...params
    // }
    data: params,
  });
}

//复制权限
export async function userDataMigrate(params: any) {
  params.type = platformId;
  return request(`${scenicHost}/permission/changePermission`, {
    method: 'PUT',
    data: params,
  });
}
//通过 userId 数组查询用户详情信息
export async function getOrgStructureUserInfoList(params: any) {
  return request(`${scenicHost}/orgStructure/userInfoList`, {
    method: 'POST',
    data: params,
  });
}

/**
 * @description: 获取用户企业在当前系统的权限列表
 */
export function getMenuPermissions(params: API.MenuPermissionsParams) {
  return request<ResponseData<API.Permission[]>>(`${scenicHost}/permission/menuPermission`, {
    method: 'GET',
    params,
  });
}
