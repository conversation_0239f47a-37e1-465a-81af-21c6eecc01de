// import { request } from './api';
import { getCompanyID } from '@/common/utils/storage';
import type { RequestData } from '@ant-design/pro-table';
import { message } from 'antd';
import { request } from '@umijs/max';
import { scenicHost } from '.';
import { getEnv } from '@/common/utils/getEnv';

import type { API, ResponseData } from './typings';

/** 获取景区列表 GET /scenic/pageList */
export async function getScenicPageList(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: Record<string, any>,
) {
  //处理省市区
  const paramsObj = {
    ...params,
    provinceCode: params?.provice?.[0]?.addressId,
    cityCode: params?.provice?.[1]?.addressId,
    areaCode: params?.provice?.[2]?.addressId,
  };
  delete paramsObj?.provice; //删除无用参数

  const { code, data } = await request(`${scenicHost}/scenic/pageList`, {
    method: 'GET',
    params: {
      ...paramsObj,
    },
    ...(options || {}),
  });

  data?.data?.map((e: any, i: any) => {
    // 添加 id
    e.id = i;
    // 处理启用状态的样式
    e.isEnable = {
      color: e.isEnable === '1' ? 'blue' : 'red',
      text: e.isEnable === '1' ? '启用' : '禁用',
    };
  });
  return {
    data: data?.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data?.total, //TODO:后端接口报错时页面会报错
  };
}

// 获取景区列表
export function apiScenicList(params: any, options?: Record<string, any>) {
  return request(`${scenicHost}/scenic/pageList`, {
    method: 'GET',
    params: params,
    ...(options || {}),
  });
}

/** 新增或修改景区详情 post  */
export async function saveScenic(params: any, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/scenic/saveAndUpdate`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 获取企业信息列表 GET /Co/pageList */
export async function getCoPageList(
  params: {
    // query
    /** 当前的页码 */
    pageNum?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: Record<string, any>,
) {
  //处理省市区
  const paramsObj = {
    ...params,
    coProvinceCode: params?.provice?.[0]?.addressId,
    coCityCode: params?.provice?.[1]?.addressId,
    coAreaCode: params?.provice?.[2]?.addressId,
  };
  delete paramsObj?.provice; //删除无用参数

  const { code, data } = await request(`${scenicHost}/co/pageList`, {
    method: 'GET',
    params: {
      ...paramsObj,
    },
    ...(options || {}),
  });

  // 处理启用状态的样式
  data?.data?.map((e: any) => {
    e.isEnable = {
      color: e.isEnable === '1' ? 'blue' : 'red',
      text: e.isEnable === '1' ? '启用' : '禁用',
    };
  });

  return {
    data: data.data,
    // success 请返回 true，
    // 不然 table 会停止解析数据，即使有数据
    success: code === 20000,
    // 不传会使用 data 的长度，如果是分页一定要传
    total: data.total,
  };
}

/** 获取省市区 GET /address/info */
export async function getProvince(
  params: {
    // query
    /** 父级 id */
    id?: number;
  },
  options?: Record<string, any>,
) {
  const { code, data, msg }: any = await request<API.RuleList>(`${scenicHost}/address/info`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
  if (code === 20000) {
    return data;
  } else {
    message.error(msg);
    return [];
  }
}

/** 获取企业详情 */
export async function getCoInfo(params: any) {
  const { data }: any = await request<API.RuleList>(`${scenicHost}/co/info/${params.id}`);
  // 处理省市区数据结构
  data.province = [];
  if (data.coProvinceName) {
    data.province[0] = data.coProvinceName;
  }
  if (data.coCityName) {
    data.province[1] = data.coCityName;
  }
  if (data.coAreaName) {
    data.province[2] = data.coAreaName;
  }
  return data;
}

/** 获取景区详情 GET /scenic/info */
export async function getScenicInfo(id?: string, options?: Record<string, any>) {
  if (!id) return {}; //如果 id 为空，不发送请求 TODO:初始化会发送一次无用请求，暂时这样处理，可能有更优雅的写法？
  const {
    code,
    msg,
    data: { scenic, scenicAddress, scenicAttribute, scenicBusiness, scenicDesc },
  } = await request(`${scenicHost}/scenic/info/` + id, {
    method: 'GET',
    ...(options || {}),
  });

  const { IMG_HOST } = getEnv();
  if (code !== 20000) {
    message.error(msg);
    return {};
  }
  //处理图片数据结构用于展示
  if (scenicBusiness.picture) {
    scenicBusiness.picture = scenicBusiness.picture.split(',').map((e: any) => {
      return {
        url: IMG_HOST + e, //展示用的路径
        pathUrl: e, //返回给后端的参数
      };
    });
  } else {
    scenicBusiness.picture = [];
  }
  if (scenic.scenicLogo) {
    scenic.scenicLogo = scenic.scenicLogo.split(',').map((e: any) => {
      return {
        url: IMG_HOST + e, //展示用的路径
        pathUrl: e, //返回给后端的参数
      };
    });
  } else {
    scenic.scenicLogo = [];
  }

  //处理省市区数据结构
  scenicAddress.province = [];
  if (scenicAddress.provinceName) {
    scenicAddress.province[0] = scenicAddress.provinceName;
  }
  if (scenicAddress.cityName) {
    scenicAddress.province[1] = scenicAddress.cityName;
  }
  if (scenicAddress.areaName) {
    scenicAddress.province[2] = scenicAddress.areaName;
  }

  scenicBusiness.isPlayRefund = scenicBusiness.isPlayRefund === '1';
  scenicBusiness.isForceRefund = scenicBusiness.isForceRefund === '1';
  scenicBusiness.isReceipt = scenicBusiness.isReceipt === '1';

  scenicAttribute.isOrderModify = scenicAttribute.isOrderModify === '1';
  scenicAttribute.isPticketRefund = scenicAttribute.isPticketRefund === '1';
  scenicAttribute.isBlockChain = scenicAttribute.isBlockChain === 1;
  return {
    ...scenic,
    ...scenicAddress,
    ...scenicAttribute,
    ...scenicBusiness,
    ...scenicDesc,
  };
}

/** 新增或修改景区状态 */
export async function postScenicStatus(params: any, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/scenic/status`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 新增或修改企业状态 */
export async function postCoStatus(params: any, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/co/status`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 新增或修改企业详情  */
export async function postCoInfo(params: any, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/co/info`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}
export function getCompanyInfo(coId: string) {
  return request(`${scenicHost}/co/info/${coId}`);
}

// 同步支付系统企业信息
export function coSyncAuth(coCode: string) {
  return request<any>(`${scenicHost}/co/sync/auth/info`, {
    method: 'PUT',
    params: { coCode },
  });
}
// 落地页，查询信用号是否占用
export function apiCheckCoCode(id: string) {
  return request<any>(`${scenicHost}/co/coCode/auth?coCode=${id}`, {
    method: 'GET',
  });
}

/** 删除企业 delete /Co/del/{id} */
export async function deleteCo(id: string, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/co/del/` + id, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 删除景区 delete /scenic/del/{id} */
export async function deleteScenic(id: string, options?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/scenic/del/` + id, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 消息列表 */
export async function apiMessageList(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/message/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/** 已读消息 */
export async function apiMessageRead(id: string | number) {
  return await request<API.RuleList>(`${scenicHost}/message/messageInfo/${id}`, {
    method: 'PUT',
  });
}

/** 已办列表 */
export async function apiProcessedFlowBizList(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/processedFlowBizPageList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
/** 待办列表 */
export async function apiProcessingFlowBizList(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/processingFlowBizPageList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
/** 审批流程详情 */
export async function apiFlowBizDetail(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/flowBizList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
/** 审核待办 */
export async function apiFlowBizApprove(params?: Record<string, any>) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/approve`, {
    method: 'POST',
    data: params,
  });
}

/** 获取消息未读数量 */
export async function apiUnReadMessageCount(params: any) {
  return await request<API.RuleList>(`${scenicHost}/message/noAuth/getUnReadMessageCount`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
/** 获取审批人代办数量 */
export async function apiProcessingFlowBizCount(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/noAuth/getProcessingFlowBizCount`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/** 获取业务审批流程列表 */
export async function apiFlowBizPageList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowBiz/FlowBizPageList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
/** 获取业务规则列表 */
export async function apiFlowRulePageList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowRule/pageList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
/** 更新业务规则 */
export async function apiUpdateFlowRule(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowRule/rule`, {
    method: 'PUT',
    data: params,
  });
}
/** 获取规则管理详情 */
export async function apiFlowRuleManage(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowRule/ruleManage`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
/** 新增规则管理 */
export async function apiPostFlowRuleManage(params: any) {
  return await request<API.RuleList>(`${scenicHost}/flowRule/ruleManage`, {
    method: 'POST',
    data: params,
  });
}
/** 景区信息查询 (根据唯一标识) */
export async function apiScenicUniqueIdentity(uniqueIdentity: string) {
  return await request<API.RuleList>(`${scenicHost}/scenic/uniqueIdentity/info/${uniqueIdentity}`, {
    method: 'GET',
  });
}

/** 微服务管理传参 */

export async function apiMSOScenicId(params: any) {
  return await request<API.RuleList>(`${scenicHost}/servicePackage/ServicePackageList`, {
    method: 'GET',
    // query: JSON.stringify(params),

    skipErrorHandler: true,
    params: {
      ...params,
    },
  });
}

/** 开通微服务 */

export async function apiActivateService(params: any) {
  return await request<API.RuleList>(`${scenicHost}/servicePackage/activateService`, {
    method: 'POST',
    data: params,
  });
}

/** 订单管理列表 */
export async function apiOderPageList(params: any) {
  // const { distributorId, orderId } = params
  console.log('params111', params);
  return await request<API.RuleList>(`${scenicHost}/order/pageList`, {
    method: 'GET',
    //   data:(params),
    params: {
      // distributorId: params.distributorId,
      // orderId: params.orderId
      ...params,
    },
    // skipErrorHandler: true
  });
}

//订单详情列表
export function apiOderPageParticularsList(params: any) {
  return request<
    ResponseData<{
      order: Record<string, any>;
      orderStatus: Record<string, any>;
      orderRefund: any[];
      ticketInfoList: RequestData<Record<string, any>>;
      orderTravelCard: any[];
    }>
  >(`${scenicHost}/order/info`, {
    method: 'GET',
    params,
  });
}
/** 获取结算开户信息 */
export async function apiSettlementInfo(creditCode: string) {
  return await request<API.RuleList>(`${scenicHost}/scenic/settlementInfo/${creditCode}`, {
    method: 'GET',
  });
}

//票号获取二维码
export async function apiAppOrderPrintStr(params: any) {
  return await request<API.RuleList>(`${scenicHost}/appOrder/printStr`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
//公告列表
export async function apiNoticeList(params: any) {
  // console.log(params);
  return request<API.RuleList>(`${scenicHost}/notice/list`, {
    method: 'GET',
    params,
  });
}
//公告新增和修改
export async function apiAddOrUpdate(params: any) {
  return await request<API.RuleList>(`${scenicHost}/notice/info`, {
    method: 'POST',
    data: params,
  });
}
//删除公告
export async function apiDeleteNotice(id: string) {
  return request(`${scenicHost}/notice/info/${id}`, {
    method: 'DELETE',
  });
}
//禁用公告
export async function apiNoticeIsEnable(params: string) {
  return request(`${scenicHost}/notice/isEnable`, {
    method: 'PUT',
    data: params,
  });
}
//修改公告
export async function apiNoticeListId(params: any) {
  return await request<API.RuleList>(`${scenicHost}/notice/info/${params}`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

// 退订单列表
export async function apiOrderCustomList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/order/customList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
//退订单详情
export async function apiOrderCustomDetail(params: any) {
  return await request<API.RuleList>(`${scenicHost}/order/info?orderId=${params}`, {
    method: 'GET',
  });
}
//通过凭证获取区块链信息
export async function apiCertInfo(certId: any) {
  return await request(`${scenicHost}/order/certInfo/${certId}`, {
    method: 'GET',
  });
}
//区块链充值接口
export async function apiAgency(params: any) {
  return await request<API.RuleList>(`${scenicHost}/co/agency`, {
    method: 'POST',
    data: params,
  });
}
//获取区块链稳定金额接口
export async function apiBalanceCoins(params: any, val: any) {
  console.log(params, val);
  // const pars = {
  //   bdsAccount: params,
  //   bdsOrg: val
  // }
  return await request<API.RuleList>(
    `${scenicHost}/co/balanceCoins?bdsAccount=${params}&bdsOrg=${val}`,
    {
      method: 'GET',
      // params: {
      //   ...pars,
      // },
    },
  );
}
//获取区块链提现接口
export async function apiCharge(params: any) {
  return await request<API.RuleList>(`${scenicHost}/co/charge`, {
    method: 'POST',
    data: params,
  });
}

//获取景区风控管理信息
export async function apiScenicControlList(params) {
  return await request<ResponseData<any[]>>(`${scenicHost}/controlConf/coControlList/${params}`, {
    method: 'GET',
  });
}
// 查看景区复核员信息
export async function apiScenicPerson(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/coPerson/${params}`, {
    method: 'GET',
  });
}

//获取风控列表
export function getRiskControlList(params) {
  return request<ResponseData<any>>(`${scenicHost}/risk/riskPageList`, {
    params,
  });
}

// 更新或修改复景区核员
export async function apiPostScenicPerson(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/coPerson`, {
    method: 'POST',
    data: params,
  });
}
// 修改操作配置
export async function apiControlConf(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/controlConf`, {
    method: 'PUT',
    data: params,
  });
}
// 给复核员发送短信
export async function apiControlConfMessage(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/message`, {
    method: 'PUT',
    data: params,
  });
}
// 校验是否有权限
export async function apiAuthority(params) {
  return await request<API.RuleList>(`${scenicHost}/controlConf/authority`, {
    method: 'PUT',
    data: params,
  });
}

// 删除订单
export async function apiDeleteOrder(params) {
  return await request<API.RuleList>(`${scenicHost}/order/order`, {
    method: 'DELETE',
    data: params,
  });
}
//操作指南列表
export async function apiWikiList(params: any) {
  return await request<API.RuleList>(`${scenicHost}/wiki/list`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * @description: 获取景区 id 和企业 id，根据登录人赋予该景区和企业的景区系统权限
 */
// export function updateRolePermission(params: {
//   coId?: string;
//   scenicId?: string[];
//   system: 1 | 2 | 3 | 4;
//   userId?: string;
// }) {
//   return request<ResponseData<null>>(`${scenicHost}/`, {
//     method: 'PUT',
//     data:(params),
//   });
// }

interface CheckInviteToAdminData {
  companyId: string;
  phone: string;
  uniqueIdentity: string;
  userId: string;
  companyName: string;
  scenicId: string;
  scenicName: string;
  url: string;
}
/**
 * @description: 景区邀请用户成为管理员
 */
export function checkInviteToAdmin(params: { code: string }) {
  return request<ResponseData<CheckInviteToAdminData>>(
    `${scenicHost}/user/scenicManager/${params.code}`,
    {
      method: 'PUT',
      data: params,
    },
  );
}

/**
 * @description: 电商邀请用户成为管理员授权接口
 */
export function exchangeInviteToAdmin(params: {
  companyId: string;
  userId: string;
  scenicId?: string;
  type: string;
  appId: string;
}) {
  return request<ResponseData<null>>(`${scenicHost}/role/servicePermission`, {
    method: 'POST',
    data: params,
  });
}
export type RiskTargetType = 'purchase' | 'ticketing ' | 'bond';
type GroupCodeType = 'backend' | 'e-commerce';
interface RiskInfoData {
  conditionList: ConditionListItem[];
  groupCode: GroupCodeType;
  id: string;
  relationId: string;
  trigger: number;
  riskTarget: RiskTargetType;
  status: 1 | 2;
}

export interface ConditionListItem {
  attribute: string;
  /** 大于：gt  */
  condition: string;
  id: string;
  riskTarget?: RiskTargetType;
  val: number;
}

/**
 * @description: 获取风控详情
 */
export function getRiskInfo(params: {
  groupCode: GroupCodeType;
  relationId: string;
  riskTarget: RiskTargetType;
}) {
  return request<ResponseData<RiskInfoData>>(`${scenicHost}/risk/info`, {
    method: 'GET',
    params,
  });
}

interface SetRiskParams {
  conditionSetList: ConditionSetList;
  groupCode: GroupCodeType;
  relationId: string;
  riskTarget: RiskTargetType;
  riskTargetId?: string;
}

interface ConditionSetList {
  add?: Omit<ConditionListItem, 'id'>[];
  del?: string[];
  edit?: Omit<ConditionListItem, 'riskTarget'>[];
}
/**
 * @description: 配置风控
 */
export function setRiskInfo(params: SetRiskParams) {
  return request<ResponseData<null>>(`${scenicHost}/risk/info`, {
    method: 'POST',
    data: params,
  });
}
/**
 * @description: 配置风控
 */
export function deleteRiskInfo(params: any) {
  return request<ResponseData<null>>(`${scenicHost}/risk/info`, {
    method: 'DELETE',
    data: params,
  });
}
/**
 * @description: 风控禁用启用
 */
export function enableRisk(params: {
  groupCode: GroupCodeType;
  relationId: string;
  riskTargetId: string;
  status: number;
}) {
  return request<ResponseData<null>>(`${scenicHost}/risk/enable`, {
    method: 'PUT',
    data: params,
  });
}

export function sendH5Message(params: API.SendH5MessageParams) {
  return request<ResponseData<boolean>>(`${scenicHost}/order/repeat/send`, {
    method: 'POST',
    data: params,
  });
}

// 获取银行列表
export function apiBankList(params: any) {
  return request<any>(`${scenicHost}/bank/list`, {
    method: 'GET',
    params: params,
  });
}

/** 编辑企业详情  */
export function updateCoInfo(params: any, options?: Record<string, any>) {
  return request<API.RuleList>(`${scenicHost}/co/info`, {
    method: 'PUT',
    data: params,
    ...(options || {}),
  });
}

/**************************** 动态引导 ****************************/
// 查询接口
export async function getGuideListReq(params = {}) {
  const data = {
    companyId: getCompanyID(),
    systemId: 'YLT',
    ...params,
  };
  const guideRes = await request(`${scenicHost}/admissionFlow/getAdmissionFlow`, {
    method: 'GET',
    params: data,
  });
  return guideRes;
}
// 更新
export async function updateGuideListReq(params = {}) {
  const data = {
    companyId: getCompanyID(),
    systemId: 'YLT',
    ...params,
  };
  const guideRes = await request(`${scenicHost}/admissionFlow/updateAdmissionFlow`, {
    method: 'POST',
    data,
  });
  return guideRes;
}
