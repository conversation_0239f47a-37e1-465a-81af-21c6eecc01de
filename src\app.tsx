import Footer from '@/components/Footer';
import { LinkOutlined } from '@ant-design/icons';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { Link, history } from '@umijs/max';
import RightContent from './components/RightContent';
import { errorConfig } from './requestErrorConfig';

import CustomErrorBoundary from '@/pages/CustomErrorBoundary';
import { Button, Modal, message, notification } from 'antd';
import { debounce } from 'lodash';
import defaultSettings from '../config/defaultSettings';
import { login, upgradeVersion } from './common/utils/app';
import { CodeType } from './common/utils/code';
import { saveCompanyInfo } from './common/utils/storage';
import GuideDrawer from './components/GuideDrawer';
import Result from './pages/Result';
import {
  confirmPermission,
  getMenuPermissions,
  getPermissionListByUserId,
  getUserSocial,
  postPermissionAuthorize,
  roleBasePermission,
} from './services/api/cas';
import { apiCoInfoList, apiMerchantList } from './services/api/distribution';
import type { API, ResponseData, User } from './services/typings';
import { getEnv } from '@/common/utils/getEnv';

const isDev = process.env.NODE_ENV === 'development';

const routes = ['/UserApplies', '/invite'];
//无需权限的路由
const isIncludedRoute = routes.some((i) => window.location.hash.includes(i));

//检查是否有设置密码，没有则提示跳转到设置
const checkPasswordSet = (code: number, r: ResponseData['data']) => {
  if (code === CodeType.SUCCUSS) {
    if (!r.password) {
      const key = `open${Date.now()}`;
      const btn = (
        <>
          <Button size="small" onClick={() => notification.destroy(key)}>
            不用了
          </Button>
          &emsp;
          <Button
            type="primary"
            size="small"
            onClick={() => {
              notification.destroy(key);
              open(`${getEnv().CAS_HOST}/#/accountsettings`);
            }}
          >
            好的
          </Button>
        </>
      );
      notification.open({
        message: null,
        description: (
          <>
            <br />
            你未设置密码，是否现在去设置？
            <br />
          </>
        ),
        duration: null, //duration	默认 4.5 秒后自动关闭，配置为 null 则不自动关闭	number	4.5
        key,
        btn,
        onClose: close, //当通知关闭时触发
      });
    }
  } else {
    console.warn('取个人信息失败');
  }
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  userInfo?: User.UserInfo;
  firmList?: []; //企业列表
  scenicList?: []; //景区列表
  permissionList: API.Permission[];
  isInitial: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
  fetchScenicInfo?: (userId: string) => Promise<any>;
  fetchPermissionList?: (userId: string, scenicId: string) => Promise<any>;
  currentCompany: any;
  guideUpdateFlag?: any;
}> {
  let isInitial = true;
  // 用来标记引导的更新状态
  const guideUpdateFlag = null;

  const handleError = debounce((error: any, type: string) => {
    console.error(error);
    history.push(`/result?type=${type}`);
    isInitial = false;
  });
  console.log(window.location);
  // 先登录
  if (!isIncludedRoute) {
    await login();
  }

  // 获取个人信息
  const fetchUserInfo = async () => {
    try {
      const { data, code } = await getUserSocial();

      localStorage.setItem('userInfo', JSON.stringify(data));
      checkPasswordSet(code, data);
      return data || {};
    } catch (error) {
      handleError(error, 'network');
      return {};
    }
  };

  //取权限信息
  const fetchPermissionList = async (userId: string, coId: string) => {
    try {
      const { APPID } = getEnv();
      const { data = [] } = await getMenuPermissions({
        appId: APPID || '',
        groups: `e-commerce/${coId}`,
        users: userId,
        type: currentCompany.type == 1 ? '031' : '032',
        companyId: coId,
      });

      return data;
    } catch (error) {
      handleError(error, 'network');
      return [];
    }
  };

  //获取当前账号下的企业列表（包含个人机构）
  const fetchCompanyList = async () => {
    try {
      const scenicData = await apiMerchantList();
      return scenicData.data;
    } catch (e) {
      handleError(e, 'network');
      return [];
    }
  };
  //获取分销商信息 (根据 userId)
  const fetchScenicInfo = async (userId: string) => {
    const scenicData = await apiCoInfoList({ userId });
    const firmList: any = [];
    const scenicList: any = [];
    try {
      scenicData?.data?.map((item: any) => {
        firmList.push({
          coId: item.coId,
          coName: item.coName,
          settlementId: item.settlementId,
          type: item.type,
          settlementStatus: item.settlementStatus,
        });
        item.scenic.map((e) => {
          scenicList.push({
            ...e,
            coId: item.coId,
          });
        });
      });
    } catch (error) {
      handleError(error, 'network');
    }
    return { firmList, scenicList };
  };

  //设置默认企业
  const companyList = await fetchCompanyList();
  // 获取禁启用状态
  const getDisableOrEnableStatue = async () => {
    const params = {
      permissionCode: [],
      appId: getEnv().APPID,
    };
    params.permissionCode = companyList.map((item: any) => {
      return {
        code: item.id,
        group: 'e-commerce/user_status',
      };
    });
    if (params.permissionCode.length > 0) {
      //不判断接口会报错
      const { data } = await confirmPermission(params);
      //有权限的公司 id 数组
      const hasPermissionIdList = data.map((e: any) => e.code);
      const comList = companyList.filter((item: any) =>
        hasPermissionIdList.some((e: any) => e === item.id || item.type == 2),
      );
      return comList.map((e: any) => e.id);
    } else {
      return [];
    }
  };

  //无有权限的企业，跳转到没权限的页面
  // if (companyList.length === 0 && !isOutRoute()) {
  //   isInitial = false;
  //   history.replace('/result?type=noPermissions');
  // }

  let currentCompanyId = localStorage.getItem('currentCompanyId');
  // 获取禁启用状态
  let statusList = {};
  try {
    statusList = await getDisableOrEnableStatue();
  } catch (error) {
    return new Promise(() => {});
  }

  let currentCompany = companyList.find((item: any) => item.id == currentCompanyId);
  if (currentCompanyId && currentCompany) {
    currentCompany = {
      coId: currentCompany?.id || 0,
      coName: currentCompany?.name || '',
      settlementId: currentCompany?.settlementId || 0,
      type: currentCompany?.type || 0,
    };
  } else {
    currentCompany = {
      coId: companyList?.[0]?.id || undefined,
      coName: companyList?.[0]?.name || undefined,
      settlementId: companyList?.[0]?.settlementId || 0,
      type: companyList?.[0]?.type || 0,
    };
    currentCompanyId = companyList?.[0]?.id || '';
    localStorage.setItem('currentCompanyId', currentCompanyId);
  }
  saveCompanyInfo(currentCompany);
  // 设置企业下拉列表
  const companyDownList = companyList.map((item: any) => ({
    value: item.id,
    label: item.name,
  }));

  let firmList: any;
  let scenicList: any;
  let permissionList: API.Permission[] = [];
  const userInfo: User.UserInfo = await fetchUserInfo();

  if (Object.keys(userInfo).length > 0) {
    const scenicInfo = await fetchScenicInfo(userInfo?.userId);
    firmList = scenicInfo.firmList;
    scenicList = scenicInfo.scenicList;

    if (companyList.length > 0 && currentCompany?.coId) {
      //获取当前公司的权限
      // 只有当前用户在当前企业是启用状态下才去获取当前企业权限
      console.log('currentCompanyId', currentCompanyId);

      permissionList = statusList.includes(currentCompanyId)
        ? await fetchPermissionList(userInfo.userId, currentCompany.coId)
        : [];

      //2024 年 8 月 22 日 首页快捷跳转交易所金融中心需要企业是否认证状态
      const settlementStatus =
        firmList.filter((item: any) => item.coId == currentCompanyId)[0].settlementStatus ?? 0;
      currentCompany.settlementStatus = settlementStatus; //当前企业认证状态 1 为已认证
    } else {
      //当前无可用公司
      const { data: defaultPermission } = await getPermissionListByUserId([], ['e-commerce']);
      const isNewUser = defaultPermission.every((e) => e.code !== 'UserInit');
      if (isNewUser && location.hash.indexOf('/UserApplies') === -1) {
        // 新用户且不是邀请页
        Modal.info({
          title: <h3>欢迎使用易旅通电商管理系统</h3>,
          okText: '开始使用',
          content: (
            <div>
              <p>
                您可以注册成为企业分销商，管理分销团队、设置分销商品、管理分销订单、查看数据报表等
              </p>
              <p>您也可以成为代理商，快速开店、管理商品、管理订单、查询佣金报表等</p>
              <span>快来体验吧！</span>
            </div>
          ),
          onOk() {
            message.loading('正在初始化');
            roleBasePermission({ type: 2 }).then(() => {
              location.reload();
            });
          },
        });
      } else {
        // 不是新用户
        permissionList = defaultPermission.map((p: API.Permission) => {
          const { user, ...list } = p;
          return list;
        });
      }
    }

    // 统计 cas 登录人数
    try {
      postPermissionAuthorize({
        permissionCode: [
          {
            group: 'cas',
            code: 'UserInit',
            action: '',
          },
        ],
        appId: getEnv().APPID,
      });
    } catch (error) {}
  }

  upgradeVersion();

  return {
    scenicList,
    userInfo,
    settings: defaultSettings,
    firmList,
    permissionList,
    fetchScenicInfo,
    fetchUserInfo,
    fetchPermissionList,
    companyList,
    isInitial,
    currentCompany,
    companyDownList,
    guideUpdateFlag,
  };
}

// ProLayout 支持的 api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    ...initialState?.settings,
    rightContentRender: () => <RightContent />,
    // avatarProps: {
    //   src: initialState?.currentUser?.avatar,
    //   title: <AvatarName />,
    //   render: (_, avatarChildren) => {
    //     return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
    //   },
    // },
    waterMarkProps: {
      content: initialState?.currentUser?.name,
    },
    footerRender: () => <Footer />,
    onPageChange: () => {
      // // 如果没有登录，重定向到 login
      // if (!initialState?.currentUser && location.pathname !== loginPath) {
      //   history.push(loginPath);
      // }
    },

    bgLayoutImgList: [
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ],
    links: isDev
      ? [
          <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
            <LinkOutlined />
            <span>OpenAPI 文档</span>
          </Link>,
        ]
      : [],
    menuHeaderRender: undefined,
    // 自定义 403 页面
    unAccessible: <Result initType={'noPermissions'} />,
    ErrorBoundary: false,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <CustomErrorBoundary>
          <PageContainer
            ghost
            header={{
              title: false,
            }}
          >
            {children}
            {/* {isDev && (
              <SettingDrawer
                disableUrlParams
                enableDarkTheme
                settings={initialState?.settings}
                onSettingChange={(settings) => {
                  setInitialState((preInitialState) => ({
                    ...preInitialState,
                    settings,
                  }));
                }}
              />
            )} */}

            {/* 引导 */}
            <GuideDrawer />
          </PageContainer>
        </CustomErrorBoundary>
      );
    },
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  withCredentials: false,
  ...errorConfig,
};
