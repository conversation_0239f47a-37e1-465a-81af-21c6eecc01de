/**
 * 装修店铺
 * lin
 * 2022.4.25
 */
import EditPop from '@/common/components/EditPop';
import {
  addOrUpdateStoreStyle,
  apiShopBannerAdd,
  apiShopBannerDel,
  apiShopBannerEdit,
  apiShopBannerList,
  apiShopContactAdd,
  apiShopContactDel,
  apiShopContactEdit,
  apiShopContactList,
  apiShopLabelAdd,
  apiShopLabelDel,
  apiShopLabelGroupAdd,
  apiShopLabelGroupDel,
  apiShopLabelList,
  getStoreStyle,
} from '@/services/api/store';
import { CloseOutlined, EllipsisOutlined, PlusOutlined, TagOutlined } from '@ant-design/icons';
import { Button, Card, Image, Popconfirm, Space, Table, Tabs, Tag, message } from 'antd';
import { useContext, useEffect, useState } from 'react';
// import FitTag from './FitTag';
import ImageUpload from '@/common/components/ImageUpload';
import { GuideStepStatus } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getHashParams, removeStateFromUrl } from '@/common/utils/tool';
import { useGuide } from '@/hooks/useGuide';
import ProForm from '@ant-design/pro-form';
import type { ColumnType } from 'antd/lib/table';
import { TabKeyContext } from '../..';
import ShopFit from '../ShopFit';
import ShopNav from '../ShopNav';
import ShopStyle from '../ShopStyles';
import './index.less';
import { getEnv } from '@/common/utils/getEnv';

const { IMG_HOST } = getEnv();
const logList = [
  {
    title: '联系渠道',
    dataIndex: 'type',
  },
  {
    title: '联系方式',
    dataIndex: 'contractWay',
  },
];

export default ({
  // visible,
  // setVisible,
  storeId,
}: {
  // visible: boolean;
  // setVisible: Function;
  storeId: string;
}) => {
  // init
  useEffect(() => {
    initBannerData();
    initContactData();
    initTagData();
    initShopConfig();
  }, [storeId]);

  const defaultShopConfig = {
    shopNav: [
      {
        name: '首页',
        link: 'home',
        icon: '1_1',
        icon_active: '1_1_',
      },
      {
        name: '导览',
        link: 'tour',
        icon: '1_2',
        icon_active: '1_2_',
      },
      {
        name: '订单',
        link: 'order',
        icon: '1_3',
        icon_active: '1_3_',
      },
      {
        name: '我的',
        link: 'my',
        icon: '1_4',
        icon_active: '1_4_',
      },
    ],
    shopStyle: {
      color: '#349FFF',
    },
  };
  const [shopConfig, setShopConfig] = useState({});
  const initShopConfig = () => {
    getStoreStyle({ storeId })
      .then((res) => {
        setShopConfig({
          ...defaultShopConfig,
          ...JSON.parse(res.data?.pageContent || '{}'),
        });
      })
      .catch(() => {});
  };
  const updateShopConfig = async (v: any) => {
    const value = { ...shopConfig, ...v };
    await addOrUpdateStoreStyle({
      storeId,
      pageContent: JSON.stringify(value),
    });
    message.success('已保存');
    setShopConfig(value);
    return true;
  };

  const { updateGuideInfo } = useGuide();
  const queryParams = getHashParams();
  // tab
  const [tabIndex, setTabIndex] = useState<any>(queryParams?.tabKey2 || '0');
  // banner
  const [bannerEditVisible, setBannerEditVisible] = useState<boolean>(false);
  const [bannerData, setBannerData] = useState<any>([]);
  const [bannerDataSource, setBannerDataSource] = useState<any>({ id: '', isEnable: 0 });
  const bannerEditColumns = [
    {
      columns: [
        {
          title: 'Banner 名称',
          dataIndex: 'name',
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: 'Banner 图片',
          dataIndex: 'pic',
          tooltip: (
            <>
              <span>最多可上传 1 张图片，每张最</span>
              <div>大为 100 M，支持 jpg、png 格式</div>
            </>
          ),
          formItemProps: { rules: [{ required: true }] },
          renderFormItem: () => (
            <ProForm.Item name="pic">
              <ImageUpload defaultValue={bannerDataSource?.pic || ''} maxCount={1} />
            </ProForm.Item>
          ),
        },
        {
          title: '链接地址',
          dataIndex: 'link',
        },
        {
          title: '排序',
          dataIndex: 'sort',
          valueType: 'digit',
        },
        {
          title: '状态',
          dataIndex: 'status',
          valueType: 'radio',
          initialValue: 1,
          formItemProps: { rules: [{ required: true }] },
          fieldProps: {
            options: [
              {
                value: 0,
                label: '禁用',
              },
              {
                value: 1,
                label: '启用',
              },
            ],
          },
        },
      ],
    },
  ];

  const initBannerData = () => {
    apiShopBannerList({ storeId }).then((res: any) => {
      setBannerData(res.data);
    });
  };
  // const DragHandle = SortableHandle(() => <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />);

  const [current, setCurrent] = useState<any>();

  // 拼接文件对象
  const toFileObj = (pic: any) => {
    return pic
      ? pic.split(',').map((item: any, index: any) => ({
          uid: (index + 1) * -1,
          name: 'image.png',
          status: 'done',
          response: [
            {
              path: item,
            },
          ],
          url: IMG_HOST + item,
        }))
      : [];
  };
  const bannerColumns: ColumnType<any>[] = [
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      align: 'center',
    },
    {
      title: 'Banner 名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: 'Banner 图片',
      dataIndex: 'pic',
      key: 'pic',
      align: 'center',
      render: (dom: string) => <Image height={50} src={IMG_HOST + dom} />,
    },
    {
      title: '链接地址',
      dataIndex: 'link',
      key: 'link',
      align: 'center',
      render: (text: string) => {
        const url = text.includes('http') ? text : `http://${text}`;
        return (
          <a href={url} target="_blank" rel="noreferrer">
            访问链接
          </a>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (dom: any) => <Tag color={['red', 'blue'][dom]}>{['禁用', '启用'][dom]}</Tag>,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (_: any, entity: any) => (
        <Space size="middle">
          <a
            onClick={() => {
              setBannerDataSource(entity);
              setBannerEditVisible(true);
              setCurrent(entity);
            }}
          >
            编辑
          </a>
          <Popconfirm
            placement="top"
            onConfirm={async () => {
              try {
                await apiShopBannerDel(entity.id);
                message.success('删除成功');
                initBannerData();
              } catch (error) {}
            }}
            okText="是"
            cancelText="否"
            title={'确定删除该 Banner？'}
          >
            <a>删除</a>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  // contact
  const [contactEditVisible, setContactEditVisible] = useState<boolean>(false);
  const [contactDataSource, setContactDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [contactData, setContactData] = useState<any>([]);
  const contactEditColumns = [
    {
      columns: [
        {
          title: '联系渠道',
          dataIndex: 'type',
          formItemProps: { rules: [{ max: 20 }] },
        },
        {
          title: '展示图标',
          dataIndex: 'label',
          tooltip: (
            <>
              <span>最多可上传 1 张图片，每张最</span>
              <div>大为 100 M，支持 jpg、png 格式</div>
            </>
          ),
          renderFormItem: () => (
            <ProForm.Item name="label">
              <ImageUpload defaultValue={contactDataSource?.label || ''} maxCount={1} />
            </ProForm.Item>
          ),
          formItemProps: { rules: [{ required: true }] },
        },
        {
          title: '联系方式',
          dataIndex: 'contractWay',
          formItemProps: { rules: [{ required: true, max: 32 }] },
        },
        {
          title: '附件',
          dataIndex: 'attachment',
          tooltip: (
            <>
              <span>最多可上传 1 张图片，每张最</span>
              <div>大为 100M，支持 jpg、png 格式</div>
            </>
          ),
          renderFormItem: () => (
            <ProForm.Item name="attachment">
              <ImageUpload defaultValue={contactDataSource?.attachment || ''} maxCount={1} />
            </ProForm.Item>
          ),
        },
      ],
    },
  ];

  const initContactData = () => {
    apiShopContactList({ id: storeId }).then((res: any) => {
      setContactData(res.data);
    });
  };
  const contactColumns: ColumnType<any>[] = [
    {
      title: '编号',
      dataIndex: 'number',
      key: 'number',
      align: 'center',
    },
    {
      title: '联系渠道',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
    },
    {
      title: '展示图标',
      dataIndex: 'label',
      key: 'label',
      align: 'center',
      render: (dom: any) => <Image src={IMG_HOST + dom} width={50} />,
    },
    {
      title: '联系方式',
      dataIndex: 'contractWay',
      key: 'contractWay',
      align: 'center',
    },
    {
      title: '附件',
      dataIndex: 'attachment',
      key: 'attachment',
      align: 'center',
      render: (dom: any) => (dom ? <Image src={IMG_HOST + dom} width={50} /> : '-'),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (_: any, entity: any) => (
        <Space size="middle">
          <a
            onClick={() => {
              setContactDataSource(entity);
              setContactEditVisible(true);
            }}
          >
            编辑
          </a>
          <Popconfirm
            placement="top"
            onConfirm={async () => {
              try {
                await apiShopContactDel(entity.id);
                addOperationLogRequest({
                  action: 'del',
                  module: tabKey,
                  content: `删除联系方式【${entity.contractWay}】`,
                });
                message.success('删除成功');
                initContactData();
              } catch (error) {}
            }}
            okText="是"
            cancelText="否"
            title={'确定删除该联系方式？'}
          >
            <a>删除</a>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  // tag
  const [tagData, setTagData] = useState<any>([]);
  const initTagData = () => {
    apiShopLabelList({ storeId }).then((res: any) => {
      setTagData(res.data);
    });
  };

  const tabKey = useContext(TabKeyContext);

  const [tagGroupEditVisible, setTagGroupEditVisible] = useState<boolean>(false);
  const tagGroupEditColumns = [
    {
      columns: [
        {
          title: '分组名称',
          dataIndex: 'name',
          formItemProps: { rules: [{ required: true, max: 30 }] },
        },
      ],
    },
  ];
  const [tagEditVisible, setTagEditVisible] = useState<boolean>(false);
  const tagEditColumns = [
    {
      columns: [
        {
          title: '标签名称',
          dataIndex: 'name',
          formItemProps: { rules: [{ required: true }] },
        },
      ],
    },
  ];
  const [tagGroupDataSource, setTagGroupDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [tagDataSource, setTagDataSource] = useState<any>({ id: '', isEnable: 0 });
  const [groupItem, setGroupItem] = useState<{ id: string; name: string; list: any[] }>();

  useEffect(() => {
    if (queryParams?.operate === 'addConcat') {
      setContactDataSource({ id: '' });
      setContactEditVisible(true);
    }
  }, [queryParams?.operate]);

  return (
    <>
      <Tabs
        tabBarStyle={{ padding: '0 24px', margin: '0', background: '#fff' }}
        defaultActiveKey="0"
        tabBarExtraContent={
          (tabIndex == 1 || tabIndex == 2) && (
            <Button
              type="primary"
              onClick={() => {
                // eval(`set${['Banner','Contact','Tag'][tabIndex]}EditVisible`)(true)
                if (tabIndex == 0) {
                  setBannerDataSource({ id: '' });

                  setBannerEditVisible(true);
                } else if (tabIndex == 1) {
                  setContactDataSource({ id: '' });

                  setContactEditVisible(true);
                } else if (tabIndex == 2) {
                  setTagGroupEditVisible(true);
                }
              }}
            >
              <PlusOutlined />
              新建{[' Banner', '联系方式', '标签分组'][tabIndex]}
            </Button>
          )
        }
        activeKey={tabIndex}
        onChange={(e) => {
          setTabIndex(e);
        }}
      >
        <Tabs.TabPane tab="页面装修" key="0">
          <ShopFit storeId={storeId} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="全店风格" key="4">
          <Card style={{ border: 'none' }}>
            <ShopStyle shopConfig={shopConfig} updateShopConfig={updateShopConfig} />
          </Card>
        </Tabs.TabPane>
        <Tabs.TabPane tab="店铺导航" key="3">
          <Card style={{ border: 'none' }}>
            <ShopNav
              shopConfig={shopConfig}
              updateShopConfig={updateShopConfig}
              storeId={storeId}
            />
          </Card>
        </Tabs.TabPane>

        <Tabs.TabPane tab="客服联系方式" key="1">
          <Card style={{ border: 'none' }}>
            <Table
              rowKey="id"
              columns={contactColumns}
              dataSource={contactData}
              bordered
              size="middle"
              style={{ marginTop: '8px' }}
            />
          </Card>
        </Tabs.TabPane>
        <Tabs.TabPane tab="商品标签" key="2">
          <Card style={{ border: 'none' }}>
            {tagData?.map((item: any) => (
              <div style={{ margin: '8px 0 16px' }} key={item.id}>
                <Space style={{ marginBottom: '8px' }}>
                  <TagOutlined />
                  {item.name}
                  <Popconfirm
                    placement="top"
                    onConfirm={async () => {
                      try {
                        await apiShopLabelGroupDel(item.id);
                        addOperationLogRequest({
                          action: 'del',
                          module: tabKey,
                          content: `删除标签分组【${item.name}】`,
                        });
                        message.success('删除成功');
                        initTagData();
                      } catch (error) {}
                    }}
                    okText="是"
                    cancelText="否"
                    title={'确定删除该分组？'}
                  >
                    <EllipsisOutlined />
                  </Popconfirm>
                </Space>
                <div>
                  {item.list.map((item: any) => (
                    <Tag
                      key={item.id}
                      style={{ marginBottom: '8px' }}
                      closable
                      closeIcon={
                        <Popconfirm
                          placement="top"
                          onConfirm={async () => {
                            try {
                              await apiShopLabelDel(item.id);
                              addOperationLogRequest({
                                action: 'del',
                                module: tabKey,
                                content: `删除标签【${item.name}】`,
                              });
                              message.success('删除成功');
                              initTagData();
                            } catch (error) {}
                          }}
                          okText="是"
                          cancelText="否"
                          title={'确定删除该标签？'}
                        >
                          <CloseOutlined />
                        </Popconfirm>
                      }
                      onClose={(e) => {
                        e.preventDefault();
                      }}
                    >
                      {item.name}
                    </Tag>
                  ))}
                  <Tag
                    style={{
                      borderStyle: 'dashed',
                      cursor: 'pointer',
                      background: '#fff',
                      marginBottom: '8px',
                    }}
                    onClick={() => {
                      setGroupItem(item);
                      setTagEditVisible(true);
                    }}
                  >
                    <PlusOutlined /> 添加标签
                  </Tag>
                </div>
              </div>
            ))}
          </Card>
        </Tabs.TabPane>
      </Tabs>
      <EditPop
        title="Banner"
        visible={bannerEditVisible}
        setVisible={setBannerEditVisible}
        columns={bannerEditColumns}
        dataSource={bannerDataSource}
        onFinish={async (val: any) => {
          const hide = message.loading(bannerDataSource.id ? '正在修改' : '正在添加');
          try {
            val.pic = val.pic;
            val.storeId = storeId;
            if (bannerDataSource.id) {
              val.id = bannerDataSource.id;
              await apiShopBannerEdit({ ...val, pic: val.pic || current.pic });
              message.success('修改成功');
            } else {
              await apiShopBannerAdd({ ...val });
              message.success('添加成功');
            }
            setBannerEditVisible(false);
            initBannerData();
          } catch (error) {}
          hide();
        }}
      />
      <EditPop
        title="标签"
        visible={tagEditVisible}
        setVisible={setTagEditVisible}
        columns={tagEditColumns}
        dataSource={tagDataSource}
        onFinish={async (val: any) => {
          if ((groupItem?.list as any[]).find((i) => i.name === val.name)) {
            message.warning('标签重复');
            return;
          }
          const hide = message.loading('正在添加');
          try {
            val.labelGroupId = groupItem?.id;
            await apiShopLabelAdd({ ...val });
            addOperationLogRequest({
              action: 'add',
              module: tabKey,
              content: `新增标签【${val.name}】`,
            });
            message.success('添加成功');
            setTagEditVisible(false);
            initTagData();
          } catch (error) {}
          hide();
        }}
      />
      <EditPop
        title="标签分组"
        visible={tagGroupEditVisible}
        setVisible={setTagGroupEditVisible}
        columns={tagGroupEditColumns}
        dataSource={tagGroupDataSource}
        onFinish={async (val: any) => {
          if (((tagData || []) as any[]).find((i) => i.name === val.name)) {
            message.warn('分组重复');
            return;
          }
          const hide = message.loading('正在添加');
          try {
            val.storeId = storeId;
            await apiShopLabelGroupAdd({ ...val });
            addOperationLogRequest({
              action: 'add',
              module: tabKey,
              content: `新增标签分组【${val.name}】`,
            });
            message.success('添加成功');
            setTagGroupEditVisible(false);
            initTagData();
          } catch (error) {}
          hide();
        }}
      />
      <EditPop
        title="联系方式"
        visible={contactEditVisible}
        setVisible={(v) => {
          if (!v && queryParams?.operate) {
            history.pushState(null, null, removeStateFromUrl('operate'));
          }
          setContactEditVisible(v);
        }}
        columns={contactEditColumns}
        dataSource={contactDataSource}
        onFinish={async (val: any) => {
          if (!val.label) {
            message.info('展示图标不能为空');
            return;
          }
          const hide = message.loading(contactDataSource.id ? '正在修改' : '正在添加');
          try {
            val.storeId = storeId;
            if (contactDataSource.id) {
              val.id = contactDataSource.id;
              await apiShopContactEdit({ ...val });
              addOperationLogRequest({
                action: 'edit',
                module: tabKey,
                changeConfig: {
                  beforeData: contactDataSource,
                  afterData: val,
                  list: logList,
                },
                content: `编辑联系方式【${val.contractWay}】`,
              });
              message.success('修改成功');
            } else {
              await apiShopContactAdd({ ...val });
              // 更新引导
              updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_7 });
              addOperationLogRequest({
                action: 'add',
                module: tabKey,
                content: `新增联系方式【${val.contractWay}】`,
              });
              message.success('添加成功');
            }
            setContactEditVisible(false);
            initContactData();
            history.pushState(null, null, removeStateFromUrl('operate'));
          } catch (error) {}
          hide();
        }}
      />

      {/* 图片弹窗 */}
      {/* <Modal
        visible={imgInfo.previewVisible}
        title={imgInfo.previewTitle}
        footer={null}
        onCancel={() =>
          handleImgInfo({
            previewImage: '',
            previewVisible: false,
            previewTitle: '预览',
          })
        }
      >
        <img alt="example" style={{ width: '100%' }} src={imgInfo.previewImage} />
      </Modal> */}
    </>
  );
};
