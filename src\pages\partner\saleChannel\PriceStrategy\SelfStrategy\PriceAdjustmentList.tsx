import React from 'react';

interface PriceAdjustment {
  date: string;
  before: string;
  after: string;
}

interface PriceAdjustmentListProps {
  // 可选的数据属性，方便后续扩展为真实数据
  adjustments?: PriceAdjustment[];
}

/**
 * 价格调整记录列表组件
 */
const PriceAdjustmentList: React.FC<PriceAdjustmentListProps> = ({ adjustments }) => {
  // 默认数据
  const defaultAdjustments = [
    { date: '2023-09-15', before: '120.00', after: '132.00' },
    { date: '2023-08-20', before: '110.00', after: '120.00' },
    { date: '2023-07-01', before: '140.00', after: '110.00' },
    { date: '2023-06-15', before: '120.00', after: '140.00' },
    { date: '2023-05-01', before: '100.00', after: '120.00' }
  ];

  // 使用传入的数据或默认数据
  const data = adjustments || defaultAdjustments;

  return (
    <div style={{ marginTop: '30px' }}>
      <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 500 }}>价格调整记录</h3>
      <div style={{ background: '#f9f9f9', padding: '16px', borderRadius: '4px' }}>
        <div style={{ display: 'flex', marginBottom: '12px' }}>
          <div style={{ width: '33%', color: '#666' }}>日期</div>
          <div style={{ width: '33%', color: '#666' }}>调整前价格</div>
          <div style={{ width: '33%', color: '#666' }}>调整后价格</div>
        </div>
        {data.map((record) => (
          <div 
            key={record.date} 
            style={{ 
              display: 'flex', 
              padding: '8px 0', 
              borderTop: record === data[0] ? 'none' : '1px solid #eee'
            }}
          >
            <div style={{ width: '33%' }}>{record.date}</div>
            <div style={{ width: '33%' }}>{record.before}元</div>
            <div style={{ width: '33%', color: parseFloat(record.after) > parseFloat(record.before) ? '#f5222d' : '#52c41a' }}>
              {record.after}元
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PriceAdjustmentList; 