/**
 * 店铺导览
 */
import { request } from '@umijs/max';
import { scenicHost } from '.';
const url = (v: string) => scenicHost + v;

/** 删除 */
export function deleteFeedback(params: any) {
  return request(url('/feedback/delete/' + params.id), {
    method: 'DELETE',
  });
}
/** 查看 */
export function infoFeedback(params: any) {
  return request(url('/feedback/info'), { params });
}
/** 分页 */
export async function pageFeedback(params: any) {
  const { data, code } = await request(url('/feedback/page'), { params });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total,
  };
}
