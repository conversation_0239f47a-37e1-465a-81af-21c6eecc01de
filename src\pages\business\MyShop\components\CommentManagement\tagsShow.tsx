import { addLabel, delLabel, findByLabelList, updSequence } from '@/services/api/comment';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Empty, Input, Modal, Popconfirm } from 'antd';
import { useEffect, useState } from 'react';
import styles from './tagShow.less';

// 将 AddTagModal 提取为独立组件
const AddTagModal = ({ visible, onOk, onCancel, value, onChange, loading }) => {
  return (
    <Modal title="新增标签" open={visible} onOk={onOk} onCancel={onCancel} loading={loading}>
      <span>标签名称：</span>
      <Input
        placeholder="请输入"
        maxLength={10}
        showCount={true}
        value={value}
        onChange={onChange}
      />
    </Modal>
  );
};

const TagsShow = ({ storeId }) => {
  const [addTagShow, setAddTagShow] = useState<boolean>(false);
  const [iptValue, setIptValue] = useState<string>('');
  const [tags, setTags] = useState([]);

  // 拖拽相关状态
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);

  // 处理拖拽开始
  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
    setIsDragging(true);
  };

  // 处理拖拽经过
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    if (index !== draggingIndex) {
      setDragOverIndex(index);
    }
  };

  // 处理拖拽结束
  const handleDragEnd = () => {
    setIsDragging(false);

    // 如果有拖拽经过的位置，交换标签
    if (draggingIndex !== null && dragOverIndex !== null) {
      const newTags = [...tags];
      [newTags[draggingIndex], newTags[dragOverIndex]] = [
        newTags[dragOverIndex],
        newTags[draggingIndex],
      ];
      setTags(newTags);
      updSequence({
        data: newTags.map((item, index) => {
          return {
            id: item.id,
            sequence: index,
          };
        }),
      });
    }

    // 重置拖拽状态
    setDraggingIndex(null);
    setDragOverIndex(null);
  };

  const getTagsList = async () => {
    const res = await findByLabelList(storeId);
    if (res.code === 20000) {
      setTags(res.data);
    }
  };

  useEffect(() => {
    if (storeId) {
      getTagsList();
    }
  }, [storeId]);

  // 添加CSS类来处理拖拽过程中的样式
  useEffect(() => {
    const dragItem = document.querySelector(`[data-index="${draggingIndex}"]`);
    const dropTarget = document.querySelector(`[data-index="${dragOverIndex}"]`);

    // 添加拖拽中的样式
    if (dragItem) {
      dragItem.classList.add(styles.dragging);
    }

    // 添加拖拽目标的样式
    if (dropTarget && dragOverIndex !== null) {
      dropTarget.classList.add(styles.dragOver);
    }

    // 清理
    return () => {
      const dragItems = document.querySelectorAll(`.${styles.dragging}`);
      const dropTargets = document.querySelectorAll(`.${styles.dragOver}`);

      dragItems.forEach((item) => item.classList.remove(styles.dragging));
      dropTargets.forEach((target) => target.classList.remove(styles.dragOver));
    };
  }, [draggingIndex, dragOverIndex]);

  // 删除标签的函数
  const handleDeleteTag = async (id: string) => {
    // const newTags = tags.filter((_, i) => i !== index);
    // setTags(newTags);
    const res = await delLabel(id);
    if (res.code === 20000) {
      getTagsList();
    }
  };
  return (
    <>
      <div className={styles.tagBox}>
        <div className={styles.sideBox}>
          {tags.length > 0 ? (
            tags?.map((item, index) => {
              // 计算当前标签是否应该有特殊的拖拽样式
              const isDraggingItem = index === draggingIndex;
              const isDragOverItem = index === dragOverIndex && dragOverIndex !== null;

              return (
                <div
                  key={item.id}
                  className={`${styles.tagItem} ${isDraggingItem ? styles.dragging : ''} ${
                    isDragOverItem ? styles.dragOver : ''
                  }`}
                  draggable={true}
                  data-index={index}
                  onDragStart={() => handleDragStart(index)}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDragEnd={handleDragEnd}
                >
                  <span>{item?.label}</span>
                  <Popconfirm
                    title="提示"
                    description="确定删除该标签？"
                    onConfirm={() => handleDeleteTag(item.id)}
                    okText="是"
                    cancelText="否"
                  >
                    <CloseOutlined style={{ fontSize: '12px' }} />
                  </Popconfirm>
                </div>
              );
            })
          ) : (
            <Empty style={{ margin: '50px auto' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </div>
        <Button
          type="dashed"
          onClick={() => {
            setAddTagShow(true);
          }}
        >
          {' '}
          + 新增标签
        </Button>
      </div>
      <AddTagModal
        visible={addTagShow}
        loading={loading}
        onOk={async () => {
          setLoading(true);
          const res = await addLabel({
            label: iptValue,
            storeId,
          });
          setLoading(false);
          if (res.code === 20000) {
            getTagsList();
          }
          // setTags((prev) => [...prev, iptValue]);
          setIptValue('');
          setAddTagShow(false);
        }}
        onCancel={() => {
          setAddTagShow(false);
        }}
        value={iptValue}
        onChange={(e: any) => {
          setIptValue(e.target.value);
        }}
      />
    </>
  );
};

export default TagsShow;
