/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-25 10:12:54
 * @LastEditTime: 2023-09-21 17:34:20
 * @LastEditors: zhangfengfei
 */
import { tableConfig } from '@/common/utils/config';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import useModal from '@/hooks/useModal';
import {
  addSettlement,
  checkOverdueTicket,
  getAgentList,
  getSellerBillList,
} from '@/services/api/billManage';
import type { API } from '@/services/api/typings';
import { PlusOutlined } from '@ant-design/icons';
import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Modal, Space, Tag, message } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import { useRef, useState } from 'react';
import { Access, useAccess, useModel } from '@umijs/max';
import SettlementDetailModal from './SettlementDetailModal';

type CommissionBillProps = Record<string, never>;

/**
 * @description 财务管理->账单管理->结算登记
 * */
const CommissionBill: FC<CommissionBillProps> = () => {
  const { initialState } = useModel('@@initialState');
  const { coId = '' } = initialState?.currentCompany || {};
  const actionRef = useRef<ActionType>();

  const detailModalState = useModal();

  const [tableListItem, setTableListItem] = useState<API.AgentBillItem>();
  const [tipVisible, setTipVisible] = useState<boolean>(false);
  const [agentId, setAgentId] = useState<string>('');
  const [overdueTicketInfo, setOverdueTicketInfo] = useState<{
    commission: string;
    ticketNumber: string;
  }>({
    commission: '0',
    ticketNumber: '0',
  });

  const [addVisible, setAddVisible] = useState(false);
  const access = useAccess();

  // const tableListReq = async (params: API.AgentBillListParams) => {
  //   const { data } = await getAgentBillList(params);
  //   return {
  //     data: data.records,
  //     total: data.total,
  //   };
  // };
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const addSettlementReq = async (agentId: string, overdueType = '2') => {
    const { data } = await addSettlement({
      agentId,
      sellerId: coId,
      overdueType,
    });
    if (data === '0') {
      message.warning('数据为空');
    } else {
      message.success('结算成功');
      actionRef.current?.reload();
    }
    addOperationLogRequest({
      action: 'add',
      content: `生成【${agentId}】佣金账单`,
    });

    setAddVisible(false);
  };
  const tableListReq = async (params: API.SellerBillListParams) => {
    const { data } = await getSellerBillList(params);
    return {
      data: data.records,
      total: data.total,
    };
  };

  const columns: ProColumns<API.AgentBillItem>[] = [
    {
      title: '单号',
      dataIndex: 'billId',
      fieldProps: (form) => {
        return {
          onChange: (e: any) => {
            form.setFieldsValue({
              billId: String(e.target.value.match(/\d+/g) || '').slice(0, 19),
            });
          },
        };
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
      renderText: (text) => {
        if (!text) {
          return '-';
        }
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startDate: value[0],
            endDate: value[1],
          };
        },
      },
    },
    {
      title: '代理商名称',
      dataIndex: 'name',
      valueType: 'select',
      request: async () => {
        const { data } = await getAgentList({
          distributorId: coId,
        });
        return data.map((item) => ({
          label: item.coName,
          value: item.coId,
        }));
      },
      search: {
        transform: (value) => ({
          agentId: value,
        }),
      },
    },

    {
      title: '应结算金额（元）',
      dataIndex: 'amount',
      search: false,
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },

    // {
    //   title: '结算开始时间',
    //   dataIndex: 'startDay',
    //   search: false,
    // },
    {
      title: '结算结束时间',
      dataIndex: 'endDay',
      search: false,
    },

    {
      title: '结算状态',
      dataIndex: 'settlementStatus',
      valueType: 'select',
      valueEnum: {
        0: '待结算',
        1: '已确认',
      },
      renderText: (dom) => {
        const colorMap = [
          {
            text: '待结算',
            color: 'orange',
          },
          {
            text: '已确认',
            color: 'blue',
          },
        ];
        return <Tag color={colorMap[dom].color}>{colorMap[dom].text}</Tag>;
      },
    },
    {
      width: 120,
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      renderText: (_, record) => (
        <Space
          onClick={() => {
            setTableListItem(record);
          }}
        >
          <Access accessible={access.canBalanceAssignment_detail}>
            <a
              onClick={() => {
                detailModalState.setVisible(true);
              }}
            >
              结算明细
            </a>
          </Access>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.SellerBillItem, API.SellerBillListParams>
        {...tableConfig}
        actionRef={actionRef}
        columns={columns}
        request={tableListReq}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => setAddVisible(true)}
          >
            生成账单
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 10,
        }}
        params={{
          sellerId: coId,
        }}
      />
      {/* 结算明细 modal */}
      <SettlementDetailModal currentItem={tableListItem} modalState={detailModalState} />

      <ModalForm
        title="生成账单"
        visible={addVisible}
        modalProps={{
          onCancel: () => setAddVisible(false),
        }}
        onFinish={async (values) => {
          setAgentId(values.agentId);
          const { data: res } = await checkOverdueTicket({
            agentId: values.agentId,
            sellerId: coId,
          });
          if (res.commission === '0' && res.ticketNumber === '0') {
            addSettlementReq(values.agentId);
            // setTipVisible(true)
          } else {
            const ticketInfo = {
              commission: res.commission,
              ticketNumber: res.ticketNumber,
            };
            setOverdueTicketInfo(ticketInfo);
            setTipVisible(true);
          }
          return true;
        }}
      >
        <ProFormSelect
          name="agentId"
          label="代理商名称"
          rules={[{ required: true }]}
          formItemProps={{
            style: { width: 328 },
          }}
          request={async () => {
            const { data } = await getAgentList({
              distributorId: coId,
            });
            return data.map((item) => ({
              label: item.coName,
              value: item.coId,
            }));
          }}
        />
      </ModalForm>
      <Modal
        title={'提示'}
        open={tipVisible}
        width={modelWidth.md}
        destroyOnClose={true}
        onCancel={async () => {
          setTipVisible(false);
          addSettlementReq(agentId);
        }}
        onOk={async () => {
          setTipVisible(false);
          addSettlementReq(agentId, '1');
        }}
      >
        <div>
          该下级售出的票中，有 {overdueTicketInfo.ticketNumber} 张票已过期，待结算佣金{' '}
          {overdueTicketInfo.commission} 元。确认一起结算？
        </div>
      </Modal>
    </>
  );
};

export default CommissionBill;
