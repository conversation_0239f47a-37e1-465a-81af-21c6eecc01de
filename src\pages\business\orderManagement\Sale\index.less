.dom {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

//Management 页面
.order_InfoData {
  height: 100%;

  .ant-card-body {
    padding: 0 !important;
  }

  .ant-pro-table-search {
    padding: 0;
  }
}

//标题样式
.titleFont {
  font-weight: 700;
}

.bgc_url {
  position: absolute;
  top: 28%;
  left: 45%;
  width: 50px;
  height: 50px;
  background: url('/public/logo/logo.png') no-repeat;
  background-color: skyblue;
  background-size: 50px auto;
  border-radius: 50%;
}

//

// TemporalInterval
.temporalInterval {
  .ant-picker {
    // padding-right: 30px;
    // padding-left: 30px;
  }
}
