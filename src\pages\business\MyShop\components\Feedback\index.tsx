import Delete from '@/common/components/Delete';
import ImageUpload from '@/common/components/ImageUpload';
import ProModal from '@/common/components/ProModal';
import useModal from '@/common/components/ProModal/useProModal';
import { tableConfig } from '@/common/utils/config';
import { FeedbackType } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { deleteFeedback, infoFeedback, pageFeedback } from '@/services/api/feedback';
import type { ActionType, ProColumnType, ProFormColumnsType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { useContext, useRef, useState } from 'react';
import { TabKeyContext } from '../..';

/*
 * @Author: bankewei
 * @Date: 2023 年 12 月 11 日
 */
export default ({ store: { value: storeId } }: any) => {
  const modalState = useModal();
  const actionRef = useRef<ActionType>();
  const [id, setId] = useState<string | null>();

  const tabKey = useContext(TabKeyContext);

  const [currentRow, setCurrentRow] = useState<Record<string, any>>();
  const tableColumns: ProColumnType[] = [
    {
      title: '序号',
      valueType: 'index',
    },
    {
      title: '账号',
      dataIndex: 'accountNumber',
    },
    {
      title: '昵称',
      dataIndex: 'nickName',
    },
    {
      title: '反馈类型',
      dataIndex: 'type',
      valueEnum: FeedbackType,
    },
    {
      title: '联系方式',
      dataIndex: 'phone',
    },
    {
      title: '反馈信息',
      dataIndex: 'content',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '反馈时间',
      valueType: 'dateTime',
      dataIndex: 'createTime',
      hideInSearch: true,
    },
    {
      title: '反馈时间',
      key: 'time',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startCreateTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            endCreateTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_: any, entity: any) => [
        <a
          onClick={() => {
            setId(entity.id);
            setCurrentRow(entity);
            modalState.setType('info');
          }}
          key="k1"
        >
          查看
        </a>,
        <Delete
          key="k3"
          access={true}
          status={entity.enableState == 2}
          params={{ id: entity.id }}
          request={async (params) => {
            const data = await deleteFeedback(params);
            addOperationLogRequest({
              action: 'del',
              module: tabKey,
              content: `删除用户【${entity.accountNumber}】反馈`,
            });
            return data;
          }}
          actionRef={actionRef}
        />,
      ],
    },
  ];
  const modalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '账号',
          dataIndex: 'accountNumber',
        },
        {
          title: '昵称',
          dataIndex: 'nickName',
        },
        {
          title: '反馈类型',
          dataIndex: 'type',
          valueEnum: FeedbackType,
        },
        {
          title: '联系方式',
          dataIndex: 'phone',
        },
        {
          title: '反馈信息',
          dataIndex: 'content',
        },
        {
          title: '	反馈图片',
          dataIndex: 'imgUrl',
          renderText: (text: any) => <ImageUpload defaultValue={text} readonly />,
        },
      ],
    },
  ];

  return (
    <>
      <ProTable
        {...tableConfig}
        actionRef={actionRef}
        columns={tableColumns}
        params={{ storeId }}
        request={pageFeedback}
      />
      <ProModal
        {...modalState}
        title="反馈"
        columns={modalColumns}
        params={{ id, storeId }}
        // infoRequest={infoFeedback}
        infoRequest={async (params) => {
          const data = await infoFeedback(params);
          addOperationLogRequest({
            action: 'info',
            module: tabKey,
            content: `查看用户【${currentRow?.accountNumber}】反馈`,
          });
          return data;
        }}
        addRequest={null}
        editRequest={null}
      />
    </>
  );
};
