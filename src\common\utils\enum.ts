/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-18 14:14:02
 * @LastEditTime: 2023-10-30 16:24:05
 * @LastEditors: zhang<PERSON><PERSON>i
 */

// 企业类型
export const companyType = {
  0: '景区运营',
  1: '分销商',
  // 2: '环球数科',
  3: '政府',
};

// 票种类型
export const ticketTypeEnum = {
  0: '成人票',
  1: '儿童票',
  2: '老人票',
  3: '保险票',
  4: '全价票',
  5: '半价票',
  7: '团体票',
};

// 产品类型（基础票）
export const baseProductTypeEnum = {
  0: '门票',
  1: '船票',
  2: '缆车',
};

// 产品类型
export const productTypeEnum = {
  ...baseProductTypeEnum,
  20: '权益卡',
};

// 使用方式
export const useTypeEnum = {
  0: '按次数',
  1: '按时长',
};

// 销售渠道
export const saleChannelEnum = {
  0: 'H5', // 分销商
  1: '微商城',
  2: 'PC 商城',
  3: '扫码购',
  4: '自助机',
  5: '售票窗口',
  6: 'App',
  7: 'OTA',
};

// 星期
export const weekEnum = {
  0: '星期日',
  1: '星期一',
  2: '星期二',
  3: '星期三',
  4: '星期四',
  5: '星期五',
  6: '星期六',
};

// 订单状态
export const orderTypeEnum = {
  10: '创建订单',
  11: '已取消',
  12: '订单超时失效',
  13: '用户取消订单',
  14: '系统取消订单',
  20: '待付款',
  21: '支付成功',
  22: '支付失败',
  30: '已完成',
  31: '出库成功',
  32: '出库失败',
  33: '出票失败',
  34: '出票成功',
  50: '退款中',
  51: '退款成功',
  52: '退款失败',
  53: '退款取消',
  54: '部分退款',
  55: '退款',
};

// 订单状态（回显）
// export const orderType = {
//   10: '创建订单',
//   11: '已取消',
//   12: '已取消',
//   13: '已取消',
//   14: '已取消',
//   20: '待付款',
//   21: '支付成功',
//   22: '支付失败',
//   30: '已完成',
//   31: '出库成功',
//   32: '出库失败',
//   33: '出票失败',
//   34: '出票成功',
//   50: '退款中',
//   51: '已退款',
//   52: '退款失败',
//   53: '退款取消',
//   54: '部分退款',
//   55: '退款',
// };

// 订单状态（搜索）
export const orderTypeSearch = {
  32: '出库失败',
  33: '出票失败',
  11: '已取消',
  20: '待付款',
  22: '支付失败',
  30: '已完成',
  50: '退款中',
  51: '退款成功',
  // 55: '退款',
};

// 结算方式
export const payTypeEnum = {
  0: '扫码支付',
  1: '银联卡',
  2: '现金支付',
  3: '微信支付',
  4: '支付宝',
  5: '区块链支付',
  6: '微信公众号支付',
  7: '银联在线',
  8: '银联商务 POS 通',
  9: '银联商务公众号支付',
  10: '银联商务网关支付',
  11: '银联商务 WAP',
  12: '支付宝服务商',
  13: '微信服务商',
  14: '银联商务 B2B',
  15: '银联商务小程序支付',
  16: '区块链分销系统支付',
  17: '银联商务 C 扫 B',
  18: '信用支付',
  19: '其他支付',
  20: '授信支付',
  21: '0 元购',
  22: '银联商务 wap 支付',
  23: '窗口售票 pos 支付',
  24: '预授信支付',
  25: '交易所支付',
};

// 检票终端类型
export const equipmentTypeEnum = {
  0: '手持机',
  1: '闸机',
  2: 'App',
  3: '后台操作',
};

// 票状态
export const ticketStatusEnum = {
  0: '未核销',
  1: '部分核销',
  2: '已过期',
  3: '已完成',
  4: '已退票',
};

// 结算开户状态
export const accountStatusEnum = {
  0: '待开户',
  1: '审核通过',
  2: '审核拒绝',
  3: '审核中',
};

// 售票类型
export const sourceTypeEnum = {
  0: '单票',
  1: '套票',
};

// 订单票种类型
export const orderTicketTypeEnum = {
  JQMP: '景区门票',
  JQTC: '权益卡',
};

export const scenicTypeEnum = {
  0: '景区',
};
// 服务商等级
export const serviceGrade = {
  0: '未评',
  1: '国家 A 级景区',
  2: '国家 2A 级景区',
  3: '国家 3A 级景区',
  4: '国家 4A 级景区',
  5: '国家 5A 级景区',
};

// 实名方式
export const realEnum = {
  0: '非实名制',
  1: '实名制',
};

// 1 是 0 否
export const whetherEnum = {
  0: '否',
  1: '是',
};

export const SettlementStatusEnum = {
  FINISHED: '已确认',
  PENDING: '待结算',
};

// 业务类型
export const BusinessTypeEnum = {
  TRADE: '交易',
  REFUND: '退款',
  CREDITADD: '授信增额',
  CREDITREDUCE: '授信减额',
  SETTLECYCLE: '结算确认循环授信',
};

// 源单类型
export const OriginTypeEnum = {
  TRADE: '交易单',
  REFUND: '退款单',
};

export const EntryTypeEnum = {
  INCOME: '收',
  OUTCOME: '支',
};
export enum AgentTypeEnum {
  企业 = 1,
  个人,
}

export enum ApplyStatusEnum {
  待审核 = 1,
  审核通过,
  已拒绝,
  /**审批失败指加入分组失败） */
  审核失败,
}

export enum SettlementStatusType {
  待开户,
  审批通过,
  审批拒绝,
  审批中,
}

//折扣率枚举
export const discountRate = {
  0: '特殊折扣率',
  1: '特殊折扣率',
  2: '特殊折扣率',
  3: '特殊折扣率',
  4: '网订折扣率',
  5: '特殊折扣率',
  7: '团体折扣率',
};

export enum UnitTypeEnum {
  单票 = 1,
  权益卡,
}

export enum StoreGoodsTypeEnum {
  单票 = 1,
  组合票,
}

// 票种类型
export enum TicketTypeEnum {
  成人票,
  儿童票,
  老人票,
  保险票,
  全价票,
  半价票,
  团体票 = 7,
}

export const RiskTypeMap = {
  purchase: '进货条件控制',
  ticketing: '购票 / 售票条件控制',
  bond: '保证金（暂未实现）',
  order: '删除订单',
};

export const RiskMenuMap = {
  purchase: '采购订单',
  ticketing: 'H5 / 窗口售票',
  bond: '采购订单 / H5、窗口',
  order: '订单管理',
};

// 禁启用状态
export const DEStateEnum = {
  0: {
    text: '禁用',
    color: 'red',
  },
  1: {
    text: '启用',
    color: 'blue',
  },
};

// 导览类型
export const tourType = {
  1: '内部',
  2: '外部',
};

// 文章类型
export const ArticleType = {
  1: '攻略',
  2: '资讯',
  3: '活动',
  4: '游记',
};

// 导览类型
export const ArticleSourceType = {
  1: '内部',
  2: '外链',
};

// 导览类型
export const ArticleEnableType = {
  1: '禁用',
  2: '启用',
};

// 反馈类型
export const FeedbackType = {
  1: '景区服务',
  2: '景区门票',
  3: '产品体验',
  4: '产品功能',
  5: '其它问题',
};

//批量权益卡状态
export const SACardApproveStateEnum = {
  1: '待审批',
  2: '审批通过',
  3: '审批驳回',
  4: '审核过期',
};

//批量权益卡详情内单条处理状态
export const SACardBatchStateEnum = {
  1: '未处理',
  2: '处理失败',
  3: '处理成功',
};

// 系统页面
export const systemPageEnum = {
  home: '主页',
  tour: '全部导览',
  order: '全部订单',
  my: '个人中心',
  ticketList: '门票预订',
  travelCardList: '权益卡',
};

// 交易类型
export const tradeTypeEnum = {
  0: '售票',
  1: '退票',
};

// 还款状态
export const repaymentStatusEnum = {
  CHECK: '待还款',
  CHECK_SUCCESS: '还款成功',
  CHECK_FAIL: '还款失败',
};

// 交易上链
export const chainStatusEnum = {
  0: '未上链',
  1: '已上链',
};

// 库存变化记录
export const inventoryRecord = {
  1: '创建库存',
  2: '销售进货',
  3: '销售退货',
  4: 'H5 售票',
  5: '窗口售票',
  6: '退票库存',
  7: '采购进货',
  8: '冻结库存',
  9: '解冻库存',
  10: '采购退货',
  11: '过期库存',
  12: '自助机',
  13:'挂单成交',
  14: '卖出挂单',
  15:'卖出撤单',
};

// 库存类型
export const stockType = {
  0: '普通库存',
  1: '区块链库存',
};

// 模态框的宽度
export enum ModelWidth {
  sm = 400,
  md = 736,
  lg = 1000,
  xl = 1200,
}

// 数字资产
export const isChainEnum = {
  0: '否',
  1: '是',
};

export const DocumentTypeSelect = {
  ID_CARD: '身份证',
  // { label: '护照', value: 'PASSPORT' },
  // { label: '港澳居民来往内地通行证', value: 'HK_MACAU_PERMIT' },
  // { label: '台湾居民来往大陆通行证', value: 'TAIWAN_PERMIT' },
};

//有效期类型，可用值:FIXED,LONG_TERM
export const ExpirationDateTypeEnum = [
  { label: '长期', value: 'LONG_TERM' },
  { label: '固定', value: 'FIXED' },
];

/**
 * 引导步骤
 */
export const GuideStepStatus = {
  // tabIndex 0
  step0_1: '完成企业认证',
  step0_2: '价格策略设置',
  step0_3: '创建店铺',
  step0_4: '上架商品',
  step0_5: '开启窗口售票权限',
  step0_6: '开启自助售票权限',
  step0_7: '完成第一个测试订单',
  // tabIndex 1
  step1_1: '未邀请经销商/代理商',
  step1_2: '销售授权',
  step1_3: '受邀成为经销商/代理商',
  step1_4: '采购进货',
  step1_5: '创建导览',
  step1_6: '创建文章',
  step1_7: '未填写客服电话',
  step1_8: '装修店铺导航',
  step1_9: '全店风格配置',
  step1_10: '装修店铺主页',
};

export const DownloadStatus = {
  0: {
    text: '生成中',
    color: 'green',
  },
  1: {
    text: '已生成',
    color: 'blue',
  },
  2: {
    text: '生成失败',
    color: 'red',
  },
};

// 评价类型
export const commentType = {
  1: '好评',
  2: '中评',
  3: '差评',
};

// 评价内容
export const commentContentType = {
  1: '有内容',
  2: '有图片',
  3: '有视频',
};

// 商家回复
export const shopAnswerType = {
  1: '未回复',
  2: '已回复',
};


