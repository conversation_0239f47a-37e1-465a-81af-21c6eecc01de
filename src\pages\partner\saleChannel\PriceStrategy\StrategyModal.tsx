import { GuideStepStatus } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { useGuide } from '@/hooks/useGuide';
import type { ModalState } from '@/hooks/useModal';
import { getAgentGroupList } from '@/services/api/auditManage';
import {
  addPriceStrategy,
  getPriceStrategyByAgent,
  updatePriceStrategy,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import { QuestionCircleOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormDependency, ProFormDigit, ProFormSelect } from '@ant-design/pro-form';
import type { ActionType } from '@ant-design/pro-table';
import { useLocation } from '@umijs/max';
import { Modal, Space, Tooltip, message } from 'antd';
import { ceil, isNil } from 'lodash';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';
import type { StrategyListItemType } from './StrategyListModal';

const rules = [
  {
    required: true,
  },
];

interface StrategyModalProps {
  goodsItem: API.PriceStrategyListItem;
  currentItem?: StrategyListItemType;
  modalState: ModalState;
  showInfo?: boolean;
  actionRef?: React.MutableRefObject<ActionType | undefined>;
  onFinish?: () => void;
  showGroup?: boolean;
  // 能区分直销
  showRate?: boolean;
}

interface FormType {
  groupId: string[];
  isCompose: '0' | '1';
  discount: number;
  salePrice: number;
  composeDiscount: number;
  composePrice: number;
  commissionRate: number;
  saleRenewPrice: number;
  renewDiscount: number;
}

const StrategyModal: FC<StrategyModalProps> = ({
  modalState: { setVisible, visible, type },
  currentItem: priceItem,
  goodsItem,
  actionRef,
  onFinish,
  showRate = true,
  showInfo = true,
  showGroup = true,
}) => {
  // 是否权益卡
  const isRightsCard = goodsItem?.unitType == 2 || goodsItem?.ruleType == 2;
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const [isFirst, setIsFirst] = useState<boolean>(false);

  // 是否权益票
  const isRightsTicket = goodsItem?.ruleType == 1;

  // 商品折扣价
  const price = (goodsItem?.overallDiscount * goodsItem?.marketPrice) / 100;

  const formRef = useRef<ProFormInstance<FormType>>();
  const { updateGuideInfo } = useGuide();

  const [beginPrice, endPrice] = [
    ceil((goodsItem?.beginDiscount * price) / 100, 2),
    ceil((goodsItem?.endDiscount * price) / 100, 2),
  ];

  // 分组下拉列表
  const getAgentGroupListReq = useRequest(getAgentGroupList, {
    manual: true,
    initialData: [],
    formatResult(res) {
      return res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    },
  });

  //为佣金比例为零时不显示；需求见：https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/143
  const [showRateZero, setShowRateZero] = useState(true);
  // const price = 100;
  const { initialState } = useModel('@@initialState');
  const { coId = '', coName = '' } = initialState?.currentCompany || {};

  const logList = [
    {
      title: '单买价格（元）',
      dataIndex: 'salePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '组合销售',
      dataIndex: 'isCompose',
      valueEnum: {
        0: '否',
        1: '是',
      },
    },
    {
      title: '组合价格（元）',
      dataIndex: 'composePrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '单买续费价格（元）',
      dataIndex: 'saleRenewPrice',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '佣金比例（%）',
      dataIndex: 'commissionRate',
    },
    {
      title: '适用分组',
      dataIndex: 'groupId',
      renderText: (text) => {
        return (text || [])
          .map((i) => getAgentGroupListReq.data?.find((item) => item.value === i)?.label)
          .join('，');
      },
    },
  ];

  const getPriceStrategyByAgentReq = useRequest(getPriceStrategyByAgent, {
    manual: true,
    onSuccess(data, params) {
      const { isCompose, discount, composeDiscount, composePrice, commissionRate, salePrice } =
        data.price.price;
      setShowRateZero(commissionRate != 0);

      formRef.current?.setFieldsValue({
        ...data.price,
        isCompose: isCompose === true ? '1' : '0',
        discount: discount * 100,
        salePrice: salePrice,
        composeDiscount: composeDiscount * 100,
        composePrice: composePrice,
        commissionRate: commissionRate,
      });
    },
  });

  const addPriceStrategyReq = useRequest(addPriceStrategy, {
    manual: true,
    onSuccess(data, params) {
      if (showRate) {
        message.success('新增成功');
      } else {
        message.success('修改成功');
      }
      actionRef?.current?.reload();
      setVisible(false);
      if (onFinish) {
        onFinish();
      }
    },
  });
  const updatePriceStrategyReq = useRequest(updatePriceStrategy, {
    manual: true,
    onSuccess(data, params) {
      message.success('修改成功');
      actionRef?.current?.reload();
      setVisible(false);
      if (onFinish) {
        onFinish();
      }
    },
  });

  const infoList = [
    {
      title: '市场标准价',
      render: goodsItem?.marketPrice,
      // type === 'add' && searchParams.get('marketPrice') && searchParams.get('tab') === 'agent'
      //   ? searchParams.get('marketPrice')
      //   : goodsItem?.marketPrice,
    },
    {
      title: (
        <>
          商品折扣
          <Tooltip
            placement="top"
            title={
              <>
                <div>门票 - 全价票为网订折扣率</div>
                <div>门票 - 团体票为团体折扣率</div>
                <div>门票 - 其他票种为特殊折扣率</div>
                <div>权益卡为商品折扣率</div>
              </>
            }
          >
            <QuestionCircleOutlined style={{ marginLeft: 4 }} />
          </Tooltip>
        </>
      ),
      render: (
        <Space direction="vertical">
          <span> {goodsItem?.overallDiscount}%</span>
          <span> {(goodsItem?.marketPrice * goodsItem?.overallDiscount) / 100}</span>
        </Space>
      ),
    },
    {
      title: '分销折扣区间',
      render: (
        <Space direction="vertical">
          <span>
            {goodsItem?.beginDiscount}% ~ {goodsItem?.endDiscount}%
          </span>
          <span>{`${beginPrice} ~ ${endPrice}`}</span>
        </Space>
      ),
    },
    {
      title: '进货价',
      render: goodsItem?.purchasePrice?.join('/'),
    },
  ];

  // 提交
  const onSubmit = async () => {
    const values = await formRef.current?.validateFields();
    const {
      isCompose,
      discount = 0,
      composeDiscount = 0,
      group,
      salePrice,
      commissionRate,
    } = priceItem || {};
    // 操作日志数据
    const changeConfig = {
      list: logList,
      beforeData: {
        ...priceItem,
        isCompose: isCompose === true ? '1' : '0',
        groupId: group?.map((i) => i.id),
        discount: discount * 100,
        composeDiscount: composeDiscount * 100,
      },
      afterData: values,
    };
    if ((type === 'add' || !showRate) && values) {
      const { groupId = [], ...rest } = values || {};

      await addPriceStrategyReq.run(
        {
          distributorId: coId,
          groupId,
          price: {
            ...rest,
            discount: ceil(rest.discount / 100, 4),
            composeDiscount: ceil(rest.composeDiscount / 100, 4),
            isCompose: rest.isCompose === '1' ? true : false,
          },
          goodsId: goodsItem?.goodsId,
          unitId: goodsItem.unitId,
        },
        showRate ? false : true,
      );
      // 更新引导
      updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_2 });
      // 添加操作日志
      if (showRate) {
        addOperationLogRequest({
          action: 'add',
          content: `新增【${goodsItem.name}】代理价格策略`,
        });
      } else {
        addOperationLogRequest({
          action: 'edit',
          changeConfig,
          content: `编辑【${goodsItem.name}】直销价格策略`,
        });
      }
      return;
    }
    if (type === 'update' && priceItem && values) {
      const { groupId = [], ...rest } = values || {};
      const priceGroupId = priceItem.group.map((i) => i.id);
      await updatePriceStrategyReq.run({
        distributorId: coId,
        goodsId: goodsItem?.goodsId,
        addGroupId: groupId.filter((i) => !priceGroupId.includes(i)),
        delGroupId: priceGroupId.filter((i) => !groupId.includes(i)),
        groupId: groupId, // 直接传递所有适用分组 ID 列表
        price: {
          ...rest,
          discount: ceil(rest.discount / 100, 4),
          composeDiscount: ceil(rest.composeDiscount / 100, 4),
          isCompose: rest.isCompose === '1' ? true : false,
        },
        priceId: priceItem.priceId,
        unitId: goodsItem.unitId, // 商品 id
        onSalePrice: salePrice,
        onCommissionRate: commissionRate,
      });
      // 更新引导
      updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_2 });
      // 添加操作日志
      addOperationLogRequest({
        action: 'edit',
        changeConfig,
        content: `编辑【${goodsItem.name}】代理价格策略`,
      });
    }
  };
  // 添加重置  详情、编辑赋值
  useEffect(() => {
    if (visible) {
      getAgentGroupListReq.run({
        distributorId: coId,
      });
      console.log(priceItem);
      if (type === 'add') {
        formRef.current?.resetFields();
        if (searchParams.get('tab') === 'agent' && !isFirst) {
          setIsFirst(true);
          formRef.current?.setFieldsValue({
            ...priceItem,
            isCompose: '0',
            groupId: searchParams.get('groupId')?.split() || [],
            commissionRate: +searchParams.get('commissionRate'),
            salePrice: +searchParams.get('salePrice'),
            discount: ceil((+searchParams.get('salePrice') * price) / 100, 2),
            // composeDiscount: priceItem.composeDiscount * 100,
          });
        }
      } else if (type === 'update' && priceItem) {
        const { isCompose, discount, composeDiscount } = priceItem || {};
        formRef.current?.setFieldsValue({
          ...priceItem,
          isCompose: isCompose === true ? '1' : '0',
          groupId: priceItem.group?.map((i) => i.id),
          discount: discount * 100,
          composeDiscount: composeDiscount * 100,
        });
      } else if (type === 'info') {
        getPriceStrategyByAgentReq.run({
          distributorId: coId,
          unitId: goodsItem?.unitId,
          priceId: goodsItem?.priceId,
        });
      }
    }
  }, [visible, type]);

  return (
    <Modal
      width={modelWidth.md}
      title={type === 'info' ? '查看佣金策略' : '设置价格策略'}
      open={visible}
      maskClosable={false}
      onCancel={() => setVisible(false)}
      onOk={onSubmit}
      okButtonProps={{
        style:
          type === 'info'
            ? {
                display: 'none',
              }
            : undefined,
        loading: addPriceStrategyReq.loading || updatePriceStrategyReq.loading,
      }}
      okText={'保存'}
    >
      {showInfo && (
        <div
          style={{
            display: 'flex',
            width: '100%',
            justifyContent: 'space-between',
            marginBottom: 24,
            background: '#f9f9f9',
            padding: '16px 24px',
            borderRadius: '6px',
          }}
        >
          {infoList.map((item, index) => (
            <div key={index} style={{ textAlign: 'center', flex: 1 }}>
              <div style={{ color: '#666', marginBottom: 8, fontSize: '14px' }}>{item.title}</div>
              <div style={{ fontWeight: 'bold', fontSize: '15px' }}>{item.render}</div>
            </div>
          ))}
        </div>
      )}

      <ProForm
        formRef={formRef}
        layout="horizontal"
        labelAlign="right"
        labelCol={{ span: 6 }}
        submitter={false}
        preserve={false}
        onValuesChange={({
          discount,
          composeDiscount,
          renewDiscount,
          saleRenewPrice,
          salePrice,
          composePrice,
        }) => {
          // 价格数据联动
          // 单买价格
          if (!isNil(discount)) {
            formRef.current?.setFieldsValue({
              salePrice: ceil((price * discount) / 100, 2),
            });
          }
          if (!isNil(salePrice)) {
            formRef.current?.setFieldsValue({
              discount: ceil((salePrice * 100) / price, 2),
            });
          }
          // 单买续费价格
          if (!isNil(renewDiscount)) {
            formRef.current?.setFieldsValue({
              saleRenewPrice: ceil((price * renewDiscount) / 100, 2),
            });
          }
          if (!isNil(saleRenewPrice)) {
            formRef.current?.setFieldsValue({
              renewDiscount: ceil((saleRenewPrice * 100) / price, 2),
            });
          }
          // 组合价格
          if (!isNil(composeDiscount)) {
            formRef.current?.setFieldsValue({
              composePrice: ceil((price * composeDiscount) / 100, 2),
            });
          }
          if (!isNil(composePrice)) {
            formRef.current?.setFieldsValue({
              composeDiscount: ceil((composePrice * 100) / price, 2),
            });
          }
        }}
      >
        <ProForm.Item label="单买价格" required style={{ margin: 0 }}>
          <Space>
            <ProFormDigit
              width="xs"
              name="discount"
              fieldProps={{
                precision: 2,
              }}
              rules={rules}
              min={goodsItem?.beginDiscount}
              max={goodsItem?.endDiscount}
              addonAfter="%"
              disabled={type === 'info'}
            />
            <ProFormDigit
              width="xs"
              addonAfter="元"
              name="salePrice"
              fieldProps={{
                precision: 2,
              }}
              min={beginPrice || 0}
              max={endPrice || undefined}
            />
          </Space>
        </ProForm.Item>

        {isRightsCard && (
          <ProForm.Item label="单买续费价格" required style={{ margin: 0 }}>
            <Space>
              <ProFormDigit
                width="xs"
                name="renewDiscount"
                fieldProps={{
                  precision: 2,
                }}
                rules={rules}
                min={goodsItem?.beginDiscount}
                max={goodsItem?.endDiscount}
                addonAfter="%"
                disabled={type === 'info'}
              />
              <ProFormDigit
                width="xs"
                addonAfter="元"
                name="saleRenewPrice"
                fieldProps={{
                  precision: 2,
                }}
                min={beginPrice || 0}
                max={endPrice || undefined}
              />
            </Space>
          </ProForm.Item>
        )}

        {!isRightsCard && !isRightsTicket && (
          <>
            <ProFormSelect
              label="组合销售"
              name="isCompose"
              width="sm"
              disabled={type === 'info'}
              valueEnum={{
                0: '否',
                1: '是',
              }}
              rules={rules}
            />
            <ProFormDependency name={['isCompose']}>
              {({ isCompose }) => {
                return isCompose === '1' ? (
                  <>
                    <ProForm.Item label={'组合价格'} required style={{ margin: 0 }}>
                      <Space>
                        <ProFormDigit
                          disabled={type === 'info'}
                          addonAfter="%"
                          width="xs"
                          name="composeDiscount"
                          fieldProps={{
                            precision: 2,
                          }}
                          rules={rules}
                          min={goodsItem?.beginDiscount}
                          max={goodsItem?.endDiscount}
                        />
                        <ProFormDigit
                          width="xs"
                          addonAfter="元"
                          name="composePrice"
                          fieldProps={{
                            precision: 2,
                          }}
                          min={beginPrice || 0}
                          max={endPrice || undefined}
                        />
                      </Space>
                    </ProForm.Item>
                  </>
                ) : null;
              }}
            </ProFormDependency>
          </>
        )}

        {showRate && showRateZero && (
          <ProFormDigit
            label="佣金比例"
            disabled={type === 'info'}
            addonAfter="%"
            width="xs"
            wrapperCol={{ span: 6 }}
            name="commissionRate"
            fieldProps={{
              precision: 0,
            }}
            rules={rules}
            min={0}
            max={100}
          />
        )}
        {showGroup && (
          <ProFormSelect
            label="适用分组"
            mode="multiple"
            name="groupId"
            disabled={type === 'info'}
            width="sm"
            fieldProps={{
              loading: getAgentGroupListReq.loading,
              options: getAgentGroupListReq.data,
            }}
            // request={async () => {
            //   const { data } = await getAgentGroupList({
            //     distributorId: coId,
            //   });
            //   return (data ?? []).map((item) => ({
            //     label: item.name,
            //     value: item.id,
            //   }));
            // }}
            rules={rules}
          />
        )}
      </ProForm>
    </Modal>
  );
};

export default StrategyModal;
