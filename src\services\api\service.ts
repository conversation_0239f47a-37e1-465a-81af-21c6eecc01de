import { fetchEventSource } from '@microsoft/fetch-event-source';
import { request } from '@umijs/max';
import { helpHost } from '.';
import { getEnv } from '@/common/utils/getEnv';

interface ResponseData {
  code: number;
  data: any;
  message: string;
}

// 新增知识库
export function apiSaveKnowledge(params: any) {
  return request(`${helpHost}/knowledge/add`, {
    method: 'POST',
    data: params,
  });
}
// 删除知识库
export function apiDelKnowledge(params: any) {
  return request(`${helpHost}/knowledge/delete`, {
    method: 'DELETE',
    params,
  });
}
// 启用的知识库列表
export function apiGetKnowledgeIds(params: { businessId: any }) {
  return request(`${helpHost}/knowledge/ids`, {
    method: 'GET',
    params,
  });
}
// 获取知识库详情
export function apiGetKnowledgeInfo(params: any) {
  return request(`${helpHost}/knowledge/info`, {
    method: 'GET',
    params,
  });
}
// 获取知识库分页列表
export async function apiGetKnowledgePage(params: any) {
  const { data, code } = await request(`${helpHost}/knowledge/page`, {
    method: 'POST',
    data: params,
  });

  return {
    data: data.records,
    success: code == 20000,
    total: data.total,
  };
}
// 知识库更新
export function apiUpdateKnowledge(data: any) {
  return request(`${helpHost}/knowledge/update`, {
    method: 'PUT',
    data,
  });
}
// 修改知识库状态
export function apiEnableKnowledge(data: any) {
  return request(`${helpHost}/knowledge/update/enable`, {
    method: 'PUT',
    data,
  });
}
// 添加知识库文件
export function apiAddKnowledgeFile(params: { knowledgeBaseId: string; fileId: string }) {
  return request(`${helpHost}/knowledge/file/callback`, {
    method: 'GET',
    params,
  });
}

// 上传文件
export async function uploadKnowledgeFile(params: any) {
  const formData = new FormData();
  // 将所有参数添加到 formData
  Object.keys(params).forEach((key) => {
    formData.append(key, params[key]);
  });
  return request<ResponseData>(
    getEnv().AI_HOST + '/algorithm/intelligent-customer-service/knowledge_base/upload',
    {
      method: 'POST',
      requestType: 'form',
      data: formData,
    },
  );
}
// 获取文件列表
export async function apiGetKnowledgeFileList(ids: any) {
  const res = await fetch(
    getEnv().AI_HOST + '/algorithm/intelligent-customer-service/knowledge_base/batch_query',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(ids),
    },
  );
  const data = await res.json();
  return data;
}
// 删除知识库文件
export function apiDelKnowledgeFile(params) {
  return request(`${helpHost}/knowledge/delete/file`, {
    method: 'DELETE',
    data: params,
  });
}

// 智能客服对话
export async function apiChatCompletion(
  params: {
    chatid: string;
    docids: string[];
    question: string;
  },
  callbacks = {},
  abortController = new AbortController(),
) {
  const { onMessage, onError, onClose } = callbacks as {
    onMessage?: (data: any) => void;
    onError?: (error: any) => void;
    onClose?: () => void;
  };

  try {
    await fetchEventSource(
      getEnv().AI_HOST + '/algorithm/intelligent-customer-service/chat/completion',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-extra-info': sessionStorage.getItem('x-extra-info') || '',
        },
        body: JSON.stringify({
          chat_id: params.chatid,
          doc_ids: params.docids,
          question: params.question,
        }),
        signal: abortController.signal,
        async onopen(response) {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
        },
        onmessage(event) {
          if (onMessage && typeof onMessage === 'function') {
            try {
              const data = event.data ? JSON.parse(event.data) : '\n';
              onMessage(data);
            } catch (e) {
              onMessage(event.data);
            }
          }
        },
        onclose() {
          if (onClose && typeof onClose === 'function') {
            onClose();
          }
        },
        onerror(err) {
          if (onError && typeof onError === 'function') {
            onError(err);
          }
          throw err; // 重新抛出错误以便结束连接
        },
      },
    );
  } catch (error) {
    console.error('流式消息处理失败：', error);
    if (onError && typeof onError === 'function') {
      onError(error);
    }
    throw error;
  }
}

// 保存智能客服聊天记录
export async function apiSaveChatRecord(params: any) {
  const { data, code } = await request(`${helpHost}/chat/add`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}
// 查询智能客服聊天记录
export async function apiGetChatRecords(params: any) {
  const { data, code } = await request(`${helpHost}/chat/page`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
  return { data, code };
}

// 点开客服弹框保存个人信息接口
export function sendMyToken(params: { commerceCompanyId?: string; scenicCode?: string }) {
  return request(`${helpHost}/chat/token`, {
    method: 'GET',
    params,
  });
}
