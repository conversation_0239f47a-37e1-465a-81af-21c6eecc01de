/*
 * @Author: 李悍宇 <EMAIL>
 * @Date: 2025-05-06 10:17:18
 * @LastEditors: 李悍宇 <EMAIL>
 * @LastEditTime: 2025-06-16 18:12:39
 * @FilePath: \exchange\src\pages\business\MyShop\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import EditPop from '@/common/components/EditPop';
import FileUpload from '@/common/components/FileUpload';
import { GuideStepStatus } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { removeStateFromUrl } from '@/common/utils/tool';
import { useGuide } from '@/hooks/useGuide';
import useModal from '@/hooks/useModal';
import {
  apiDistributorShop,
  apiShopAdd,
  apiShopDetail,
  apiShopList,
  checkEnterpriseScenic,
} from '@/services/api/store';
import { EditOutlined } from '@ant-design/icons';
import { ProCard, ProFormSelect, type ProFormColumnsType } from '@ant-design/pro-components';
import { Access, useAccess, useLocation, useModel, useRequest } from '@umijs/max';
import type { TabsProps } from 'antd';
import { Button, Empty, Row, Space, Tabs, message } from 'antd';
import { createContext, useEffect, useState } from 'react';
import Goods from './Goods';
import Article from './components/Article';
import CommissionStatement from './components/CommissionStatement';
import Decoration from './components/Decoration';
import Feedback from './components/Feedback';
import OrderForm from './components/OrderForm';
import CommentManagement from './components/CommentManagement';
import OrderReturn from './components/OrderReturn';
import PreviewStore from './components/PreviewStore';
import ProductManage from './components/ProductManage';
import SaleAuthorityModal from './components/SaleAuthorityModal';
import Tour from './components/Tour';
import WindowTrading from './components/WindowTrading';
import { getEnv } from '@/common/utils/getEnv';

const { DEFAULT_LOGO, MALL_HOST } = getEnv();

export const storeTabItems: TabsProps['items'] = [
  {
    key: 'productManage',
    label: '商品管理',
  },
  {
    key: 'shopFit',
    label: '店铺装修',
  },

  {
    key: 'shopPreview',
    label: '预览店铺',
  },
  {
    key: 'orderForm',
    label: '订单管理',
  },
  {
    key: 'commentManagement',
    label: '评论管理',
  },
  {
    key: 'orderReturn',
    label: '退单管理',
  },
  {
    key: 'commissionStatement',
    label: '佣金报表',
  },
  {
    key: 'windowTrading',
    label: '窗口交易报表',
  },
  {
    key: 'tour',
    label: '导览管理',
  },
  {
    key: 'article',
    label: '文章管理',
  },
  {
    key: 'feedback',
    label: '反馈管理',
  },
];

export const TabKeyContext = createContext<string>('productManage');

export default () => {
  const access = useAccess();
  // 解析 URL 传参
  const urlObj: any = {};
  window.location.href
    .split('?')[1]
    ?.split('&')
    .map((item) => {
      urlObj[item.split('=')[0]] = item.split('=')[1];
    });
  const { updateGuideInfo } = useGuide();
  const [goodsRef, setGoodsRef] = useState();

  const [type, setType] = useState('');
  const [id, setId] = useState('');
  // 控制是否显示推荐选项
  const [showRecommend, setShowRecommend] = useState<boolean>(false);

  const { initialState } = useModel('@@initialState');
  const { coId, settlementId }: any = initialState?.currentCompany || {};
  const [key, setKey] = useState<string>(urlObj.tabKey || 'productManage');
  const [store, setStore] = useState<any>();
  const [isEmpty, setIsEmpty] = useState<boolean>(false);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const t = searchParams.get('t');
  const menuList: any = [
    {
      access: true,
      key: 'productManage',
      label: '商品管理',
      components: (
        <ProductManage setType={setType} setId={setId} store={store} setGoodsRef={setGoodsRef} />
      ),
    },
    {
      access: access.canMyShop_decorateShop,
      key: 'shopFit',
      label: '店铺装修',
      components: <Decoration storeId={store?.value} />,
    },
    {
      access: access.canMyShop_previewShop,
      key: 'shopPreview',
      label: '预览店铺',
      components: <PreviewStore store={store} />,
    },
    {
      access: access.canMyShop_orderSelect,
      key: 'orderForm',
      label: '订单管理',
      components: <OrderForm storeId={store?.value} />,
    },
    {
      access: true,
      key: 'commentManagement',
      label: '评论管理',
      components: <CommentManagement storeId={store?.value} />,
    },
    {
      access: access.canMyShop_refundSelect,
      key: 'orderReturn',
      label: '退单管理',
      components: <OrderReturn storeId={store?.value} />,
    },
    {
      access: access.canMyShop_commissionSelect,
      key: 'commissionStatement',
      label: '佣金报表',
      components: <CommissionStatement storeId={store?.value} />,
    },
    {
      access: access.canMyShop_dailyReport,
      key: 'windowTrading',
      label: '窗口交易报表',
      components: <WindowTrading store={store} />,
    },
    {
      access: true,
      key: 'tour',
      label: '导览管理',
      components: <Tour store={store} />,
    },
    {
      access: true,
      key: 'article',
      label: '文章管理',
      components: <Article store={store} />,
    },
    {
      access: true,
      key: 'feedback',
      label: '反馈管理',
      components: <Feedback store={store} />,
    },
  ].filter((item) => item.access);

  // const menuList = [];
  // [新增/编辑] 店铺
  const [dataSource, setDataSource] = useState<any>();
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const editColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '店铺名称',
          dataIndex: 'name',
          fieldProps: {
            showCount: true,
            maxLength: 10,
          },
          formItemProps: { rules: [{ required: true }] },
          width: 'xl',
          colProps: { span: 24 },
        },
        {
          title: '店铺 Icon',
          tooltip: '显示在 H5 登录注册页面体现品牌特性，建议上传尺寸为 330*90 的 PNG 图片',
          dataIndex: 'iconType',
          valueType: 'radio',
          fieldProps: {
            options: [
              { label: '默认', value: 'ICON_DEFAULT' },
              { label: '自定义', value: 'ICON_CUSTOMIZE' },
            ],
            defaultValue: 'ICON_DEFAULT',
          },
          colProps: { span: 24 },
        },
        {
          valueType: 'dependency',
          name: ['iconType'],
          columns: ({ iconType }) => {
            return [
              {
                title: '',
                dataIndex: 'fileInfo',
                hideInForm: iconType !== 'ICON_CUSTOMIZE',
                renderFormItem: (_, __, form) => {
                  // 从表单获取回显数据
                  const fileInfo = form.getFieldValue('fileInfo');
                  console.log('从表单获取回显数据——————', fileInfo);
                  return (
                    <FileUpload
                      defaultValue={fileInfo}
                      maxCount={1}
                      size={5}
                      onChange={(fileInfo) => {
                        // 将文件 ID 保存到表单字段
                        console.log('fileInfo——————', fileInfo);
                        // 保存完整的 fileInfo 对象用于回显
                        form.setFieldValue('fileInfo', fileInfo);
                      }}
                    />
                  );
                },
                colProps: { span: 24 },
              },
            ];
          },
        },
        {
          title: '店铺简介',
          dataIndex: 'introduce',
          valueType: 'textarea',
          fieldProps: {
            placeholder: '请输入',
            showCount: true,
            maxLength: 20,
          },
          width: 'xl',
          colProps: { span: 24 },
          formItemProps: { rules: [{ required: true }] },
          initialValue: '基于联盟链的全场景目的地云服务开创者',
        },
        {
          title: '勾选后优先推荐',
          dataIndex: 'recommend',
          valueType: 'switch',
          tooltip: '勾选后将在 Travel AI 上架并被优先推荐',
          colProps: { span: 24 },
          hideInForm: !showRecommend,
        },
      ],
    },
  ];
  const selectRequest = useRequest(apiShopList, {
    defaultParams: [{ id: coId, pageSize: 999 }],
    formatResult({ data }: any) {
      if (data.length && !store) {
        setStore({ label: data[0].name, value: data[0].id });
      }
      setIsEmpty(!data.length);
      return data.map((item: any) => {
        if (urlObj.storeId) {
          if (urlObj.storeId == item.id) {
            setStore({ label: item.name, value: item.id });
          }
        } else if (dataSource && store && store.value == item.id) {
          setStore({ label: item.name, value: item.id });
        }
        return {
          label: item.name,
          value: item.id,
        };
      });
    },
  });
  const selectReload = () => {
    selectRequest.run({ id: coId, pageSize: 999 });
  };
  // 员工销售权限
  const saleAuthorityModalState = useModal();
  useEffect(() => {
    if (t) {
      setKey(searchParams.get('tabKey'));
    }
  }, [t]);
  useEffect(() => {
    // 在页面加载时检查企业是否绑定景区
    if (coId) {
      checkEnterpriseScenic(coId)
        .then((res) => {
          console.log('企业绑定景区验证结果：', res);
          // 根据结果控制是否显示推荐选项
          setShowRecommend(res.data === true);
        })
        .catch((err) => {
          console.error('企业绑定景区验证失败', err);
          setShowRecommend(false);
        });
    }
  }, []);
  useEffect(() => {
    if (urlObj?.operate === 'addStore') {
      // 创建店铺
      setDataSource(null);
      setEditVisible(true);
    } else if (urlObj?.operate === 'saleAuthority' && saleAuthorityModalState) {
      saleAuthorityModalState.setVisible(true);
    }

    // 在页面加载时检查企业是否绑定景区
    if (coId) {
      checkEnterpriseScenic(coId)
        .then((res) => {
          console.log('企业绑定景区验证结果：', res);
          // 根据结果控制是否显示推荐选项
          setShowRecommend(res.data === true);
        })
        .catch((err) => {
          console.error('企业绑定景区验证失败', err);
          setShowRecommend(false);
        });
    }
  }, [urlObj, saleAuthorityModalState, coId]);

  return (
    <>
      <TabKeyContext.Provider value={key}>
        <div style={type ? { display: 'none' } : {}}>
          <Tabs
            tabBarStyle={{ margin: '0', background: '#fff' }}
            items={menuList}
            activeKey={key}
            onChange={setKey}
            destroyInactiveTabPane
            tabBarExtraContent={{
              left: (
                <div style={{ margin: '16px 24px -8px', width: 190 }}>
                  <ProFormSelect
                    showSearch
                    allowClear={false}
                    placeholder="请选择店铺"
                    options={selectRequest.data}
                    fieldProps={{
                      value: store,
                      labelInValue: true,
                      loading: selectRequest.loading,
                      onChange: (obj, option) => {
                        setStore(obj);
                        addOperationLogRequest({
                          action: 'select',
                          module: key,
                          content: `切换当前店铺为【${obj.label}】`,
                        });
                      },
                      optionItemRender: (item: any) => (
                        <Row justify="space-between" align="middle">
                          <span>{item.label}</span>
                          <Access accessible={access.canMyShop_ediitShop}>
                            <EditOutlined
                              onClick={(e) => {
                                e.stopPropagation();
                                setDataSource({
                                  id: item.value,
                                  name: item.label,
                                });
                                setEditVisible(true);
                                console.log('检查企业是否绑定景区', item.value);
                                apiShopDetail(item.value)
                                  .then((res) => {
                                    if (res && res.data) {
                                      setDataSource({
                                        id: item.value,
                                        name: item.label,
                                        iconType: res.data.iconType || 'ICON_DEFAULT',
                                        fileInfo: res.data.fileInfo ? [res.data.fileInfo] : [],
                                        introduce: res.data.introduce,
                                        recommend: res.data.recommend,
                                      });
                                    }
                                  })
                                  .catch((err) => {
                                    console.error('获取店铺详情失败', err);
                                  });
                              }}
                            />
                          </Access>
                        </Row>
                      ),
                    }}
                  />
                </div>
              ),
              right: (
                <Space style={{ margin: '0 24px' }}>
                  <Access accessible={access.canMyShop_selectAuthority}>
                    <Button
                      type="primary"
                      ghost
                      block
                      onClick={() => {
                        saleAuthorityModalState.setVisible(true);
                      }}
                    >
                      员工销售权限
                    </Button>
                  </Access>
                  <Access accessible={access.canMyShop_insertShop}>
                    <Button
                      type="primary"
                      ghost
                      block
                      onClick={() => {
                        setDataSource(null);
                        setEditVisible(true);

                        // 创建店铺时也检查企业绑定景区状态
                        if (coId) {
                          checkEnterpriseScenic(coId)
                            .then((res) => {
                              console.log('创建店铺时企业绑定景区验证结果：', res);
                              setShowRecommend(res.data === true);
                            })
                            .catch((err) => {
                              console.error('创建店铺时企业绑定景区验证失败', err);
                              setShowRecommend(false);
                            });
                        }
                      }}
                    >
                      创建店铺
                    </Button>
                  </Access>
                </Space>
              ),
            }}
          />
          <ProCard ghost={store} loading={!store && !isEmpty}>
            {isEmpty ? (
              <Empty
                // style={{
                //   position: 'absolute',
                //   top: '50%',
                //   left: '50%',
                //   transform: 'translate(-50%,-50%)',
                // }}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="请先创建店铺"
              />
            ) : (
              menuList.find((item: any) => item.key == key)?.components
            )}
          </ProCard>
          {/* [新增/编辑] 店铺 */}
          <EditPop
            width={modelWidth.sm}
            title="店铺"
            visible={editVisible}
            setVisible={(v) => {
              if (!v && urlObj?.operate) {
                history.pushState(null, null, removeStateFromUrl('operate'));
              }
              setEditVisible(v);
            }}
            columns={editColumns}
            dataSource={dataSource}
            // 新增/修改
            onFinish={async (val: any) => {
              console.log('新增/修改', val);
              if (dataSource) val.id = dataSource.id;

              if (val.fileInfo && val.fileInfo.length > 0) val.iconFileId = val.fileInfo[0].id;
              // 确保 iconType 始终有值
              if (!val.iconType) {
                val.iconType = 'ICON_DEFAULT';
              }
              const msgType = dataSource ? '修改' : '添加';
              try {
                if (dataSource) {
                  // 修改
                  const updateData = {
                    ...val,
                    distributorId: coId,
                  };

                  // 如果 recommend 未修改则不传递
                  if (updateData.recommend === dataSource.recommend) {
                    delete updateData.recommend;
                  }

                  await apiDistributorShop(updateData);
                } else {
                  // 添加
                  if (settlementId) {
                    await apiShopAdd({
                      distributorId: coId,
                      name: val.name,
                      iconType: val.iconType || 'ICON_DEFAULT',
                      iconFileId: val.iconFileId,
                      introduce: val.introduce,
                      recommend: val.recommend || false,
                      appInfo: {
                        appBrief: val.introduce, // 应用简介
                        applicationName: val.name, // 应用名称
                        enterpriseId: '1', // 企业 ID
                        imgUrl: DEFAULT_LOGO, // 图片 url
                        loginType: 2, // 登录方式：1 单设备 2 多设备
                        logoutType: 2, // 登出方式：1 一退全退 2 只退一个
                        type: 2, // 应用类型：1 PC 端 2 移动端
                        url: MALL_HOST + '?storeId=', // 回调地址
                      },
                    });

                    addOperationLogRequest({
                      action: 'add',
                      content: `创建店铺【${val.name}】`,
                    });
                  } else {
                    message.error('请先实名认证');
                    return;
                  }
                }
                message.success(msgType + '成功');
                // 关闭弹窗并刷新列表
                setEditVisible(false);
                selectReload();
                // 更新引导
                updateGuideInfo({ tabIndex: 0, status: GuideStepStatus.step0_3 });
                history.pushState(null, null, removeStateFromUrl('operate'));
              } catch (error) {
                console.log(error);
              }
            }}
          />
          {/* 员工销售权限 */}
          <SaleAuthorityModal modalState={saleAuthorityModalState} />
        </div>
        {type && (
          <Goods type={type} id={id} setType={setType} storeId={store?.value} goodsRef={goodsRef} />
        )}
      </TabKeyContext.Provider>
    </>
  );
};
