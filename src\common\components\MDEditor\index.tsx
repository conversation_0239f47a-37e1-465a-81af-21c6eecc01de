/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-05 16:07:28
 * @LastEditTime: 2022-07-22 16:54:44
 * @LastEditors: zhangfengfei
 */
import { uploadFile } from '@/services/api/file';
import gfm from '@bytemd/plugin-gfm';
import zh_<PERSON>_ from '@bytemd/plugin-gfm/locales/zh_Hans.json';
import { Editor, Viewer } from '@bytemd/react';
import 'bytemd/dist/index.css';
import zh_Hans from 'bytemd/locales/zh_Hans.json';
import 'github-markdown-css/github-markdown.css';
import type { FC } from 'react';
import styles from './index.less';
import { getEnv } from '@/common/utils/getEnv';

const plugins = [
  gfm({ locale: zh_Hans_ }),
  // 其他插件
];

//
interface MDEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  /**只读模式 value 必传 */
  readonly?: boolean;
}

/**
 * @params 如被 FormItem 嵌套，value 和 onChange 无需再定义
 * @description  markdown 编辑器
 * @see https://github.com/bytedance/bytemd
 *
 */
const MDEditor: FC<MDEditorProps> = ({ value = '', onChange, readonly }) => {
  if (readonly) {
    return <Viewer value={value} plugins={plugins} />;
  }

  return (
    <div className={styles.editor}>
      <Editor
        // previewDebounce={300}
        mode="split"
        value={value}
        plugins={plugins}
        locale={zh_Hans}
        onChange={(v) => {
          if (onChange) {
            onChange(v);
          }
        }}
        // 图片上传 支持多选
        uploadImages={async (files: File[]) => {
          try {
            const data = await uploadFile(files);
            const { IMG_HOST } = getEnv();
            return data.map((item) => ({
              alt: item.name,
              url: `${IMG_HOST}${item.path}`,
            }));
          } catch (error) {
            return [];
          }
        }}
      />
    </div>
  );
};
export default MDEditor;
