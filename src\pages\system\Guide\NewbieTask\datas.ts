const STEP_ITEMS = [
  {
    key: 1,
    label: '完成企业认证',
    isUsed: true,
    desc: '完成认证后可使用收款功能',
  },
  {
    key: 2,
    label: '价格策略设置',
    isUsed: true,
    desc: '您尚未完成价格设置。合理设置价格，既能保证利润，又能吸引更多顾客下单，提升店铺销量！',
  },
  {
    key: 3,
    label: '创建店铺',
    isUsed: true,
    desc: '创建店铺并设置一个好名字能增加游客好感度和信任度',
  },
  {
    key: 4,
    label: '上架商品',
    isUsed: true,
    desc: '您的店铺尚未上架商品！快来添加心仪的产品，开启您的销售之旅',
  },
  {
    key: 5,
    label: '开启窗口售票权限',
    isUsed: true,
    desc: '开启后游客可在现场直接购票，提升服务效率，抓住更多销售机会',
  },
  {
    key: 6,
    label: '开启自助售票权限',
    isUsed: true,
    desc: '开启后游客可自助购票，提升用户体验，轻松拓展销售渠道',
  },
  {
    key: 7,
    label: '完成第一个测试订单',
    isUsed: true,
    desc: '通过测试订单，您可以提前熟悉购票流程，优化设置，为正式销售做好准备！',
  },
];

const UrlEnum = {
  1: {
    url: '/enterprise',
    isUsed: true,
  },
  2: {
    url: '/partner/sale-channel/price-strategy',
    isUsed: true,
  },
  3: {
    url: '/business/my-shop?operate=addStore',
    isUsed: true,
  },
  4: {
    url: '/business/my-shop?operate=addGoods',
    isUsed: true,
  },
  5: {
    url: '/business/my-shop?operate=saleAuthority',
    isUsed: true,
  },
  6: {
    url: '/business/my-shop?operate=saleAuthority',
    isUsed: true,
  },
  7: {
    url: '/business/my-shop?tabKey=shopPreview',
    isUsed: true,
  },
};

export { STEP_ITEMS, UrlEnum };
