import { useEffect, useState } from 'react';

export interface SubpageState {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  tableStyle: React.CSSProperties;
  setTableStyle: React.Dispatch<React.SetStateAction<React.CSSProperties>>;
}

export default function useSubpage(): SubpageState {
  const [open, setOpen] = useState<boolean>(false);
  const [tableStyle, setTableStyle] = useState<React.CSSProperties>({});

  useEffect(() => {
    setTableStyle(open ? { display: 'none' } : {});
  }, [open]);

  return {
    /** 二级页面状态 */
    open,
    /** 设置二级页面状态 */
    setOpen,
    /** 表格样式 */
    tableStyle,
    /** 设置表格样式 */
    setTableStyle,
  };
}
