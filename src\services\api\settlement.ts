/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-23 09:37:33
 * @LastEditTime: 2022-06-10 14:43:20
 * @LastEditors: zhangfengfei
 */

import { request } from '@umijs/max';
import { scenicHost } from '.';
import type { API } from './typings';

// 授信后端 list 接口格式
interface ResponseListData<T, S = Record<string, any>> {
  code: number;
  data: {
    otherAttribute?: S;
    page: T;
    pageNumber: number;
    pageSize: number;
    totalNumberOfResults: number;
  };
  msg: string;
}
// 授信后端普通接口格式
interface ResponseData<T> {
  code: number;
  data: T;
  msg: string;
}

/******************* 授信管理 ********************************/

/** 查询授信账户信息列表 授信客户 list */
export async function getCreditAccountList(
  params: API.CreditAccountListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.CreditAccountItem[]>>(
    `${scenicHost}/paypal/creditAccountsInfo`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/** 创建授信客户信息 */
export async function addCreditAccount(
  params: API.AddCreditAccountParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<null>>(`${scenicHost}/paypal/creditAccounts`, {
    method: 'POST',
    data: params,
    ...options,
  });
}

/** 修改授信客户信息 */
export async function updateCreditAccount(
  params: API.UpdateCreditAccountParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<null>>(`${scenicHost}/paypal/creditAccounts`, {
    method: 'PUT',
    data: params,
    ...options,
  });
}

/** 增减授信额度 */
export async function updateCreditBalance(
  params: API.UpdateCreditBalanceParams,
  options: Record<string, any> = {},
) {
  return request<ResponseData<null>>(`${scenicHost}/paypal/creditBalance`, {
    method: 'POST',
    data: params,
    ...options,
  });
}
/** 根据企业 id 查询下级供应商、代理商基础信息 */
export async function getAgentInfoList(params: { id: string }, options: Record<string, any> = {}) {
  return request<ResponseData<API.AgentInfoItem[]>>(
    `${scenicHost}/ticketAgent/baseAgentInfoList/${params.id}`,
    {
      method: 'GET',
      ...options,
    },
  );
}

/** 查询授信客户信息详情 */
export async function getCreditAccountInfo(
  params: API.CreditDefaultParams & {
    id: string;
  },
  options: Record<string, any> = {},
) {
  return request<ResponseData<API.CreditAccountItem>>(`${scenicHost}/paypal/creditAccounts`, {
    method: 'GET',
    params,
    ...options,
  });
}

/** 授信账户收支明细  授信结算单*/
export async function getBillDetail(
  params: API.BillDetailParams,
  options: Record<string, any> = {},
) {
  return request<
    ResponseListData<
      API.BillDetailItem[],
      {
        income: number;
        outcome: number;
      }
    >
  >(`${scenicHost}/paypal/creditEntry`, {
    method: 'GET',
    params,
    ...options,
  });
}

/** 查询授信操作记录 */
export async function getOperationRecordList(
  params: API.OperationRecordListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.OperationRecordItem[]>>(
    `${scenicHost}/paypal/creditAccountChangePage`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/** 授信账户结算单（卖家账单） */
export async function getProviderSettlementList(
  params: API.ProviderSettlementListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.ProviderSettlementItem[]>>(
    `${scenicHost}/paypal/creditProviderSettlements`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/** 卖家账单 - 收支明细 */
export async function getProviderBillDetail(
  params: API.ProviderBillDetailParams,
  options: Record<string, any> = {},
) {
  return request<
    ResponseListData<
      API.ProviderBillDetailItem[],
      {
        income: number;
        outcome: number;
      }
    >
  >(`${scenicHost}/paypal/creditSettlementBillItems`, {
    method: 'GET',
    params,
    ...options,
  });
}
/** 卖家账单 - 收支明细（授信结单） */
export async function getCreditSettlementBillItemsDetail(
  params: API.ProviderBillDetailParams,
  options: Record<string, any> = {},
) {
  return request<
    ResponseListData<
      API.ProviderBillDetailItem[],
      {
        income: number;
        outcome: number;
      }
    >
  >(`${scenicHost}/paypal/creditSettlementBillItemsDetail`, {
    method: 'GET',
    params,
    ...options,
  });
}
/** 卖家账单 - 收支明细（还款记录） */
export async function getReturnRecordSettlementBillItems(
  params: API.ProviderBillDetailParams,
  options: Record<string, any> = {},
) {
  return request<
    ResponseListData<
      API.ProviderBillDetailItem[],
      {
        income: number;
        outcome: number;
      }
    >
  >(`${scenicHost}/paypal/returnRecordSettlementBillItems`, {
    method: 'GET',
    params,
    ...options,
  });
}
/******************* 账户管理 ********************************/

/** 查询当前可用授信账户信息列表 授信账户 list */
export async function getConsumerAccountList(
  params: API.CreditDefaultParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.CreditAccountItem[], { balance: number }>>(
    `${scenicHost}/paypal/creditConsumerAccounts`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}
/** 授信账户结算单（买家账单） */
export async function getConsumerSettlementList(
  params: API.ConsumerSettlementListParams,
  options: Record<string, any> = {},
) {
  return request<ResponseListData<API.ConsumerSettlementItem[]>>(
    `${scenicHost}/paypal/creditConsumerSettlements`,
    {
      method: 'GET',
      params,
      ...options,
    },
  );
}

/** 授信结算账单确认 */
export async function confirmCreditBill(
  params: { id: string; merchantId: string },
  options: Record<string, any> = {},
) {
  return request<ResponseData<null>>(`${scenicHost}/paypal/creditSettlementBills`, {
    method: 'PUT',
    data: params,
    ...options,
  });
}

/******************* 账单管理 ********************************/
// 由于接口格式不同，写在 biiManager 里面
