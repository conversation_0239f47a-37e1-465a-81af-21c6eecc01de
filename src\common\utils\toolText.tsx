import dayjs from 'dayjs';

// 列表时间范围展示处理
export const transformTimeText = (
  t1: number | string,
  t2: number | string,
  symbol: string = '至',
  emptySymbol = '--',
) => {
  if (t1 && t2) {
    return `${t1 ? dayjs(t1).format('YYYY-MM-DD') : emptySymbol} ${symbol} ${
      t2 ? dayjs(t2).format('YYYY-MM-DD') : emptySymbol
    }`;
  } else {
    return <div style={{ textAlign: 'center' }}>{emptySymbol}</div>;
  }
};
