import { request } from '@umijs/max';
import { scenicHost } from '.';
import { getEnv } from '@/common/utils/getEnv';
/* 【系统设置模块】 */

const { OPERATION_HOST } = getEnv();
// 全局配置 - 详情
export async function apiScenicConfig(params: any) {
  const { data } = await request(`${scenicHost}/scenicConfig/info/${params.id}`, {
    method: 'GET',
  });
  return data;
}
// 全局配置 - 修改
export async function apiScenicConfigInfo(params: any) {
  const { data } = await request(`${scenicHost}/scenicConfig/info`, {
    method: 'POST',
    data: params,
  });
  return data;
}

// 全局配置 - 分页条件查询埋点日志信息
export async function apiSensorLogList(params: any) {
  return await request(`${OPERATION_HOST}/page`, {
    method: 'GET',
    params: params,
  });
}
// 查询模块列表
export async function apiModuleList(params: any) {
  return await request(`${OPERATION_HOST}/moduleList`, {
    method: 'GET',
    params: params,
  });
}
