/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-24 16:35:25
 * @LastEditTime: 2022-08-26 09:40:54
 * @LastEditors: zhangfengfei
 */

import { tableConfig } from '@/common/utils/config';
import { payTypeEnum, saleChannelEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { downloadBlobFile } from '@/common/utils/tool';
import type { ModalState } from '@/hooks/useModal';
import { exportBillDetailDetail, getComissionBillDetail } from '@/services/api/billManage';
import type { API } from '@/services/api/typings';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import type { FC } from 'react';
import { useRequest } from '@umijs/max';

interface SettlementDetailModalProps {
  currentItem?: API.AgentBillItem;
  modalState: ModalState;
}

/**
 * @description 结算登记->结算明细
 * */
const SettlementDetailModal: FC<SettlementDetailModalProps> = ({
  currentItem,
  modalState: { visible, setVisible },
}) => {
  const exportReq = useRequest(exportBillDetailDetail, {
    manual: true,
    formatResult: (res) => {
      downloadBlobFile(res, `结算单${currentItem?.billId}明细.xls`, 'msexcel');
    },
  });
  const onExport = () => {
    if (currentItem)
      exportReq.run({
        id: currentItem.billId,
      });
  };

  const tableListReq = async (params: { id: string }) => {
    const { data } = await getComissionBillDetail(params);
    addOperationLogRequest({
      action: 'info',
      content: `查看【${params.id}】佣金账单`,
    });

    return {
      data,
    };
  };

  const onCancel = () => setVisible(false);

  const columns: ProColumns<API.AgentBillDetailItem>[] = [
    {
      title: '结算单号',
      dataIndex: 'tradeNo',
    },
    {
      title: '创建时间',
      dataIndex: 'payTime',
      valueType: 'select',
      valueEnum: saleChannelEnum,
    },
    {
      title: '购买终端',
      dataIndex: 'sourceType',
      valueType: 'select',
      valueEnum: saleChannelEnum,
    },
    {
      title: '对应订单号',
      dataIndex: 'orderId',
    },
    {
      title: '买家账号',
      dataIndex: 'userId',
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      valueType: 'select',
      valueEnum: payTypeEnum,
    },

    {
      title: '票号',
      dataIndex: 'ticketNumber',
    },
    {
      title: '佣金费率(%)',
      dataIndex: 'commissionRate',
    },
    // {
    //   title: '固定佣金（元）',
    //   dataIndex: 'commissionAmount',
    // },
    {
      title: '价格（元）',
      dataIndex: 'price',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
    {
      title: '分佣金额（元）',
      dataIndex: 'actualComAmount',
      align: 'right',
      renderText: (dom) => (parseFloat(dom) ? parseFloat(dom).toFixed(2) : dom),
    },
  ];

  return (
    <Modal
      width={1600}
      title={'结算明细详情'}
      open={visible}
      maskClosable
      destroyOnClose
      onCancel={onCancel}
      okText="导出"
      confirmLoading={exportReq.loading}
      onOk={onExport}
    >
      <ProTable<API.AgentBillDetailItem, API.AgentBillDetailParams>
        {...tableConfig}
        headerTitle={`代理商名称：${currentItem?.name || '-'}`}
        search={false}
        pagination={false}
        params={{ id: currentItem?.billId || '' }}
        request={tableListReq}
        columns={columns}
      />
    </Modal>
  );
};

export default SettlementDetailModal;
