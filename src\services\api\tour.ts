/**
 * 店铺导览
 */
import { request } from '@umijs/max';
import { scenicHost } from '.';
const url = (v: string) => scenicHost + v;

/** 新增导览 */
export function addStore(data: any) {
  return request(url('/navigation/store/add'), {
    method: 'POST',
    data,
  });
}
/** 修改导览 */
export function editStore(data: any) {
  return request(url('/navigation/store/update'), {
    method: 'PUT',
    data,
  });
}
/** 查看导览 */
export function infoStore(params: any) {
  return request(url('/navigation/store/info'), { params });
}
/** 禁用导览 */
export function enableStore(data: any) {
  return request(url('/navigation/store/enable'), {
    method: 'PUT',
    data,
  });
}
/** 删除导览 */
export function deleteStore(params: any) {
  return request(url('/navigation/store/delete/' + params.id), {
    method: 'DELETE',
  });
}
/** 导览分页 */
export async function pageStore(params: any) {
  const { data, code } = await request(url('/navigation/store/page'), { params });
  return {
    data: data.data,
    success: code == 20000,
    total: data.total,
  };
}
/** 导览排序列表 */
export async function sortStoreList(params: any) {
  return request(url('/navigation/store/sort/list'), { params });
}
/** 导览排序 */
export async function sortStore(data: any) {
  return request(url('/navigation/store/batch/sort'), {
    method: 'PUT',
    data,
  });
}
