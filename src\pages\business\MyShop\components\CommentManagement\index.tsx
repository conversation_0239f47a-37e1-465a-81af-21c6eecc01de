/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-14 14:17:08
 * @LastEditTime: 2025-07-09 14:44:58
 * @LastEditors: 李悍宇 <EMAIL>
 */
import TagCom from '@/common/components/Tag';
import { tableConfig } from '@/common/utils/config';
import { commentContentType, commentType, shopAnswerType } from '@/common/utils/enum';
import {
  addMerchantComment,
  getCommentPage,
  getScencList,
  updRecommend,
} from '@/services/api/comment';
import { PlayCircleOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Image, Input, message, Modal, Popconfirm, Space, Tabs, Tag } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import TagsShow from './tagsShow';
import { getEnv } from '@/common/utils/getEnv';

export type TabKeyType = '1' | '0';
let scencList = [];
const CommentManagement = ({ storeId }) => {
  const [tabKey, setTabKey] = useState<TabKeyType>('1');
  const [replyModalShow, setReplyModalShow] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [tableListItem, setTableListItem] = useState<any>();
  const formRef = React.useRef<ProFormInstance<any>>();
  const [iptVal, setIptVal] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const { IMG_HOSTCOMMENT } = getEnv();
  const tableListRequest = async (params: any) => {
    // 数据处理
    console.log(params);
    const { data } = await getCommentPage({
      ...params,
      storeId,
    });
    return {
      data: data.data,
      total: data.total,
    };
  };
  const getScenc = async () => {
    const res = await getScencList();
    if (res.code === 20000) {
      scencList = res.data.map((item) => {
        return {
          label: item.keyName,
          value: item.keyId,
        };
      });
    }
  };
  const isVideo = (url) => {
    const videoExtensions = ['mp4', 'mov', 'avi', 'wmv', 'flv'];
    const ext = url.split('.').pop().toLowerCase();
    return videoExtensions.includes(ext);
  };
  // 处理视频预览
  const handleVideoPreview = (url: string) => {
    setVideoPreviewUrl(`${getEnv().IMG_HOSTCOMMENT}${url}`);
    setVideoPreviewVisible(true);
  };
  useEffect(() => {
    getScenc();
  }, []);
  const columns: ProColumns<any>[] = [
    {
      title: '评价内容',
      dataIndex: 'comment',
      search: false,
      // width: 300,
      render: (dom, record) => {
        return (
          <div
            style={{
              width: 'auto',
              minWidth: '300px',
              maxWidth: '800px',
              whiteSpace: 'normal',
              wordWrap: 'break-word',
            }}
          >
            <div style={{ marginBottom: '10px' }}>
              {record.recommend === 1 && <Tag color="success">推荐</Tag>}
              {dom}
            </div>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
              {record?.msgUrl?.map((item, index) => {
                return isVideo(item) ? (
                  // 修改视频展示方式，添加预览功能
                  <div
                    key={index}
                    style={{
                      position: 'relative',
                      cursor: 'pointer',
                      width: '80px',
                      height: '80px',
                      overflow: 'hidden',
                      borderRadius: '4px',
                    }}
                    onClick={() => handleVideoPreview(item)}
                  >
                    <video
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        backgroundColor: '#000',
                      }}
                      autoPlay={false}
                      controls={false}
                      src={`${IMG_HOSTCOMMENT}${item}`}
                    />
                    <PlayCircleOutlined
                      style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        fontSize: '24px',
                        color: 'rgba(255,255,255,0.8)',
                      }}
                    />
                  </div>
                ) : (
                  <Image
                    key={index}
                    width={80}
                    height={80}
                    style={{ objectFit: 'cover', borderRadius: '4px' }}
                    src={`${IMG_HOSTCOMMENT}${item}`}
                    preview={{ src: `${IMG_HOSTCOMMENT}${item}` }}
                  />
                );
              })}
            </div>
            {record?.merchantCommentsList.map((item) => {
              return (
                <div style={{ padding: '6px 0', borderTop: '1px solid #ccc' }}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: '#1890ff' }}>商家回复</span>
                    <span style={{ marginLeft: '12px' }}>{item.createTime || '-'}</span>
                  </div>
                  <div>{item?.content}</div>
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '综合评价',
      dataIndex: 'score',
      search: false,
      renderText: (dom) => {
        return `${dom}.0分 ${['不佳', '一般', '不错', '满意', '超棒'][dom - 1]}`;
      },
    },
    {
      title: '服务评价',
      dataIndex: 'serviceScore',
      search: false,
      renderText: (dom, record) => {
        return `景色 ${record?.environmentScore}.0 / 趣味 ${record?.interestScore}.0 / 性价比 ${record?.serviceScore}.0`;
      },
    },
    {
      title: '评价时间',
      dataIndex: 'createTime',
      search: false,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      render: (dom, record) => {
        return record.orderId ? dom : '-';
      },
    },
    {
      title: '景区名称',
      dataIndex: 'scenicId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: scencList,
        showSearch: true,
      },
      render: (_, { keyId }) => {
        const current = scencList?.find((i) => i.value === keyId);
        return current?.keyName || '-';
      },
    },
    {
      title: '评价时间',
      dataIndex: 'time',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '评价类型',
      hideInTable: true,
      dataIndex: 'score',
      valueEnum: commentType,
    },
    {
      title: '评价内容',
      dataIndex: 'evaluationContent',
      valueEnum: commentContentType,
      hideInTable: true,
      renderText: (dom) => <TagCom type="chainStatus" value={dom} />,
    },
    {
      title: '商家回复',
      dataIndex: 'merchantType',
      valueEnum: shopAnswerType,
      hideInTable: true,
      renderText: (dom) => <TagCom type="chainStatus" value={dom} />,
    },
    {
      title: '订单号',
      dataIndex: 'orderId',
    },
    {
      title: '买家昵称',
      dataIndex: 'nickname',
      search: false,
    },
    {
      width: 180,
      title: '操作',
      dataIndex: '_option',
      valueType: 'option',
      render: (_, entity) => (
        <Space>
          <a
            onClick={() => {
              setTableListItem(entity);
              setReplyModalShow(true);
            }}
          >
            回复
          </a>
          <Popconfirm
            key={entity.id}
            title={`是否修改状态为${entity.recommend == 0 ? '推荐' : '不推荐'}`}
            okText="是"
            cancelText="否"
            onConfirm={async () => {
              const res = await updRecommend({
                id: entity.id,
                recommendType: entity.recommend === 0 ? 1 : 0, //0不推荐 1推荐
              });
              if (res.code === 20000) {
                actionRef.current?.reload();
              }
            }}
          >
            <a>{entity.recommend === 0 ? '推荐' : '不推荐'}</a>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Modal
        title="视频预览"
        open={videoPreviewVisible}
        footer={null}
        onCancel={() => setVideoPreviewVisible(false)}
        width={800}
        destroyOnClose
        centered
      >
        <video
          controls
          autoPlay
          style={{ width: '100%', maxHeight: '70vh' }}
          src={videoPreviewUrl}
        />
      </Modal>
      <Tabs
        style={{ background: '#fff' }}
        tabBarStyle={{ padding: '0 24px', margin: '0' }}
        onChange={(activeKey: any) => {
          setTabKey(activeKey);
          // actionRef?.current?.reload();
        }}
        items={[
          {
            label: '评论管理',
            key: '1',
          },
          {
            label: '评论标签',
            key: '0',
          },
        ]}
      />
      {tabKey === '1' ? (
        <ProTable<{ storeId: string }, any>
          {...tableConfig}
          key={tabKey}
          // options={false}
          actionRef={actionRef}
          columns={columns}
          pagination={false}
          params={{ storeId }}
          formRef={formRef}
          request={tableListRequest}
        />
      ) : (
        <TagsShow storeId={storeId} />
      )}
      <ReplyModal
        tableListItem={tableListItem}
        replyModalShow={replyModalShow}
        loading={loading}
        setLoading={setLoading}
        storeId={storeId}
        iptVal={iptVal}
        setReplyModalShow={setReplyModalShow}
        setIptVal={setIptVal}
        actionRef={actionRef}
      />
    </>
  );
};

const ReplyModal = ({
  tableListItem,
  replyModalShow,
  loading,
  setLoading,
  storeId,
  iptVal,
  setReplyModalShow,
  setIptVal,
  actionRef,
}) => {
  return (
    <Modal
      title={tableListItem?.nickname || '-'}
      open={replyModalShow}
      loading={loading}
      onOk={async () => {
        if (iptVal.replace(/ /g, '').length < 5) {
          message.warning('回复内容不能少于5个字');
          return;
        }
        setLoading(true);
        const res = await addMerchantComment({
          storeId,
          labelId: tableListItem?.id,
          content: iptVal,
        });
        setLoading(false);
        if (res.code === 20000) {
          setIptVal('');
          setReplyModalShow(false);
          actionRef?.current?.reload();
        }
      }}
      onCancel={() => {
        setReplyModalShow(false);
      }}
    >
      <Input.TextArea
        rows={6}
        placeholder="用礼貌的回复给买家留下一个美好的服务印象吧（不少于5个字）"
        maxLength={200}
        showCount={true}
        style={{ marginBottom: '15px' }}
        value={iptVal}
        onChange={(e) => {
          setIptVal(e.target.value);
        }}
      />
    </Modal>
  );
};

export default CommentManagement;
