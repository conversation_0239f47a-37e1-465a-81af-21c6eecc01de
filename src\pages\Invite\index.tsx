/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-13 13:53:56
 * @LastEditTime: 2022-09-22 17:56:23
 * @LastEditors: zhangfeng<PERSON>i
 */
import { copyText, isMobile } from '@/common/utils/tool';
import { postPermissionAuthorize } from '@/services/api/cas';
import { checkInviteToAdmin, exchangeInviteToAdmin } from '@/services/api/erp';
import { Button, Result, Space, Spin } from 'antd';
import qs from 'qs';
import type { FC } from 'react';
import { useEffect, useMemo, useState } from 'react';
import { history, useLocation, useRequest } from '@umijs/max';
import styles from './index.less';
import { getEnv } from '@/common/utils/getEnv';

interface InviteProps {}

const Invite: FC<InviteProps> = () => {
  // 访问设备
  const [device, setDevice] = useState<'PC' | 'phone'>('PC');
  // 接口状态
  const [status, setStatus] = useState<'success' | 'error'>('error');

  const { search } = useLocation();

  const { code } = qs.parse(search, {
    ignoreQueryPrefix: true,
  }) as unknown as {
    code: string;
  };

  // 赋予该景区和企业的电商和景区系统权限
  const updateRolePermissionReq = useRequest(exchangeInviteToAdmin, {
    manual: true,
    onSuccess(data, params) {
      setStatus('success');
      if (isMobile()) {
        setDevice('phone');
      }
    },
  });

  // 启用电商权限
  const setPermissionAuthorize = useRequest(postPermissionAuthorize, {
    manual: true,
    onSuccess(data, params) {
      if (checkInviteToAdminReq.data) {
        const { companyId, userId } = checkInviteToAdminReq.data;
        updateRolePermissionReq.run({
          companyId,
          type: '031',
          userId,
          appId: getEnv().APPID || '',
        });
      }
    },
  });

  // 验证邀请code码
  const checkInviteToAdminReq = useRequest(checkInviteToAdmin, {
    manual: true,
    onSuccess: (data, params) => {
      setPermissionAuthorize.run({
        permissionCode: [
          {
            group: 'backend/user_status',
            code: data.scenicId + '/' + data.companyId,
            action: '',
          },
          {
            group: 'e-commerce/user_status',
            code: data.companyId,
            action: '',
          },
        ],
        users: [data.userId],
        appId: getEnv().APPID,
      });
    },
  });

  const isLoading =
    checkInviteToAdminReq.loading ||
    setPermissionAuthorize.loading ||
    updateRolePermissionReq.loading;

  const result = useMemo(() => {
    return status === 'success' ? (
      <Result
        status="success"
        title="接受邀请成功"
        subTitle={`您已成为 ${checkInviteToAdminReq.data?.companyName} 企业管理员`}
        extra={[
          <Button
            type="primary"
            key="home"
            onClick={() => {
              history.push('/welcome');
              setTimeout(() => {
                window.location.reload();
              }, 500);
            }}
          >
            去首页
          </Button>,
        ]}
      />
    ) : (
      <Result
        status="error"
        title="接受邀请失败"
        subTitle="链接已失效或授权失败"
        extra={[
          <Button
            type="primary"
            key="reload"
            onClick={() => {
              window.location.reload();
            }}
          >
            刷新
          </Button>,
        ]}
      />
    );
  }, [checkInviteToAdminReq.data?.scenicName, status]);

  // 初始化操作
  useEffect(() => {
    setDevice('PC');
    checkInviteToAdminReq.run({ code });
    setStatus('error');
  }, []);

  return (
    <div className={styles.wrap}>
      <div className={styles.content}>
        {device === 'PC' ? (
          <>{isLoading ? <Spin size="large" spinning={isLoading} /> : result}</>
        ) : (
          <Space direction="vertical">
            <div>您已成为 {checkInviteToAdminReq.data?.companyName} 企业超级管理员</div>
            <div>您可以复制此链接登录电脑端进行管理操作。</div>
            <a>{window.location.href.split('/invite')[0].concat('/welcome')}</a>
            <div style={{ textAlign: 'center', marginTop: 24 }}>
              <Button
                type="primary"
                onClick={() => {
                  copyText(window.location.href.split('/invite')[0].concat('/welcome'));
                }}
              >
                复制链接
              </Button>
            </div>
          </Space>
        )}
      </div>
    </div>
  );
};

export default Invite;
