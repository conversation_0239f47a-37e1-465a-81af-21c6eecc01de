import React, { useState } from 'react';
import Chart from '@/components/Chart';
import { Radio, Space, Tabs } from 'antd';

interface EntranceStatisticsProps {
  // 可选的数据属性，方便后续扩展为真实数据
  entranceData?: {
    date: string;
    value: number;
    type: '入园人数' | '承载率';
  }[];
}

/**
 * 入园情况统计图表组件
 */
const EntranceStatisticsChart: React.FC<EntranceStatisticsProps> = ({ entranceData }) => {
  // 统计时段选择
  const [timeRange, setTimeRange] = useState('本周');
  // 当前激活的图表类型
  const [activeTab, setActiveTab] = useState('入园人数');

  // 默认的入园人数数据
  const defaultEntranceCount = [
    { date: '周一', value: 1200 },
    { date: '周二', value: 1800 },
    { date: '周三', value: 1500 },
    { date: '周四', value: 1300 },
    { date: '周五', value: 2200 },
    { date: '周六', value: 3500 },
    { date: '周日', value: 3800 },
  ];

  // 默认的承载率数据
  const defaultCapacityRate = [
    { date: '周一', value: 35 },
    { date: '周二', value: 45 },
    { date: '周三', value: 40 },
    { date: '周四', value: 38 },
    { date: '周五', value: 65 },
    { date: '周六', value: 85 },
    { date: '周日', value: 92 },
  ];

  // 根据选择的选项卡确定数据
  const currentData = activeTab === '入园人数' ? defaultEntranceCount : defaultCapacityRate;
  
  // 构建入园人数图表配置
  const entranceChartOptions = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${activeTab}: ${activeTab === '入园人数' ? param.value + '人' : param.value + '%'}`;
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '40px',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: currentData.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#eaeaea'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      name: activeTab === '入园人数' ? '人数' : '比率(%)',
      nameTextStyle: {
        color: '#666',
        padding: [0, 0, 0, 40]
      },
      axisLabel: {
        formatter: activeTab === '入园人数' ? '{value}' : '{value}%',
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#eaeaea'
        }
      },
      max: activeTab === '入园人数' ? null : 100
    },
    series: [
      {
        name: activeTab,
        type: 'bar',
        barWidth: '40%',
        data: currentData.map(item => item.value),
        itemStyle: {
          color: function(params: any) {
            // 承载率时用渐变色条
            if (activeTab === '承载率') {
              const rate = params.value;
              if (rate >= 80) return '#ff4d4f';  // 高承载 - 红色
              if (rate >= 60) return '#faad14';  // 中承载 - 黄色
              return '#52c41a';                   // 低承载 - 绿色
            }
            return '#1890ff';  // 入园人数统一使用蓝色
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: activeTab === '入园人数' ? '{c}人' : '{c}%',
          fontSize: 12,
          color: '#666'
        }
      }
    ]
  };

  return (
    <div style={{ marginTop: '40px', marginBottom: '24px' }}>
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <h3 style={{ 
          marginBottom: '16px', 
          fontSize: '16px', 
          fontWeight: 500, 
          color: '#1890ff', 
          borderLeft: '3px solid #1890ff',
          paddingLeft: '10px'
        }}>入园情况</h3>
        
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          marginBottom: '20px'
        }}>
          <Radio.Group 
            value={timeRange} 
            onChange={e => setTimeRange(e.target.value)}
            buttonStyle="solid"
          >
            <Radio.Button value="今日">今日</Radio.Button>
            <Radio.Button value="本周">本周</Radio.Button>
            <Radio.Button value="本月">本月</Radio.Button>
          </Radio.Group>
          
          <Radio.Group 
            value={activeTab}
            onChange={e => setActiveTab(e.target.value)}
            buttonStyle="outline"
            optionType="button"
          >
            <Radio.Button value="入园人数">入园人数</Radio.Button>
            <Radio.Button value="承载率">承载率</Radio.Button>
          </Radio.Group>
        </div>
      </div>
      
      <Chart options={entranceChartOptions} />
      
      <div style={{
        marginTop: '16px', 
        padding: '12px 16px', 
        background: '#f9f9f9', 
        borderRadius: '4px',
        color: '#666',
        fontSize: '13px'
      }}>
        <div style={{ marginBottom: '6px' }}>
          <span style={{ fontWeight: 'bold' }}>本周入园情况：</span>
          平均日入园人数 <span style={{ fontWeight: 'bold', color: '#1890ff' }}>2,185</span> 人，
          高峰日承载率 <span style={{ fontWeight: 'bold', color: '#ff4d4f' }}>92%</span>
        </div>
        <div>
          <span style={{ fontWeight: 'bold' }}>价格建议：</span>
          周末建议上调价格 <span style={{ fontWeight: 'bold', color: '#ff4d4f' }}>10%</span>，
          工作日可适当降价 <span style={{ fontWeight: 'bold', color: '#52c41a' }}>5%</span> 提高客流量
        </div>
      </div>
    </div>
  );
};

export default EntranceStatisticsChart; 