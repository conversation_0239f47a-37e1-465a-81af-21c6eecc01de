/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-26 11:47:45
 * @LastEditTime: 2022-06-02 11:15:07
 * @LastEditors: zhangfengfei
 */

import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Form, Select, Space } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';
import type { FormListFieldData, FormListOperation } from 'antd/lib/form/FormList';
import type { CSSProperties, FC } from 'react';
import { useState } from 'react';

const monthOptions = new Array(12).fill(0).map((_, index) => ({
  label: index + 1,
  value: index + 1,
}));

const data = [
  {
    months: [1, 3, 5, 7, 8, 10, 12],
    days: 31,
  },
  {
    months: [4, 6, 9, 11],
    days: 30,
  },
  {
    months: [2],
    days: 29,
  },
];

interface BirthdayPickerProps {
  field: FormListFieldData;
  action: FormListOperation;
}

const styles: CSSProperties = {
  marginBottom: 0,
};

/**
 * @description: 月份日期选择器
 */
export const BirthdayPicker: FC<BirthdayPickerProps> = ({
  field: { name, ...restFields },
  action: { add, remove },
  ...rest
}) => {
  console.log(rest);

  const [dayOptions, setDayOptions] = useState<DefaultOptionType[]>([]);

  const handleChange = (month: number) => {
    const dataItem = data.find((item) => item.months.includes(month));
    const options = new Array(dataItem!.days).fill(0).map((_, index) => ({
      label: index + 1,
      value: index + 1,
    }));
    setDayOptions(options);
  };

  return (
    <Space style={{ display: 'flex', alignItems: 'baseline', marginBottom: 12 }}>
      <Form.Item
        {...restFields}
        key="month"
        name={[name, 'month']}
        rules={[
          {
            required: true,
            message: '请选择月份',
          },
        ]}
        style={styles}
        // noStyle
      >
        <Select
          style={{ display: 'inline-block', width: 100 }}
          onChange={handleChange}
          placeholder="请输入"
          options={monthOptions}
        />
      </Form.Item>
      月
      <Form.Item
        {...restFields}
        key="day"
        name={[name, 'day']}
        rules={[
          {
            required: true,
            message: '请选择日期',
          },
        ]}
        style={styles}
      >
        <Select
          style={{ display: 'inline-block', width: 100 }}
          placeholder="请输入"
          options={dayOptions}
        />
      </Form.Item>
      日
      <MinusCircleOutlined style={{ fontSize: 16 }} onClick={() => remove(name)} />
      <PlusCircleOutlined style={{ fontSize: 16 }} onClick={() => add()} />
    </Space>
  );
};
